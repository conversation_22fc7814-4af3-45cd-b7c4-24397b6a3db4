import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Router, ActivatedRoute } from '@angular/router';
import { GroupService } from '../../services/group.service';
import { CreateGroupRequest, JoinGroupRequest } from '../../types/group.types';

@Component({
  selector: 'app-group-manager',
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './group-manager.component.html',
  styleUrl: './group-manager.component.scss'
})
export class GroupManagerComponent implements OnInit {
  mode: 'create' | 'join' = 'create';
  isLoading = false;
  errorMessage = '';
  successMessage = '';

  // Create group form
  createForm: CreateGroupRequest = {
    onionAddress: '',
    encryptedConfig: '',
    masterKey: '',
    securityThreshold: 2
  };

  // Join group form
  joinForm: JoinGroupRequest = {
    inviteToken: '',
    secretWord: ''
  };

  securityThresholdOptions = [
    { value: 1, label: 'Low (1 confirmation)' },
    { value: 2, label: 'Medium (2 confirmations)' },
    { value: 3, label: 'High (3 confirmations)' },
    { value: 5, label: 'Maximum (5 confirmations)' }
  ];

  constructor(
    private groupService: GroupService,
    private router: Router,
    private route: ActivatedRoute
  ) {}

  ngOnInit(): void {
    // Determine mode from route
    const path = this.route.snapshot.url[this.route.snapshot.url.length - 1]?.path;
    this.mode = path === 'join' ? 'join' : 'create';

    // Generate default values for create mode
    if (this.mode === 'create') {
      this.generateDefaults();
    }
  }

  private generateDefaults(): void {
    // Generate a random onion address placeholder
    this.createForm.onionAddress = this.generateOnionAddress();
    
    // Generate a random master key
    this.createForm.masterKey = this.generateMasterKey();
    
    // Generate encrypted config placeholder
    this.createForm.encryptedConfig = this.generateEncryptedConfig();
  }

  private generateOnionAddress(): string {
    // Generate a realistic-looking onion address
    const chars = 'abcdefghijklmnopqrstuvwxyz234567';
    let result = '';
    for (let i = 0; i < 56; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result + '.onion';
  }

  private generateMasterKey(): string {
    // Generate a base64-like master key
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/';
    let result = '';
    for (let i = 0; i < 64; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  }

  private generateEncryptedConfig(): string {
    // Generate encrypted config placeholder
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/';
    let result = '';
    for (let i = 0; i < 128; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  }

  onCreateGroup(): void {
    if (!this.validateCreateForm()) {
      return;
    }

    this.isLoading = true;
    this.errorMessage = '';
    this.successMessage = '';

    this.groupService.createGroup(this.createForm).subscribe({
      next: (group) => {
        this.isLoading = false;
        this.successMessage = 'Group created successfully!';
        
        setTimeout(() => {
          this.router.navigate(['/main']);
        }, 1500);
      },
      error: (error) => {
        this.isLoading = false;
        this.errorMessage = error.message || 'Failed to create group. Please try again.';
      }
    });
  }

  onJoinGroup(): void {
    if (!this.validateJoinForm()) {
      return;
    }

    this.isLoading = true;
    this.errorMessage = '';
    this.successMessage = '';

    this.groupService.joinGroup(this.joinForm).subscribe({
      next: (membership) => {
        this.isLoading = false;
        this.successMessage = 'Successfully joined group!';
        
        setTimeout(() => {
          this.router.navigate(['/main']);
        }, 1500);
      },
      error: (error) => {
        this.isLoading = false;
        this.errorMessage = error.message || 'Failed to join group. Please check your invite token and secret word.';
      }
    });
  }

  private validateCreateForm(): boolean {
    if (!this.createForm.onionAddress.trim()) {
      this.errorMessage = 'Onion address is required';
      return false;
    }

    if (!this.createForm.masterKey.trim()) {
      this.errorMessage = 'Master key is required';
      return false;
    }

    if (!this.createForm.encryptedConfig.trim()) {
      this.errorMessage = 'Encrypted config is required';
      return false;
    }

    return true;
  }

  private validateJoinForm(): boolean {
    if (!this.joinForm.inviteToken.trim()) {
      this.errorMessage = 'Invite token is required';
      return false;
    }

    if (!this.joinForm.secretWord.trim()) {
      this.errorMessage = 'Secret word is required';
      return false;
    }

    return true;
  }

  onCancel(): void {
    this.router.navigate(['/main']);
  }

  switchMode(newMode: 'create' | 'join'): void {
    this.mode = newMode;
    this.errorMessage = '';
    this.successMessage = '';
    
    if (newMode === 'create') {
      this.generateDefaults();
    }
  }
}
