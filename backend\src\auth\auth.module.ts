import { Module, forwardRef } from '@nestjs/common';
import { JwtModule } from '@nestjs/jwt';
import { PassportModule } from '@nestjs/passport';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { AuthService } from './auth.service';
import { AuthController } from './auth.controller';
import { JwtStrategy } from './strategies/jwt.strategy';
import { UserModule } from '../user/user.module';
import { EmailModule } from '../email/email.module';
import { SecurityModule } from '../security/security.module';
import { DilithiumService } from './services/dilithium.service';
import { SecretWordService } from './services/secret-word.service';
// import { RedisModule } from '../redis/redis.module'; // Disabled for development

@Module({
  imports: [
    forwardRef(() => UserModule),
    // EmailModule, // Disabled for development to avoid Redis dependency
    SecurityModule,
    PassportModule,
    // RedisModule, // Disabled for development
    JwtModule.registerAsync({
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => ({
        secret: configService.get<string>('JWT_SECRET'),
        signOptions: { expiresIn: '7d' },
      }),
      inject: [ConfigService],
    }),
  ],
  providers: [AuthService, JwtStrategy, DilithiumService, SecretWordService],
  controllers: [AuthController],
  exports: [AuthService, DilithiumService, SecretWordService],
})
export class AuthModule {}