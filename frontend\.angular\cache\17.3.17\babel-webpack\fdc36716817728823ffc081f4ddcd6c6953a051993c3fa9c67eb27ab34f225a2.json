{"ast": null, "code": "import { provideRouter, withEnabledBlockingInitialNavigation } from '@angular/router';\nimport { provideHttpClient, withInterceptors, withXsrfConfiguration } from '@angular/common/http';\nimport { provideAnimations } from '@angular/platform-browser/animations';\nimport { routes } from './app.routes';\nimport { WasmService } from './services/wasm.service';\nimport { ErrorHandlingService } from './services/error-handling.service';\nimport { NotificationService } from './services/notification.service';\nimport { ChainDeletionService } from './services/chain-deletion.service';\nimport { SecureStorageService } from './services/secure-storage.service';\nimport { AuthService } from './services/auth.service';\nimport { ApiService } from './services/api.service';\nimport { StateService } from './services/state.service';\nimport { EncryptionService } from './services/encryption.service';\nimport { GroupService } from './services/group.service';\nimport { MessageDeletionService } from './services/message-deletion.service';\nimport { SecretWordService } from './services/secret-word.service';\nimport { SecureTokenService } from './services/secure-token.service';\nimport { authInterceptor } from './interceptors/auth.interceptor';\nexport const appConfig = {\n  providers: [provideRouter(routes, withEnabledBlockingInitialNavigation()), provideHttpClient(withXsrfConfiguration({\n    cookieName: 'XSRF-TOKEN',\n    headerName: 'X-XSRF-TOKEN'\n  }), withInterceptors([authInterceptor])), provideAnimations(),\n  // Core Services\n  WasmService, ErrorHandlingService, NotificationService, ChainDeletionService, SecureStorageService, AuthService, ApiService, StateService, EncryptionService, GroupService, MessageDeletionService, SecretWordService, SecureTokenService]\n};", "map": {"version": 3, "names": ["provideRouter", "withEnabledBlockingInitialNavigation", "provideHttpClient", "withInterceptors", "withXsrfConfiguration", "provideAnimations", "routes", "WasmService", "ErrorHandlingService", "NotificationService", "ChainDeletionService", "SecureStorageService", "AuthService", "ApiService", "StateService", "EncryptionService", "GroupService", "MessageDeletionService", "SecretWordService", "SecureTokenService", "authInterceptor", "appConfig", "providers", "cookieName", "headerName"], "sources": ["C:\\Users\\<USER>\\Projects\\QSC1\\frontend\\src\\app\\app.config.ts"], "sourcesContent": ["import { ApplicationConfig, importProvidersFrom } from '@angular/core';\r\nimport { provideRouter, withEnabledBlockingInitialNavigation } from '@angular/router';\r\nimport { provideHttpClient, withInterceptors, withXsrfConfiguration } from '@angular/common/http';\r\nimport { provideAnimations } from '@angular/platform-browser/animations';\r\n\r\nimport { routes } from './app.routes';\r\nimport { WasmService } from './services/wasm.service';\r\nimport { ErrorHandlingService } from './services/error-handling.service';\r\nimport { NotificationService } from './services/notification.service';\r\nimport { ChainDeletionService } from './services/chain-deletion.service';\r\nimport { SecureStorageService } from './services/secure-storage.service';\r\nimport { AuthService } from './services/auth.service';\r\nimport { ApiService } from './services/api.service';\r\nimport { StateService } from './services/state.service';\r\nimport { EncryptionService } from './services/encryption.service';\r\nimport { GroupService } from './services/group.service';\r\nimport { MessageDeletionService } from './services/message-deletion.service';\r\nimport { SecretWordService } from './services/secret-word.service';\r\nimport { SecureTokenService } from './services/secure-token.service';\r\nimport { authInterceptor } from './interceptors/auth.interceptor';\r\n\r\nexport const appConfig: ApplicationConfig = {\r\n  providers: [\r\n    provideRouter(routes, withEnabledBlockingInitialNavigation()),\r\n    provideHttpClient(\r\n      withXsrfConfiguration({\r\n        cookieName: 'XSRF-TOKEN',\r\n        headerName: 'X-XSRF-TOKEN',\r\n      }),\r\n      withInterceptors([authInterceptor])\r\n    ),\r\n    provideAnimations(),\r\n\r\n    // Core Services\r\n    WasmService,\r\n    ErrorHandlingService,\r\n    NotificationService,\r\n    ChainDeletionService,\r\n    SecureStorageService,\r\n    AuthService,\r\n    ApiService,\r\n    StateService,\r\n    EncryptionService,\r\n    GroupService,\r\n    MessageDeletionService,\r\n    SecretWordService,\r\n    SecureTokenService\r\n  ]\r\n};\r\n"], "mappings": "AACA,SAASA,aAAa,EAAEC,oCAAoC,QAAQ,iBAAiB;AACrF,SAASC,iBAAiB,EAAEC,gBAAgB,EAAEC,qBAAqB,QAAQ,sBAAsB;AACjG,SAASC,iBAAiB,QAAQ,sCAAsC;AAExE,SAASC,MAAM,QAAQ,cAAc;AACrC,SAASC,WAAW,QAAQ,yBAAyB;AACrD,SAASC,oBAAoB,QAAQ,mCAAmC;AACxE,SAASC,mBAAmB,QAAQ,iCAAiC;AACrE,SAASC,oBAAoB,QAAQ,mCAAmC;AACxE,SAASC,oBAAoB,QAAQ,mCAAmC;AACxE,SAASC,WAAW,QAAQ,yBAAyB;AACrD,SAASC,UAAU,QAAQ,wBAAwB;AACnD,SAASC,YAAY,QAAQ,0BAA0B;AACvD,SAASC,iBAAiB,QAAQ,+BAA+B;AACjE,SAASC,YAAY,QAAQ,0BAA0B;AACvD,SAASC,sBAAsB,QAAQ,qCAAqC;AAC5E,SAASC,iBAAiB,QAAQ,gCAAgC;AAClE,SAASC,kBAAkB,QAAQ,iCAAiC;AACpE,SAASC,eAAe,QAAQ,iCAAiC;AAEjE,OAAO,MAAMC,SAAS,GAAsB;EAC1CC,SAAS,EAAE,CACTtB,aAAa,CAACM,MAAM,EAAEL,oCAAoC,EAAE,CAAC,EAC7DC,iBAAiB,CACfE,qBAAqB,CAAC;IACpBmB,UAAU,EAAE,YAAY;IACxBC,UAAU,EAAE;GACb,CAAC,EACFrB,gBAAgB,CAAC,CAACiB,eAAe,CAAC,CAAC,CACpC,EACDf,iBAAiB,EAAE;EAEnB;EACAE,WAAW,EACXC,oBAAoB,EACpBC,mBAAmB,EACnBC,oBAAoB,EACpBC,oBAAoB,EACpBC,WAAW,EACXC,UAAU,EACVC,YAAY,EACZC,iBAAiB,EACjBC,YAAY,EACZC,sBAAsB,EACtBC,iBAAiB,EACjBC,kBAAkB;CAErB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}