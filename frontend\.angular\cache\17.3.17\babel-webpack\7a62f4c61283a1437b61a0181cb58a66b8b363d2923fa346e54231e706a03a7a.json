{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../services/auth.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@angular/forms\";\nfunction LoginComponent_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 17);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.errorMessage, \" \");\n  }\n}\nfunction LoginComponent_span_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Sign In\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LoginComponent_span_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 18);\n    i0.ɵɵtext(1, \"\\u25CF\");\n    i0.ɵɵelementEnd();\n  }\n}\nexport let LoginComponent = /*#__PURE__*/(() => {\n  class LoginComponent {\n    constructor(authService, router) {\n      this.authService = authService;\n      this.router = router;\n      this.credentials = {\n        email: '',\n        password: ''\n      };\n      this.isLoading = false;\n      this.errorMessage = '';\n    }\n    onSubmit() {\n      if (!this.credentials.email || !this.credentials.password) {\n        this.errorMessage = 'Please enter both email and secret word';\n        return;\n      }\n      this.isLoading = true;\n      this.errorMessage = '';\n      this.authService.login(this.credentials).subscribe({\n        next: response => {\n          this.isLoading = false;\n          this.router.navigate(['/main']);\n        },\n        error: error => {\n          this.isLoading = false;\n          this.errorMessage = error.message || 'Login failed. Please check your credentials.';\n        }\n      });\n    }\n    onKeyPress(event) {\n      if (event.key === 'Enter') {\n        this.onSubmit();\n      }\n    }\n    static {\n      this.ɵfac = function LoginComponent_Factory(t) {\n        return new (t || LoginComponent)(i0.ɵɵdirectiveInject(i1.AuthService), i0.ɵɵdirectiveInject(i2.Router));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: LoginComponent,\n        selectors: [[\"app-login\"]],\n        standalone: true,\n        features: [i0.ɵɵStandaloneFeature],\n        decls: 26,\n        vars: 8,\n        consts: [[1, \"login-container\"], [1, \"login-card\"], [1, \"logo-container\"], [1, \"qsc-logo\"], [1, \"circle\"], [1, \"wind-effect\"], [1, \"login-form\", 3, \"ngSubmit\"], [1, \"form-group\"], [\"for\", \"email\"], [\"type\", \"email\", \"id\", \"email\", \"name\", \"email\", \"placeholder\", \"Enter your email\", \"required\", \"\", 3, \"ngModelChange\", \"keypress\", \"ngModel\", \"disabled\"], [\"for\", \"password\"], [\"type\", \"password\", \"id\", \"password\", \"name\", \"password\", \"placeholder\", \"Enter your secret word\", \"required\", \"\", 3, \"ngModelChange\", \"keypress\", \"ngModel\", \"disabled\"], [\"class\", \"error-message\", 4, \"ngIf\"], [\"type\", \"submit\", 1, \"login-button\", 3, \"disabled\"], [4, \"ngIf\"], [\"class\", \"loading-spinner\", 4, \"ngIf\"], [1, \"security-notice\"], [1, \"error-message\"], [1, \"loading-spinner\"]],\n        template: function LoginComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"div\", 4);\n            i0.ɵɵelement(5, \"div\", 5);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(6, \"h1\");\n            i0.ɵɵtext(7, \"QSC\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(8, \"p\");\n            i0.ɵɵtext(9, \"Quantum Secure Communication\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(10, \"form\", 6);\n            i0.ɵɵlistener(\"ngSubmit\", function LoginComponent_Template_form_ngSubmit_10_listener() {\n              return ctx.onSubmit();\n            });\n            i0.ɵɵelementStart(11, \"div\", 7)(12, \"label\", 8);\n            i0.ɵɵtext(13, \"Email\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(14, \"input\", 9);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function LoginComponent_Template_input_ngModelChange_14_listener($event) {\n              i0.ɵɵtwoWayBindingSet(ctx.credentials.email, $event) || (ctx.credentials.email = $event);\n              return $event;\n            });\n            i0.ɵɵlistener(\"keypress\", function LoginComponent_Template_input_keypress_14_listener($event) {\n              return ctx.onKeyPress($event);\n            });\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(15, \"div\", 7)(16, \"label\", 10);\n            i0.ɵɵtext(17, \"Secret Word\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(18, \"input\", 11);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function LoginComponent_Template_input_ngModelChange_18_listener($event) {\n              i0.ɵɵtwoWayBindingSet(ctx.credentials.password, $event) || (ctx.credentials.password = $event);\n              return $event;\n            });\n            i0.ɵɵlistener(\"keypress\", function LoginComponent_Template_input_keypress_18_listener($event) {\n              return ctx.onKeyPress($event);\n            });\n            i0.ɵɵelementEnd()();\n            i0.ɵɵtemplate(19, LoginComponent_div_19_Template, 2, 1, \"div\", 12);\n            i0.ɵɵelementStart(20, \"button\", 13);\n            i0.ɵɵtemplate(21, LoginComponent_span_21_Template, 2, 0, \"span\", 14)(22, LoginComponent_span_22_Template, 2, 0, \"span\", 15);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(23, \"div\", 16)(24, \"p\");\n            i0.ɵɵtext(25, \"\\uD83D\\uDD12 Protected by post-quantum cryptography\");\n            i0.ɵɵelementEnd()()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(14);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.credentials.email);\n            i0.ɵɵproperty(\"disabled\", ctx.isLoading);\n            i0.ɵɵadvance(4);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.credentials.password);\n            i0.ɵɵproperty(\"disabled\", ctx.isLoading);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.errorMessage);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"disabled\", ctx.isLoading);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", !ctx.isLoading);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          }\n        },\n        dependencies: [CommonModule, i3.NgIf, FormsModule, i4.ɵNgNoValidate, i4.DefaultValueAccessor, i4.NgControlStatus, i4.NgControlStatusGroup, i4.RequiredValidator, i4.NgModel, i4.NgForm],\n        styles: [\".login-container[_ngcontent-%COMP%]{min-height:100vh;display:flex;align-items:center;justify-content:center;background:linear-gradient(135deg,#667eea,#764ba2);padding:1rem}.login-card[_ngcontent-%COMP%]{background:#fffffff2;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px);border-radius:20px;padding:3rem 2rem;box-shadow:0 20px 40px #0000001a;width:100%;max-width:400px;text-align:center}.logo-container[_ngcontent-%COMP%]{margin-bottom:2rem}.logo-container[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{font-size:2.5rem;font-weight:300;color:#333;margin:1rem 0 .5rem;letter-spacing:.1em}.logo-container[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:#666;font-size:.9rem;margin:0}.qsc-logo[_ngcontent-%COMP%]{display:flex;justify-content:center;margin-bottom:1rem}.qsc-logo[_ngcontent-%COMP%]   .circle[_ngcontent-%COMP%]{width:80px;height:80px;border:4px solid #667eea;border-radius:50%;position:relative;overflow:hidden}.qsc-logo[_ngcontent-%COMP%]   .circle[_ngcontent-%COMP%]   .wind-effect[_ngcontent-%COMP%]{position:absolute;top:0;right:-20px;width:40px;height:100%;background:linear-gradient(90deg,transparent 0%,rgba(255,255,255,.8) 50%,transparent 100%);transform:skew(-20deg);animation:_ngcontent-%COMP%_windBlow 3s ease-in-out infinite}@keyframes _ngcontent-%COMP%_windBlow{0%,to{opacity:0;transform:translate(-40px) skew(-20deg)}50%{opacity:1;transform:translate(20px) skew(-20deg)}}.login-form[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]{margin-bottom:1.5rem;text-align:left}.login-form[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   label[_ngcontent-%COMP%]{display:block;margin-bottom:.5rem;color:#333;font-weight:500;font-size:.9rem}.login-form[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]{width:100%;padding:.75rem 1rem;border:2px solid #e1e5e9;border-radius:10px;font-size:1rem;transition:all .3s ease;background:#fff}.login-form[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]:focus{outline:none;border-color:#667eea;box-shadow:0 0 0 3px #667eea1a}.login-form[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]:disabled{background:#f5f5f5;cursor:not-allowed}.login-form[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]::placeholder{color:#999}.error-message[_ngcontent-%COMP%]{background:#fee;color:#c33;padding:.75rem;border-radius:8px;margin-bottom:1rem;font-size:.9rem;border:1px solid #fcc}.login-button[_ngcontent-%COMP%]{width:100%;padding:.875rem;background:linear-gradient(135deg,#667eea,#764ba2);color:#fff;border:none;border-radius:10px;font-size:1rem;font-weight:600;cursor:pointer;transition:all .3s ease;margin-bottom:1.5rem}.login-button[_ngcontent-%COMP%]:hover:not(:disabled){transform:translateY(-2px);box-shadow:0 10px 20px #667eea4d}.login-button[_ngcontent-%COMP%]:disabled{opacity:.7;cursor:not-allowed;transform:none}.login-button[_ngcontent-%COMP%]   .loading-spinner[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_spin 1s linear infinite}@keyframes _ngcontent-%COMP%_spin{0%{transform:rotate(0)}to{transform:rotate(360deg)}}.security-notice[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:#666;font-size:.8rem;margin:0}@media (max-width: 480px){.login-card[_ngcontent-%COMP%]{padding:2rem 1.5rem}.qsc-logo[_ngcontent-%COMP%]   .circle[_ngcontent-%COMP%]{width:60px;height:60px}.logo-container[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{font-size:2rem}}\"]\n      });\n    }\n  }\n  return LoginComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}