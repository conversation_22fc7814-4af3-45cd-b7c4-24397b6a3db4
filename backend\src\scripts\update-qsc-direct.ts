import { DataSource } from 'typeorm';
import { User } from '../users/entities/user.entity';
import * as argon2 from 'argon2';
import * as crypto from 'crypto';
import { join } from 'path';

async function updateQSCUser() {
  console.log('🔄 Updating QSC user...');

  // Create database connection
  const dataSource = new DataSource({
    type: 'better-sqlite3',
    database: process.env.DB_PATH || join(process.cwd(), 'secure_chat.sqlite'),
    entities: [User],
    synchronize: false,
    logging: false,
  });

  try {
    await dataSource.initialize();
    console.log('✅ Database connected');

    const userRepository = dataSource.getRepository(User);

    // Find QSC user
    const user = await userRepository.findOne({ where: { username: 'QSC' } });
    
    if (!user) {
      console.error('❌ QSC user not found');
      return;
    }

    console.log(`📋 Found QSC user:`);
    console.log(`   ID: ${user.id}`);
    console.log(`   Username: ${user.username}`);
    console.log(`   Email: ${user.email}`);
    console.log(`   Current Phone: ${user.phone || 'Not set'}`);

    // Generate new secret word
    function generateSecretWord(): string {
      const uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
      const lowercase = 'abcdefghijklmnopqrstuvwxyz';
      const digits = '0123456789';
      const symbols = '!@#$%^&*()_+-=[]{}|;:,.<>?';

      const getRandomChar = (str: string) => str[Math.floor(Math.random() * str.length)];

      return [
        getRandomChar(uppercase),
        getRandomChar(lowercase),
        getRandomChar(digits),
        getRandomChar(symbols)
      ].sort(() => Math.random() - 0.5).join('');
    }

    const newSecretWord = generateSecretWord();

    // Hash the new secret word
    const salt = Buffer.from(crypto.randomBytes(32)).toString('base64');
    const hash = await argon2.hash(newSecretWord, {
      type: argon2.argon2id,
      salt: Buffer.from(salt, 'base64'),
      memoryCost: 65536,
      timeCost: 3,
      parallelism: 4,
    });

    const newSecretWordHash = {
      hash,
      salt,
      attempts: 0,
      lastAttempt: new Date().toISOString(),
    };

    // Update user
    user.phone = '+************';
    user.secretWordHash = newSecretWordHash;
    user.failedAttempts = 0;
    user.lastAttemptAt = null;
    user.isCompromised = false;
    user.accountStatus = 'active';

    await userRepository.save(user);

    console.log('');
    console.log('✅ QSC user updated successfully:');
    console.log(`   Username: ${user.username}`);
    console.log(`   Email: ${user.email}`);
    console.log(`   Phone: ${user.phone}`);
    console.log(`   New Secret Word: ${newSecretWord}`);
    console.log('');
    console.log('🔐 IMPORTANT: Save these credentials securely!');
    console.log('🔐 The secret word will not be displayed again.');
    console.log('📱 Phone number has been added to the user profile.');

  } catch (error) {
    console.error('❌ Error updating QSC user:', error);
  } finally {
    await dataSource.destroy();
    console.log('🔌 Database connection closed');
  }
}

// Run the script
updateQSCUser().catch(console.error);
