import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ChainNodeListComponent } from './chain-node-list.component';
import { ChainDeletionService } from '../../services/chain-deletion.service';
import { ErrorHandlingService } from '../../services/error-handling.service';

describe('ChainNodeListComponent', () => {
  let component: ChainNodeListComponent;
  let fixture: ComponentFixture<ChainNodeListComponent>;
  let chainDeletionService: jasmine.SpyObj<ChainDeletionService>;
  let errorHandlingService: jasmine.SpyObj<ErrorHandlingService>;

  const mockChainNodes = [
    {
      id: '1',
      messageId: 'msg1',
      timestamp: new Date(),
      deleted: false
    },
    {
      id: '2',
      messageId: 'msg2',
      timestamp: new Date(),
      deleted: true,
      deletionTimestamp: new Date()
    }
  ];

  beforeEach(async () => {
    const chainDeletionSpy = jasmine.createSpyObj('ChainDeletionService', [
      'getChainNodes'
    ]);
    const errorHandlingSpy = jasmine.createSpyObj('ErrorHandlingService', [
      'handleError'
    ]);

    await TestBed.configureTestingModule({
      imports: [ChainNodeListComponent],
      providers: [
        { provide: ChainDeletionService, useValue: chainDeletionSpy },
        { provide: ErrorHandlingService, useValue: errorHandlingSpy }
      ]
    }).compileComponents();

    chainDeletionService = TestBed.inject(ChainDeletionService) as jasmine.SpyObj<ChainDeletionService>;
    errorHandlingService = TestBed.inject(ErrorHandlingService) as jasmine.SpyObj<ErrorHandlingService>;
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(ChainNodeListComponent);
    component = fixture.componentInstance;
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should load chain nodes on init', async () => {
    chainDeletionService.getChainNodes.and.returnValue(Promise.resolve(mockChainNodes));

    await component.ngOnInit();
    expect(component.chainNodes).toEqual(mockChainNodes);
    expect(component.errorMessage).toBeNull();
  });

  it('should handle chain node loading error', async () => {
    const error = new Error('Failed to load chain nodes');
    chainDeletionService.getChainNodes.and.returnValue(Promise.reject(error));

    await component.ngOnInit();
    expect(errorHandlingService.handleError).toHaveBeenCalledWith(error, 'STORAGE');
    expect(component.errorMessage).toBe('Failed to load chain nodes');
  });
});
