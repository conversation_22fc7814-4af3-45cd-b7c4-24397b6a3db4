import { NestFactory } from '@nestjs/core';
import { AppModule } from '../app.module';
import { UserService } from '../user/user.service';
import { SecretWordService } from '../auth/services/secret-word.service';
import { Logger } from '@nestjs/common';

async function createQSCAdmin() {
  const logger = new Logger('CreateQSCAdmin');

  try {
    const app = await NestFactory.create(AppModule);
    const userService = app.get(UserService);
    const secretWordService = app.get(SecretWordService);

    const username = 'QSC';
    const email = '<EMAIL>';

    // Check if user with this username already exists
    try {
      const existingUser = await userService.findByUsername(username);
      if (existingUser) {
        logger.log(`User with username '${username}' already exists:`);
        logger.log(`  ID: ${existingUser.id}`);
        logger.log(`  Email: ${existingUser.email}`);
        logger.log(`  Is Admin: ${existingUser.isAdmin}`);
        logger.log(`  Is Active: ${existingUser.isActive}`);
        await app.close();
        return;
      }
    } catch (error) {
      // User doesn't exist, continue with creation
    }

    // Check if user with this email already exists
    const existingEmailUser = await userService.findByEmailSafe(email);
    if (existingEmailUser) {
      logger.log(`User with email '${email}' already exists:`);
      logger.log(`  ID: ${existingEmailUser.id}`);
      logger.log(`  Username: ${existingEmailUser.username}`);
      logger.log(`  Is Admin: ${existingEmailUser.isAdmin}`);
      await app.close();
      return;
    }

    // Generate a secure 4-character secret word
    function generateSecretWord(): string {
      const uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
      const lowercase = 'abcdefghijklmnopqrstuvwxyz';
      const digits = '0123456789';
      const symbols = '!@#$%^&*()_+-=[]{}|;:,.<>?';

      const getRandomChar = (str: string) => str[Math.floor(Math.random() * str.length)];

      return [
        getRandomChar(uppercase),
        getRandomChar(lowercase),
        getRandomChar(digits),
        getRandomChar(symbols)
      ].sort(() => Math.random() - 0.5).join('');
    }

    const secretWord = generateSecretWord();

    // Create secret word hash
    const secretWordHash = await secretWordService.hashSecretWord(secretWord);

    // Create the admin user
    const adminUser = await userService.createAdmin({
      username,
      email,
      secretWordHash,
    });

    logger.log(`✅ QSC Admin user created successfully:`);
    logger.log(`   Username: ${adminUser.username}`);
    logger.log(`   Email: ${adminUser.email}`);
    logger.log(`   ID: ${adminUser.id}`);
    logger.log(`   Secret Word: ${secretWord}`);
    logger.log('');
    logger.log('🔐 IMPORTANT: Save these credentials securely!');
    logger.log('🔐 The secret word will not be displayed again.');

    await app.close();
  } catch (error) {
    logger.error('Failed to create QSC admin user:', error);
    process.exit(1);
  }
}

// Run the script if this file is executed directly
if (require.main === module) {
  createQSCAdmin();
}

export { createQSCAdmin };
