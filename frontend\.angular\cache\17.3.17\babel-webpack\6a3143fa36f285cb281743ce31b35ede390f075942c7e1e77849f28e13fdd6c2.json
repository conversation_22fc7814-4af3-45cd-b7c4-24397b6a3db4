{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../services/auth.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@angular/forms\";\nfunction LoginComponent_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 17);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.errorMessage, \" \");\n  }\n}\nfunction LoginComponent_span_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Sign In\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LoginComponent_span_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 18);\n    i0.ɵɵtext(1, \"\\u25CF\");\n    i0.ɵɵelementEnd();\n  }\n}\nexport class LoginComponent {\n  constructor(authService, router) {\n    this.authService = authService;\n    this.router = router;\n    this.credentials = {\n      username: '',\n      secretWord: ''\n    };\n    this.isLoading = false;\n    this.errorMessage = '';\n  }\n  onSubmit() {\n    if (!this.credentials.username || !this.credentials.secretWord) {\n      this.errorMessage = 'Please enter both username and secret word';\n      return;\n    }\n    this.isLoading = true;\n    this.errorMessage = '';\n    this.authService.login(this.credentials).subscribe({\n      next: response => {\n        this.isLoading = false;\n        this.router.navigate(['/main']);\n      },\n      error: error => {\n        this.isLoading = false;\n        this.errorMessage = error.message || 'Login failed. Please check your credentials.';\n      }\n    });\n  }\n  onKeyPress(event) {\n    if (event.key === 'Enter') {\n      this.onSubmit();\n    }\n  }\n  static {\n    this.ɵfac = function LoginComponent_Factory(t) {\n      return new (t || LoginComponent)(i0.ɵɵdirectiveInject(i1.AuthService), i0.ɵɵdirectiveInject(i2.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: LoginComponent,\n      selectors: [[\"app-login\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 26,\n      vars: 8,\n      consts: [[1, \"login-container\"], [1, \"login-card\"], [1, \"logo-container\"], [1, \"qsc-logo\"], [1, \"circle\"], [1, \"wind-effect\"], [1, \"login-form\", 3, \"ngSubmit\"], [1, \"form-group\"], [\"for\", \"username\"], [\"type\", \"text\", \"id\", \"username\", \"name\", \"username\", \"placeholder\", \"Enter your username\", \"required\", \"\", 3, \"ngModelChange\", \"keypress\", \"ngModel\", \"disabled\"], [\"for\", \"secretWord\"], [\"type\", \"password\", \"id\", \"secretWord\", \"name\", \"secretWord\", \"placeholder\", \"Enter your secret word\", \"required\", \"\", 3, \"ngModelChange\", \"keypress\", \"ngModel\", \"disabled\"], [\"class\", \"error-message\", 4, \"ngIf\"], [\"type\", \"submit\", 1, \"login-button\", 3, \"disabled\"], [4, \"ngIf\"], [\"class\", \"loading-spinner\", 4, \"ngIf\"], [1, \"security-notice\"], [1, \"error-message\"], [1, \"loading-spinner\"]],\n      template: function LoginComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"div\", 4);\n          i0.ɵɵelement(5, \"div\", 5);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(6, \"h1\");\n          i0.ɵɵtext(7, \"QSC\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(8, \"p\");\n          i0.ɵɵtext(9, \"Quantum Secure Communication\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(10, \"form\", 6);\n          i0.ɵɵlistener(\"ngSubmit\", function LoginComponent_Template_form_ngSubmit_10_listener() {\n            return ctx.onSubmit();\n          });\n          i0.ɵɵelementStart(11, \"div\", 7)(12, \"label\", 8);\n          i0.ɵɵtext(13, \"Username\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(14, \"input\", 9);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function LoginComponent_Template_input_ngModelChange_14_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.credentials.username, $event) || (ctx.credentials.username = $event);\n            return $event;\n          });\n          i0.ɵɵlistener(\"keypress\", function LoginComponent_Template_input_keypress_14_listener($event) {\n            return ctx.onKeyPress($event);\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(15, \"div\", 7)(16, \"label\", 10);\n          i0.ɵɵtext(17, \"Secret Word\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(18, \"input\", 11);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function LoginComponent_Template_input_ngModelChange_18_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.credentials.secretWord, $event) || (ctx.credentials.secretWord = $event);\n            return $event;\n          });\n          i0.ɵɵlistener(\"keypress\", function LoginComponent_Template_input_keypress_18_listener($event) {\n            return ctx.onKeyPress($event);\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(19, LoginComponent_div_19_Template, 2, 1, \"div\", 12);\n          i0.ɵɵelementStart(20, \"button\", 13);\n          i0.ɵɵtemplate(21, LoginComponent_span_21_Template, 2, 0, \"span\", 14)(22, LoginComponent_span_22_Template, 2, 0, \"span\", 15);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(23, \"div\", 16)(24, \"p\");\n          i0.ɵɵtext(25, \"\\uD83D\\uDD12 Protected by post-quantum cryptography\");\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(14);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.credentials.username);\n          i0.ɵɵproperty(\"disabled\", ctx.isLoading);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.credentials.secretWord);\n          i0.ɵɵproperty(\"disabled\", ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.errorMessage);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"disabled\", ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n        }\n      },\n      dependencies: [CommonModule, i3.NgIf, FormsModule, i4.ɵNgNoValidate, i4.DefaultValueAccessor, i4.NgControlStatus, i4.NgControlStatusGroup, i4.RequiredValidator, i4.NgModel, i4.NgForm],\n      styles: [\".login-container[_ngcontent-%COMP%] {\\n  min-height: 100vh;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n  padding: 1rem;\\n}\\n\\n.login-card[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.95);\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  border-radius: 20px;\\n  padding: 3rem 2rem;\\n  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);\\n  width: 100%;\\n  max-width: 400px;\\n  text-align: center;\\n}\\n\\n.logo-container[_ngcontent-%COMP%] {\\n  margin-bottom: 2rem;\\n}\\n.logo-container[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n  font-size: 2.5rem;\\n  font-weight: 300;\\n  color: #333;\\n  margin: 1rem 0 0.5rem 0;\\n  letter-spacing: 0.1em;\\n}\\n.logo-container[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: #666;\\n  font-size: 0.9rem;\\n  margin: 0;\\n}\\n\\n.qsc-logo[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  margin-bottom: 1rem;\\n}\\n.qsc-logo[_ngcontent-%COMP%]   .circle[_ngcontent-%COMP%] {\\n  width: 80px;\\n  height: 80px;\\n  border: 4px solid #667eea;\\n  border-radius: 50%;\\n  position: relative;\\n  overflow: hidden;\\n}\\n.qsc-logo[_ngcontent-%COMP%]   .circle[_ngcontent-%COMP%]   .wind-effect[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  right: -20px;\\n  width: 40px;\\n  height: 100%;\\n  background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.8) 50%, transparent 100%);\\n  transform: skew(-20deg);\\n  animation: _ngcontent-%COMP%_windBlow 3s ease-in-out infinite;\\n}\\n\\n@keyframes _ngcontent-%COMP%_windBlow {\\n  0%, 100% {\\n    opacity: 0;\\n    transform: translateX(-40px) skew(-20deg);\\n  }\\n  50% {\\n    opacity: 1;\\n    transform: translateX(20px) skew(-20deg);\\n  }\\n}\\n.login-form[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%] {\\n  margin-bottom: 1.5rem;\\n  text-align: left;\\n}\\n.login-form[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  display: block;\\n  margin-bottom: 0.5rem;\\n  color: #333;\\n  font-weight: 500;\\n  font-size: 0.9rem;\\n}\\n.login-form[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   input[_ngcontent-%COMP%] {\\n  width: 100%;\\n  padding: 0.75rem 1rem;\\n  border: 2px solid #e1e5e9;\\n  border-radius: 10px;\\n  font-size: 1rem;\\n  transition: all 0.3s ease;\\n  background: white;\\n}\\n.login-form[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]:focus {\\n  outline: none;\\n  border-color: #667eea;\\n  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);\\n}\\n.login-form[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]:disabled {\\n  background: #f5f5f5;\\n  cursor: not-allowed;\\n}\\n.login-form[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]::placeholder {\\n  color: #999;\\n}\\n\\n.error-message[_ngcontent-%COMP%] {\\n  background: #fee;\\n  color: #c33;\\n  padding: 0.75rem;\\n  border-radius: 8px;\\n  margin-bottom: 1rem;\\n  font-size: 0.9rem;\\n  border: 1px solid #fcc;\\n}\\n\\n.login-button[_ngcontent-%COMP%] {\\n  width: 100%;\\n  padding: 0.875rem;\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n  color: white;\\n  border: none;\\n  border-radius: 10px;\\n  font-size: 1rem;\\n  font-weight: 600;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  margin-bottom: 1.5rem;\\n}\\n.login-button[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  transform: translateY(-2px);\\n  box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);\\n}\\n.login-button[_ngcontent-%COMP%]:disabled {\\n  opacity: 0.7;\\n  cursor: not-allowed;\\n  transform: none;\\n}\\n.login-button[_ngcontent-%COMP%]   .loading-spinner[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_spin 1s linear infinite;\\n}\\n\\n@keyframes _ngcontent-%COMP%_spin {\\n  from {\\n    transform: rotate(0deg);\\n  }\\n  to {\\n    transform: rotate(360deg);\\n  }\\n}\\n.security-notice[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: #666;\\n  font-size: 0.8rem;\\n  margin: 0;\\n}\\n\\n@media (max-width: 480px) {\\n  .login-card[_ngcontent-%COMP%] {\\n    padding: 2rem 1.5rem;\\n  }\\n  .qsc-logo[_ngcontent-%COMP%]   .circle[_ngcontent-%COMP%] {\\n    width: 60px;\\n    height: 60px;\\n  }\\n  .logo-container[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n    font-size: 2rem;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "FormsModule", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r0", "errorMessage", "LoginComponent", "constructor", "authService", "router", "credentials", "username", "secretWord", "isLoading", "onSubmit", "login", "subscribe", "next", "response", "navigate", "error", "message", "onKeyPress", "event", "key", "ɵɵdirectiveInject", "i1", "AuthService", "i2", "Router", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "LoginComponent_Template", "rf", "ctx", "ɵɵelement", "ɵɵlistener", "LoginComponent_Template_form_ngSubmit_10_listener", "ɵɵtwoWayListener", "LoginComponent_Template_input_ngModelChange_14_listener", "$event", "ɵɵtwoWayBindingSet", "LoginComponent_Template_input_keypress_14_listener", "LoginComponent_Template_input_ngModelChange_18_listener", "LoginComponent_Template_input_keypress_18_listener", "ɵɵtemplate", "LoginComponent_div_19_Template", "LoginComponent_span_21_Template", "LoginComponent_span_22_Template", "ɵɵtwoWayProperty", "ɵɵproperty", "i3", "NgIf", "i4", "ɵNgNoValidate", "DefaultValueAccessor", "NgControlStatus", "NgControlStatusGroup", "RequiredValidator", "NgModel", "NgForm", "styles"], "sources": ["C:\\Users\\<USER>\\Projects\\QSC1\\frontend\\src\\app\\components\\login\\login.component.ts", "C:\\Users\\<USER>\\Projects\\QSC1\\frontend\\src\\app\\components\\login\\login.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { Router } from '@angular/router';\nimport { AuthService } from '../../services/auth.service';\nimport { LoginRequest } from '../../types/auth.types';\n\n@Component({\n  selector: 'app-login',\n  standalone: true,\n  imports: [CommonModule, FormsModule],\n  templateUrl: './login.component.html',\n  styleUrl: './login.component.scss'\n})\nexport class LoginComponent {\n  credentials: LoginRequest = {\n    username: '',\n    secretWord: ''\n  };\n\n  isLoading = false;\n  errorMessage = '';\n\n  constructor(\n    private authService: AuthService,\n    private router: Router\n  ) {}\n\n  onSubmit(): void {\n    if (!this.credentials.username || !this.credentials.secretWord) {\n      this.errorMessage = 'Please enter both username and secret word';\n      return;\n    }\n\n    this.isLoading = true;\n    this.errorMessage = '';\n\n    this.authService.login(this.credentials).subscribe({\n      next: (response) => {\n        this.isLoading = false;\n        this.router.navigate(['/main']);\n      },\n      error: (error) => {\n        this.isLoading = false;\n        this.errorMessage = error.message || 'Login failed. Please check your credentials.';\n      }\n    });\n  }\n\n  onKeyPress(event: KeyboardEvent): void {\n    if (event.key === 'Enter') {\n      this.onSubmit();\n    }\n  }\n}\n", "<div class=\"login-container\">\n  <div class=\"login-card\">\n    <div class=\"logo-container\">\n      <div class=\"qsc-logo\">\n        <div class=\"circle\">\n          <div class=\"wind-effect\"></div>\n        </div>\n      </div>\n      <h1>QSC</h1>\n      <p>Quantum Secure Communication</p>\n    </div>\n\n    <form (ngSubmit)=\"onSubmit()\" class=\"login-form\">\n      <div class=\"form-group\">\n        <label for=\"username\">Username</label>\n        <input\n          type=\"text\"\n          id=\"username\"\n          name=\"username\"\n          [(ngModel)]=\"credentials.username\"\n          (keypress)=\"onKeyPress($event)\"\n          placeholder=\"Enter your username\"\n          required\n          [disabled]=\"isLoading\"\n        />\n      </div>\n\n      <div class=\"form-group\">\n        <label for=\"secretWord\">Secret Word</label>\n        <input\n          type=\"password\"\n          id=\"secretWord\"\n          name=\"secretWord\"\n          [(ngModel)]=\"credentials.secretWord\"\n          (keypress)=\"onKeyPress($event)\"\n          placeholder=\"Enter your secret word\"\n          required\n          [disabled]=\"isLoading\"\n        />\n      </div>\n\n      <div class=\"error-message\" *ngIf=\"errorMessage\">\n        {{ errorMessage }}\n      </div>\n\n      <button\n        type=\"submit\"\n        class=\"login-button\"\n        [disabled]=\"isLoading\"\n      >\n        <span *ngIf=\"!isLoading\">Sign In</span>\n        <span *ngIf=\"isLoading\" class=\"loading-spinner\">●</span>\n      </button>\n    </form>\n\n    <div class=\"security-notice\">\n      <p>🔒 Protected by post-quantum cryptography</p>\n    </div>\n  </div>\n</div>\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;;;;;;;;ICuCtCC,EAAA,CAAAC,cAAA,cAAgD;IAC9CD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAAC,YAAA,MACF;;;;;IAOEP,EAAA,CAAAC,cAAA,WAAyB;IAAAD,EAAA,CAAAE,MAAA,cAAO;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IACvCH,EAAA,CAAAC,cAAA,eAAgD;IAAAD,EAAA,CAAAE,MAAA,aAAC;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;ADrChE,OAAM,MAAOK,cAAc;EASzBC,YACUC,WAAwB,EACxBC,MAAc;IADd,KAAAD,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IAVhB,KAAAC,WAAW,GAAiB;MAC1BC,QAAQ,EAAE,EAAE;MACZC,UAAU,EAAE;KACb;IAED,KAAAC,SAAS,GAAG,KAAK;IACjB,KAAAR,YAAY,GAAG,EAAE;EAKd;EAEHS,QAAQA,CAAA;IACN,IAAI,CAAC,IAAI,CAACJ,WAAW,CAACC,QAAQ,IAAI,CAAC,IAAI,CAACD,WAAW,CAACE,UAAU,EAAE;MAC9D,IAAI,CAACP,YAAY,GAAG,4CAA4C;MAChE;;IAGF,IAAI,CAACQ,SAAS,GAAG,IAAI;IACrB,IAAI,CAACR,YAAY,GAAG,EAAE;IAEtB,IAAI,CAACG,WAAW,CAACO,KAAK,CAAC,IAAI,CAACL,WAAW,CAAC,CAACM,SAAS,CAAC;MACjDC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAI,CAACL,SAAS,GAAG,KAAK;QACtB,IAAI,CAACJ,MAAM,CAACU,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC;MACjC,CAAC;MACDC,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACP,SAAS,GAAG,KAAK;QACtB,IAAI,CAACR,YAAY,GAAGe,KAAK,CAACC,OAAO,IAAI,8CAA8C;MACrF;KACD,CAAC;EACJ;EAEAC,UAAUA,CAACC,KAAoB;IAC7B,IAAIA,KAAK,CAACC,GAAG,KAAK,OAAO,EAAE;MACzB,IAAI,CAACV,QAAQ,EAAE;;EAEnB;;;uBAvCWR,cAAc,EAAAR,EAAA,CAAA2B,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAA7B,EAAA,CAAA2B,iBAAA,CAAAG,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAAdvB,cAAc;MAAAwB,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAAlC,EAAA,CAAAmC,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,wBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCVnBzC,EAJR,CAAAC,cAAA,aAA6B,aACH,aACM,aACJ,aACA;UAClBD,EAAA,CAAA2C,SAAA,aAA+B;UAEnC3C,EADE,CAAAG,YAAA,EAAM,EACF;UACNH,EAAA,CAAAC,cAAA,SAAI;UAAAD,EAAA,CAAAE,MAAA,UAAG;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACZH,EAAA,CAAAC,cAAA,QAAG;UAAAD,EAAA,CAAAE,MAAA,mCAA4B;UACjCF,EADiC,CAAAG,YAAA,EAAI,EAC/B;UAENH,EAAA,CAAAC,cAAA,eAAiD;UAA3CD,EAAA,CAAA4C,UAAA,sBAAAC,kDAAA;YAAA,OAAYH,GAAA,CAAA1B,QAAA,EAAU;UAAA,EAAC;UAEzBhB,EADF,CAAAC,cAAA,cAAwB,gBACA;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACtCH,EAAA,CAAAC,cAAA,gBASE;UALAD,EAAA,CAAA8C,gBAAA,2BAAAC,wDAAAC,MAAA;YAAAhD,EAAA,CAAAiD,kBAAA,CAAAP,GAAA,CAAA9B,WAAA,CAAAC,QAAA,EAAAmC,MAAA,MAAAN,GAAA,CAAA9B,WAAA,CAAAC,QAAA,GAAAmC,MAAA;YAAA,OAAAA,MAAA;UAAA,EAAkC;UAClChD,EAAA,CAAA4C,UAAA,sBAAAM,mDAAAF,MAAA;YAAA,OAAYN,GAAA,CAAAlB,UAAA,CAAAwB,MAAA,CAAkB;UAAA,EAAC;UAKnChD,EAVE,CAAAG,YAAA,EASE,EACE;UAGJH,EADF,CAAAC,cAAA,cAAwB,iBACE;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC3CH,EAAA,CAAAC,cAAA,iBASE;UALAD,EAAA,CAAA8C,gBAAA,2BAAAK,wDAAAH,MAAA;YAAAhD,EAAA,CAAAiD,kBAAA,CAAAP,GAAA,CAAA9B,WAAA,CAAAE,UAAA,EAAAkC,MAAA,MAAAN,GAAA,CAAA9B,WAAA,CAAAE,UAAA,GAAAkC,MAAA;YAAA,OAAAA,MAAA;UAAA,EAAoC;UACpChD,EAAA,CAAA4C,UAAA,sBAAAQ,mDAAAJ,MAAA;YAAA,OAAYN,GAAA,CAAAlB,UAAA,CAAAwB,MAAA,CAAkB;UAAA,EAAC;UAKnChD,EAVE,CAAAG,YAAA,EASE,EACE;UAENH,EAAA,CAAAqD,UAAA,KAAAC,8BAAA,kBAAgD;UAIhDtD,EAAA,CAAAC,cAAA,kBAIC;UAECD,EADA,CAAAqD,UAAA,KAAAE,+BAAA,mBAAyB,KAAAC,+BAAA,mBACuB;UAEpDxD,EADE,CAAAG,YAAA,EAAS,EACJ;UAGLH,EADF,CAAAC,cAAA,eAA6B,SACxB;UAAAD,EAAA,CAAAE,MAAA,2DAAyC;UAGlDF,EAHkD,CAAAG,YAAA,EAAI,EAC5C,EACF,EACF;;;UAxCIH,EAAA,CAAAI,SAAA,IAAkC;UAAlCJ,EAAA,CAAAyD,gBAAA,YAAAf,GAAA,CAAA9B,WAAA,CAAAC,QAAA,CAAkC;UAIlCb,EAAA,CAAA0D,UAAA,aAAAhB,GAAA,CAAA3B,SAAA,CAAsB;UAUtBf,EAAA,CAAAI,SAAA,GAAoC;UAApCJ,EAAA,CAAAyD,gBAAA,YAAAf,GAAA,CAAA9B,WAAA,CAAAE,UAAA,CAAoC;UAIpCd,EAAA,CAAA0D,UAAA,aAAAhB,GAAA,CAAA3B,SAAA,CAAsB;UAIEf,EAAA,CAAAI,SAAA,EAAkB;UAAlBJ,EAAA,CAAA0D,UAAA,SAAAhB,GAAA,CAAAnC,YAAA,CAAkB;UAO5CP,EAAA,CAAAI,SAAA,EAAsB;UAAtBJ,EAAA,CAAA0D,UAAA,aAAAhB,GAAA,CAAA3B,SAAA,CAAsB;UAEff,EAAA,CAAAI,SAAA,EAAgB;UAAhBJ,EAAA,CAAA0D,UAAA,UAAAhB,GAAA,CAAA3B,SAAA,CAAgB;UAChBf,EAAA,CAAAI,SAAA,EAAe;UAAfJ,EAAA,CAAA0D,UAAA,SAAAhB,GAAA,CAAA3B,SAAA,CAAe;;;qBDzClBjB,YAAY,EAAA6D,EAAA,CAAAC,IAAA,EAAE7D,WAAW,EAAA8D,EAAA,CAAAC,aAAA,EAAAD,EAAA,CAAAE,oBAAA,EAAAF,EAAA,CAAAG,eAAA,EAAAH,EAAA,CAAAI,oBAAA,EAAAJ,EAAA,CAAAK,iBAAA,EAAAL,EAAA,CAAAM,OAAA,EAAAN,EAAA,CAAAO,MAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}