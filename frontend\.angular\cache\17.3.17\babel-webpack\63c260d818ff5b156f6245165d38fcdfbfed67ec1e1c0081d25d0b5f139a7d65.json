{"ast": null, "code": "import _asyncToGenerator from \"D:/TCL1/Projects/Projects/QSC1/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"./error-handling.service\";\nexport class WasmService {\n  constructor(errorHandling) {\n    this.errorHandling = errorHandling;\n    this.wasmModule = null;\n    this.currentKeyPair = null;\n    this.KEY_ROTATION_INTERVAL = 30 * 24 * 60 * 60 * 1000; // 30 days\n    this.wasmInstance = null;\n    this.wasmFunctions = null;\n    this.initializeWasm();\n    this.startKeyRotation();\n  }\n  init() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      try {\n        // Try to load WASM module - will fail gracefully in development\n        const wasmModule = yield _this.loadWasmModule();\n        if (wasmModule) {\n          _this.wasmInstance = wasmModule;\n          yield _this.wasmInstance.default();\n          // Extract WASM functions\n          _this.wasmFunctions = {\n            generate_key_pair: _this.wasmInstance.generate_key_pair,\n            encrypt_message: _this.wasmInstance.encrypt_message,\n            decrypt_message: _this.wasmInstance.decrypt_message,\n            rotate_keys: _this.wasmInstance.rotate_keys\n          };\n        } else {\n          throw new Error('WASM module not available');\n        }\n      } catch (error) {\n        console.warn('WASM module not available, using fallback implementation');\n        console.info('This is expected in development mode - fallback crypto implementations will be used');\n        _this.initializeFallback();\n      }\n    })();\n  }\n  loadWasmModule() {\n    return _asyncToGenerator(function* () {\n      try {\n        // For development, we'll skip WASM loading and use fallbacks\n        // In production, this would load the actual compiled WASM module\n        console.warn('WASM module loading skipped in development - using fallback implementations');\n        return null;\n      } catch (error) {\n        console.warn('Could not load WASM module:', error);\n        return null;\n      }\n    })();\n  }\n  initializeWasm() {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        yield _this2.init();\n        yield _this2.generateNewKeyPair();\n      } catch (error) {\n        yield _this2.errorHandling.handleError(error instanceof Error ? error : new Error('WASM initialization failed'), 'WASM');\n        // Don't throw error, allow fallback to work\n        console.warn('WASM initialization failed, continuing with fallback');\n      }\n    })();\n  }\n  /**\n   * Initialize fallback cryptographic implementation\n   */\n  initializeFallback() {\n    console.warn('Using fallback cryptographic implementation - NOT SECURE FOR PRODUCTION');\n    this.wasmFunctions = {\n      generate_key_pair: this.fallbackGenerateKeyPair.bind(this),\n      encrypt_message: this.fallbackEncryptMessage.bind(this),\n      decrypt_message: this.fallbackDecryptMessage.bind(this),\n      rotate_keys: this.fallbackRotateKeys.bind(this)\n    };\n  }\n  generateNewKeyPair() {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        if (!_this3.wasmFunctions) {\n          throw new Error('WASM functions not initialized');\n        }\n        const keyPairBytes = yield _this3.wasmFunctions.generate_key_pair();\n        const keyPair = JSON.parse(new TextDecoder().decode(keyPairBytes));\n        _this3.currentKeyPair = {\n          public_key: new Uint8Array(keyPair.public_key),\n          private_key: new Uint8Array(keyPair.private_key),\n          timestamp: new Date(),\n          version: (_this3.currentKeyPair?.version ?? 0) + 1\n        };\n      } catch (error) {\n        yield _this3.errorHandling.handleError(error instanceof Error ? error : new Error('Key pair generation failed'), 'WASM');\n        throw error;\n      }\n    })();\n  }\n  startKeyRotation() {\n    var _this4 = this;\n    setInterval(/*#__PURE__*/_asyncToGenerator(function* () {\n      try {\n        yield _this4.rotateKeys();\n      } catch (error) {\n        yield _this4.errorHandling.handleError(error instanceof Error ? error : new Error('Key rotation failed'), 'SECURITY');\n      }\n    }), this.KEY_ROTATION_INTERVAL);\n  }\n  rotateKeys() {\n    var _this5 = this;\n    return _asyncToGenerator(function* () {\n      if (!_this5.currentKeyPair) {\n        throw new Error('No key pair available for rotation');\n      }\n      if (!_this5.wasmFunctions) {\n        throw new Error('WASM functions not initialized');\n      }\n      try {\n        const newKeyPairBytes = yield _this5.wasmFunctions.rotate_keys(_this5.currentKeyPair.private_key);\n        const newKeyPair = JSON.parse(new TextDecoder().decode(newKeyPairBytes));\n        _this5.currentKeyPair = {\n          public_key: new Uint8Array(newKeyPair.public_key),\n          private_key: new Uint8Array(newKeyPair.private_key),\n          timestamp: new Date(),\n          version: _this5.currentKeyPair.version + 1\n        };\n      } catch (error) {\n        yield _this5.errorHandling.handleError(error instanceof Error ? error : new Error('Key rotation failed'), 'SECURITY');\n        throw error;\n      }\n    })();\n  }\n  encryptMessage(message) {\n    var _this6 = this;\n    return _asyncToGenerator(function* () {\n      if (!_this6.currentKeyPair) {\n        throw new Error('No key pair available for encryption');\n      }\n      if (!_this6.wasmFunctions) {\n        throw new Error('WASM functions not initialized');\n      }\n      try {\n        const messageBytes = new TextEncoder().encode(message);\n        const encryptedBytes = yield _this6.wasmFunctions.encrypt_message(messageBytes, _this6.currentKeyPair.public_key);\n        return new Uint8Array(encryptedBytes);\n      } catch (error) {\n        yield _this6.errorHandling.handleError(error instanceof Error ? error : new Error('Message encryption failed'), 'SECURITY');\n        throw error;\n      }\n    })();\n  }\n  decryptMessage(encrypted) {\n    var _this7 = this;\n    return _asyncToGenerator(function* () {\n      if (!_this7.currentKeyPair) {\n        throw new Error('No key pair available for decryption');\n      }\n      if (!_this7.wasmFunctions) {\n        throw new Error('WASM functions not initialized');\n      }\n      try {\n        const decryptedBytes = yield _this7.wasmFunctions.decrypt_message(encrypted, _this7.currentKeyPair.private_key);\n        return new TextDecoder().decode(decryptedBytes);\n      } catch (error) {\n        yield _this7.errorHandling.handleError(error instanceof Error ? error : new Error('Message decryption failed'), 'SECURITY');\n        throw error;\n      }\n    })();\n  }\n  signMessage(message) {\n    var _this8 = this;\n    return _asyncToGenerator(function* () {\n      if (!_this8.currentKeyPair) {\n        throw new Error('No key pair available for signing');\n      }\n      try {\n        // TODO: Implement signing logic using WASM\n        throw new Error('Signing method not implemented');\n      } catch (error) {\n        yield _this8.errorHandling.handleError(error instanceof Error ? error : new Error('Signing failed'), 'SECURITY');\n        throw error;\n      }\n    })();\n  }\n  verifySignature(message, signature) {\n    var _this9 = this;\n    return _asyncToGenerator(function* () {\n      if (!_this9.currentKeyPair) {\n        throw new Error('No key pair available for signature verification');\n      }\n      try {\n        // TODO: Implement signature verification logic using WASM\n        throw new Error('Signature verification method not implemented');\n      } catch (error) {\n        yield _this9.errorHandling.handleError(error instanceof Error ? error : new Error('Signature verification failed'), 'SECURITY');\n        throw error;\n      }\n    })();\n  }\n  importKeyPair(keyPair) {\n    var _this0 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        _this0.currentKeyPair = keyPair;\n      } catch (error) {\n        yield _this0.errorHandling.handleError(error instanceof Error ? error : new Error('Key pair import failed'), 'WASM');\n        throw error;\n      }\n    })();\n  }\n  getCurrentKeyPair() {\n    return this.currentKeyPair;\n  }\n  wipeMemory() {\n    this.currentKeyPair = null;\n    this.wasmModule = null;\n    this.wasmInstance = null;\n    this.wasmFunctions = null;\n  }\n  // Fallback implementations (NOT SECURE FOR PRODUCTION)\n  fallbackGenerateKeyPair() {\n    return _asyncToGenerator(function* () {\n      const keyPair = {\n        public_key: Array.from(crypto.getRandomValues(new Uint8Array(32))),\n        private_key: Array.from(crypto.getRandomValues(new Uint8Array(64)))\n      };\n      return new TextEncoder().encode(JSON.stringify(keyPair));\n    })();\n  }\n  fallbackEncryptMessage(message, publicKey) {\n    return _asyncToGenerator(function* () {\n      // Simple XOR encryption (NOT SECURE)\n      const key = publicKey.slice(0, 32);\n      const encrypted = new Uint8Array(message.length);\n      for (let i = 0; i < message.length; i++) {\n        encrypted[i] = message[i] ^ key[i % key.length];\n      }\n      return encrypted;\n    })();\n  }\n  fallbackDecryptMessage(encrypted, privateKey) {\n    return _asyncToGenerator(function* () {\n      // Simple XOR decryption (NOT SECURE)\n      const key = privateKey.slice(0, 32);\n      const decrypted = new Uint8Array(encrypted.length);\n      for (let i = 0; i < encrypted.length; i++) {\n        decrypted[i] = encrypted[i] ^ key[i % key.length];\n      }\n      return decrypted;\n    })();\n  }\n  fallbackRotateKeys(oldPrivateKey) {\n    var _this1 = this;\n    return _asyncToGenerator(function* () {\n      // Generate new key pair\n      return _this1.fallbackGenerateKeyPair();\n    })();\n  }\n  static {\n    this.ɵfac = function WasmService_Factory(t) {\n      return new (t || WasmService)(i0.ɵɵinject(i1.ErrorHandlingService));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: WasmService,\n      factory: WasmService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["WasmService", "constructor", "errorHandling", "wasmModule", "currentKeyPair", "KEY_ROTATION_INTERVAL", "wasmInstance", "wasmFunctions", "initializeWasm", "startKeyRotation", "init", "_this", "_asyncToGenerator", "loadWasmModule", "default", "generate_key_pair", "encrypt_message", "decrypt_message", "rotate_keys", "Error", "error", "console", "warn", "info", "initializeFallback", "_this2", "generateNewKeyPair", "handleError", "fallbackGenerateKeyPair", "bind", "fallbackEncryptMessage", "fallbackDecryptMessage", "fallback<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_this3", "keyPairBytes", "keyPair", "JSON", "parse", "TextDecoder", "decode", "public_key", "Uint8Array", "private_key", "timestamp", "Date", "version", "_this4", "setInterval", "rotateKeys", "_this5", "newKeyPairBytes", "newKeyPair", "encryptMessage", "message", "_this6", "messageBytes", "TextEncoder", "encode", "encryptedBytes", "decryptMessage", "encrypted", "_this7", "decryptedBytes", "signMessage", "_this8", "verifySignature", "signature", "_this9", "importKeyPair", "_this0", "getCurrentKeyPair", "wipeMemory", "Array", "from", "crypto", "getRandomValues", "stringify", "public<PERSON>ey", "key", "slice", "length", "i", "privateKey", "decrypted", "oldPrivateKey", "_this1", "i0", "ɵɵinject", "i1", "ErrorHandlingService", "factory", "ɵfac", "providedIn"], "sources": ["D:\\TCL1\\Projects\\Projects\\QSC1\\frontend\\src\\app\\services\\wasm.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { ErrorHandlingService } from './error-handling.service';\r\n\r\nexport interface KeyPair {\r\n  version: number;\r\n  timestamp: Date;\r\n  public_key: Uint8Array;\r\n  private_key: Uint8Array;\r\n}\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class WasmService {\r\n  private wasmModule: WebAssembly.Module | null = null;\r\n  private currentKeyPair: KeyPair | null = null;\r\n  private readonly KEY_ROTATION_INTERVAL = 30 * 24 * 60 * 60 * 1000; // 30 days\r\n  private wasmInstance: any = null;\r\n  private wasmFunctions: any = null;\r\n\r\n  constructor(private errorHandling: ErrorHandlingService) {\r\n    this.initializeWasm();\r\n    this.startKeyRotation();\r\n  }\r\n\r\n  async init(): Promise<void> {\r\n    try {\r\n      // Try to load WASM module - will fail gracefully in development\r\n      const wasmModule = await this.loadWasmModule();\r\n      if (wasmModule) {\r\n        this.wasmInstance = wasmModule;\r\n        await this.wasmInstance.default();\r\n\r\n        // Extract WASM functions\r\n        this.wasmFunctions = {\r\n          generate_key_pair: this.wasmInstance.generate_key_pair,\r\n          encrypt_message: this.wasmInstance.encrypt_message,\r\n          decrypt_message: this.wasmInstance.decrypt_message,\r\n          rotate_keys: this.wasmInstance.rotate_keys\r\n        };\r\n      } else {\r\n        throw new Error('WASM module not available');\r\n      }\r\n    } catch (error) {\r\n      console.warn('WASM module not available, using fallback implementation');\r\n      console.info('This is expected in development mode - fallback crypto implementations will be used');\r\n      this.initializeFallback();\r\n    }\r\n  }\r\n\r\n  private async loadWasmModule(): Promise<any> {\r\n    try {\r\n      // For development, we'll skip WASM loading and use fallbacks\r\n      // In production, this would load the actual compiled WASM module\r\n      console.warn('WASM module loading skipped in development - using fallback implementations');\r\n      return null;\r\n    } catch (error) {\r\n      console.warn('Could not load WASM module:', error);\r\n      return null;\r\n    }\r\n  }\r\n\r\n  private async initializeWasm(): Promise<void> {\r\n    try {\r\n      await this.init();\r\n      await this.generateNewKeyPair();\r\n    } catch (error) {\r\n      await this.errorHandling.handleError(\r\n        error instanceof Error ? error : new Error('WASM initialization failed'),\r\n        'WASM'\r\n      );\r\n      // Don't throw error, allow fallback to work\r\n      console.warn('WASM initialization failed, continuing with fallback');\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Initialize fallback cryptographic implementation\r\n   */\r\n  private initializeFallback(): void {\r\n    console.warn('Using fallback cryptographic implementation - NOT SECURE FOR PRODUCTION');\r\n    this.wasmFunctions = {\r\n      generate_key_pair: this.fallbackGenerateKeyPair.bind(this),\r\n      encrypt_message: this.fallbackEncryptMessage.bind(this),\r\n      decrypt_message: this.fallbackDecryptMessage.bind(this),\r\n      rotate_keys: this.fallbackRotateKeys.bind(this)\r\n    };\r\n  }\r\n\r\n  private async generateNewKeyPair(): Promise<void> {\r\n    try {\r\n      if (!this.wasmFunctions) {\r\n        throw new Error('WASM functions not initialized');\r\n      }\r\n\r\n      const keyPairBytes = await this.wasmFunctions.generate_key_pair();\r\n      const keyPair = JSON.parse(new TextDecoder().decode(keyPairBytes));\r\n\r\n      this.currentKeyPair = {\r\n        public_key: new Uint8Array(keyPair.public_key),\r\n        private_key: new Uint8Array(keyPair.private_key),\r\n        timestamp: new Date(),\r\n        version: (this.currentKeyPair?.version ?? 0) + 1\r\n      };\r\n    } catch (error) {\r\n      await this.errorHandling.handleError(\r\n        error instanceof Error ? error : new Error('Key pair generation failed'),\r\n        'WASM'\r\n      );\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  private startKeyRotation(): void {\r\n    setInterval(async () => {\r\n      try {\r\n        await this.rotateKeys();\r\n      } catch (error) {\r\n        await this.errorHandling.handleError(\r\n          error instanceof Error ? error : new Error('Key rotation failed'),\r\n          'SECURITY'\r\n        );\r\n      }\r\n    }, this.KEY_ROTATION_INTERVAL);\r\n  }\r\n\r\n  public async rotateKeys(): Promise<void> {\r\n    if (!this.currentKeyPair) {\r\n      throw new Error('No key pair available for rotation');\r\n    }\r\n\r\n    if (!this.wasmFunctions) {\r\n      throw new Error('WASM functions not initialized');\r\n    }\r\n\r\n    try {\r\n      const newKeyPairBytes = await this.wasmFunctions.rotate_keys(this.currentKeyPair.private_key);\r\n      const newKeyPair = JSON.parse(new TextDecoder().decode(newKeyPairBytes));\r\n\r\n      this.currentKeyPair = {\r\n        public_key: new Uint8Array(newKeyPair.public_key),\r\n        private_key: new Uint8Array(newKeyPair.private_key),\r\n        timestamp: new Date(),\r\n        version: this.currentKeyPair.version + 1\r\n      };\r\n    } catch (error) {\r\n      await this.errorHandling.handleError(\r\n        error instanceof Error ? error : new Error('Key rotation failed'),\r\n        'SECURITY'\r\n      );\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  public async encryptMessage(message: string): Promise<Uint8Array> {\r\n    if (!this.currentKeyPair) {\r\n      throw new Error('No key pair available for encryption');\r\n    }\r\n\r\n    if (!this.wasmFunctions) {\r\n      throw new Error('WASM functions not initialized');\r\n    }\r\n\r\n    try {\r\n      const messageBytes = new TextEncoder().encode(message);\r\n      const encryptedBytes = await this.wasmFunctions.encrypt_message(messageBytes, this.currentKeyPair.public_key);\r\n      return new Uint8Array(encryptedBytes);\r\n    } catch (error) {\r\n      await this.errorHandling.handleError(\r\n        error instanceof Error ? error : new Error('Message encryption failed'),\r\n        'SECURITY'\r\n      );\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  public async decryptMessage(encrypted: Uint8Array): Promise<string> {\r\n    if (!this.currentKeyPair) {\r\n      throw new Error('No key pair available for decryption');\r\n    }\r\n\r\n    if (!this.wasmFunctions) {\r\n      throw new Error('WASM functions not initialized');\r\n    }\r\n\r\n    try {\r\n      const decryptedBytes = await this.wasmFunctions.decrypt_message(encrypted, this.currentKeyPair.private_key);\r\n      return new TextDecoder().decode(decryptedBytes);\r\n    } catch (error) {\r\n      await this.errorHandling.handleError(\r\n        error instanceof Error ? error : new Error('Message decryption failed'),\r\n        'SECURITY'\r\n      );\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  public async signMessage(message: string): Promise<Uint8Array> {\r\n    if (!this.currentKeyPair) {\r\n      throw new Error('No key pair available for signing');\r\n    }\r\n\r\n    try {\r\n      // TODO: Implement signing logic using WASM\r\n      throw new Error('Signing method not implemented');\r\n    } catch (error) {\r\n      await this.errorHandling.handleError(\r\n        error instanceof Error ? error : new Error('Signing failed'),\r\n        'SECURITY'\r\n      );\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  public async verifySignature(message: string, signature: Uint8Array): Promise<boolean> {\r\n    if (!this.currentKeyPair) {\r\n      throw new Error('No key pair available for signature verification');\r\n    }\r\n\r\n    try {\r\n      // TODO: Implement signature verification logic using WASM\r\n      throw new Error('Signature verification method not implemented');\r\n    } catch (error) {\r\n      await this.errorHandling.handleError(\r\n        error instanceof Error ? error : new Error('Signature verification failed'),\r\n        'SECURITY'\r\n      );\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  public async importKeyPair(keyPair: KeyPair): Promise<void> {\r\n    try {\r\n      this.currentKeyPair = keyPair;\r\n    } catch (error) {\r\n      await this.errorHandling.handleError(\r\n        error instanceof Error ? error : new Error('Key pair import failed'),\r\n        'WASM'\r\n      );\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  public getCurrentKeyPair(): KeyPair | null {\r\n    return this.currentKeyPair;\r\n  }\r\n\r\n  public wipeMemory(): void {\r\n    this.currentKeyPair = null;\r\n    this.wasmModule = null;\r\n    this.wasmInstance = null;\r\n    this.wasmFunctions = null;\r\n  }\r\n\r\n  // Fallback implementations (NOT SECURE FOR PRODUCTION)\r\n  private async fallbackGenerateKeyPair(): Promise<Uint8Array> {\r\n    const keyPair = {\r\n      public_key: Array.from(crypto.getRandomValues(new Uint8Array(32))),\r\n      private_key: Array.from(crypto.getRandomValues(new Uint8Array(64)))\r\n    };\r\n    return new TextEncoder().encode(JSON.stringify(keyPair));\r\n  }\r\n\r\n  private async fallbackEncryptMessage(message: Uint8Array, publicKey: Uint8Array): Promise<Uint8Array> {\r\n    // Simple XOR encryption (NOT SECURE)\r\n    const key = publicKey.slice(0, 32);\r\n    const encrypted = new Uint8Array(message.length);\r\n    for (let i = 0; i < message.length; i++) {\r\n      encrypted[i] = message[i] ^ key[i % key.length];\r\n    }\r\n    return encrypted;\r\n  }\r\n\r\n  private async fallbackDecryptMessage(encrypted: Uint8Array, privateKey: Uint8Array): Promise<Uint8Array> {\r\n    // Simple XOR decryption (NOT SECURE)\r\n    const key = privateKey.slice(0, 32);\r\n    const decrypted = new Uint8Array(encrypted.length);\r\n    for (let i = 0; i < encrypted.length; i++) {\r\n      decrypted[i] = encrypted[i] ^ key[i % key.length];\r\n    }\r\n    return decrypted;\r\n  }\r\n\r\n  private async fallbackRotateKeys(oldPrivateKey: Uint8Array): Promise<Uint8Array> {\r\n    // Generate new key pair\r\n    return this.fallbackGenerateKeyPair();\r\n  }\r\n}\r\n"], "mappings": ";;;AAaA,OAAM,MAAOA,WAAW;EAOtBC,YAAoBC,aAAmC;IAAnC,KAAAA,aAAa,GAAbA,aAAa;IANzB,KAAAC,UAAU,GAA8B,IAAI;IAC5C,KAAAC,cAAc,GAAmB,IAAI;IAC5B,KAAAC,qBAAqB,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;IAC3D,KAAAC,YAAY,GAAQ,IAAI;IACxB,KAAAC,aAAa,GAAQ,IAAI;IAG/B,IAAI,CAACC,cAAc,EAAE;IACrB,IAAI,CAACC,gBAAgB,EAAE;EACzB;EAEMC,IAAIA,CAAA;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MACR,IAAI;QACF;QACA,MAAMT,UAAU,SAASQ,KAAI,CAACE,cAAc,EAAE;QAC9C,IAAIV,UAAU,EAAE;UACdQ,KAAI,CAACL,YAAY,GAAGH,UAAU;UAC9B,MAAMQ,KAAI,CAACL,YAAY,CAACQ,OAAO,EAAE;UAEjC;UACAH,KAAI,CAACJ,aAAa,GAAG;YACnBQ,iBAAiB,EAAEJ,KAAI,CAACL,YAAY,CAACS,iBAAiB;YACtDC,eAAe,EAAEL,KAAI,CAACL,YAAY,CAACU,eAAe;YAClDC,eAAe,EAAEN,KAAI,CAACL,YAAY,CAACW,eAAe;YAClDC,WAAW,EAAEP,KAAI,CAACL,YAAY,CAACY;WAChC;SACF,MAAM;UACL,MAAM,IAAIC,KAAK,CAAC,2BAA2B,CAAC;;OAE/C,CAAC,OAAOC,KAAK,EAAE;QACdC,OAAO,CAACC,IAAI,CAAC,0DAA0D,CAAC;QACxED,OAAO,CAACE,IAAI,CAAC,qFAAqF,CAAC;QACnGZ,KAAI,CAACa,kBAAkB,EAAE;;IAC1B;EACH;EAEcX,cAAcA,CAAA;IAAA,OAAAD,iBAAA;MAC1B,IAAI;QACF;QACA;QACAS,OAAO,CAACC,IAAI,CAAC,6EAA6E,CAAC;QAC3F,OAAO,IAAI;OACZ,CAAC,OAAOF,KAAK,EAAE;QACdC,OAAO,CAACC,IAAI,CAAC,6BAA6B,EAAEF,KAAK,CAAC;QAClD,OAAO,IAAI;;IACZ;EACH;EAEcZ,cAAcA,CAAA;IAAA,IAAAiB,MAAA;IAAA,OAAAb,iBAAA;MAC1B,IAAI;QACF,MAAMa,MAAI,CAACf,IAAI,EAAE;QACjB,MAAMe,MAAI,CAACC,kBAAkB,EAAE;OAChC,CAAC,OAAON,KAAK,EAAE;QACd,MAAMK,MAAI,CAACvB,aAAa,CAACyB,WAAW,CAClCP,KAAK,YAAYD,KAAK,GAAGC,KAAK,GAAG,IAAID,KAAK,CAAC,4BAA4B,CAAC,EACxE,MAAM,CACP;QACD;QACAE,OAAO,CAACC,IAAI,CAAC,sDAAsD,CAAC;;IACrE;EACH;EAEA;;;EAGQE,kBAAkBA,CAAA;IACxBH,OAAO,CAACC,IAAI,CAAC,yEAAyE,CAAC;IACvF,IAAI,CAACf,aAAa,GAAG;MACnBQ,iBAAiB,EAAE,IAAI,CAACa,uBAAuB,CAACC,IAAI,CAAC,IAAI,CAAC;MAC1Db,eAAe,EAAE,IAAI,CAACc,sBAAsB,CAACD,IAAI,CAAC,IAAI,CAAC;MACvDZ,eAAe,EAAE,IAAI,CAACc,sBAAsB,CAACF,IAAI,CAAC,IAAI,CAAC;MACvDX,WAAW,EAAE,IAAI,CAACc,kBAAkB,CAACH,IAAI,CAAC,IAAI;KAC/C;EACH;EAEcH,kBAAkBA,CAAA;IAAA,IAAAO,MAAA;IAAA,OAAArB,iBAAA;MAC9B,IAAI;QACF,IAAI,CAACqB,MAAI,CAAC1B,aAAa,EAAE;UACvB,MAAM,IAAIY,KAAK,CAAC,gCAAgC,CAAC;;QAGnD,MAAMe,YAAY,SAASD,MAAI,CAAC1B,aAAa,CAACQ,iBAAiB,EAAE;QACjE,MAAMoB,OAAO,GAAGC,IAAI,CAACC,KAAK,CAAC,IAAIC,WAAW,EAAE,CAACC,MAAM,CAACL,YAAY,CAAC,CAAC;QAElED,MAAI,CAAC7B,cAAc,GAAG;UACpBoC,UAAU,EAAE,IAAIC,UAAU,CAACN,OAAO,CAACK,UAAU,CAAC;UAC9CE,WAAW,EAAE,IAAID,UAAU,CAACN,OAAO,CAACO,WAAW,CAAC;UAChDC,SAAS,EAAE,IAAIC,IAAI,EAAE;UACrBC,OAAO,EAAE,CAACZ,MAAI,CAAC7B,cAAc,EAAEyC,OAAO,IAAI,CAAC,IAAI;SAChD;OACF,CAAC,OAAOzB,KAAK,EAAE;QACd,MAAMa,MAAI,CAAC/B,aAAa,CAACyB,WAAW,CAClCP,KAAK,YAAYD,KAAK,GAAGC,KAAK,GAAG,IAAID,KAAK,CAAC,4BAA4B,CAAC,EACxE,MAAM,CACP;QACD,MAAMC,KAAK;;IACZ;EACH;EAEQX,gBAAgBA,CAAA;IAAA,IAAAqC,MAAA;IACtBC,WAAW,cAAAnC,iBAAA,CAAC,aAAW;MACrB,IAAI;QACF,MAAMkC,MAAI,CAACE,UAAU,EAAE;OACxB,CAAC,OAAO5B,KAAK,EAAE;QACd,MAAM0B,MAAI,CAAC5C,aAAa,CAACyB,WAAW,CAClCP,KAAK,YAAYD,KAAK,GAAGC,KAAK,GAAG,IAAID,KAAK,CAAC,qBAAqB,CAAC,EACjE,UAAU,CACX;;IAEL,CAAC,GAAE,IAAI,CAACd,qBAAqB,CAAC;EAChC;EAEa2C,UAAUA,CAAA;IAAA,IAAAC,MAAA;IAAA,OAAArC,iBAAA;MACrB,IAAI,CAACqC,MAAI,CAAC7C,cAAc,EAAE;QACxB,MAAM,IAAIe,KAAK,CAAC,oCAAoC,CAAC;;MAGvD,IAAI,CAAC8B,MAAI,CAAC1C,aAAa,EAAE;QACvB,MAAM,IAAIY,KAAK,CAAC,gCAAgC,CAAC;;MAGnD,IAAI;QACF,MAAM+B,eAAe,SAASD,MAAI,CAAC1C,aAAa,CAACW,WAAW,CAAC+B,MAAI,CAAC7C,cAAc,CAACsC,WAAW,CAAC;QAC7F,MAAMS,UAAU,GAAGf,IAAI,CAACC,KAAK,CAAC,IAAIC,WAAW,EAAE,CAACC,MAAM,CAACW,eAAe,CAAC,CAAC;QAExED,MAAI,CAAC7C,cAAc,GAAG;UACpBoC,UAAU,EAAE,IAAIC,UAAU,CAACU,UAAU,CAACX,UAAU,CAAC;UACjDE,WAAW,EAAE,IAAID,UAAU,CAACU,UAAU,CAACT,WAAW,CAAC;UACnDC,SAAS,EAAE,IAAIC,IAAI,EAAE;UACrBC,OAAO,EAAEI,MAAI,CAAC7C,cAAc,CAACyC,OAAO,GAAG;SACxC;OACF,CAAC,OAAOzB,KAAK,EAAE;QACd,MAAM6B,MAAI,CAAC/C,aAAa,CAACyB,WAAW,CAClCP,KAAK,YAAYD,KAAK,GAAGC,KAAK,GAAG,IAAID,KAAK,CAAC,qBAAqB,CAAC,EACjE,UAAU,CACX;QACD,MAAMC,KAAK;;IACZ;EACH;EAEagC,cAAcA,CAACC,OAAe;IAAA,IAAAC,MAAA;IAAA,OAAA1C,iBAAA;MACzC,IAAI,CAAC0C,MAAI,CAAClD,cAAc,EAAE;QACxB,MAAM,IAAIe,KAAK,CAAC,sCAAsC,CAAC;;MAGzD,IAAI,CAACmC,MAAI,CAAC/C,aAAa,EAAE;QACvB,MAAM,IAAIY,KAAK,CAAC,gCAAgC,CAAC;;MAGnD,IAAI;QACF,MAAMoC,YAAY,GAAG,IAAIC,WAAW,EAAE,CAACC,MAAM,CAACJ,OAAO,CAAC;QACtD,MAAMK,cAAc,SAASJ,MAAI,CAAC/C,aAAa,CAACS,eAAe,CAACuC,YAAY,EAAED,MAAI,CAAClD,cAAc,CAACoC,UAAU,CAAC;QAC7G,OAAO,IAAIC,UAAU,CAACiB,cAAc,CAAC;OACtC,CAAC,OAAOtC,KAAK,EAAE;QACd,MAAMkC,MAAI,CAACpD,aAAa,CAACyB,WAAW,CAClCP,KAAK,YAAYD,KAAK,GAAGC,KAAK,GAAG,IAAID,KAAK,CAAC,2BAA2B,CAAC,EACvE,UAAU,CACX;QACD,MAAMC,KAAK;;IACZ;EACH;EAEauC,cAAcA,CAACC,SAAqB;IAAA,IAAAC,MAAA;IAAA,OAAAjD,iBAAA;MAC/C,IAAI,CAACiD,MAAI,CAACzD,cAAc,EAAE;QACxB,MAAM,IAAIe,KAAK,CAAC,sCAAsC,CAAC;;MAGzD,IAAI,CAAC0C,MAAI,CAACtD,aAAa,EAAE;QACvB,MAAM,IAAIY,KAAK,CAAC,gCAAgC,CAAC;;MAGnD,IAAI;QACF,MAAM2C,cAAc,SAASD,MAAI,CAACtD,aAAa,CAACU,eAAe,CAAC2C,SAAS,EAAEC,MAAI,CAACzD,cAAc,CAACsC,WAAW,CAAC;QAC3G,OAAO,IAAIJ,WAAW,EAAE,CAACC,MAAM,CAACuB,cAAc,CAAC;OAChD,CAAC,OAAO1C,KAAK,EAAE;QACd,MAAMyC,MAAI,CAAC3D,aAAa,CAACyB,WAAW,CAClCP,KAAK,YAAYD,KAAK,GAAGC,KAAK,GAAG,IAAID,KAAK,CAAC,2BAA2B,CAAC,EACvE,UAAU,CACX;QACD,MAAMC,KAAK;;IACZ;EACH;EAEa2C,WAAWA,CAACV,OAAe;IAAA,IAAAW,MAAA;IAAA,OAAApD,iBAAA;MACtC,IAAI,CAACoD,MAAI,CAAC5D,cAAc,EAAE;QACxB,MAAM,IAAIe,KAAK,CAAC,mCAAmC,CAAC;;MAGtD,IAAI;QACF;QACA,MAAM,IAAIA,KAAK,CAAC,gCAAgC,CAAC;OAClD,CAAC,OAAOC,KAAK,EAAE;QACd,MAAM4C,MAAI,CAAC9D,aAAa,CAACyB,WAAW,CAClCP,KAAK,YAAYD,KAAK,GAAGC,KAAK,GAAG,IAAID,KAAK,CAAC,gBAAgB,CAAC,EAC5D,UAAU,CACX;QACD,MAAMC,KAAK;;IACZ;EACH;EAEa6C,eAAeA,CAACZ,OAAe,EAAEa,SAAqB;IAAA,IAAAC,MAAA;IAAA,OAAAvD,iBAAA;MACjE,IAAI,CAACuD,MAAI,CAAC/D,cAAc,EAAE;QACxB,MAAM,IAAIe,KAAK,CAAC,kDAAkD,CAAC;;MAGrE,IAAI;QACF;QACA,MAAM,IAAIA,KAAK,CAAC,+CAA+C,CAAC;OACjE,CAAC,OAAOC,KAAK,EAAE;QACd,MAAM+C,MAAI,CAACjE,aAAa,CAACyB,WAAW,CAClCP,KAAK,YAAYD,KAAK,GAAGC,KAAK,GAAG,IAAID,KAAK,CAAC,+BAA+B,CAAC,EAC3E,UAAU,CACX;QACD,MAAMC,KAAK;;IACZ;EACH;EAEagD,aAAaA,CAACjC,OAAgB;IAAA,IAAAkC,MAAA;IAAA,OAAAzD,iBAAA;MACzC,IAAI;QACFyD,MAAI,CAACjE,cAAc,GAAG+B,OAAO;OAC9B,CAAC,OAAOf,KAAK,EAAE;QACd,MAAMiD,MAAI,CAACnE,aAAa,CAACyB,WAAW,CAClCP,KAAK,YAAYD,KAAK,GAAGC,KAAK,GAAG,IAAID,KAAK,CAAC,wBAAwB,CAAC,EACpE,MAAM,CACP;QACD,MAAMC,KAAK;;IACZ;EACH;EAEOkD,iBAAiBA,CAAA;IACtB,OAAO,IAAI,CAAClE,cAAc;EAC5B;EAEOmE,UAAUA,CAAA;IACf,IAAI,CAACnE,cAAc,GAAG,IAAI;IAC1B,IAAI,CAACD,UAAU,GAAG,IAAI;IACtB,IAAI,CAACG,YAAY,GAAG,IAAI;IACxB,IAAI,CAACC,aAAa,GAAG,IAAI;EAC3B;EAEA;EACcqB,uBAAuBA,CAAA;IAAA,OAAAhB,iBAAA;MACnC,MAAMuB,OAAO,GAAG;QACdK,UAAU,EAAEgC,KAAK,CAACC,IAAI,CAACC,MAAM,CAACC,eAAe,CAAC,IAAIlC,UAAU,CAAC,EAAE,CAAC,CAAC,CAAC;QAClEC,WAAW,EAAE8B,KAAK,CAACC,IAAI,CAACC,MAAM,CAACC,eAAe,CAAC,IAAIlC,UAAU,CAAC,EAAE,CAAC,CAAC;OACnE;MACD,OAAO,IAAIe,WAAW,EAAE,CAACC,MAAM,CAACrB,IAAI,CAACwC,SAAS,CAACzC,OAAO,CAAC,CAAC;IAAC;EAC3D;EAEcL,sBAAsBA,CAACuB,OAAmB,EAAEwB,SAAqB;IAAA,OAAAjE,iBAAA;MAC7E;MACA,MAAMkE,GAAG,GAAGD,SAAS,CAACE,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC;MAClC,MAAMnB,SAAS,GAAG,IAAInB,UAAU,CAACY,OAAO,CAAC2B,MAAM,CAAC;MAChD,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG5B,OAAO,CAAC2B,MAAM,EAAEC,CAAC,EAAE,EAAE;QACvCrB,SAAS,CAACqB,CAAC,CAAC,GAAG5B,OAAO,CAAC4B,CAAC,CAAC,GAAGH,GAAG,CAACG,CAAC,GAAGH,GAAG,CAACE,MAAM,CAAC;;MAEjD,OAAOpB,SAAS;IAAC;EACnB;EAEc7B,sBAAsBA,CAAC6B,SAAqB,EAAEsB,UAAsB;IAAA,OAAAtE,iBAAA;MAChF;MACA,MAAMkE,GAAG,GAAGI,UAAU,CAACH,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC;MACnC,MAAMI,SAAS,GAAG,IAAI1C,UAAU,CAACmB,SAAS,CAACoB,MAAM,CAAC;MAClD,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGrB,SAAS,CAACoB,MAAM,EAAEC,CAAC,EAAE,EAAE;QACzCE,SAAS,CAACF,CAAC,CAAC,GAAGrB,SAAS,CAACqB,CAAC,CAAC,GAAGH,GAAG,CAACG,CAAC,GAAGH,GAAG,CAACE,MAAM,CAAC;;MAEnD,OAAOG,SAAS;IAAC;EACnB;EAEcnD,kBAAkBA,CAACoD,aAAyB;IAAA,IAAAC,MAAA;IAAA,OAAAzE,iBAAA;MACxD;MACA,OAAOyE,MAAI,CAACzD,uBAAuB,EAAE;IAAC;EACxC;;;uBAjRW5B,WAAW,EAAAsF,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,oBAAA;IAAA;EAAA;;;aAAXzF,WAAW;MAAA0F,OAAA,EAAX1F,WAAW,CAAA2F,IAAA;MAAAC,UAAA,EAFV;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}