{"ast": null, "code": "import { map } from './map';\nimport { innerFrom } from '../observable/innerFrom';\nimport { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nexport function exhaustMap(project, resultSelector) {\n  if (resultSelector) {\n    return source => source.pipe(exhaustMap((a, i) => innerFrom(project(a, i)).pipe(map((b, ii) => resultSelector(a, b, i, ii)))));\n  }\n  return operate((source, subscriber) => {\n    let index = 0;\n    let innerSub = null;\n    let isComplete = false;\n    source.subscribe(createOperatorSubscriber(subscriber, outerValue => {\n      if (!innerSub) {\n        innerSub = createOperatorSubscriber(subscriber, undefined, () => {\n          innerSub = null;\n          isComplete && subscriber.complete();\n        });\n        innerFrom(project(outerValue, index++)).subscribe(innerSub);\n      }\n    }, () => {\n      isComplete = true;\n      !innerSub && subscriber.complete();\n    }));\n  });\n}", "map": {"version": 3, "names": ["map", "innerFrom", "operate", "createOperatorSubscriber", "exhaustMap", "project", "resultSelector", "source", "pipe", "a", "i", "b", "ii", "subscriber", "index", "innerSub", "isComplete", "subscribe", "outerValue", "undefined", "complete"], "sources": ["D:/TCL1/Projects/Projects/QSC1/frontend/node_modules/rxjs/dist/esm/internal/operators/exhaustMap.js"], "sourcesContent": ["import { map } from './map';\nimport { innerFrom } from '../observable/innerFrom';\nimport { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nexport function exhaustMap(project, resultSelector) {\n    if (resultSelector) {\n        return (source) => source.pipe(exhaustMap((a, i) => innerFrom(project(a, i)).pipe(map((b, ii) => resultSelector(a, b, i, ii)))));\n    }\n    return operate((source, subscriber) => {\n        let index = 0;\n        let innerSub = null;\n        let isComplete = false;\n        source.subscribe(createOperatorSubscriber(subscriber, (outerValue) => {\n            if (!innerSub) {\n                innerSub = createOperatorSubscriber(subscriber, undefined, () => {\n                    innerSub = null;\n                    isComplete && subscriber.complete();\n                });\n                innerFrom(project(outerValue, index++)).subscribe(innerSub);\n            }\n        }, () => {\n            isComplete = true;\n            !innerSub && subscriber.complete();\n        }));\n    });\n}\n"], "mappings": "AAAA,SAASA,GAAG,QAAQ,OAAO;AAC3B,SAASC,SAAS,QAAQ,yBAAyB;AACnD,SAASC,OAAO,QAAQ,cAAc;AACtC,SAASC,wBAAwB,QAAQ,sBAAsB;AAC/D,OAAO,SAASC,UAAUA,CAACC,OAAO,EAAEC,cAAc,EAAE;EAChD,IAAIA,cAAc,EAAE;IAChB,OAAQC,MAAM,IAAKA,MAAM,CAACC,IAAI,CAACJ,UAAU,CAAC,CAACK,CAAC,EAAEC,CAAC,KAAKT,SAAS,CAACI,OAAO,CAACI,CAAC,EAAEC,CAAC,CAAC,CAAC,CAACF,IAAI,CAACR,GAAG,CAAC,CAACW,CAAC,EAAEC,EAAE,KAAKN,cAAc,CAACG,CAAC,EAAEE,CAAC,EAAED,CAAC,EAAEE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EACpI;EACA,OAAOV,OAAO,CAAC,CAACK,MAAM,EAAEM,UAAU,KAAK;IACnC,IAAIC,KAAK,GAAG,CAAC;IACb,IAAIC,QAAQ,GAAG,IAAI;IACnB,IAAIC,UAAU,GAAG,KAAK;IACtBT,MAAM,CAACU,SAAS,CAACd,wBAAwB,CAACU,UAAU,EAAGK,UAAU,IAAK;MAClE,IAAI,CAACH,QAAQ,EAAE;QACXA,QAAQ,GAAGZ,wBAAwB,CAACU,UAAU,EAAEM,SAAS,EAAE,MAAM;UAC7DJ,QAAQ,GAAG,IAAI;UACfC,UAAU,IAAIH,UAAU,CAACO,QAAQ,CAAC,CAAC;QACvC,CAAC,CAAC;QACFnB,SAAS,CAACI,OAAO,CAACa,UAAU,EAAEJ,KAAK,EAAE,CAAC,CAAC,CAACG,SAAS,CAACF,QAAQ,CAAC;MAC/D;IACJ,CAAC,EAAE,MAAM;MACLC,UAAU,GAAG,IAAI;MACjB,CAACD,QAAQ,IAAIF,UAAU,CAACO,QAAQ,CAAC,CAAC;IACtC,CAAC,CAAC,CAAC;EACP,CAAC,CAAC;AACN", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}