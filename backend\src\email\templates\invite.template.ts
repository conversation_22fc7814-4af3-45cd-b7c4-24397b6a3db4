import { Injectable } from '@nestjs/common';

export interface InviteEmailData {
  recipientEmail: string;
  inviteUrl: string;
  inviterName: string;
  expiresIn: string;
}

@Injectable()
export class InviteEmailTemplate {
  generateInviteEmail(data: InviteEmailData): { html: string; text: string } {
    const html = this.generateHtmlTemplate(data);
    const text = this.generateTextTemplate(data);
    
    return { html, text };
  }

  private generateHtmlTemplate(data: InviteEmailData): string {
    return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>QSC Invitation</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
        .container {
            background: white;
            border-radius: 12px;
            padding: 40px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .logo {
            text-align: center;
            margin-bottom: 30px;
        }
        .logo-circle {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 50%;
            margin: 0 auto 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            font-weight: bold;
        }
        .title {
            color: #2d3748;
            font-size: 28px;
            font-weight: 600;
            margin-bottom: 10px;
            text-align: center;
        }
        .subtitle {
            color: #718096;
            text-align: center;
            margin-bottom: 30px;
        }
        .invite-button {
            display: block;
            width: 200px;
            margin: 30px auto;
            padding: 15px 30px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-decoration: none;
            border-radius: 8px;
            text-align: center;
            font-weight: 600;
            font-size: 16px;
            transition: transform 0.2s;
        }
        .invite-button:hover {
            transform: translateY(-2px);
        }
        .security-notice {
            background: #f7fafc;
            border-left: 4px solid #4299e1;
            padding: 15px;
            margin: 30px 0;
            border-radius: 4px;
        }
        .security-icon {
            color: #4299e1;
            margin-right: 8px;
        }
        .footer {
            text-align: center;
            color: #a0aec0;
            font-size: 14px;
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #e2e8f0;
        }
        .expires {
            color: #e53e3e;
            font-weight: 600;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">
            <div class="logo-circle">Q</div>
            <h1 class="title">QSC</h1>
            <p class="subtitle">Quantum Secure Communication</p>
        </div>
        
        <h2>You're Invited!</h2>
        
        <p>Hello,</p>
        
        <p><strong>${data.inviterName}</strong> has invited you to join QSC, a quantum-secure communication platform that provides unbreakable encryption for your messages.</p>
        
        <div class="security-notice">
            <span class="security-icon">🔒</span>
            <strong>Post-Quantum Security:</strong> QSC uses CRYSTALS-Dilithium and CRYSTALS-Kyber algorithms to protect against both classical and quantum computer attacks.
        </div>
        
        <p>Click the button below to accept your invitation and create your account:</p>
        
        <a href="${data.inviteUrl}" class="invite-button">Accept Invitation</a>
        
        <p><small>Or copy and paste this link into your browser:</small><br>
        <code style="background: #f1f5f9; padding: 4px 8px; border-radius: 4px; font-size: 12px; word-break: break-all;">${data.inviteUrl}</code></p>
        
        <p class="expires"><strong>⏰ This invitation expires in ${data.expiresIn}</strong></p>
        
        <div class="footer">
            <p>This invitation was sent to ${data.recipientEmail}</p>
            <p>If you didn't expect this invitation, you can safely ignore this email.</p>
            <p>QSC - Secure by Design, Quantum-Ready</p>
        </div>
    </div>
</body>
</html>`;
  }

  private generateTextTemplate(data: InviteEmailData): string {
    return `
QSC - Quantum Secure Communication
==================================

You're Invited!

Hello,

${data.inviterName} has invited you to join QSC, a quantum-secure communication platform that provides unbreakable encryption for your messages.

🔒 POST-QUANTUM SECURITY: QSC uses CRYSTALS-Dilithium and CRYSTALS-Kyber algorithms to protect against both classical and quantum computer attacks.

To accept your invitation and create your account, visit:
${data.inviteUrl}

⏰ This invitation expires in ${data.expiresIn}

This invitation was sent to ${data.recipientEmail}
If you didn't expect this invitation, you can safely ignore this email.

QSC - Secure by Design, Quantum-Ready
`;
  }
}
