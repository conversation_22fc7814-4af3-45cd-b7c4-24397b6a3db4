import { Injectable } from '@angular/core';
import { BehaviorSubject, Subject } from 'rxjs';
import { SecureStorageService } from './secure-storage.service';
import { ErrorHandlingService } from './error-handling.service';

export interface Notification {
  id: string;
  type: 'DELETION' | 'COMPROMISE' | 'success' | 'error' | 'warning' | 'info';
  message: string;
  timestamp: Date;
  read: boolean;
}

export interface ToastNotification {
  id: string;
  message: string;
  type: 'success' | 'error' | 'warning' | 'info';
  duration?: number;
  timestamp: Date;
}

@Injectable({
  providedIn: 'root'
})
export class NotificationService {
  private toastNotificationsSubject = new BehaviorSubject<ToastNotification[]>([]);
  private newToastSubject = new Subject<ToastNotification>();

  public toastNotifications$ = this.toastNotificationsSubject.asObservable();
  public newToast$ = this.newToastSubject.asObservable();

  private defaultDuration = 5000; // 5 seconds

  constructor(
    private secureStorage: SecureStorageService,
    private errorHandling: ErrorHandlingService
  ) {
    // Request notification permission on service initialization
    this.requestNotificationPermission();
  }

  async getNotifications(): Promise<Notification[]> {
    try {
      const notifications = await this.secureStorage.retrieveSecurely('notifications', 'notifications');
      return notifications || [];
    } catch (error) {
      this.errorHandling.handleError(error as Error, 'STORAGE');
      throw error;
    }
  }

  async markAsRead(id: string): Promise<void> {
    try {
      const notifications = await this.getNotifications();
      const notification = notifications.find(n => n.id === id);
      if (notification) {
        notification.read = true;
        await this.secureStorage.storeSecurely('notifications', notifications, 'notifications');
      }
    } catch (error) {
      this.errorHandling.handleError(error as Error, 'STORAGE');
      throw error;
    }
  }

  async createCompromiseNotification(messageIds: string[]): Promise<void> {
    try {
      const notifications = await this.getNotifications();
      const newNotification: Notification = {
        id: crypto.randomUUID(),
        type: 'COMPROMISE',
        message: `Messages ${messageIds.join(', ')} may have been compromised`,
        timestamp: new Date(),
        read: false
      };
      notifications.push(newNotification);
      await this.secureStorage.storeSecurely('notifications', notifications, 'notifications');
    } catch (error) {
      this.errorHandling.handleError(error as Error, 'STORAGE');
      throw error;
    }
  }

  async createDeletionNotification(messageId: string): Promise<void> {
    try {
      const notifications = await this.getNotifications();
      const newNotification: Notification = {
        id: crypto.randomUUID(),
        type: 'DELETION',
        message: `Message ${messageId} has been deleted from the chain`,
        timestamp: new Date(),
        read: false
      };
      notifications.push(newNotification);
      await this.secureStorage.storeSecurely('notifications', notifications, 'notifications');
    } catch (error) {
      this.errorHandling.handleError(error as Error, 'STORAGE');
      throw error;
    }
  }

  /**
   * Show a toast notification (for UI feedback)
   */
  showNotification(
    message: string,
    type: 'success' | 'error' | 'warning' | 'info' = 'info',
    duration?: number
  ): void {
    const notification: ToastNotification = {
      id: this.generateToastId(),
      message,
      type,
      duration: duration || this.defaultDuration,
      timestamp: new Date()
    };

    // Add to toast notifications list
    const currentToasts = this.toastNotificationsSubject.value;
    this.toastNotificationsSubject.next([notification, ...currentToasts]);

    // Emit new toast
    this.newToastSubject.next(notification);

    // Show browser notification if permission granted
    this.showBrowserNotification(message, type);

    // Auto-remove notification after duration
    if (notification.duration) {
      setTimeout(() => {
        this.removeToastNotification(notification.id);
      }, notification.duration);
    }
  }

  /**
   * Show success notification
   */
  showSuccess(message: string, duration?: number): void {
    this.showNotification(message, 'success', duration);
  }

  /**
   * Show error notification
   */
  showError(message: string, duration?: number): void {
    this.showNotification(message, 'error', duration || 8000);
  }

  /**
   * Show warning notification
   */
  showWarning(message: string, duration?: number): void {
    this.showNotification(message, 'warning', duration || 6000);
  }

  /**
   * Show info notification
   */
  showInfo(message: string, duration?: number): void {
    this.showNotification(message, 'info', duration);
  }

  /**
   * Remove a specific toast notification
   */
  removeToastNotification(id: string): void {
    const currentToasts = this.toastNotificationsSubject.value;
    const updatedToasts = currentToasts.filter(n => n.id !== id);
    this.toastNotificationsSubject.next(updatedToasts);
  }

  /**
   * Clear all toast notifications
   */
  clearAllToasts(): void {
    this.toastNotificationsSubject.next([]);
  }

  /**
   * Request browser notification permission
   */
  private requestNotificationPermission(): void {
    if ('Notification' in window && Notification.permission === 'default') {
      Notification.requestPermission().then(permission => {
        console.log('Notification permission:', permission);
      });
    }
  }

  /**
   * Show browser notification
   */
  private showBrowserNotification(message: string, type: string): void {
    if ('Notification' in window && Notification.permission === 'granted') {
      const options: NotificationOptions = {
        body: message,
        icon: '/assets/icons/qsc-icon.png',
        badge: '/assets/icons/qsc-badge.png',
        tag: 'qsc-notification',
        requireInteraction: type === 'error',
        silent: false
      };

      const notification = new Notification('QSC', options);

      // Auto-close after 5 seconds (except for errors)
      if (type !== 'error') {
        setTimeout(() => {
          notification.close();
        }, 5000);
      }

      // Handle notification click
      notification.onclick = () => {
        window.focus();
        notification.close();
      };
    }
  }

  /**
   * Generate unique ID for toast notifications
   */
  private generateToastId(): string {
    return `toast_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}
