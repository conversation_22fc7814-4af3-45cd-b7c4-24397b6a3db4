{"ast": null, "code": "import { HttpHeaders } from '@angular/common/http';\nimport { throwError } from 'rxjs';\nimport { catchError, map } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class ApiService {\n  constructor(http) {\n    this.http = http;\n    this.baseUrl = 'http://localhost:3001/api';\n  }\n  getHeaders() {\n    const token = localStorage.getItem('qsc_token');\n    let headers = new HttpHeaders({\n      'Content-Type': 'application/json'\n    });\n    if (token) {\n      headers = headers.set('Authorization', `Bearer ${token}`);\n    }\n    return headers;\n  }\n  handleError(error) {\n    let errorMessage = 'An unknown error occurred';\n    if (error.error instanceof ErrorEvent) {\n      // Client-side error\n      errorMessage = error.error.message;\n    } else {\n      // Server-side error\n      if (error.error && error.error.message) {\n        errorMessage = error.error.message;\n      } else if (error.message) {\n        errorMessage = error.message;\n      }\n    }\n    console.error('API Error:', error);\n    return throwError(() => new Error(errorMessage));\n  }\n  get(endpoint) {\n    return this.http.get(`${this.baseUrl}${endpoint}`, {\n      headers: this.getHeaders()\n    }).pipe(map(response => response.data || response), catchError(this.handleError));\n  }\n  post(endpoint, data) {\n    return this.http.post(`${this.baseUrl}${endpoint}`, data, {\n      headers: this.getHeaders()\n    }).pipe(map(response => response.data || response), catchError(this.handleError));\n  }\n  put(endpoint, data) {\n    return this.http.put(`${this.baseUrl}${endpoint}`, data, {\n      headers: this.getHeaders()\n    }).pipe(map(response => response.data || response), catchError(this.handleError));\n  }\n  delete(endpoint) {\n    return this.http.delete(`${this.baseUrl}${endpoint}`, {\n      headers: this.getHeaders()\n    }).pipe(map(response => response.data || response), catchError(this.handleError));\n  }\n  checkHealth() {\n    return this.http.get(`${this.baseUrl}/health`);\n  }\n  testDatabase() {\n    return this.http.get(`${this.baseUrl}/test-db`);\n  }\n  static {\n    this.ɵfac = function ApiService_Factory(t) {\n      return new (t || ApiService)(i0.ɵɵinject(i1.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: ApiService,\n      factory: ApiService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["HttpHeaders", "throwError", "catchError", "map", "ApiService", "constructor", "http", "baseUrl", "getHeaders", "token", "localStorage", "getItem", "headers", "set", "handleError", "error", "errorMessage", "ErrorEvent", "message", "console", "Error", "get", "endpoint", "pipe", "response", "data", "post", "put", "delete", "checkHealth", "testDatabase", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Projects\\QSC1\\frontend\\src\\app\\services\\api.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { HttpClient, HttpHeaders, HttpErrorResponse } from '@angular/common/http';\nimport { Observable, throwError } from 'rxjs';\nimport { catchError, map } from 'rxjs/operators';\nimport { ApiResponse, ApiError } from '../types/api.types';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class ApiService {\n  private readonly baseUrl = 'http://localhost:3001/api';\n\n  constructor(private http: HttpClient) {}\n\n  private getHeaders(): HttpHeaders {\n    const token = localStorage.getItem('qsc_token');\n    let headers = new HttpHeaders({\n      'Content-Type': 'application/json'\n    });\n\n    if (token) {\n      headers = headers.set('Authorization', `Bearer ${token}`);\n    }\n\n    return headers;\n  }\n\n  private handleError(error: HttpErrorResponse): Observable<never> {\n    let errorMessage = 'An unknown error occurred';\n\n    if (error.error instanceof ErrorEvent) {\n      // Client-side error\n      errorMessage = error.error.message;\n    } else {\n      // Server-side error\n      if (error.error && error.error.message) {\n        errorMessage = error.error.message;\n      } else if (error.message) {\n        errorMessage = error.message;\n      }\n    }\n\n    console.error('API Error:', error);\n    return throwError(() => new Error(errorMessage));\n  }\n\n  get<T>(endpoint: string): Observable<T> {\n    return this.http.get<ApiResponse<T>>(`${this.baseUrl}${endpoint}`, {\n      headers: this.getHeaders()\n    }).pipe(\n      map(response => response.data || response as any),\n      catchError(this.handleError)\n    );\n  }\n\n  post<T>(endpoint: string, data: any): Observable<T> {\n    return this.http.post<ApiResponse<T>>(`${this.baseUrl}${endpoint}`, data, {\n      headers: this.getHeaders()\n    }).pipe(\n      map(response => response.data || response as any),\n      catchError(this.handleError)\n    );\n  }\n\n  put<T>(endpoint: string, data: any): Observable<T> {\n    return this.http.put<ApiResponse<T>>(`${this.baseUrl}${endpoint}`, data, {\n      headers: this.getHeaders()\n    }).pipe(\n      map(response => response.data || response as any),\n      catchError(this.handleError)\n    );\n  }\n\n  delete<T>(endpoint: string): Observable<T> {\n    return this.http.delete<ApiResponse<T>>(`${this.baseUrl}${endpoint}`, {\n      headers: this.getHeaders()\n    }).pipe(\n      map(response => response.data || response as any),\n      catchError(this.handleError)\n    );\n  }\n\n  checkHealth(): Observable<any> {\n    return this.http.get(`${this.baseUrl}/health`);\n  }\n\n  testDatabase(): Observable<any> {\n    return this.http.get(`${this.baseUrl}/test-db`);\n  }\n}\n"], "mappings": "AACA,SAAqBA,WAAW,QAA2B,sBAAsB;AACjF,SAAqBC,UAAU,QAAQ,MAAM;AAC7C,SAASC,UAAU,EAAEC,GAAG,QAAQ,gBAAgB;;;AAMhD,OAAM,MAAOC,UAAU;EAGrBC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IAFP,KAAAC,OAAO,GAAG,2BAA2B;EAEf;EAE/BC,UAAUA,CAAA;IAChB,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC;IAC/C,IAAIC,OAAO,GAAG,IAAIZ,WAAW,CAAC;MAC5B,cAAc,EAAE;KACjB,CAAC;IAEF,IAAIS,KAAK,EAAE;MACTG,OAAO,GAAGA,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE,UAAUJ,KAAK,EAAE,CAAC;;IAG3D,OAAOG,OAAO;EAChB;EAEQE,WAAWA,CAACC,KAAwB;IAC1C,IAAIC,YAAY,GAAG,2BAA2B;IAE9C,IAAID,KAAK,CAACA,KAAK,YAAYE,UAAU,EAAE;MACrC;MACAD,YAAY,GAAGD,KAAK,CAACA,KAAK,CAACG,OAAO;KACnC,MAAM;MACL;MACA,IAAIH,KAAK,CAACA,KAAK,IAAIA,KAAK,CAACA,KAAK,CAACG,OAAO,EAAE;QACtCF,YAAY,GAAGD,KAAK,CAACA,KAAK,CAACG,OAAO;OACnC,MAAM,IAAIH,KAAK,CAACG,OAAO,EAAE;QACxBF,YAAY,GAAGD,KAAK,CAACG,OAAO;;;IAIhCC,OAAO,CAACJ,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;IAClC,OAAOd,UAAU,CAAC,MAAM,IAAImB,KAAK,CAACJ,YAAY,CAAC,CAAC;EAClD;EAEAK,GAAGA,CAAIC,QAAgB;IACrB,OAAO,IAAI,CAAChB,IAAI,CAACe,GAAG,CAAiB,GAAG,IAAI,CAACd,OAAO,GAAGe,QAAQ,EAAE,EAAE;MACjEV,OAAO,EAAE,IAAI,CAACJ,UAAU;KACzB,CAAC,CAACe,IAAI,CACLpB,GAAG,CAACqB,QAAQ,IAAIA,QAAQ,CAACC,IAAI,IAAID,QAAe,CAAC,EACjDtB,UAAU,CAAC,IAAI,CAACY,WAAW,CAAC,CAC7B;EACH;EAEAY,IAAIA,CAAIJ,QAAgB,EAAEG,IAAS;IACjC,OAAO,IAAI,CAACnB,IAAI,CAACoB,IAAI,CAAiB,GAAG,IAAI,CAACnB,OAAO,GAAGe,QAAQ,EAAE,EAAEG,IAAI,EAAE;MACxEb,OAAO,EAAE,IAAI,CAACJ,UAAU;KACzB,CAAC,CAACe,IAAI,CACLpB,GAAG,CAACqB,QAAQ,IAAIA,QAAQ,CAACC,IAAI,IAAID,QAAe,CAAC,EACjDtB,UAAU,CAAC,IAAI,CAACY,WAAW,CAAC,CAC7B;EACH;EAEAa,GAAGA,CAAIL,QAAgB,EAAEG,IAAS;IAChC,OAAO,IAAI,CAACnB,IAAI,CAACqB,GAAG,CAAiB,GAAG,IAAI,CAACpB,OAAO,GAAGe,QAAQ,EAAE,EAAEG,IAAI,EAAE;MACvEb,OAAO,EAAE,IAAI,CAACJ,UAAU;KACzB,CAAC,CAACe,IAAI,CACLpB,GAAG,CAACqB,QAAQ,IAAIA,QAAQ,CAACC,IAAI,IAAID,QAAe,CAAC,EACjDtB,UAAU,CAAC,IAAI,CAACY,WAAW,CAAC,CAC7B;EACH;EAEAc,MAAMA,CAAIN,QAAgB;IACxB,OAAO,IAAI,CAAChB,IAAI,CAACsB,MAAM,CAAiB,GAAG,IAAI,CAACrB,OAAO,GAAGe,QAAQ,EAAE,EAAE;MACpEV,OAAO,EAAE,IAAI,CAACJ,UAAU;KACzB,CAAC,CAACe,IAAI,CACLpB,GAAG,CAACqB,QAAQ,IAAIA,QAAQ,CAACC,IAAI,IAAID,QAAe,CAAC,EACjDtB,UAAU,CAAC,IAAI,CAACY,WAAW,CAAC,CAC7B;EACH;EAEAe,WAAWA,CAAA;IACT,OAAO,IAAI,CAACvB,IAAI,CAACe,GAAG,CAAC,GAAG,IAAI,CAACd,OAAO,SAAS,CAAC;EAChD;EAEAuB,YAAYA,CAAA;IACV,OAAO,IAAI,CAACxB,IAAI,CAACe,GAAG,CAAC,GAAG,IAAI,CAACd,OAAO,UAAU,CAAC;EACjD;;;uBA/EWH,UAAU,EAAA2B,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAAV9B,UAAU;MAAA+B,OAAA,EAAV/B,UAAU,CAAAgC,IAAA;MAAAC,UAAA,EAFT;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}