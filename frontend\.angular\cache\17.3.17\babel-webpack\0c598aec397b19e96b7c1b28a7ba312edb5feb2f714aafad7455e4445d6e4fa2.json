{"ast": null, "code": "import { Subject, BehaviorSubject } from 'rxjs';\nimport { filter, map } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nexport let WebSocketService = /*#__PURE__*/(() => {\n  class WebSocketService {\n    constructor() {\n      this.socket = null;\n      this.messageSubject = new Subject();\n      this.connectionStateSubject = new BehaviorSubject(false);\n      this.messages$ = this.messageSubject.asObservable();\n      this.connectionState$ = this.connectionStateSubject.asObservable();\n      this.reconnectAttempts = 0;\n      this.maxReconnectAttempts = 5;\n      this.reconnectInterval = 5000; // 5 seconds\n      // Auto-connect when service is instantiated\n      // this.connect();\n    }\n    /**\n     * Connect to WebSocket server\n     */\n    connect(url) {\n      if (this.socket && this.socket.readyState === WebSocket.OPEN) {\n        return; // Already connected\n      }\n      const wsUrl = url || this.getWebSocketUrl();\n      try {\n        this.socket = new WebSocket(wsUrl);\n        this.socket.onopen = () => {\n          console.log('WebSocket connected');\n          this.connectionStateSubject.next(true);\n          this.reconnectAttempts = 0;\n        };\n        this.socket.onmessage = event => {\n          try {\n            const message = JSON.parse(event.data);\n            this.messageSubject.next(message);\n          } catch (error) {\n            console.error('Failed to parse WebSocket message:', error);\n          }\n        };\n        this.socket.onclose = () => {\n          console.log('WebSocket disconnected');\n          this.connectionStateSubject.next(false);\n          this.attemptReconnect();\n        };\n        this.socket.onerror = error => {\n          console.error('WebSocket error:', error);\n          this.connectionStateSubject.next(false);\n        };\n      } catch (error) {\n        console.error('Failed to create WebSocket connection:', error);\n        this.connectionStateSubject.next(false);\n      }\n    }\n    /**\n     * Disconnect from WebSocket server\n     */\n    disconnect() {\n      if (this.socket) {\n        this.socket.close();\n        this.socket = null;\n      }\n      this.connectionStateSubject.next(false);\n    }\n    /**\n     * Send a message through WebSocket\n     */\n    send(message) {\n      if (this.socket && this.socket.readyState === WebSocket.OPEN) {\n        this.socket.send(JSON.stringify(message));\n      } else {\n        console.warn('WebSocket is not connected. Message not sent:', message);\n      }\n    }\n    /**\n     * Listen for specific message types\n     */\n    on(messageType) {\n      return this.messages$.pipe(filter(message => message.type === messageType), map(message => message.data));\n    }\n    /**\n     * Check if WebSocket is connected\n     */\n    isConnected() {\n      return this.socket?.readyState === WebSocket.OPEN;\n    }\n    /**\n     * Get WebSocket URL based on current environment\n     */\n    getWebSocketUrl() {\n      const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';\n      const host = window.location.host;\n      // In development, use localhost:3000 for backend\n      if (host.includes('localhost') || host.includes('127.0.0.1')) {\n        return `${protocol}//localhost:3000/ws`;\n      }\n      // In production, use same host\n      return `${protocol}//${host}/ws`;\n    }\n    /**\n     * Attempt to reconnect to WebSocket\n     */\n    attemptReconnect() {\n      if (this.reconnectAttempts < this.maxReconnectAttempts) {\n        this.reconnectAttempts++;\n        console.log(`Attempting to reconnect... (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);\n        setTimeout(() => {\n          this.connect();\n        }, this.reconnectInterval);\n      } else {\n        console.error('Max reconnection attempts reached. Please refresh the page.');\n      }\n    }\n    /**\n     * Send authentication message\n     */\n    authenticate(token) {\n      this.send({\n        type: 'auth',\n        data: {\n          token\n        }\n      });\n    }\n    /**\n     * Join a room/channel\n     */\n    joinRoom(roomId) {\n      this.send({\n        type: 'join',\n        data: {\n          roomId\n        }\n      });\n    }\n    /**\n     * Leave a room/channel\n     */\n    leaveRoom(roomId) {\n      this.send({\n        type: 'leave',\n        data: {\n          roomId\n        }\n      });\n    }\n    /**\n     * Send a chat message\n     */\n    sendMessage(content, recipient, groupId) {\n      this.send({\n        type: 'message',\n        data: {\n          content,\n          recipient,\n          groupId,\n          timestamp: Date.now()\n        }\n      });\n    }\n    /**\n     * Send typing indicator\n     */\n    sendTyping(isTyping, recipient, groupId) {\n      this.send({\n        type: 'typing',\n        data: {\n          isTyping,\n          recipient,\n          groupId\n        }\n      });\n    }\n    static {\n      this.ɵfac = function WebSocketService_Factory(t) {\n        return new (t || WebSocketService)();\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: WebSocketService,\n        factory: WebSocketService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return WebSocketService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}