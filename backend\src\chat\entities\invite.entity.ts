import { 
  <PERSON><PERSON><PERSON>, 
  Column, 
  PrimaryGeneratedC<PERSON>umn, 
  CreateDateColumn, 
  ManyToOne,
  JoinColumn
} from 'typeorm';
import { User } from '../../users/entities/user.entity';
import { IInvite } from '@qsc/shared';

@Entity('invites')
export class Invite implements IInvite {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  email: string;

  @Column({ unique: true })
  inviteCode: string;

  @Column()
  invitedBy: string;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'invited_by' })
  inviter: User;

  @Column()
  expiresAt: Date;

  @Column({ default: false })
  isUsed: boolean;

  @Column({ nullable: true })
  usedAt?: Date;

  @Column({ nullable: true })
  usedBy?: string;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'used_by' })
  usedByUser?: User;

  @CreateDateColumn()
  createdAt: Date;
}
