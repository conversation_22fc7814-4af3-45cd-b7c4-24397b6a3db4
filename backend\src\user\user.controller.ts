import { <PERSON>, Get, Post, Body, Param, UseGuards, Request } from '@nestjs/common';
import { UserService } from './user.service';
import { User } from '../users/entities/user.entity';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';

@Controller('users')
export class UserController {
  constructor(private readonly userService: UserService) {}

  @Post()
  async create(
    @Body('username') username: string,
    @Body('email') email: string,
    @Body('password') password: string,
  ): Promise<User> {
    return this.userService.create(username, email, password);
  }

  @UseGuards(JwtAuthGuard)
  @Get('profile')
  async getProfile(@Request() req: any): Promise<User> {
    return this.userService.findById(req.user.id);
  }

  @UseGuards(JwtAuthGuard)
  @Post('public-key')
  async updatePublicKey(
    @Request() req: any,
    @Body('publicKey') publicKey: string,
  ): Promise<User> {
    return this.userService.updatePublicKey(req.user.id, publicKey);
  }

  @UseGuards(JwtAuthGuard)
  @Post('devices')
  async addDevice(
    @Request() req: any,
    @Body('deviceId') deviceId: string,
  ): Promise<User> {
    return this.userService.addDevice(req.user.id, deviceId);
  }

  @UseGuards(JwtAuthGuard)
  @Post('devices/remove')
  async removeDevice(
    @Request() req: any,
    @Body('deviceId') deviceId: string,
  ): Promise<User> {
    return this.userService.removeDevice(req.user.id, deviceId);
  }
} 