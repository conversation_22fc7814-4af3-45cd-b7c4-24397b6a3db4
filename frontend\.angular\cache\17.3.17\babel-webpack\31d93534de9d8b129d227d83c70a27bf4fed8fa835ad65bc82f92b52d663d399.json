{"ast": null, "code": "import { authGuard } from './guards/auth.guard';\nexport const routes = [{\n  path: '',\n  loadComponent: () => import('./components/qsc-main/qsc-main.component').then(m => m.QscMainComponent),\n  canActivate: [authGuard],\n  pathMatch: 'full'\n}, {\n  path: 'login',\n  loadComponent: () => import('./components/login/login.component').then(m => m.LoginComponent)\n}, {\n  path: '**',\n  redirectTo: ''\n}];", "map": {"version": 3, "names": ["<PERSON>th<PERSON><PERSON>", "routes", "path", "loadComponent", "then", "m", "QscMainComponent", "canActivate", "pathMatch", "LoginComponent", "redirectTo"], "sources": ["C:\\Users\\<USER>\\Projects\\QSC1\\frontend\\src\\app\\app.routes.ts"], "sourcesContent": ["import { Routes } from '@angular/router';\r\nimport { authGuard } from './guards/auth.guard';\r\n\r\nexport const routes: Routes = [\r\n  {\r\n    path: '',\r\n    loadComponent: () => import('./components/qsc-main/qsc-main.component').then(m => m.QscMainComponent),\r\n    canActivate: [authGuard],\r\n    pathMatch: 'full'\r\n  },\r\n  {\r\n    path: 'login',\r\n    loadComponent: () => import('./components/login/login.component').then(m => m.LoginComponent)\r\n  },\r\n  {\r\n    path: '**',\r\n    redirectTo: ''\r\n  }\r\n];\r\n"], "mappings": "AACA,SAASA,SAAS,QAAQ,qBAAqB;AAE/C,OAAO,MAAMC,MAAM,GAAW,CAC5B;EACEC,IAAI,EAAE,EAAE;EACRC,aAAa,EAAEA,CAAA,KAAM,MAAM,CAAC,0CAA0C,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,gBAAgB,CAAC;EACrGC,WAAW,EAAE,CAACP,SAAS,CAAC;EACxBQ,SAAS,EAAE;CACZ,EACD;EACEN,IAAI,EAAE,OAAO;EACbC,aAAa,EAAEA,CAAA,KAAM,MAAM,CAAC,oCAAoC,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACI,cAAc;CAC7F,EACD;EACEP,IAAI,EAAE,IAAI;EACVQ,UAAU,EAAE;CACb,CACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}