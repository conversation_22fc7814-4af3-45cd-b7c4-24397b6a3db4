{"ast": null, "code": "export const routes = [{\n  path: '',\n  loadComponent: () => import('./components/qsc-main/qsc-main.component').then(m => m.QscMainComponent),\n  pathMatch: 'full'\n}, {\n  path: '**',\n  redirectTo: ''\n}];", "map": {"version": 3, "names": ["routes", "path", "loadComponent", "then", "m", "QscMainComponent", "pathMatch", "redirectTo"], "sources": ["D:\\TCL1\\Projects\\Projects\\QSC1\\frontend\\src\\app\\app.routes.ts"], "sourcesContent": ["import { Routes } from '@angular/router';\r\nimport { authGuard } from './guards/auth.guard';\r\n\r\nexport const routes: Routes = [\r\n  {\r\n    path: '',\r\n    loadComponent: () => import('./components/qsc-main/qsc-main.component').then(m => m.QscMainComponent),\r\n    pathMatch: 'full'\r\n  },\r\n  {\r\n    path: '**',\r\n    redirectTo: ''\r\n  }\r\n];\r\n"], "mappings": "AAGA,OAAO,MAAMA,MAAM,GAAW,CAC5B;EACEC,IAAI,EAAE,EAAE;EACRC,aAAa,EAAEA,CAAA,KAAM,MAAM,CAAC,0CAA0C,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,gBAAgB,CAAC;EACrGC,SAAS,EAAE;CACZ,EACD;EACEL,IAAI,EAAE,IAAI;EACVM,UAAU,EAAE;CACb,CACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}