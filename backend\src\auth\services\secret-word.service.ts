import { Injectable, UnauthorizedException } from '@nestjs/common';
import { SecretWordValidation, SecretWordHash, SecretWordConfig } from '../types/secret-word.types';
import * as argon2 from 'argon2';
import * as crypto from 'crypto';

@Injectable()
export class SecretWordService {
  private readonly config: SecretWordConfig = {
    minLength: 4,
    maxAttempts: 3,
    lockoutDuration: 30, // 30 minutes
  };

  async validateSecretWord(word: string): Promise<SecretWordValidation> {
    const validation: SecretWordValidation = {
      hasUpperCase: /[A-Z]/.test(word),
      hasLowerCase: /[a-z]/.test(word),
      hasDigit: /[0-9]/.test(word),
      hasSymbol: /[!@#$%^&*(),.?":{}|<>]/.test(word),
      isLongEnough: word.length >= this.config.minLength,
    };

    return validation;
  }

  async hashSecretWord(word: string): Promise<SecretWordHash> {
    const salt = Buffer.from(crypto.randomBytes(32)).toString('base64');
    const hash = await argon2.hash(word, {
      type: argon2.argon2id,
      salt: Buffer.from(salt, 'base64'),
      memoryCost: 65536,
      timeCost: 3,
      parallelism: 4,
    });

    return {
      hash,
      salt,
      attempts: 0,
      lastAttempt: new Date().toISOString(),
    };
  }

  async verifySecretWord(word: string, storedHash: SecretWordHash): Promise<boolean> {
    if (storedHash.attempts >= this.config.maxAttempts) {
      const lockoutTime = new Date(storedHash.lastAttempt);
      lockoutTime.setMinutes(lockoutTime.getMinutes() + this.config.lockoutDuration);

      if (new Date() < lockoutTime) {
        throw new UnauthorizedException('Account is locked. Try again later.');
      }

      // Reset attempts if lockout period has passed
      storedHash.attempts = 0;
    }

    try {
      const isValid = await argon2.verify(storedHash.hash, word);
      if (!isValid) {
        storedHash.attempts++;
        storedHash.lastAttempt = new Date().toISOString();
      } else {
        storedHash.attempts = 0;
      }
      return isValid;
    } catch (error) {
      throw new UnauthorizedException('Invalid secret word');
    }
  }
} 