{"ast": null, "code": "import { LoginComponent } from './components/login/login.component';\nimport { MainScreenComponent } from './components/main-screen/main-screen.component';\nimport { MessageComposerComponent } from './components/message-composer/message-composer.component';\nimport { GroupManagerComponent } from './components/group-manager/group-manager.component';\nimport { authGuard, loginGuard } from './guards/auth.guard';\nexport const routes = [{\n  path: '',\n  redirectTo: '/main',\n  pathMatch: 'full'\n}, {\n  path: 'login',\n  component: LoginComponent,\n  canActivate: [loginGuard]\n}, {\n  path: 'main',\n  component: MainScreenComponent,\n  canActivate: [authGuard]\n}, {\n  path: 'compose',\n  component: MessageComposerComponent,\n  canActivate: [authGuard]\n}, {\n  path: 'groups/create',\n  component: GroupManagerComponent,\n  canActivate: [authGuard]\n}, {\n  path: 'groups/join',\n  component: GroupManagerComponent,\n  canActivate: [authGuard]\n}, {\n  path: '**',\n  redirectTo: '/login'\n}];", "map": {"version": 3, "names": ["LoginComponent", "MainScreenComponent", "MessageComposerComponent", "GroupManagerComponent", "<PERSON>th<PERSON><PERSON>", "loginGuard", "routes", "path", "redirectTo", "pathMatch", "component", "canActivate"], "sources": ["C:\\Users\\<USER>\\Projects\\QSC1\\frontend\\src\\app\\app.routes.ts"], "sourcesContent": ["import { Routes } from '@angular/router';\r\nimport { LoginComponent } from './components/login/login.component';\r\nimport { MainScreenComponent } from './components/main-screen/main-screen.component';\r\nimport { MessageComposerComponent } from './components/message-composer/message-composer.component';\r\nimport { GroupManagerComponent } from './components/group-manager/group-manager.component';\r\nimport { authGuard, loginGuard } from './guards/auth.guard';\r\n\r\nexport const routes: Routes = [\r\n  {\r\n    path: '',\r\n    redirectTo: '/main',\r\n    pathMatch: 'full'\r\n  },\r\n  {\r\n    path: 'login',\r\n    component: LoginComponent,\r\n    canActivate: [loginGuard]\r\n  },\r\n  {\r\n    path: 'main',\r\n    component: MainScreenComponent,\r\n    canActivate: [authGuard]\r\n  },\r\n  {\r\n    path: 'compose',\r\n    component: MessageComposerComponent,\r\n    canActivate: [authGuard]\r\n  },\r\n  {\r\n    path: 'groups/create',\r\n    component: GroupManagerComponent,\r\n    canActivate: [authGuard]\r\n  },\r\n  {\r\n    path: 'groups/join',\r\n    component: GroupManagerComponent,\r\n    canActivate: [authGuard]\r\n  },\r\n  {\r\n    path: '**',\r\n    redirectTo: '/login'\r\n  }\r\n];\r\n"], "mappings": "AACA,SAASA,cAAc,QAAQ,oCAAoC;AACnE,SAASC,mBAAmB,QAAQ,gDAAgD;AACpF,SAASC,wBAAwB,QAAQ,0DAA0D;AACnG,SAASC,qBAAqB,QAAQ,oDAAoD;AAC1F,SAASC,SAAS,EAAEC,UAAU,QAAQ,qBAAqB;AAE3D,OAAO,MAAMC,MAAM,GAAW,CAC5B;EACEC,IAAI,EAAE,EAAE;EACRC,UAAU,EAAE,OAAO;EACnBC,SAAS,EAAE;CACZ,EACD;EACEF,IAAI,EAAE,OAAO;EACbG,SAAS,EAAEV,cAAc;EACzBW,WAAW,EAAE,CAACN,UAAU;CACzB,EACD;EACEE,IAAI,EAAE,MAAM;EACZG,SAAS,EAAET,mBAAmB;EAC9BU,WAAW,EAAE,CAACP,SAAS;CACxB,EACD;EACEG,IAAI,EAAE,SAAS;EACfG,SAAS,EAAER,wBAAwB;EACnCS,WAAW,EAAE,CAACP,SAAS;CACxB,EACD;EACEG,IAAI,EAAE,eAAe;EACrBG,SAAS,EAAEP,qBAAqB;EAChCQ,WAAW,EAAE,CAACP,SAAS;CACxB,EACD;EACEG,IAAI,EAAE,aAAa;EACnBG,SAAS,EAAEP,qBAAqB;EAChCQ,WAAW,EAAE,CAACP,SAAS;CACxB,EACD;EACEG,IAAI,EAAE,IAAI;EACVC,UAAU,EAAE;CACb,CACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}