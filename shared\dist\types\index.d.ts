export interface IUser {
    id: string;
    username: string;
    email?: string;
    phone?: string;
    publicKey?: string;
    isAdmin: boolean;
    isActive: boolean;
    createdAt: Date;
    updatedAt: Date;
    lastLoginAt?: Date;
    deviceIds: string[];
    secretWordHash: ISecretWordHash;
    isVerified: boolean;
    accountStatus: 'active' | 'deactivated' | 'compromised';
    deactivatedBy?: string;
    deactivatedAt?: Date;
    deactivationReason?: string;
    reinvitedBy?: string;
    reinvitedAt?: Date;
    newInviteCode?: string;
    failedAttempts: number;
    lastAttemptAt?: Date;
    isCompromised: boolean;
}
export interface IMessage {
    id: string;
    senderId: string;
    recipientId?: string;
    roomId?: string;
    encryptedContent: string;
    symmetricKey?: string;
    iv?: string;
    authTag?: string;
    pqcSignature?: string;
    messageHash: string;
    isDeleted: boolean;
    deletedAt?: Date;
    expiresAt?: Date;
    isRead: boolean;
    readAt?: Date;
    isCompromised: boolean;
    compromiseAttempts: number;
    createdAt: Date;
    updatedAt: Date;
}
export interface IChatRoom {
    id: string;
    name?: string;
    type: 'direct' | 'group' | 'channel';
    ownerId?: string;
    isActive: boolean;
    encryptedConfig?: string;
    masterKey?: string;
    lastKeyRotation?: Date;
    securityThreshold?: number;
    onionAddress?: string;
    createdAt: Date;
    updatedAt: Date;
}
export interface IGroup {
    id: string;
    ownerId: string;
    onionAddress: string;
    encryptedConfig: string;
    masterKey: string;
    ownerDeviceHash: string;
    createdAt: Date;
    lastKeyRotation: Date;
    isActive: boolean;
    securityThreshold: number;
}
export interface IGroupMember {
    id: string;
    groupId: string;
    userId: string;
    role: 'admin' | 'member' | 'viewer';
    joinedAt: Date;
    publicKey: string;
    deviceHash: string;
    isActive: boolean;
}
export interface IInvite {
    id: string;
    email: string;
    inviteCode: string;
    invitedBy: string;
    expiresAt: Date;
    isUsed: boolean;
    usedAt?: Date;
    usedBy?: string;
    createdAt: Date;
}
export interface ISessionKey {
    id: string;
    user1Id: string;
    user2Id: string;
    encryptedKey: string;
    keyHash: string;
    expiresAt: Date;
    isActive: boolean;
    createdAt: Date;
    lastUsed: Date;
}
export interface ISecretWordHash {
    hash: string;
    salt: string;
    attempts: number;
    lastAttempt: string;
}
export interface IPQCKeyPair {
    publicKey: Buffer;
    privateKey: Buffer;
    algorithm: 'dilithium' | 'kyber';
}
export interface IPQCSignature {
    signature: Buffer;
    message: Buffer;
    algorithm: 'dilithium';
}
export interface IKyberKeyExchange {
    publicKey: Buffer;
    privateKey: Buffer;
    sharedSecret: Buffer;
    ciphertext: Buffer;
}
export interface IApiResponse<T = any> {
    success: boolean;
    data?: T;
    error?: string;
    message?: string;
    timestamp: string;
    requestId?: string;
}
export interface ILoginRequest {
    username: string;
    secretWord: string;
    deviceId?: string;
}
export interface IRegisterRequest {
    username: string;
    email?: string;
    secretWord: string;
    inviteCode: string;
}
export interface IAuthResponse {
    accessToken: string;
    refreshToken?: string;
    user: IUserProfile;
    expiresIn: number;
    tokenType: 'Bearer';
    dilithiumSignature?: string;
}
export interface IUserProfile {
    id: string;
    username: string;
    email?: string;
    phone?: string;
    publicKey?: string;
    isAdmin: boolean;
    isActive: boolean;
    isVerified: boolean;
    accountStatus: AccountStatus;
    createdAt: Date;
    updatedAt: Date;
    lastLoginAt?: Date;
    deviceIds: string[];
}
export interface IJwtPayload {
    sub: string;
    username: string;
    email?: string;
    type: 'access' | 'refresh' | 'invite';
    iat?: number;
    exp?: number;
    dilithiumSignature?: string;
}
export interface ITokenRefreshRequest {
    refreshToken: string;
}
export interface ITokenRefreshResponse {
    accessToken: string;
    expiresIn: number;
    tokenType: 'Bearer';
}
export interface IMessageResponse {
    id: string;
    content: string;
    senderId: string;
    senderUsername: string;
    timestamp: Date;
    isOwn: boolean;
    isRead: boolean;
    expiresAt?: Date;
}
export interface IWebSocketEvent {
    type: string;
    payload: any;
    timestamp: Date;
    userId?: string;
    roomId?: string;
}
export interface IMessageEvent extends IWebSocketEvent {
    type: 'message' | 'message_read' | 'message_deleted';
    payload: IMessageResponse;
}
export interface IUserEvent extends IWebSocketEvent {
    type: 'user_online' | 'user_offline' | 'user_typing';
    payload: {
        userId: string;
        username: string;
        status?: string;
    };
}
export interface IAuthError extends IQSCError {
    code: 'INVALID_CREDENTIALS' | 'ACCOUNT_LOCKED' | 'ACCOUNT_DEACTIVATED' | 'INVALID_SECRET_WORD' | 'TOKEN_EXPIRED' | 'TOKEN_INVALID' | 'DEVICE_NOT_AUTHORIZED';
    attempts?: number;
    lockoutDuration?: number;
    nextAttemptAllowed?: Date;
}
export interface IValidationError extends IQSCError {
    code: 'VALIDATION_FAILED';
    fieldErrors: Record<string, string[]>;
}
export interface ICreateUserDto {
    username: string;
    email?: string;
    secretWord: string;
    inviteCode: string;
}
export interface ILoginDto {
    username: string;
    secretWord: string;
    deviceId?: string;
}
export interface ISecretWordValidation {
    isValid: boolean;
    errors: string[];
    strength: 'weak' | 'medium' | 'strong';
    requirements: {
        length: boolean;
        characters: boolean;
        notCommon: boolean;
    };
}
export interface ICreateMessageDto {
    content: string;
    recipientId?: string;
    roomId?: string;
    expiresIn?: number;
}
export interface ICreateRoomDto {
    name?: string;
    type: 'direct' | 'group' | 'channel';
    memberIds?: string[];
}
export interface IQSCError {
    code: string;
    message: string;
    details?: any;
    timestamp: Date;
    userId?: string;
    requestId?: string;
}
export interface IDatabaseConfig {
    type: 'sqlite' | 'better-sqlite3';
    database: string;
    encryptionKey: string;
    synchronize: boolean;
    logging: boolean;
    migrationsRun: boolean;
}
export interface ISecurityConfig {
    jwtSecret: string;
    jwtExpiresIn: string;
    maxLoginAttempts: number;
    lockoutDuration: number;
    sessionTimeout: number;
    keyRotationInterval: number;
}
export type UserRole = 'admin' | 'user';
export type MessageType = 'text' | 'file' | 'image' | 'system';
export type RoomType = 'direct' | 'group' | 'channel';
export type AccountStatus = 'active' | 'deactivated' | 'compromised';
export type PQCAlgorithm = 'dilithium' | 'kyber' | 'falcon' | 'sphincs';
export type SecurityEventType = 'login_success' | 'login_failure' | 'account_locked' | 'key_rotation' | 'message_sent' | 'message_read' | 'user_invited' | 'user_deactivated' | 'suspicious_activity' | 'crypto_error';
//# sourceMappingURL=index.d.ts.map