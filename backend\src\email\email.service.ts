import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as nodemailer from 'nodemailer';
import { InviteEmailTemplate } from './templates/invite.template';

export interface EmailOptions {
  to: string;
  subject: string;
  html: string;
  text?: string;
}

@Injectable()
export class EmailService {
  private readonly logger = new Logger(EmailService.name);
  private transporter: nodemailer.Transporter;

  constructor(
    private configService: ConfigService,
  ) {
    this.initializeTransporter();
  }

  private initializeTransporter() {
    const emailService = this.configService.get<string>('EMAIL_SERVICE');
    
    if (emailService === 'smtp') {
      this.transporter = nodemailer.createTransport({
        host: this.configService.get<string>('SMTP_HOST'),
        port: this.configService.get<number>('SMTP_PORT'),
        secure: this.configService.get<boolean>('SMTP_SECURE'),
        auth: {
          user: this.configService.get<string>('SMTP_USER'),
          pass: this.configService.get<string>('SMTP_PASS'),
        },
        tls: {
          rejectUnauthorized: false, // For development only
        },
      });
    } else {
      this.logger.warn('No email service configured. Emails will be logged only.');
    }
  }

  async sendEmail(options: EmailOptions): Promise<boolean> {
    try {
      if (!this.transporter) {
        this.logger.log('Email service not configured. Would send email:');
        this.logger.log(`To: ${options.to}`);
        this.logger.log(`Subject: ${options.subject}`);
        this.logger.log(`Content: ${options.text || 'HTML content'}`);
        return true;
      }

      const mailOptions = {
        from: this.configService.get<string>('EMAIL_FROM'),
        to: options.to,
        subject: options.subject,
        html: options.html,
        text: options.text,
      };

      const result = await this.transporter.sendMail(mailOptions);
      this.logger.log(`Email sent successfully to ${options.to}. Message ID: ${result.messageId}`);
      return true;
    } catch (error) {
      this.logger.error(`Failed to send email to ${options.to}:`, error);
      return false;
    }
  }

  async sendInviteEmail(
    recipientEmail: string,
    inviteToken: string,
    inviterName: string,
  ): Promise<boolean> {
    const inviteUrl = `${this.configService.get<string>('FRONTEND_URL')}/invite?token=${inviteToken}`;

    const inviteTemplate = new InviteEmailTemplate();
    const { html, text } = inviteTemplate.generateInviteEmail({
      recipientEmail,
      inviteUrl,
      inviterName,
      expiresIn: '24 hours',
    });

    return this.sendEmail({
      to: recipientEmail,
      subject: 'You\'re invited to join QSC - Quantum Secure Communication',
      html,
      text,
    });
  }

  async testConnection(): Promise<boolean> {
    try {
      if (!this.transporter) {
        this.logger.warn('No email transporter configured');
        return false;
      }

      await this.transporter.verify();
      this.logger.log('Email service connection verified successfully');
      return true;
    } catch (error) {
      this.logger.error('Email service connection failed:', error);
      return false;
    }
  }
}
