{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterLink } from '@angular/router';\nimport * as i0 from \"@angular/core\";\nexport class HomeComponent {\n  static {\n    this.ɵfac = function HomeComponent_Factory(t) {\n      return new (t || HomeComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: HomeComponent,\n      selectors: [[\"app-home\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 8,\n      vars: 0,\n      consts: [[1, \"home-container\"], [1, \"actions\"], [\"routerLink\", \"/key-management\", 1, \"button\"]],\n      template: function HomeComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"h2\");\n          i0.ɵɵtext(2, \"Welcome to Quantum Shield\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"p\");\n          i0.ɵɵtext(4, \"A secure messaging platform with post-quantum cryptography\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"div\", 1)(6, \"a\", 2);\n          i0.ɵɵtext(7, \"Manage Keys\");\n          i0.ɵɵelementEnd()()();\n        }\n      },\n      dependencies: [CommonModule, RouterLink],\n      styles: [\".home-container[_ngcontent-%COMP%] {\\n      text-align: center;\\n      padding: 2rem;\\n    }\\n    h2[_ngcontent-%COMP%] {\\n      color: #2c3e50;\\n      margin-bottom: 1rem;\\n    }\\n    .actions[_ngcontent-%COMP%] {\\n      margin-top: 2rem;\\n    }\\n    .button[_ngcontent-%COMP%] {\\n      display: inline-block;\\n      padding: 0.8rem 1.5rem;\\n      background-color: #3498db;\\n      color: white;\\n      text-decoration: none;\\n      border-radius: 4px;\\n      transition: background-color 0.3s;\\n    }\\n    .button[_ngcontent-%COMP%]:hover {\\n      background-color: #2980b9;\\n    }\\n  \\n/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImhvbWUuY29tcG9uZW50LnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiI7SUFDSTtNQUNFLGtCQUFrQjtNQUNsQixhQUFhO0lBQ2Y7SUFDQTtNQUNFLGNBQWM7TUFDZCxtQkFBbUI7SUFDckI7SUFDQTtNQUNFLGdCQUFnQjtJQUNsQjtJQUNBO01BQ0UscUJBQXFCO01BQ3JCLHNCQUFzQjtNQUN0Qix5QkFBeUI7TUFDekIsWUFBWTtNQUNaLHFCQUFxQjtNQUNyQixrQkFBa0I7TUFDbEIsaUNBQWlDO0lBQ25DO0lBQ0E7TUFDRSx5QkFBeUI7SUFDM0IiLCJmaWxlIjoiaG9tZS5jb21wb25lbnQudHMiLCJzb3VyY2VzQ29udGVudCI6WyJcbiAgICAuaG9tZS1jb250YWluZXIge1xuICAgICAgdGV4dC1hbGlnbjogY2VudGVyO1xuICAgICAgcGFkZGluZzogMnJlbTtcbiAgICB9XG4gICAgaDIge1xuICAgICAgY29sb3I6ICMyYzNlNTA7XG4gICAgICBtYXJnaW4tYm90dG9tOiAxcmVtO1xuICAgIH1cbiAgICAuYWN0aW9ucyB7XG4gICAgICBtYXJnaW4tdG9wOiAycmVtO1xuICAgIH1cbiAgICAuYnV0dG9uIHtcbiAgICAgIGRpc3BsYXk6IGlubGluZS1ibG9jaztcbiAgICAgIHBhZGRpbmc6IDAuOHJlbSAxLjVyZW07XG4gICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjMzQ5OGRiO1xuICAgICAgY29sb3I6IHdoaXRlO1xuICAgICAgdGV4dC1kZWNvcmF0aW9uOiBub25lO1xuICAgICAgYm9yZGVyLXJhZGl1czogNHB4O1xuICAgICAgdHJhbnNpdGlvbjogYmFja2dyb3VuZC1jb2xvciAwLjNzO1xuICAgIH1cbiAgICAuYnV0dG9uOmhvdmVyIHtcbiAgICAgIGJhY2tncm91bmQtY29sb3I6ICMyOTgwYjk7XG4gICAgfVxuICAiXX0= */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvY29tcG9uZW50cy9ob21lL2hvbWUuY29tcG9uZW50LnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiI7SUFDSTtNQUNFLGtCQUFrQjtNQUNsQixhQUFhO0lBQ2Y7SUFDQTtNQUNFLGNBQWM7TUFDZCxtQkFBbUI7SUFDckI7SUFDQTtNQUNFLGdCQUFnQjtJQUNsQjtJQUNBO01BQ0UscUJBQXFCO01BQ3JCLHNCQUFzQjtNQUN0Qix5QkFBeUI7TUFDekIsWUFBWTtNQUNaLHFCQUFxQjtNQUNyQixrQkFBa0I7TUFDbEIsaUNBQWlDO0lBQ25DO0lBQ0E7TUFDRSx5QkFBeUI7SUFDM0I7O0FBRUosb29DQUFvb0MiLCJzb3VyY2VzQ29udGVudCI6WyJcbiAgICAuaG9tZS1jb250YWluZXIge1xuICAgICAgdGV4dC1hbGlnbjogY2VudGVyO1xuICAgICAgcGFkZGluZzogMnJlbTtcbiAgICB9XG4gICAgaDIge1xuICAgICAgY29sb3I6ICMyYzNlNTA7XG4gICAgICBtYXJnaW4tYm90dG9tOiAxcmVtO1xuICAgIH1cbiAgICAuYWN0aW9ucyB7XG4gICAgICBtYXJnaW4tdG9wOiAycmVtO1xuICAgIH1cbiAgICAuYnV0dG9uIHtcbiAgICAgIGRpc3BsYXk6IGlubGluZS1ibG9jaztcbiAgICAgIHBhZGRpbmc6IDAuOHJlbSAxLjVyZW07XG4gICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjMzQ5OGRiO1xuICAgICAgY29sb3I6IHdoaXRlO1xuICAgICAgdGV4dC1kZWNvcmF0aW9uOiBub25lO1xuICAgICAgYm9yZGVyLXJhZGl1czogNHB4O1xuICAgICAgdHJhbnNpdGlvbjogYmFja2dyb3VuZC1jb2xvciAwLjNzO1xuICAgIH1cbiAgICAuYnV0dG9uOmhvdmVyIHtcbiAgICAgIGJhY2tncm91bmQtY29sb3I6ICMyOTgwYjk7XG4gICAgfVxuICAiXSwic291cmNlUm9vdCI6IiJ9 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "RouterLink", "HomeComponent", "selectors", "standalone", "features", "i0", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "HomeComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "styles"], "sources": ["C:\\Users\\<USER>\\Projects\\QSC1\\frontend\\src\\app\\components\\home\\home.component.ts"], "sourcesContent": ["import { Component } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { RouterLink } from '@angular/router';\r\n\r\n@Component({\r\n  selector: 'app-home',\r\n  standalone: true,\r\n  imports: [CommonModule, RouterLink],\r\n  template: `\r\n    <div class=\"home-container\">\r\n      <h2>Welcome to Quantum Shield</h2>\r\n      <p>A secure messaging platform with post-quantum cryptography</p>\r\n\r\n      <div class=\"actions\">\r\n        <a routerLink=\"/key-management\" class=\"button\">Manage Keys</a>\r\n      </div>\r\n    </div>\r\n  `,\r\n  styles: [`\r\n    .home-container {\r\n      text-align: center;\r\n      padding: 2rem;\r\n    }\r\n    h2 {\r\n      color: #2c3e50;\r\n      margin-bottom: 1rem;\r\n    }\r\n    .actions {\r\n      margin-top: 2rem;\r\n    }\r\n    .button {\r\n      display: inline-block;\r\n      padding: 0.8rem 1.5rem;\r\n      background-color: #3498db;\r\n      color: white;\r\n      text-decoration: none;\r\n      border-radius: 4px;\r\n      transition: background-color 0.3s;\r\n    }\r\n    .button:hover {\r\n      background-color: #2980b9;\r\n    }\r\n  `]\r\n})\r\nexport class HomeComponent {}\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,UAAU,QAAQ,iBAAiB;;AA0C5C,OAAM,MAAOC,aAAa;;;uBAAbA,aAAa;IAAA;EAAA;;;YAAbA,aAAa;MAAAC,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAAC,EAAA,CAAAC,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,uBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAlCpBP,EADF,CAAAS,cAAA,aAA4B,SACtB;UAAAT,EAAA,CAAAU,MAAA,gCAAyB;UAAAV,EAAA,CAAAW,YAAA,EAAK;UAClCX,EAAA,CAAAS,cAAA,QAAG;UAAAT,EAAA,CAAAU,MAAA,iEAA0D;UAAAV,EAAA,CAAAW,YAAA,EAAI;UAG/DX,EADF,CAAAS,cAAA,aAAqB,WAC4B;UAAAT,EAAA,CAAAU,MAAA,kBAAW;UAE9DV,EAF8D,CAAAW,YAAA,EAAI,EAC1D,EACF;;;qBATEjB,YAAY,EAAEC,UAAU;MAAAiB,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}