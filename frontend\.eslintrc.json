{"root": true, "ignorePatterns": ["projects/**/*"], "overrides": [{"files": ["*.ts"], "extends": ["eslint:recommended", "plugin:@typescript-eslint/recommended", "plugin:@angular-eslint/recommended", "plugin:@angular-eslint/template/process-inline-templates", "plugin:prettier/recommended"], "rules": {"@angular-eslint/directive-selector": ["error", {"type": "attribute", "prefix": "qs", "style": "camelCase"}], "@angular-eslint/component-selector": ["error", {"type": "element", "prefix": "qs", "style": "kebab-case"}], "@typescript-eslint/no-explicit-any": "error", "@typescript-eslint/explicit-function-return-type": "error", "@typescript-eslint/strict-boolean-expressions": "error"}}]}