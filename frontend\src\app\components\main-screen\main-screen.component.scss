.main-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  position: relative;
  overflow: hidden;
}

.logo-area {
  text-align: center;
  z-index: 1;
}

.qsc-logo {
  display: inline-block;
  margin-bottom: 2rem;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    transform: scale(1.05);
  }

  &:active {
    transform: scale(0.95);
  }

  .circle {
    width: 120px;
    height: 120px;
    border: 6px solid #667eea;
    border-radius: 50%;
    position: relative;
    overflow: hidden;
    margin: 0 auto;

    .wind-effect {
      position: absolute;
      top: 0;
      right: -30px;
      width: 60px;
      height: 100%;
      background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.8) 50%, transparent 100%);
      transform: skew(-20deg);
      animation: windBlow 4s ease-in-out infinite;
    }
  }
}

@keyframes windBlow {
  0%, 100% {
    opacity: 0;
    transform: translateX(-60px) skew(-20deg);
  }
  50% {
    opacity: 1;
    transform: translateX(30px) skew(-20deg);
  }
}

.logo-text {
  h1 {
    font-size: 3rem;
    font-weight: 300;
    color: #333;
    margin: 0 0 0.5rem 0;
    letter-spacing: 0.2em;
  }

  p {
    color: #666;
    font-size: 1rem;
    margin: 0;
    opacity: 0.8;
  }
}

.context-menu {
  position: fixed;
  background: white;
  border-radius: 8px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  z-index: 1000;
  min-width: 150px;
  overflow: hidden;

  .menu-item {
    padding: 0.75rem 1rem;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: background 0.2s ease;

    &:hover {
      background: #f5f5f5;
    }

    .shortcut {
      font-size: 0.8rem;
      color: #999;
    }
  }

  .menu-divider {
    height: 1px;
    background: #eee;
    margin: 0.25rem 0;
  }
}

.group-panel {
  position: fixed;
  left: -400px;
  top: 0;
  width: 400px;
  height: 100vh;
  background: white;
  box-shadow: 2px 0 20px rgba(0, 0, 0, 0.1);
  z-index: 999;
  transition: left 0.3s ease;
  overflow-y: auto;

  &.visible {
    left: 0;
  }

  .panel-header {
    padding: 1.5rem;
    border-bottom: 1px solid #eee;
    display: flex;
    justify-content: space-between;
    align-items: center;

    h3 {
      margin: 0;
      color: #333;
    }

    .close-btn {
      background: none;
      border: none;
      font-size: 1.5rem;
      cursor: pointer;
      color: #999;
      padding: 0;
      width: 30px;
      height: 30px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 50%;
      transition: all 0.2s ease;

      &:hover {
        background: #f5f5f5;
        color: #333;
      }
    }
  }

  .panel-content {
    padding: 1.5rem;
  }

  .group-actions {
    display: flex;
    gap: 0.75rem;
    margin-bottom: 2rem;

    .action-btn {
      flex: 1;
      padding: 0.75rem;
      border: none;
      border-radius: 8px;
      cursor: pointer;
      font-weight: 500;
      transition: all 0.2s ease;

      &.primary {
        background: #667eea;
        color: white;

        &:hover {
          background: #5a6fd8;
        }
      }

      &.secondary {
        background: #f5f5f5;
        color: #333;

        &:hover {
          background: #e8e8e8;
        }
      }
    }
  }

  .groups-list {
    h4 {
      margin: 0 0 1rem 0;
      color: #666;
      font-size: 0.9rem;
      text-transform: uppercase;
      letter-spacing: 0.05em;
    }

    .group-item {
      padding: 1rem;
      border: 1px solid #eee;
      border-radius: 8px;
      margin-bottom: 0.75rem;
      cursor: pointer;
      transition: all 0.2s ease;
      display: flex;
      justify-content: space-between;
      align-items: center;

      &:hover {
        border-color: #667eea;
        background: #f8f9ff;
      }

      .group-info {
        .group-name {
          display: block;
          font-weight: 500;
          color: #333;
          margin-bottom: 0.25rem;
        }

        .group-meta {
          font-size: 0.8rem;
          color: #999;
        }
      }

      .role-badge {
        padding: 0.25rem 0.5rem;
        border-radius: 4px;
        font-size: 0.7rem;
        text-transform: uppercase;
        font-weight: 600;

        &.admin {
          background: #fee;
          color: #c33;
        }

        &.write {
          background: #efe;
          color: #3c3;
        }

        &.read {
          background: #eef;
          color: #33c;
        }
      }
    }
  }

  .empty-state {
    text-align: center;
    color: #999;
    padding: 2rem 0;

    p {
      margin: 0.5rem 0;
    }

    .hint {
      font-size: 0.9rem;
    }
  }
}

.overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.3);
  z-index: 998;
}

.user-info {
  position: fixed;
  top: 1rem;
  right: 1rem;
  color: #666;
  font-size: 0.9rem;
  z-index: 1;
}

.keyboard-hints {
  position: fixed;
  bottom: 1rem;
  left: 50%;
  transform: translateX(-50%);
  color: #999;
  font-size: 0.8rem;
  z-index: 1;
}

@media (max-width: 768px) {
  .group-panel {
    width: 100vw;
    left: -100vw;
  }

  .qsc-logo .circle {
    width: 100px;
    height: 100px;
  }

  .logo-text h1 {
    font-size: 2.5rem;
  }

  .keyboard-hints {
    display: none;
  }
}
