import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class AddPhoneToUser1733486400000 implements MigrationInterface {
  name = 'AddPhoneToUser1733486400000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.addColumn(
      'users',
      new TableColumn({
        name: 'phone',
        type: 'varchar',
        isNullable: true,
      })
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropColumn('users', 'phone');
  }
}
