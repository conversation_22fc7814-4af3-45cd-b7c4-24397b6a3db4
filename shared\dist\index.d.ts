export * from './types';
export * from './utils';
export declare const SHARED_VERSION = "1.0.0";
export declare const QSC_CONSTANTS: {
    readonly APP_NAME: "Quantum-Secure Chat";
    readonly APP_VERSION: "1.0.0";
    readonly API_VERSION: "v1";
    readonly MAX_LOGIN_ATTEMPTS: 3;
    readonly LOCKOUT_DURATION: number;
    readonly SESSION_TIMEOUT: number;
    readonly KEY_ROTATION_INTERVAL: number;
    readonly MESSAGE_EXPIRY_DEFAULT: number;
    readonly MESSAGE_EXPIRY_MAX: number;
    readonly MAX_FILE_SIZE: number;
    readonly ALLOWED_FILE_TYPES: readonly ["image/jpeg", "image/png", "image/gif", "image/webp", "application/pdf", "text/plain", "application/json"];
    readonly RATE_LIMIT_WINDOW: number;
    readonly RATE_LIMIT_MAX_REQUESTS: 100;
    readonly WS_EVENTS: {
        readonly MESSAGE: "message";
        readonly MESSAGE_READ: "message_read";
        readonly MESSAGE_DELETED: "message_deleted";
        readonly USER_ONLINE: "user_online";
        readonly USER_OFFLINE: "user_offline";
        readonly USER_TYPING: "user_typing";
        readonly ROOM_JOINED: "room_joined";
        readonly ROOM_LEFT: "room_left";
        readonly KEY_ROTATION: "key_rotation";
        readonly SECURITY_ALERT: "security_alert";
    };
    readonly ERROR_CODES: {
        readonly INVALID_CREDENTIALS: "INVALID_CREDENTIALS";
        readonly ACCOUNT_LOCKED: "ACCOUNT_LOCKED";
        readonly ACCOUNT_COMPROMISED: "ACCOUNT_COMPROMISED";
        readonly TOKEN_EXPIRED: "TOKEN_EXPIRED";
        readonly TOKEN_INVALID: "TOKEN_INVALID";
        readonly INSUFFICIENT_PERMISSIONS: "INSUFFICIENT_PERMISSIONS";
        readonly ACCESS_DENIED: "ACCESS_DENIED";
        readonly VALIDATION_ERROR: "VALIDATION_ERROR";
        readonly INVALID_INPUT: "INVALID_INPUT";
        readonly MISSING_REQUIRED_FIELD: "MISSING_REQUIRED_FIELD";
        readonly CRYPTO_ERROR: "CRYPTO_ERROR";
        readonly KEY_GENERATION_FAILED: "KEY_GENERATION_FAILED";
        readonly ENCRYPTION_FAILED: "ENCRYPTION_FAILED";
        readonly DECRYPTION_FAILED: "DECRYPTION_FAILED";
        readonly SIGNATURE_VERIFICATION_FAILED: "SIGNATURE_VERIFICATION_FAILED";
        readonly DATABASE_ERROR: "DATABASE_ERROR";
        readonly RECORD_NOT_FOUND: "RECORD_NOT_FOUND";
        readonly DUPLICATE_RECORD: "DUPLICATE_RECORD";
        readonly NETWORK_ERROR: "NETWORK_ERROR";
        readonly CONNECTION_TIMEOUT: "CONNECTION_TIMEOUT";
        readonly SERVICE_UNAVAILABLE: "SERVICE_UNAVAILABLE";
        readonly FILE_TOO_LARGE: "FILE_TOO_LARGE";
        readonly INVALID_FILE_TYPE: "INVALID_FILE_TYPE";
        readonly FILE_UPLOAD_FAILED: "FILE_UPLOAD_FAILED";
        readonly RATE_LIMIT_EXCEEDED: "RATE_LIMIT_EXCEEDED";
        readonly INTERNAL_SERVER_ERROR: "INTERNAL_SERVER_ERROR";
        readonly BAD_REQUEST: "BAD_REQUEST";
        readonly NOT_FOUND: "NOT_FOUND";
        readonly CONFLICT: "CONFLICT";
    };
    readonly HTTP_STATUS: {
        readonly OK: 200;
        readonly CREATED: 201;
        readonly NO_CONTENT: 204;
        readonly BAD_REQUEST: 400;
        readonly UNAUTHORIZED: 401;
        readonly FORBIDDEN: 403;
        readonly NOT_FOUND: 404;
        readonly CONFLICT: 409;
        readonly UNPROCESSABLE_ENTITY: 422;
        readonly TOO_MANY_REQUESTS: 429;
        readonly INTERNAL_SERVER_ERROR: 500;
        readonly SERVICE_UNAVAILABLE: 503;
    };
};
//# sourceMappingURL=index.d.ts.map