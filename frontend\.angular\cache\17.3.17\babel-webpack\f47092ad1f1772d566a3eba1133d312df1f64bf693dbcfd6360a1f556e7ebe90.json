{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterOutlet } from '@angular/router';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"./services/wasm.service\";\nimport * as i2 from \"./services/error-handling.service\";\nexport class AppComponent {\n  constructor(wasmService, errorHandling) {\n    this.wasmService = wasmService;\n    this.errorHandling = errorHandling;\n  }\n  static {\n    this.ɵfac = function AppComponent_Factory(t) {\n      return new (t || AppComponent)(i0.ɵɵdirectiveInject(i1.WasmService), i0.ɵɵdirectiveInject(i2.ErrorHandlingService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AppComponent,\n      selectors: [[\"app-root\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 5,\n      vars: 0,\n      consts: [[1, \"container\"]],\n      template: function AppComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"main\", 0)(1, \"header\")(2, \"h1\");\n          i0.ɵɵtext(3, \"Quantum Shield\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelement(4, \"router-outlet\");\n          i0.ɵɵelementEnd();\n        }\n      },\n      dependencies: [CommonModule, RouterOutlet],\n      styles: [\".container[_ngcontent-%COMP%] {\\n      max-width: 1200px;\\n      margin: 0 auto;\\n      padding: 1rem;\\n    }\\n    header[_ngcontent-%COMP%] {\\n      margin-bottom: 2rem;\\n      text-align: center;\\n    }\\n    h1[_ngcontent-%COMP%] {\\n      color: #2c3e50;\\n      font-size: 2.5rem;\\n    }\\n  \\n/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImFwcC50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiO0lBQ0k7TUFDRSxpQkFBaUI7TUFDakIsY0FBYztNQUNkLGFBQWE7SUFDZjtJQUNBO01BQ0UsbUJBQW1CO01BQ25CLGtCQUFrQjtJQUNwQjtJQUNBO01BQ0UsY0FBYztNQUNkLGlCQUFpQjtJQUNuQiIsImZpbGUiOiJhcHAudHMiLCJzb3VyY2VzQ29udGVudCI6WyJcbiAgICAuY29udGFpbmVyIHtcbiAgICAgIG1heC13aWR0aDogMTIwMHB4O1xuICAgICAgbWFyZ2luOiAwIGF1dG87XG4gICAgICBwYWRkaW5nOiAxcmVtO1xuICAgIH1cbiAgICBoZWFkZXIge1xuICAgICAgbWFyZ2luLWJvdHRvbTogMnJlbTtcbiAgICAgIHRleHQtYWxpZ246IGNlbnRlcjtcbiAgICB9XG4gICAgaDEge1xuICAgICAgY29sb3I6ICMyYzNlNTA7XG4gICAgICBmb250LXNpemU6IDIuNXJlbTtcbiAgICB9XG4gICJdfQ== */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvYXBwLnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiI7SUFDSTtNQUNFLGlCQUFpQjtNQUNqQixjQUFjO01BQ2QsYUFBYTtJQUNmO0lBQ0E7TUFDRSxtQkFBbUI7TUFDbkIsa0JBQWtCO0lBQ3BCO0lBQ0E7TUFDRSxjQUFjO01BQ2QsaUJBQWlCO0lBQ25COztBQUVKLGdwQkFBZ3BCIiwic291cmNlc0NvbnRlbnQiOlsiXG4gICAgLmNvbnRhaW5lciB7XG4gICAgICBtYXgtd2lkdGg6IDEyMDBweDtcbiAgICAgIG1hcmdpbjogMCBhdXRvO1xuICAgICAgcGFkZGluZzogMXJlbTtcbiAgICB9XG4gICAgaGVhZGVyIHtcbiAgICAgIG1hcmdpbi1ib3R0b206IDJyZW07XG4gICAgICB0ZXh0LWFsaWduOiBjZW50ZXI7XG4gICAgfVxuICAgIGgxIHtcbiAgICAgIGNvbG9yOiAjMmMzZTUwO1xuICAgICAgZm9udC1zaXplOiAyLjVyZW07XG4gICAgfVxuICAiXSwic291cmNlUm9vdCI6IiJ9 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "RouterOutlet", "AppComponent", "constructor", "wasmService", "errorHandling", "i0", "ɵɵdirectiveInject", "i1", "WasmService", "i2", "ErrorHandlingService", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "AppComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement", "styles"], "sources": ["C:\\Users\\<USER>\\Projects\\QSC1\\frontend\\src\\app\\app.ts"], "sourcesContent": ["import { Component } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { RouterOutlet } from '@angular/router';\r\nimport { WasmService } from './services/wasm.service';\r\nimport { ErrorHandlingService } from './services/error-handling.service';\r\n\r\n@Component({\r\n  selector: 'app-root',\r\n  standalone: true,\r\n  imports: [CommonModule, RouterOutlet],\r\n  template: `\r\n    <main class=\"container\">\r\n      <header>\r\n        <h1>Quantum Shield</h1>\r\n      </header>\r\n      <router-outlet></router-outlet>\r\n    </main>\r\n  `,\r\n  styles: [`\r\n    .container {\r\n      max-width: 1200px;\r\n      margin: 0 auto;\r\n      padding: 1rem;\r\n    }\r\n    header {\r\n      margin-bottom: 2rem;\r\n      text-align: center;\r\n    }\r\n    h1 {\r\n      color: #2c3e50;\r\n      font-size: 2.5rem;\r\n    }\r\n  `]\r\n})\r\nexport class AppComponent {\r\n  constructor(\r\n    private wasmService: WasmService,\r\n    private errorHandling: ErrorHandlingService\r\n  ) {}\r\n}\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,YAAY,QAAQ,iBAAiB;;;;AAgC9C,OAAM,MAAOC,YAAY;EACvBC,YACUC,WAAwB,EACxBC,aAAmC;IADnC,KAAAD,WAAW,GAAXA,WAAW;IACX,KAAAC,aAAa,GAAbA,aAAa;EACpB;;;uBAJQH,YAAY,EAAAI,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAH,EAAA,CAAAC,iBAAA,CAAAG,EAAA,CAAAC,oBAAA;IAAA;EAAA;;;YAAZT,YAAY;MAAAU,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAAR,EAAA,CAAAS,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,sBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UArBjBf,EAFJ,CAAAiB,cAAA,cAAwB,aACd,SACF;UAAAjB,EAAA,CAAAkB,MAAA,qBAAc;UACpBlB,EADoB,CAAAmB,YAAA,EAAK,EAChB;UACTnB,EAAA,CAAAoB,SAAA,oBAA+B;UACjCpB,EAAA,CAAAmB,YAAA,EAAO;;;qBAPCzB,YAAY,EAAEC,YAAY;MAAA0B,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}