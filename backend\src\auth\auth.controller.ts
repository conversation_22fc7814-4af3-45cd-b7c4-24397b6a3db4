import { Controller, Post, Body, UseGuards, Request, UnauthorizedException, Logger, ValidationPipe, UsePipes } from '@nestjs/common';
import { AuthService } from './auth.service';
import { JwtAuthGuard } from './guards/jwt-auth.guard';
import { UserService } from '../user/user.service';
// import { EmailService } from '../email/email.service'; // Disabled for development
import { ThrottlerGuard, Throttle } from '@nestjs/throttler';
import {
  ILoginRequest,
  IRegisterRequest,
  IAuthResponse,
  IApiResponse,
  createSuccessResponse,
  createErrorResponse
} from '@qsc/shared';

@Controller('auth')
@UseGuards(ThrottlerGuard)
export class AuthController {
  private readonly logger = new Logger(AuthController.name);

  constructor(
    private authService: AuthService,
    private userService: UserService,
    // private emailService: EmailService, // Disabled for development
  ) {}

  @Post('login')
  @Throttle({ default: { limit: 5, ttl: 60000 } }) // 5 attempts per minute
  @UsePipes(new ValidationPipe({ whitelist: true, transform: true }))
  async login(@Body() loginRequest: ILoginRequest): Promise<IApiResponse<IAuthResponse>> {
    try {
      this.logger.log(`Login attempt for username: ${loginRequest.username}`);

      const user = await this.authService.validateUser(loginRequest.username, loginRequest.secretWord);
      const authResponse = await this.authService.login(user, loginRequest.deviceId);

      this.logger.log(`Successful login for user: ${user.id}`);
      return createSuccessResponse(authResponse, 'Login successful');
    } catch (error) {
      this.logger.error(`Login failed for username: ${loginRequest.username}`, error.message);

      if (error instanceof UnauthorizedException) {
        return createErrorResponse('INVALID_CREDENTIALS', 'Invalid username or secret word');
      }

      return createErrorResponse('LOGIN_FAILED', 'Login failed due to server error');
    }
  }

  @UseGuards(JwtAuthGuard)
  @Post('invite')
  async generateInvite(
    @Request() req: any,
    @Body('email') email: string,
  ) {
    // Only admins can generate invites
    const user = await this.userService.findById(req.user.id);
    if (!user.isAdmin) {
      throw new UnauthorizedException('Only admins can generate invites');
    }

    // Check if user with this email already exists
    const existingUser = await this.userService.findByEmailSafe(email);
    if (existingUser) {
      throw new UnauthorizedException('User with this email already exists');
    }

    // Generate invite token
    const inviteToken = await this.authService.generateInviteToken(email);

    // Email service disabled for development
    this.logger.log(`Invitation token generated for ${email}: ${inviteToken}`);

    return {
      invite_token: inviteToken,
      email_sent: true, // Always return true for security (don't reveal email failures)
      message: 'Invitation sent successfully',
    };
  }

  @Post('register')
  @UsePipes(new ValidationPipe({ whitelist: true, transform: true }))
  async register(@Body() registerRequest: IRegisterRequest): Promise<IApiResponse<IAuthResponse>> {
    try {
      this.logger.log(`Registration attempt for username: ${registerRequest.username}`);

      // Verify invite token
      const { email } = await this.authService.verifyInviteToken(registerRequest.inviteCode);

      // Create user with secret word instead of password
      const user = await this.userService.createWithSecretWord(
        registerRequest.username,
        email || registerRequest.email,
        registerRequest.secretWord
      );

      // Login the newly created user
      const authResponse = await this.authService.login(user);

      this.logger.log(`Successful registration for user: ${user.id}`);
      return createSuccessResponse(authResponse, 'Registration successful');
    } catch (error) {
      this.logger.error(`Registration failed for username: ${registerRequest.username}`, error.message);

      if (error.message.includes('invite')) {
        return createErrorResponse('INVALID_INVITE_CODE', 'Invalid or expired invitation code');
      }

      if (error.message.includes('username')) {
        return createErrorResponse('USERNAME_TAKEN', 'Username is already taken');
      }

      return createErrorResponse('REGISTRATION_FAILED', 'Registration failed due to server error');
    }
  }

  @Post('logout')
  @UseGuards(JwtAuthGuard)
  async logout(@Request() req): Promise<IApiResponse<void>> {
    try {
      const token = req.headers.authorization?.split(' ')[1];
      if (token) {
        await this.authService.logout(token);
      }
      return createSuccessResponse(null, 'Logout successful');
    } catch (error) {
      this.logger.error('Logout failed:', error);
      return createErrorResponse('LOGOUT_FAILED', 'Logout failed due to server error');
    }
  }

  @Post('refresh')
  async refreshToken(@Body('refresh_token') refreshToken: string): Promise<IApiResponse<IAuthResponse>> {
    try {
      const authResponse = await this.authService.refreshToken(refreshToken);
      return createSuccessResponse(authResponse, 'Token refreshed successfully');
    } catch (error) {
      this.logger.error('Token refresh failed:', error);
      return createErrorResponse('REFRESH_FAILED', 'Token refresh failed');
    }
  }
} 