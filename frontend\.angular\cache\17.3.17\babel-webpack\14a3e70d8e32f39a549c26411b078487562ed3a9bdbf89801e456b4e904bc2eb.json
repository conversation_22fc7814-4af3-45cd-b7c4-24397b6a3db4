{"ast": null, "code": "import { inject } from '@angular/core';\nimport { catchError, throwError } from 'rxjs';\nimport { SecureTokenService } from '../services/secure-token.service';\nimport { Router } from '@angular/router';\nexport const authInterceptor = (req, next) => {\n  const secureTokenService = inject(SecureTokenService);\n  const router = inject(Router);\n  // Skip auth for login and public endpoints\n  const skipAuth = req.url.includes('/auth/login') || req.url.includes('/auth/register') || req.url.includes('/auth/invite') || req.url.includes('/public/') || req.url.includes('/health');\n  if (skipAuth) {\n    return next(req);\n  }\n  const token = secureTokenService.getToken();\n  if (!token) {\n    // No token available, redirect to login\n    router.navigate(['/login']);\n    return throwError(() => new Error('No authentication token available'));\n  }\n  if (!secureTokenService.isTokenValid()) {\n    // Token is invalid, try to refresh or logout\n    const refreshToken = secureTokenService.getRefreshToken();\n    if (refreshToken) {\n      // TODO: Implement token refresh logic here\n      // For now, just clear tokens and redirect\n      secureTokenService.clearTokens();\n      router.navigate(['/login']);\n      return throwError(() => new Error('Token expired'));\n    } else {\n      secureTokenService.clearTokens();\n      router.navigate(['/login']);\n      return throwError(() => new Error('Token expired'));\n    }\n  }\n  // Clone the request and add the authorization header\n  const authReq = req.clone({\n    setHeaders: {\n      Authorization: `Bearer ${token}`,\n      'Content-Type': 'application/json',\n      // Add CSRF protection\n      'X-Requested-With': 'XMLHttpRequest'\n    }\n  });\n  return next(authReq).pipe(catchError(error => {\n    // Handle 401 Unauthorized responses\n    if (error.status === 401) {\n      secureTokenService.clearTokens();\n      router.navigate(['/login']);\n    }\n    // Handle 403 Forbidden responses (CSRF issues)\n    if (error.status === 403) {\n      console.error('CSRF token validation failed');\n    }\n    return throwError(() => error);\n  }));\n};", "map": {"version": 3, "names": ["inject", "catchError", "throwError", "SecureTokenService", "Router", "authInterceptor", "req", "next", "secureTokenService", "router", "<PERSON><PERSON><PERSON>", "url", "includes", "token", "getToken", "navigate", "Error", "isTokenValid", "refreshToken", "getRefreshToken", "clearTokens", "authReq", "clone", "setHeaders", "Authorization", "pipe", "error", "status", "console"], "sources": ["C:\\Users\\<USER>\\Projects\\QSC1\\frontend\\src\\app\\interceptors\\auth.interceptor.ts"], "sourcesContent": ["import { HttpInterceptorFn, HttpRequest, HttpHandlerFn } from '@angular/common/http';\nimport { inject } from '@angular/core';\nimport { catchError, switchMap, throwError } from 'rxjs';\nimport { SecureTokenService } from '../services/secure-token.service';\nimport { Router } from '@angular/router';\n\nexport const authInterceptor: HttpInterceptorFn = (req: HttpRequest<unknown>, next: HttpHandlerFn) => {\n  const secureTokenService = inject(SecureTokenService);\n  const router = inject(Router);\n\n  // Skip auth for login and public endpoints\n  const skipAuth = req.url.includes('/auth/login') ||\n                   req.url.includes('/auth/register') ||\n                   req.url.includes('/auth/invite') ||\n                   req.url.includes('/public/') ||\n                   req.url.includes('/health');\n\n  if (skipAuth) {\n    return next(req);\n  }\n\n  const token = secureTokenService.getToken();\n\n  if (!token) {\n    // No token available, redirect to login\n    router.navigate(['/login']);\n    return throwError(() => new Error('No authentication token available'));\n  }\n\n  if (!secureTokenService.isTokenValid()) {\n    // Token is invalid, try to refresh or logout\n    const refreshToken = secureTokenService.getRefreshToken();\n    if (refreshToken) {\n      // TODO: Implement token refresh logic here\n      // For now, just clear tokens and redirect\n      secureTokenService.clearTokens();\n      router.navigate(['/login']);\n      return throwError(() => new Error('Token expired'));\n    } else {\n      secureTokenService.clearTokens();\n      router.navigate(['/login']);\n      return throwError(() => new Error('Token expired'));\n    }\n  }\n\n  // Clone the request and add the authorization header\n  const authReq = req.clone({\n    setHeaders: {\n      Authorization: `Bearer ${token}`,\n      'Content-Type': 'application/json',\n      // Add CSRF protection\n      'X-Requested-With': 'XMLHttpRequest'\n    }\n  });\n\n  return next(authReq).pipe(\n    catchError((error) => {\n      // Handle 401 Unauthorized responses\n      if (error.status === 401) {\n        secureTokenService.clearTokens();\n        router.navigate(['/login']);\n      }\n\n      // Handle 403 Forbidden responses (CSRF issues)\n      if (error.status === 403) {\n        console.error('CSRF token validation failed');\n      }\n\n      return throwError(() => error);\n    })\n  );\n};\n"], "mappings": "AACA,SAASA,MAAM,QAAQ,eAAe;AACtC,SAASC,UAAU,EAAaC,UAAU,QAAQ,MAAM;AACxD,SAASC,kBAAkB,QAAQ,kCAAkC;AACrE,SAASC,MAAM,QAAQ,iBAAiB;AAExC,OAAO,MAAMC,eAAe,GAAsBA,CAACC,GAAyB,EAAEC,IAAmB,KAAI;EACnG,MAAMC,kBAAkB,GAAGR,MAAM,CAACG,kBAAkB,CAAC;EACrD,MAAMM,MAAM,GAAGT,MAAM,CAACI,MAAM,CAAC;EAE7B;EACA,MAAMM,QAAQ,GAAGJ,GAAG,CAACK,GAAG,CAACC,QAAQ,CAAC,aAAa,CAAC,IAC/BN,GAAG,CAACK,GAAG,CAACC,QAAQ,CAAC,gBAAgB,CAAC,IAClCN,GAAG,CAACK,GAAG,CAACC,QAAQ,CAAC,cAAc,CAAC,IAChCN,GAAG,CAACK,GAAG,CAACC,QAAQ,CAAC,UAAU,CAAC,IAC5BN,GAAG,CAACK,GAAG,CAACC,QAAQ,CAAC,SAAS,CAAC;EAE5C,IAAIF,QAAQ,EAAE;IACZ,OAAOH,IAAI,CAACD,GAAG,CAAC;;EAGlB,MAAMO,KAAK,GAAGL,kBAAkB,CAACM,QAAQ,EAAE;EAE3C,IAAI,CAACD,KAAK,EAAE;IACV;IACAJ,MAAM,CAACM,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;IAC3B,OAAOb,UAAU,CAAC,MAAM,IAAIc,KAAK,CAAC,mCAAmC,CAAC,CAAC;;EAGzE,IAAI,CAACR,kBAAkB,CAACS,YAAY,EAAE,EAAE;IACtC;IACA,MAAMC,YAAY,GAAGV,kBAAkB,CAACW,eAAe,EAAE;IACzD,IAAID,YAAY,EAAE;MAChB;MACA;MACAV,kBAAkB,CAACY,WAAW,EAAE;MAChCX,MAAM,CAACM,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;MAC3B,OAAOb,UAAU,CAAC,MAAM,IAAIc,KAAK,CAAC,eAAe,CAAC,CAAC;KACpD,MAAM;MACLR,kBAAkB,CAACY,WAAW,EAAE;MAChCX,MAAM,CAACM,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;MAC3B,OAAOb,UAAU,CAAC,MAAM,IAAIc,KAAK,CAAC,eAAe,CAAC,CAAC;;;EAIvD;EACA,MAAMK,OAAO,GAAGf,GAAG,CAACgB,KAAK,CAAC;IACxBC,UAAU,EAAE;MACVC,aAAa,EAAE,UAAUX,KAAK,EAAE;MAChC,cAAc,EAAE,kBAAkB;MAClC;MACA,kBAAkB,EAAE;;GAEvB,CAAC;EAEF,OAAON,IAAI,CAACc,OAAO,CAAC,CAACI,IAAI,CACvBxB,UAAU,CAAEyB,KAAK,IAAI;IACnB;IACA,IAAIA,KAAK,CAACC,MAAM,KAAK,GAAG,EAAE;MACxBnB,kBAAkB,CAACY,WAAW,EAAE;MAChCX,MAAM,CAACM,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;;IAG7B;IACA,IAAIW,KAAK,CAACC,MAAM,KAAK,GAAG,EAAE;MACxBC,OAAO,CAACF,KAAK,CAAC,8BAA8B,CAAC;;IAG/C,OAAOxB,UAAU,CAAC,MAAMwB,KAAK,CAAC;EAChC,CAAC,CAAC,CACH;AACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}