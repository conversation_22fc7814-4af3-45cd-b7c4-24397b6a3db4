{"ast": null, "code": "import { provideRouter } from '@angular/router';\nimport { routes } from './app.routes';\nimport { provideClientHydration } from '@angular/platform-browser';\nimport { WasmService } from './services/wasm.service';\nimport { ErrorHandlingService } from './services/error-handling.service';\nexport const appConfig = {\n  providers: [provideRouter(routes), provideClientHydration(), WasmService, ErrorHandlingService]\n};", "map": {"version": 3, "names": ["provideRouter", "routes", "provideClientHydration", "WasmService", "ErrorHandlingService", "appConfig", "providers"], "sources": ["C:\\Users\\<USER>\\Projects\\QSC1\\frontend\\src\\app\\app.config.ts"], "sourcesContent": ["import { ApplicationConfig } from '@angular/core';\r\nimport { provideRouter } from '@angular/router';\r\nimport { routes } from './app.routes';\r\nimport { provideClientHydration } from '@angular/platform-browser';\r\nimport { WasmService } from './services/wasm.service';\r\nimport { ErrorHandlingService } from './services/error-handling.service';\r\n\r\nexport const appConfig: ApplicationConfig = {\r\n  providers: [\r\n    provideRouter(routes),\r\n    provideClientHydration(),\r\n    WasmService,\r\n    ErrorHandlingService\r\n  ]\r\n};\r\n"], "mappings": "AACA,SAASA,aAAa,QAAQ,iBAAiB;AAC/C,SAASC,MAAM,QAAQ,cAAc;AACrC,SAASC,sBAAsB,QAAQ,2BAA2B;AAClE,SAASC,WAAW,QAAQ,yBAAyB;AACrD,SAASC,oBAAoB,QAAQ,mCAAmC;AAExE,OAAO,MAAMC,SAAS,GAAsB;EAC1CC,SAAS,EAAE,CACTN,aAAa,CAACC,MAAM,CAAC,EACrBC,sBAAsB,EAAE,EACxBC,WAAW,EACXC,oBAAoB;CAEvB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}