import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { NotificationService } from '../../services/notification.service';
import { ErrorHandlingService } from '../../services/error-handling.service';

interface Notification {
  id: string;
  type: 'DELETION' | 'COMPROMISE';
  message: string;
  timestamp: Date;
  read: boolean;
}

@Component({
  selector: 'qs-notification-list',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div class="notification-list">
      <div *ngIf="errorMessage" class="error-message">
        {{ errorMessage }}
      </div>
      <div *ngIf="notifications.length === 0" class="no-notifications">
        No notifications
      </div>
      <div *ngFor="let notification of notifications"
           class="notification"
           [class.unread]="!notification.read">
        <div class="notification-header">
          <span class="notification-type">{{ notification.type }}</span>
          <span class="notification-time">
            {{ notification.timestamp | date:'medium' }}
          </span>
        </div>
        <div class="notification-message">{{ notification.message }}</div>
      </div>
    </div>
  `,
  styles: [`
    .notification-list {
      padding: 1rem;
    }

    .error-message {
      color: #dc3545;
      padding: 0.5rem;
      margin-bottom: 1rem;
      border: 1px solid #dc3545;
      border-radius: 4px;
      background-color: #f8d7da;
    }

    .no-notifications {
      text-align: center;
      color: #6c757d;
      padding: 2rem;
    }

    .notification {
      padding: 1rem;
      margin-bottom: 1rem;
      border: 1px solid #dee2e6;
      border-radius: 4px;
      background-color: #fff;
      transition: all 0.2s ease;

      &.unread {
        border-left: 4px solid #007bff;
        background-color: #f8f9fa;
      }
    }

    .notification-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 0.5rem;
    }

    .notification-type {
      font-weight: bold;
      color: #007bff;
    }

    .notification-time {
      color: #6c757d;
      font-size: 0.875rem;
    }

    .notification-message {
      color: #212529;
      line-height: 1.5;
    }
  `]
})
export class NotificationListComponent implements OnInit {
  notifications: Notification[] = [];
  errorMessage: string | null = null;

  constructor(
    private notificationService: NotificationService,
    private errorHandlingService: ErrorHandlingService
  ) {}

  async ngOnInit() {
    await this.loadNotifications();
  }

  private async loadNotifications() {
    try {
      this.notifications = await this.notificationService.getNotifications();
    } catch (error) {
      this.errorHandlingService.handleError(error as Error, 'STORAGE');
      this.errorMessage = 'Failed to load notifications';
    }
  }

  async markAsRead(notification: Notification) {
    if (notification.read) return;

    try {
      await this.notificationService.markAsRead(notification.id);
      notification.read = true;
    } catch (error) {
      this.errorHandlingService.handleError(error as Error, 'STORAGE');
      this.errorMessage = 'Failed to mark notification as read';
    }
  }
}
