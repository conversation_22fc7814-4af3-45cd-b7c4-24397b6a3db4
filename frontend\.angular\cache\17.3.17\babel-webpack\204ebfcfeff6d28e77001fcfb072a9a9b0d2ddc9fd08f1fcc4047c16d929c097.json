{"ast": null, "code": "import { bootstrapApplication } from '@angular/platform-browser';\nimport { AppComponent } from './app/app';\nimport { appConfig } from './app/app.config';\nbootstrapApplication(AppComponent, appConfig).catch(err => console.error(err));", "map": {"version": 3, "names": ["bootstrapApplication", "AppComponent", "appConfig", "catch", "err", "console", "error"], "sources": ["D:\\TCL1\\Projects\\Projects\\QSC1\\frontend\\src\\main.ts"], "sourcesContent": ["import { bootstrapApplication } from '@angular/platform-browser';\r\nimport { AppComponent } from './app/app';\r\nimport { appConfig } from './app/app.config';\r\n\r\nbootstrapApplication(AppComponent, appConfig)\r\n  .catch(err => console.error(err));\r\n"], "mappings": "AAAA,SAASA,oBAAoB,QAAQ,2BAA2B;AAChE,SAASC,YAAY,QAAQ,WAAW;AACxC,SAASC,SAAS,QAAQ,kBAAkB;AAE5CF,oBAAoB,CAACC,YAAY,EAAEC,SAAS,CAAC,CAC1CC,KAAK,CAACC,GAAG,IAAIC,OAAO,CAACC,KAAK,CAACF,GAAG,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}