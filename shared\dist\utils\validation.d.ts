/**
 * Validation utilities for QSC application
 * Ensures data integrity and security compliance
 */
export declare const VALIDATION_CONSTANTS: {
    readonly USERNAME_MIN_LENGTH: 3;
    readonly USERNAME_MAX_LENGTH: 30;
    readonly EMAIL_MAX_LENGTH: 254;
    readonly MESSAGE_MAX_LENGTH: 4096;
    readonly ROOM_NAME_MAX_LENGTH: 100;
    readonly INVITE_CODE_LENGTH: 16;
    readonly UUID_REGEX: RegExp;
    readonly USERNAME_REGEX: RegExp;
    readonly EMAIL_REGEX: RegExp;
    readonly HEX_REGEX: RegExp;
    readonly BASE64_REGEX: RegExp;
    readonly BASE64URL_REGEX: RegExp;
};
export interface ValidationResult {
    isValid: boolean;
    errors: string[];
    warnings?: string[];
}
/**
 * Validate username format and constraints
 */
export declare function validateUsername(username: string): ValidationResult;
/**
 * Validate email format
 */
export declare function validateEmail(email: string): ValidationResult;
/**
 * Validate secret word using crypto utility
 */
export declare function validateSecretWord(secretWord: string): ValidationResult;
/**
 * Validate message content
 */
export declare function validateMessageContent(content: string): ValidationResult;
/**
 * Validate room name
 */
export declare function validateRoomName(name: string): ValidationResult;
/**
 * Validate UUID format
 */
export declare function validateUUID(uuid: string): ValidationResult;
/**
 * Validate invite code format
 */
export declare function validateInviteCode(code: string): ValidationResult;
/**
 * Validate hexadecimal string
 */
export declare function validateHexString(hex: string, expectedLength?: number): ValidationResult;
/**
 * Validate Base64 string
 */
export declare function validateBase64(base64: string): ValidationResult;
/**
 * Validate Base64URL string
 */
export declare function validateBase64URL(base64url: string): ValidationResult;
/**
 * Validate timestamp
 */
export declare function validateTimestamp(timestamp: any): ValidationResult;
/**
 * Validate expiration time (must be in the future)
 */
export declare function validateExpirationTime(expiresAt: Date | string | number): ValidationResult;
/**
 * Sanitize input string to prevent XSS and injection attacks
 */
export declare function sanitizeInput(input: string): string;
/**
 * Validate and sanitize user input
 */
export declare function validateAndSanitizeInput(input: string, validator: (input: string) => ValidationResult): {
    isValid: boolean;
    sanitized: string;
    errors: string[];
};
/**
 * Validate pagination parameters
 */
export declare function validatePagination(page?: number, limit?: number): ValidationResult;
//# sourceMappingURL=validation.d.ts.map