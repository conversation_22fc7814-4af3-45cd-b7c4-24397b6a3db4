import { Injectable } from '@angular/core';
import { StateService } from './state.service';
import { EncryptionService } from './encryption.service';
import { NotificationService } from './notification.service';
import { ChainDeletionService } from './chain-deletion.service';

@Injectable({
  providedIn: 'root'
})
export class MessageDeletionService {
  constructor(
    private stateService: StateService,
    private encryptionService: EncryptionService,
    private notificationService: NotificationService,
    private chainDeletionService: ChainDeletionService
  ) {}

  public async deleteMessage(id: string): Promise<void> {
    // 1. Remove from state
    this.stateService.removeMessage(id);

    // 2. Wipe encryption keys
    this.encryptionService.wipeMemory();

    // 3. Delete from chain
    await this.chainDeletionService.deleteFromChain(id);

    // 4. Notify sender
    await this.notificationService.createDeletionNotification(id);
  }

  public async deleteCompromisedMessages(ids: string[]): Promise<void> {
    // 1. Remove all compromised messages
    ids.forEach(id => this.stateService.removeMessage(id));

    // 2. Wipe all encryption keys
    this.encryptionService.wipeMemory();

    // 3. Delete from chain
    await this.chainDeletionService.deleteCompromisedChain(ids);

    // 4. Force re-authentication
    this.stateService.setAuthenticated(false);
  }
}
