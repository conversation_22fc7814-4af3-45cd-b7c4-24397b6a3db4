{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Projects/QSC1/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../services/wasm.service\";\nimport * as i2 from \"../../services/error-handling.service\";\nimport * as i3 from \"@angular/common\";\nfunction KeyManagementComponent_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 4)(1, \"h3\");\n    i0.ɵɵtext(2, \"Current Key Pair\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6);\n    i0.ɵɵpipe(7, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 5)(9, \"button\", 6);\n    i0.ɵɵlistener(\"click\", function KeyManagementComponent_div_3_Template_button_click_9_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.rotateKeys());\n    });\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"button\", 7);\n    i0.ɵɵlistener(\"click\", function KeyManagementComponent_div_3_Template_button_click_11_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.exportPublicKey());\n    });\n    i0.ɵɵtext(12, \"Export Public Key\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"Version: \", ctx_r1.currentKeyPair.version, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"Last Rotation: \", i0.ɵɵpipeBind2(7, 4, ctx_r1.currentKeyPair.timestamp, \"medium\"), \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.isRotating);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.isRotating ? \"Rotating...\" : \"Rotate Keys\", \" \");\n  }\n}\nfunction KeyManagementComponent_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 8)(1, \"h3\");\n    i0.ɵɵtext(2, \"Import Key Pair\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 9)(4, \"input\", 10);\n    i0.ɵɵlistener(\"change\", function KeyManagementComponent_div_4_Template_input_change_4_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onFileSelected($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 11);\n    i0.ɵɵlistener(\"click\", function KeyManagementComponent_div_4_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.importKeys());\n    });\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.isImporting);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.isImporting ? \"Importing...\" : \"Import Keys\", \" \");\n  }\n}\nfunction KeyManagementComponent_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 12);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.errorMessage, \" \");\n  }\n}\nexport let KeyManagementComponent = /*#__PURE__*/(() => {\n  class KeyManagementComponent {\n    constructor(wasmService, errorHandling) {\n      this.wasmService = wasmService;\n      this.errorHandling = errorHandling;\n      this.currentKeyPair = null;\n      this.isRotating = false;\n      this.isImporting = false;\n      this.selectedFile = null;\n      this.errorMessage = '';\n    }\n    ngOnInit() {\n      this.loadCurrentKeyPair();\n    }\n    ngOnDestroy() {\n      // Clean up any subscriptions or resources\n    }\n    loadCurrentKeyPair() {\n      var _this = this;\n      return _asyncToGenerator(function* () {\n        try {\n          _this.currentKeyPair = _this.wasmService.getCurrentKeyPair();\n        } catch (error) {\n          _this.errorMessage = 'Failed to load current key pair';\n          yield _this.errorHandling.handleError(error instanceof Error ? error : new Error(_this.errorMessage), 'SECURITY');\n        }\n      })();\n    }\n    rotateKeys() {\n      var _this2 = this;\n      return _asyncToGenerator(function* () {\n        if (_this2.isRotating) return;\n        _this2.isRotating = true;\n        _this2.errorMessage = '';\n        try {\n          yield _this2.wasmService.rotateKeys();\n          yield _this2.loadCurrentKeyPair();\n        } catch (error) {\n          _this2.errorMessage = 'Failed to rotate keys';\n          yield _this2.errorHandling.handleError(error instanceof Error ? error : new Error(_this2.errorMessage), 'SECURITY');\n        } finally {\n          _this2.isRotating = false;\n        }\n      })();\n    }\n    onFileSelected(event) {\n      const input = event.target;\n      if (input.files?.length) {\n        this.selectedFile = input.files[0];\n      }\n    }\n    importKeys() {\n      var _this3 = this;\n      return _asyncToGenerator(function* () {\n        if (!_this3.selectedFile || _this3.isImporting) return;\n        _this3.isImporting = true;\n        _this3.errorMessage = '';\n        try {\n          const fileContent = yield _this3.selectedFile.text();\n          const keyPair = JSON.parse(fileContent);\n          yield _this3.wasmService.importKeyPair(keyPair);\n          yield _this3.loadCurrentKeyPair();\n        } catch (error) {\n          _this3.errorMessage = 'Failed to import keys';\n          yield _this3.errorHandling.handleError(error instanceof Error ? error : new Error(_this3.errorMessage), 'SECURITY');\n        } finally {\n          _this3.isImporting = false;\n          _this3.selectedFile = null;\n        }\n      })();\n    }\n    exportPublicKey() {\n      var _this4 = this;\n      return _asyncToGenerator(function* () {\n        if (!_this4.currentKeyPair) return;\n        try {\n          const publicKey = _this4.currentKeyPair.public_key;\n          const blob = new Blob([publicKey], {\n            type: 'application/octet-stream'\n          });\n          const url = window.URL.createObjectURL(blob);\n          const a = document.createElement('a');\n          a.href = url;\n          a.download = `public-key-v${_this4.currentKeyPair.version}.key`;\n          document.body.appendChild(a);\n          a.click();\n          window.URL.revokeObjectURL(url);\n          document.body.removeChild(a);\n        } catch (error) {\n          _this4.errorMessage = 'Failed to export public key';\n          yield _this4.errorHandling.handleError(error instanceof Error ? error : new Error(_this4.errorMessage), 'SECURITY');\n        }\n      })();\n    }\n    static {\n      this.ɵfac = function KeyManagementComponent_Factory(t) {\n        return new (t || KeyManagementComponent)(i0.ɵɵdirectiveInject(i1.WasmService), i0.ɵɵdirectiveInject(i2.ErrorHandlingService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: KeyManagementComponent,\n        selectors: [[\"qs-key-management\"]],\n        standalone: true,\n        features: [i0.ɵɵStandaloneFeature],\n        decls: 6,\n        vars: 3,\n        consts: [[1, \"key-management\"], [\"class\", \"key-status\", 4, \"ngIf\"], [\"class\", \"key-import\", 4, \"ngIf\"], [\"class\", \"error-message\", 4, \"ngIf\"], [1, \"key-status\"], [1, \"key-actions\"], [3, \"click\", \"disabled\"], [3, \"click\"], [1, \"key-import\"], [1, \"import-form\"], [\"type\", \"file\", \"accept\", \".key\", 3, \"change\", \"disabled\"], [\"[disabled\", \"!selectedFile || isImporting\", 3, \"click\"], [1, \"error-message\"]],\n        template: function KeyManagementComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"h2\");\n            i0.ɵɵtext(2, \"Key Management\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(3, KeyManagementComponent_div_3_Template, 13, 7, \"div\", 1)(4, KeyManagementComponent_div_4_Template, 7, 2, \"div\", 2)(5, KeyManagementComponent_div_5_Template, 2, 1, \"div\", 3);\n            i0.ɵɵelementEnd();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"ngIf\", ctx.currentKeyPair);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", !ctx.currentKeyPair);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.errorMessage);\n          }\n        },\n        dependencies: [CommonModule, i3.NgIf, i3.DatePipe, FormsModule],\n        styles: [\".key-management[_ngcontent-%COMP%]{padding:20px;max-width:800px;margin:0 auto}.key-status[_ngcontent-%COMP%]{background:#f5f5f5;padding:20px;border-radius:8px;margin-bottom:20px}.key-actions[_ngcontent-%COMP%]{display:flex;gap:10px;margin-top:15px}button[_ngcontent-%COMP%]{padding:8px 16px;border:none;border-radius:4px;background:#007bff;color:#fff;cursor:pointer;transition:background .2s}button[_ngcontent-%COMP%]:disabled{background:#ccc;cursor:not-allowed}button[_ngcontent-%COMP%]:hover:not(:disabled){background:#0056b3}.key-import[_ngcontent-%COMP%]{background:#f5f5f5;padding:20px;border-radius:8px}.import-form[_ngcontent-%COMP%]{display:flex;gap:10px;margin-top:15px}input[type=file][_ngcontent-%COMP%]{flex:1;padding:8px;border:1px solid #ddd;border-radius:4px}.error-message[_ngcontent-%COMP%]{color:#dc3545;margin-top:15px;padding:10px;background:#f8d7da;border-radius:4px}\"]\n      });\n    }\n  }\n  return KeyManagementComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}