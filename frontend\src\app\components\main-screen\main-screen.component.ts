import { Component, OnInit, HostListener } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { AuthService } from '../../services/auth.service';
import { GroupService } from '../../services/group.service';
import { User } from '../../types/auth.types';
import { GroupListItem } from '../../types/group.types';

@Component({
  selector: 'app-main-screen',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './main-screen.component.html',
  styleUrl: './main-screen.component.scss'
})
export class MainScreenComponent implements OnInit {
  user: User | null = null;
  groups: GroupListItem[] = [];
  showContextMenu = false;
  showGroupPanel = false;
  contextMenuPosition = { x: 0, y: 0 };

  constructor(
    private authService: AuthService,
    private groupService: GroupService,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.authService.authState$.subscribe(state => {
      this.user = state.user;
      if (state.isAuthenticated) {
        this.loadUserGroups();
      }
    });
  }

  private loadUserGroups(): void {
    this.groupService.getUserGroups().subscribe({
      next: (groups) => {
        this.groups = groups;
      },
      error: (error) => {
        console.error('Error loading groups:', error);
      }
    });
  }

  onLogoClick(): void {
    this.router.navigate(['/compose']);
  }

  onLogoRightClick(event: MouseEvent): void {
    event.preventDefault();
    this.contextMenuPosition = { x: event.clientX, y: event.clientY };
    this.showContextMenu = true;
  }

  onLogoLongPress(): void {
    // For mobile devices
    this.showContextMenu = true;
  }

  @HostListener('document:click', ['$event'])
  onDocumentClick(event: Event): void {
    this.showContextMenu = false;
  }

  @HostListener('document:keydown', ['$event'])
  onKeyDown(event: KeyboardEvent): void {
    if ((event.ctrlKey || event.metaKey) && event.key === 'g') {
      event.preventDefault();
      this.toggleGroupPanel();
    }
    if (event.key === 'Escape') {
      this.showGroupPanel = false;
      this.showContextMenu = false;
    }
  }

  toggleGroupPanel(): void {
    this.showGroupPanel = !this.showGroupPanel;
    if (this.showGroupPanel) {
      this.loadUserGroups();
    }
  }

  onCreateGroup(): void {
    this.router.navigate(['/groups/create']);
  }

  onJoinGroup(): void {
    this.router.navigate(['/groups/join']);
  }

  onGroupSelect(group: GroupListItem): void {
    this.router.navigate(['/groups', group.id]);
  }

  onLogout(): void {
    this.authService.logout();
    this.router.navigate(['/login']);
  }
}
