.group-manager-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 1rem;
}

.group-manager-card {
  background: white;
  border-radius: 20px;
  padding: 2rem;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  width: 100%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
}

.manager-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #eee;

  h2 {
    margin: 0;
    color: #333;
    font-weight: 500;
  }

  .close-btn {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: #999;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.2s ease;

    &:hover {
      background: #f5f5f5;
      color: #333;
    }
  }
}

.mode-switcher {
  display: flex;
  background: #f5f5f5;
  border-radius: 10px;
  padding: 0.25rem;
  margin-bottom: 2rem;

  .mode-btn {
    flex: 1;
    padding: 0.75rem;
    border: none;
    border-radius: 8px;
    background: transparent;
    color: #666;
    cursor: pointer;
    transition: all 0.2s ease;
    font-weight: 500;

    &.active {
      background: white;
      color: #333;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    &:disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }
  }
}

.group-form {
  .form-group {
    margin-bottom: 1.5rem;

    label {
      display: block;
      margin-bottom: 0.5rem;
      color: #333;
      font-weight: 500;
      font-size: 0.9rem;
    }

    input,
    select,
    textarea {
      width: 100%;
      padding: 0.75rem 1rem;
      border: 2px solid #e1e5e9;
      border-radius: 10px;
      font-size: 1rem;
      transition: all 0.3s ease;
      background: white;
      font-family: inherit;

      &:focus {
        outline: none;
        border-color: #667eea;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
      }

      &:disabled,
      &[readonly] {
        background: #f8f9fa;
        cursor: not-allowed;
      }

      &::placeholder {
        color: #999;
      }
    }

    textarea {
      resize: vertical;
      min-height: 80px;
      font-family: 'Courier New', monospace;
      font-size: 0.9rem;
    }

    .help-text {
      display: block;
      margin-top: 0.5rem;
      color: #666;
      font-size: 0.8rem;
      line-height: 1.4;
    }
  }
}

.message-feedback {
  margin-bottom: 1.5rem;

  .error-message {
    background: #fee;
    color: #c33;
    padding: 0.75rem;
    border-radius: 8px;
    font-size: 0.9rem;
    border: 1px solid #fcc;
  }

  .success-message {
    background: #efe;
    color: #3c3;
    padding: 0.75rem;
    border-radius: 8px;
    font-size: 0.9rem;
    border: 1px solid #cfc;
  }
}

.manager-actions {
  display: flex;
  gap: 1rem;
  margin-bottom: 1.5rem;

  .cancel-btn,
  .action-btn {
    flex: 1;
    padding: 0.875rem;
    border: none;
    border-radius: 10px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;

    &:disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }
  }

  .cancel-btn {
    background: #f5f5f5;
    color: #666;

    &:hover:not(:disabled) {
      background: #e8e8e8;
    }
  }

  .action-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;

    &:hover:not(:disabled) {
      transform: translateY(-2px);
      box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
    }

    .loading-spinner {
      animation: pulse 1.5s ease-in-out infinite;
    }
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.security-notice {
  text-align: center;
  padding-top: 1rem;
  border-top: 1px solid #eee;

  .security-badge {
    color: #667eea;
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
    font-weight: 500;
  }

  .warning-text {
    color: #e67e22;
    font-size: 0.8rem;
    font-weight: 500;
    margin-top: 0.5rem;
  }
}

@media (max-width: 768px) {
  .group-manager-container {
    padding: 0.5rem;
  }

  .group-manager-card {
    padding: 1.5rem;
    border-radius: 15px;
  }

  .manager-actions {
    flex-direction: column;
  }

  .mode-switcher {
    flex-direction: column;

    .mode-btn {
      margin-bottom: 0.25rem;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}
