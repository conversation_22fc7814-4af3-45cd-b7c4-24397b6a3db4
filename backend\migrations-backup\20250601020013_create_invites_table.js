/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = function(knex) {
  return knex.schema.createTable('invites', function(table) {
    table.uuid('id').primary();
    table.uuid('group_id').references('id').inTable('groups').onDelete('CASCADE');
    table.uuid('created_by').references('id').inTable('users').onDelete('CASCADE');
    table.string('token').unique().notNullable(); // Dilithium-signed token
    table.enum('permission', ['read', 'write', 'admin']).defaultTo('read');
    table.timestamp('created_at').defaultTo(knex.fn.now());
    table.timestamp('expires_at').notNullable(); // 24h validity
    table.boolean('is_used').defaultTo(false);
    table.uuid('used_by').references('id').inTable('users');
    table.timestamp('used_at');
    table.string('creator_device_hash').notNullable(); // Creator's device fingerprint
    
    // Index for faster lookups and cleanup
    table.index(['expires_at', 'is_used']);
    table.index(['group_id', 'created_by']);
  });
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function(knex) {
  return knex.schema.dropTable('invites');
}; 