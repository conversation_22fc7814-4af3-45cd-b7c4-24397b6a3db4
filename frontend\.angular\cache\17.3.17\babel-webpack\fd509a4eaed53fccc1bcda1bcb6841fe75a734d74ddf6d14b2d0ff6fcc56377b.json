{"ast": null, "code": "import { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nexport function defaultIfEmpty(defaultValue) {\n  return operate((source, subscriber) => {\n    let hasValue = false;\n    source.subscribe(createOperatorSubscriber(subscriber, value => {\n      hasValue = true;\n      subscriber.next(value);\n    }, () => {\n      if (!hasValue) {\n        subscriber.next(defaultValue);\n      }\n      subscriber.complete();\n    }));\n  });\n}\n//# sourceMappingURL=defaultIfEmpty.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}