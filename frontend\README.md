# Quantum Shield Frontend

A secure messaging application with post-quantum cryptography and chain-based message deletion.

## Features

- Post-quantum cryptography using WebAssembly
- Secure key management with rotation
- Chain-based message deletion
- Encrypted notifications
- Secure storage using IndexedDB
- Error handling and recovery mechanisms

## Prerequisites

- Node.js (v18 or later)
- Rust (for WASM compilation)
- wasm-bindgen-cli

## Installation

1. Install dependencies:
```bash
npm install
```

2. Install Rust and wasm-bindgen:
```bash
curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh
cargo install wasm-bindgen-cli
```

## Development

1. Start the development server:
```bash
npm start
```

2. Build the WASM module:
```bash
npm run build:wasm
```

3. Run tests:
```bash
npm test
```

## Building for Production

1. Build the WASM module and Angular application:
```bash
npm run build:all
```

2. The production build will be available in the `dist/quantumshield-frontend` directory.

## Project Structure

- `src/app/components/` - Angular components
- `src/app/services/` - Angular services
- `src/assets/wasm/` - WebAssembly module
- `pqc-wasm/` - Rust source for WASM module

## Security Features

- Triple-layer encryption
- Memory wiping
- Secure key storage
- Chain-based deletion
- Encrypted notifications
- Error recovery

## Testing

The project uses Jasmine and Karma for testing. Run tests with:

```bash
npm test
```

For continuous testing during development:

```bash
npm run test:watch
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Commit your changes
4. Push to the branch
5. Create a Pull Request

## License

This project is licensed under the MIT License - see the LICENSE file for details.
