import { Injectable, UnauthorizedException, Logger } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { UserService } from '../user/user.service';
import { DilithiumService } from './services/dilithium.service';
import { SecretWordService } from './services/secret-word.service';
import { DilithiumJwtPayload } from './types/dilithium.types';
import { ISecretWordHash } from '@qsc/shared';
// import { RedisService } from '../redis/redis.service'; // Disabled for development
import { validateSecretWordFormat } from '@qsc/shared';

interface IJwtPayload {
  sub: string;
  username: string;
  email?: string;
  type: 'access' | 'refresh' | 'invite';
  iat?: number;
  exp?: number;
  dilithiumSignature?: string;
}

interface IAuthResponse {
  accessToken: string;
  refreshToken?: string;
  user: any; // Use any for now to avoid type conflicts
  expiresIn: number;
  tokenType: 'Bearer';
  dilithiumSignature?: string;
}

@Injectable()
export class AuthService {
  private readonly BLACKLIST_PREFIX = 'blacklist:';
  private readonly BLACKLIST_TTL = 7 * 24 * 60 * 60; // 7 days in seconds
  private readonly logger = new Logger(AuthService.name);
  private readonly tokenBlacklist = new Set<string>(); // In-memory blacklist for development

  constructor(
    private jwtService: JwtService,
    private userService: UserService,
    private dilithiumService: DilithiumService,
    private secretWordService: SecretWordService,
    // private redisService: RedisService, // Disabled for development
  ) {}

  async validateUser(username: string, secretWord: string): Promise<any> {
    // Validate secret word format first
    const validation = validateSecretWordFormat(secretWord);
    if (!validation.isValid) {
      throw new UnauthorizedException(validation.errors.join(', '));
    }

    const user = await this.userService.findByUsername(username);
    if (!user) {
      throw new UnauthorizedException('Invalid username or secret word');
    }

    // Verify secret word
    const isValid = await this.secretWordService.verifySecretWord(secretWord, user.secretWordHash);
    if (!isValid) {
      throw new UnauthorizedException('Invalid username or secret word');
    }

    return user;
  }

  async login(user: any, deviceId?: string): Promise<IAuthResponse> {
    const payload: IJwtPayload = {
      sub: user.id,
      username: user.username,
      email: user.email,
      type: 'access',
    };

    // Create Dilithium payload with required fields
    const dilithiumPayload: DilithiumJwtPayload = {
      sub: user.id,
      email: user.email || '',
      type: 'access',
    };

    const signature = await this.dilithiumService.sign(dilithiumPayload);

    // Update last login time and device if provided
    await this.userService.updateLastLogin(user.id, deviceId);

    const accessToken = this.jwtService.sign({
      ...payload,
      dilithiumSignature: signature.signature.toString('base64'),
    });

    // Generate refresh token
    const refreshPayload: IJwtPayload = {
      sub: user.id,
      username: user.username,
      email: user.email,
      type: 'refresh',
    };

    const refreshToken = this.jwtService.sign(refreshPayload, { expiresIn: '30d' });

    return {
      accessToken,
      refreshToken,
      user: {
        id: user.id,
        username: user.username,
        email: user.email,
        phone: user.phone,
        publicKey: user.publicKey,
        isAdmin: user.isAdmin,
        isActive: user.isActive,
        isVerified: user.isVerified,
        accountStatus: user.accountStatus,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt,
        lastLoginAt: user.lastLoginAt,
        deviceIds: user.deviceIds,
        failedAttempts: user.failedAttempts,
        isCompromised: user.isCompromised,
        deactivatedBy: user.deactivatedBy,
        deactivatedAt: user.deactivatedAt,
        deactivationReason: user.deactivationReason,
        reinvitedBy: user.reinvitedBy,
        reinvitedAt: user.reinvitedAt,
        newInviteCode: user.newInviteCode,
        lastAttemptAt: user.lastAttemptAt,
      },
      expiresIn: 7 * 24 * 60 * 60, // 7 days in seconds
      tokenType: 'Bearer' as const,
      dilithiumSignature: signature.signature.toString('base64'),
    };
  }

  async generateInviteToken(email: string): Promise<string> {
    const payload: DilithiumJwtPayload = {
      email,
      sub: 'invite',
      type: 'invite',
    };

    const signature = await this.dilithiumService.sign(payload);
    
    return this.jwtService.sign({
      ...payload,
      signature: signature.signature.toString('base64'),
    }, { expiresIn: '24h' });
  }

  async verifyInviteToken(token: string): Promise<{ email: string }> {
    try {
      const payload = await this.jwtService.verify(token);
      if (payload.type !== 'invite') {
        throw new UnauthorizedException('Invalid token type');
      }

      const signature = {
        signature: Buffer.from(payload.signature, 'base64'),
        message: Buffer.from(JSON.stringify({
          email: payload.email,
          sub: payload.sub,
          type: payload.type,
        })),
      };

      const isValid = await this.dilithiumService.verify(signature);
      if (!isValid) {
        throw new UnauthorizedException('Invalid signature');
      }

      return { email: payload.email };
    } catch (error) {
      throw new UnauthorizedException('Invalid or expired token');
    }
  }

  async logout(token: string): Promise<void> {
    try {
      const decoded = this.jwtService.decode(token);
      if (decoded && typeof decoded === 'object') {
        // Use in-memory blacklist for development
        this.tokenBlacklist.add(token);
      }
    } catch (error) {
      this.logger.error('Error blacklisting token:', error);
    }
  }

  async isTokenBlacklisted(token: string): Promise<boolean> {
    // Use in-memory blacklist for development
    return this.tokenBlacklist.has(token);
  }

  async validate(payload: any) {
    const user = await this.userService.findById(payload.sub);
    if (!user) {
      throw new UnauthorizedException();
    }

    // Check if token is blacklisted
    const isBlacklisted = await this.isTokenBlacklisted(payload.jti);
    if (isBlacklisted) {
      throw new UnauthorizedException('Token has been revoked');
    }

    return { id: user.id, email: user.email, username: user.username };
  }

  async refreshToken(refreshToken: string): Promise<IAuthResponse> {
    try {
      const payload = await this.jwtService.verify(refreshToken);
      if (payload.type !== 'refresh') {
        throw new UnauthorizedException('Invalid token type');
      }

      const user = await this.userService.findById(payload.sub);
      if (!user) {
        throw new UnauthorizedException('User not found');
      }

      return this.login(user);
    } catch (error) {
      throw new UnauthorizedException('Invalid refresh token');
    }
  }
} 