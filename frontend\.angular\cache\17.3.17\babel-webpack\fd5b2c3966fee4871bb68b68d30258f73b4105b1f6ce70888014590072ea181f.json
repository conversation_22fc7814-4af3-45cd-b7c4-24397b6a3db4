{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Projects/QSC1/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { TestBed } from '@angular/core/testing';\nimport { AppComponent } from './app.component';\nimport { WasmService } from './services/wasm.service';\nimport { ErrorHandlingService } from './services/error-handling.service';\nimport { KeyManagementComponent } from './components/key-management/key-management.component';\nimport { NotificationListComponent } from './components/notification-list/notification-list.component';\nimport { ChainNodeListComponent } from './components/chain-node-list/chain-node-list.component';\ndescribe('AppComponent', () => {\n  let component;\n  let fixture;\n  let wasmService;\n  let errorHandlingService;\n  beforeEach(/*#__PURE__*/_asyncToGenerator(function* () {\n    const wasmSpy = jasmine.createSpyObj('WasmService', ['init']);\n    const errorHandlingSpy = jasmine.createSpyObj('ErrorHandlingService', ['handleError']);\n    yield TestBed.configureTestingModule({\n      imports: [AppComponent, KeyManagementComponent, NotificationListComponent, ChainNodeListComponent],\n      providers: [{\n        provide: WasmService,\n        useValue: wasmSpy\n      }, {\n        provide: ErrorHandlingService,\n        useValue: errorHandlingSpy\n      }]\n    }).compileComponents();\n    wasmService = TestBed.inject(WasmService);\n    errorHandlingService = TestBed.inject(ErrorHandlingService);\n  }));\n  beforeEach(() => {\n    fixture = TestBed.createComponent(AppComponent);\n    component = fixture.componentInstance;\n  });\n  it('should create', () => {\n    expect(component).toBeTruthy();\n  });\n  it('should initialize WASM module on init', /*#__PURE__*/_asyncToGenerator(function* () {\n    wasmService.init.and.returnValue(Promise.resolve());\n    yield component.ngOnInit();\n    expect(wasmService.init).toHaveBeenCalled();\n    expect(component.errorMessage).toBeNull();\n  }));\n  it('should handle WASM initialization error', /*#__PURE__*/_asyncToGenerator(function* () {\n    const error = new Error('WASM initialization failed');\n    wasmService.init.and.returnValue(Promise.reject(error));\n    yield component.ngOnInit();\n    expect(errorHandlingService.handleError).toHaveBeenCalledWith(error, 'WASM');\n    expect(component.errorMessage).toBe('Failed to initialize WASM module');\n  }));\n});", "map": {"version": 3, "names": ["TestBed", "AppComponent", "WasmService", "ErrorHandlingService", "KeyManagementComponent", "NotificationListComponent", "ChainNodeListComponent", "describe", "component", "fixture", "wasmService", "errorHandlingService", "beforeEach", "_asyncToGenerator", "wasmSpy", "jasmine", "createSpyObj", "errorHandlingSpy", "configureTestingModule", "imports", "providers", "provide", "useValue", "compileComponents", "inject", "createComponent", "componentInstance", "it", "expect", "toBeTruthy", "init", "and", "returnValue", "Promise", "resolve", "ngOnInit", "toHaveBeenCalled", "errorMessage", "toBeNull", "error", "Error", "reject", "handleError", "toHaveBeenCalledWith", "toBe"], "sources": ["C:\\Users\\<USER>\\Projects\\QSC1\\frontend\\src\\app\\app.component.spec.ts"], "sourcesContent": ["import { ComponentFixture, TestBed } from '@angular/core/testing';\r\nimport { AppComponent } from './app.component';\r\nimport { WasmService } from './services/wasm.service';\r\nimport { ErrorHandlingService } from './services/error-handling.service';\r\nimport { KeyManagementComponent } from './components/key-management/key-management.component';\r\nimport { NotificationListComponent } from './components/notification-list/notification-list.component';\r\nimport { ChainNodeListComponent } from './components/chain-node-list/chain-node-list.component';\r\n\r\ndescribe('AppComponent', () => {\r\n  let component: AppComponent;\r\n  let fixture: ComponentFixture<AppComponent>;\r\n  let wasmService: jasmine.SpyObj<WasmService>;\r\n  let errorHandlingService: jasmine.SpyObj<ErrorHandlingService>;\r\n\r\n  beforeEach(async () => {\r\n    const wasmSpy = jasmine.createSpyObj('WasmService', ['init']);\r\n    const errorHandlingSpy = jasmine.createSpyObj('ErrorHandlingService', ['handleError']);\r\n\r\n    await TestBed.configureTestingModule({\r\n      imports: [\r\n        AppComponent,\r\n        KeyManagementComponent,\r\n        NotificationListComponent,\r\n        ChainNodeListComponent\r\n      ],\r\n      providers: [\r\n        { provide: WasmService, useValue: wasmSpy },\r\n        { provide: ErrorHandlingService, useValue: errorHandlingSpy }\r\n      ]\r\n    }).compileComponents();\r\n\r\n    wasmService = TestBed.inject(WasmService) as jasmine.SpyObj<WasmService>;\r\n    errorHandlingService = TestBed.inject(ErrorHandlingService) as jasmine.SpyObj<ErrorHandlingService>;\r\n  });\r\n\r\n  beforeEach(() => {\r\n    fixture = TestBed.createComponent(AppComponent);\r\n    component = fixture.componentInstance;\r\n  });\r\n\r\n  it('should create', () => {\r\n    expect(component).toBeTruthy();\r\n  });\r\n\r\n  it('should initialize WASM module on init', async () => {\r\n    wasmService.init.and.returnValue(Promise.resolve());\r\n\r\n    await component.ngOnInit();\r\n    expect(wasmService.init).toHaveBeenCalled();\r\n    expect(component.errorMessage).toBeNull();\r\n  });\r\n\r\n  it('should handle WASM initialization error', async () => {\r\n    const error = new Error('WASM initialization failed');\r\n    wasmService.init.and.returnValue(Promise.reject(error));\r\n\r\n    await component.ngOnInit();\r\n    expect(errorHandlingService.handleError).toHaveBeenCalledWith(error, 'WASM');\r\n    expect(component.errorMessage).toBe('Failed to initialize WASM module');\r\n  });\r\n});\r\n"], "mappings": ";AAAA,SAA2BA,OAAO,QAAQ,uBAAuB;AACjE,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,yBAAyB;AACrD,SAASC,oBAAoB,QAAQ,mCAAmC;AACxE,SAASC,sBAAsB,QAAQ,sDAAsD;AAC7F,SAASC,yBAAyB,QAAQ,4DAA4D;AACtG,SAASC,sBAAsB,QAAQ,wDAAwD;AAE/FC,QAAQ,CAAC,cAAc,EAAE,MAAK;EAC5B,IAAIC,SAAuB;EAC3B,IAAIC,OAAuC;EAC3C,IAAIC,WAAwC;EAC5C,IAAIC,oBAA0D;EAE9DC,UAAU,cAAAC,iBAAA,CAAC,aAAW;IACpB,MAAMC,OAAO,GAAGC,OAAO,CAACC,YAAY,CAAC,aAAa,EAAE,CAAC,MAAM,CAAC,CAAC;IAC7D,MAAMC,gBAAgB,GAAGF,OAAO,CAACC,YAAY,CAAC,sBAAsB,EAAE,CAAC,aAAa,CAAC,CAAC;IAEtF,MAAMhB,OAAO,CAACkB,sBAAsB,CAAC;MACnCC,OAAO,EAAE,CACPlB,YAAY,EACZG,sBAAsB,EACtBC,yBAAyB,EACzBC,sBAAsB,CACvB;MACDc,SAAS,EAAE,CACT;QAAEC,OAAO,EAAEnB,WAAW;QAAEoB,QAAQ,EAAER;MAAO,CAAE,EAC3C;QAAEO,OAAO,EAAElB,oBAAoB;QAAEmB,QAAQ,EAAEL;MAAgB,CAAE;KAEhE,CAAC,CAACM,iBAAiB,EAAE;IAEtBb,WAAW,GAAGV,OAAO,CAACwB,MAAM,CAACtB,WAAW,CAAgC;IACxES,oBAAoB,GAAGX,OAAO,CAACwB,MAAM,CAACrB,oBAAoB,CAAyC;EACrG,CAAC,EAAC;EAEFS,UAAU,CAAC,MAAK;IACdH,OAAO,GAAGT,OAAO,CAACyB,eAAe,CAACxB,YAAY,CAAC;IAC/CO,SAAS,GAAGC,OAAO,CAACiB,iBAAiB;EACvC,CAAC,CAAC;EAEFC,EAAE,CAAC,eAAe,EAAE,MAAK;IACvBC,MAAM,CAACpB,SAAS,CAAC,CAACqB,UAAU,EAAE;EAChC,CAAC,CAAC;EAEFF,EAAE,CAAC,uCAAuC,eAAAd,iBAAA,CAAE,aAAW;IACrDH,WAAW,CAACoB,IAAI,CAACC,GAAG,CAACC,WAAW,CAACC,OAAO,CAACC,OAAO,EAAE,CAAC;IAEnD,MAAM1B,SAAS,CAAC2B,QAAQ,EAAE;IAC1BP,MAAM,CAAClB,WAAW,CAACoB,IAAI,CAAC,CAACM,gBAAgB,EAAE;IAC3CR,MAAM,CAACpB,SAAS,CAAC6B,YAAY,CAAC,CAACC,QAAQ,EAAE;EAC3C,CAAC,EAAC;EAEFX,EAAE,CAAC,yCAAyC,eAAAd,iBAAA,CAAE,aAAW;IACvD,MAAM0B,KAAK,GAAG,IAAIC,KAAK,CAAC,4BAA4B,CAAC;IACrD9B,WAAW,CAACoB,IAAI,CAACC,GAAG,CAACC,WAAW,CAACC,OAAO,CAACQ,MAAM,CAACF,KAAK,CAAC,CAAC;IAEvD,MAAM/B,SAAS,CAAC2B,QAAQ,EAAE;IAC1BP,MAAM,CAACjB,oBAAoB,CAAC+B,WAAW,CAAC,CAACC,oBAAoB,CAACJ,KAAK,EAAE,MAAM,CAAC;IAC5EX,MAAM,CAACpB,SAAS,CAAC6B,YAAY,CAAC,CAACO,IAAI,CAAC,kCAAkC,CAAC;EACzE,CAAC,EAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}