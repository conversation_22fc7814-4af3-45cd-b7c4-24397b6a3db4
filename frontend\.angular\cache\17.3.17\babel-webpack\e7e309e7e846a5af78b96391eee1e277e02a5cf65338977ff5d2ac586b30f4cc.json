{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Projects/QSC1/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { TestBed } from '@angular/core/testing';\nimport { KeyManagementComponent } from './key-management.component';\nimport { WasmService } from '../../services/wasm.service';\nimport { ErrorHandlingService } from '../../services/error-handling.service';\ndescribe('KeyManagementComponent', () => {\n  let component;\n  let fixture;\n  let wasmService;\n  let errorHandlingService;\n  beforeEach(/*#__PURE__*/_asyncToGenerator(function* () {\n    const wasmSpy = jasmine.createSpyObj('WasmService', ['rotateKeys', 'getCurrentKeyPair', 'importKeyPair']);\n    const errorHandlingSpy = jasmine.createSpyObj('ErrorHandlingService', ['handleError']);\n    yield TestBed.configureTestingModule({\n      imports: [KeyManagementComponent],\n      providers: [{\n        provide: WasmService,\n        useValue: wasmSpy\n      }, {\n        provide: ErrorHandlingService,\n        useValue: errorHandlingSpy\n      }]\n    }).compileComponents();\n    wasmService = TestBed.inject(WasmService);\n    errorHandlingService = TestBed.inject(ErrorHandlingService);\n  }));\n  beforeEach(() => {\n    fixture = TestBed.createComponent(KeyManagementComponent);\n    component = fixture.componentInstance;\n  });\n  it('should create', () => {\n    expect(component).toBeTruthy();\n  });\n  it('should load current key pair on init', /*#__PURE__*/_asyncToGenerator(function* () {\n    const mockKeyPair = {\n      version: 1,\n      timestamp: new Date(),\n      public_key: new Uint8Array([1, 2, 3]),\n      private_key: new Uint8Array([4, 5, 6])\n    };\n    wasmService.getCurrentKeyPair.and.returnValue(Promise.resolve(mockKeyPair));\n    yield component.ngOnInit();\n    expect(component.currentKeyPair).toEqual(mockKeyPair);\n  }));\n  it('should handle key rotation', /*#__PURE__*/_asyncToGenerator(function* () {\n    const mockKeyPair = {\n      version: 2,\n      timestamp: new Date(),\n      public_key: new Uint8Array([1, 2, 3]),\n      private_key: new Uint8Array([4, 5, 6])\n    };\n    wasmService.rotateKeys.and.returnValue(Promise.resolve());\n    wasmService.getCurrentKeyPair.and.returnValue(Promise.resolve(mockKeyPair));\n    yield component.rotateKeys();\n    expect(wasmService.rotateKeys).toHaveBeenCalled();\n    expect(component.currentKeyPair).toEqual(mockKeyPair);\n    expect(component.isRotating).toBeFalse();\n  }));\n  it('should handle key rotation error', /*#__PURE__*/_asyncToGenerator(function* () {\n    const error = new Error('Rotation failed');\n    wasmService.rotateKeys.and.returnValue(Promise.reject(error));\n    yield component.rotateKeys();\n    expect(errorHandlingService.handleError).toHaveBeenCalledWith(error, 'SECURITY');\n    expect(component.errorMessage).toBe('Failed to rotate keys');\n    expect(component.isRotating).toBeFalse();\n  }));\n  it('should handle file selection', () => {\n    const mockFile = new File(['test'], 'test.key', {\n      type: 'application/octet-stream'\n    });\n    const event = {\n      target: {\n        files: [mockFile]\n      }\n    };\n    component.onFileSelected(event);\n    expect(component.selectedFile).toBe(mockFile);\n  });\n  it('should handle key import', /*#__PURE__*/_asyncToGenerator(function* () {\n    const mockFile = new File(['test'], 'test.key', {\n      type: 'application/octet-stream'\n    });\n    const mockKeyPair = {\n      version: 1,\n      timestamp: new Date(),\n      public_key: new Uint8Array([1, 2, 3]),\n      private_key: new Uint8Array([4, 5, 6])\n    };\n    component.selectedFile = mockFile;\n    wasmService.importKeyPair.and.returnValue(Promise.resolve());\n    wasmService.getCurrentKeyPair.and.returnValue(Promise.resolve(mockKeyPair));\n    yield component.importKeys();\n    expect(wasmService.importKeyPair).toHaveBeenCalled();\n    expect(component.currentKeyPair).toEqual(mockKeyPair);\n    expect(component.isImporting).toBeFalse();\n    expect(component.selectedFile).toBeNull();\n  }));\n  it('should handle key import error', /*#__PURE__*/_asyncToGenerator(function* () {\n    const mockFile = new File(['test'], 'test.key', {\n      type: 'application/octet-stream'\n    });\n    const error = new Error('Import failed');\n    component.selectedFile = mockFile;\n    wasmService.importKeyPair.and.returnValue(Promise.reject(error));\n    yield component.importKeys();\n    expect(errorHandlingService.handleError).toHaveBeenCalledWith(error, 'SECURITY');\n    expect(component.errorMessage).toBe('Failed to import keys');\n    expect(component.isImporting).toBeFalse();\n    expect(component.selectedFile).toBeNull();\n  }));\n  it('should export public key', /*#__PURE__*/_asyncToGenerator(function* () {\n    const mockKeyPair = {\n      version: 1,\n      timestamp: new Date(),\n      public_key: new Uint8Array([1, 2, 3]),\n      private_key: new Uint8Array([4, 5, 6])\n    };\n    component.currentKeyPair = mockKeyPair;\n    spyOn(window.URL, 'createObjectURL');\n    spyOn(window.URL, 'revokeObjectURL');\n    yield component.exportPublicKey();\n    expect(window.URL.createObjectURL).toHaveBeenCalled();\n    expect(window.URL.revokeObjectURL).toHaveBeenCalled();\n  }));\n  it('should handle export error', /*#__PURE__*/_asyncToGenerator(function* () {\n    const mockKeyPair = {\n      version: 1,\n      timestamp: new Date(),\n      public_key: new Uint8Array([1, 2, 3]),\n      private_key: new Uint8Array([4, 5, 6])\n    };\n    component.currentKeyPair = mockKeyPair;\n    spyOn(window.URL, 'createObjectURL').and.throwError('Export failed');\n    yield component.exportPublicKey();\n    expect(errorHandlingService.handleError).toHaveBeenCalled();\n    expect(component.errorMessage).toBe('Failed to export public key');\n  }));\n});", "map": {"version": 3, "names": ["TestBed", "KeyManagementComponent", "WasmService", "ErrorHandlingService", "describe", "component", "fixture", "wasmService", "errorHandlingService", "beforeEach", "_asyncToGenerator", "wasmSpy", "jasmine", "createSpyObj", "errorHandlingSpy", "configureTestingModule", "imports", "providers", "provide", "useValue", "compileComponents", "inject", "createComponent", "componentInstance", "it", "expect", "toBeTruthy", "mockKeyPair", "version", "timestamp", "Date", "public_key", "Uint8Array", "private_key", "getCurrentKeyPair", "and", "returnValue", "Promise", "resolve", "ngOnInit", "currentKeyPair", "toEqual", "rotateKeys", "toHaveBeenCalled", "isRotating", "toBeFalse", "error", "Error", "reject", "handleError", "toHaveBeenCalledWith", "errorMessage", "toBe", "mockFile", "File", "type", "event", "target", "files", "onFileSelected", "selectedFile", "importKeyPair", "importKeys", "isImporting", "toBeNull", "spyOn", "window", "URL", "exportPublicKey", "createObjectURL", "revokeObjectURL", "throwError"], "sources": ["C:\\Users\\<USER>\\Projects\\QSC1\\frontend\\src\\app\\components\\key-management\\key-management.component.spec.ts"], "sourcesContent": ["import { ComponentFixture, TestBed } from '@angular/core/testing';\r\nimport { KeyManagementComponent } from './key-management.component';\r\nimport { WasmService } from '../../services/wasm.service';\r\nimport { ErrorHandlingService } from '../../services/error-handling.service';\r\nimport { of, throwError } from 'rxjs';\r\n\r\ndescribe('KeyManagementComponent', () => {\r\n  let component: KeyManagementComponent;\r\n  let fixture: ComponentFixture<KeyManagementComponent>;\r\n  let wasmService: jasmine.SpyObj<WasmService>;\r\n  let errorHandlingService: jasmine.SpyObj<ErrorHandlingService>;\r\n\r\n  beforeEach(async () => {\r\n    const wasmSpy = jasmine.createSpyObj('WasmService', [\r\n      'rotateKeys',\r\n      'getCurrentKeyPair',\r\n      'importKeyPair'\r\n    ]);\r\n    const errorHandlingSpy = jasmine.createSpyObj('ErrorHandlingService', [\r\n      'handleError'\r\n    ]);\r\n\r\n    await TestBed.configureTestingModule({\r\n      imports: [KeyManagementComponent],\r\n      providers: [\r\n        { provide: WasmService, useValue: wasmSpy },\r\n        { provide: ErrorHandlingService, useValue: errorHandlingSpy }\r\n      ]\r\n    }).compileComponents();\r\n\r\n    wasmService = TestBed.inject(WasmService) as jasmine.SpyObj<WasmService>;\r\n    errorHandlingService = TestBed.inject(ErrorHandlingService) as jasmine.SpyObj<ErrorHandlingService>;\r\n  });\r\n\r\n  beforeEach(() => {\r\n    fixture = TestBed.createComponent(KeyManagementComponent);\r\n    component = fixture.componentInstance;\r\n  });\r\n\r\n  it('should create', () => {\r\n    expect(component).toBeTruthy();\r\n  });\r\n\r\n  it('should load current key pair on init', async () => {\r\n    const mockKeyPair = {\r\n      version: 1,\r\n      timestamp: new Date(),\r\n      public_key: new Uint8Array([1, 2, 3]),\r\n      private_key: new Uint8Array([4, 5, 6])\r\n    };\r\n\r\n    wasmService.getCurrentKeyPair.and.returnValue(Promise.resolve(mockKeyPair));\r\n\r\n    await component.ngOnInit();\r\n    expect(component.currentKeyPair).toEqual(mockKeyPair);\r\n  });\r\n\r\n  it('should handle key rotation', async () => {\r\n    const mockKeyPair = {\r\n      version: 2,\r\n      timestamp: new Date(),\r\n      public_key: new Uint8Array([1, 2, 3]),\r\n      private_key: new Uint8Array([4, 5, 6])\r\n    };\r\n\r\n    wasmService.rotateKeys.and.returnValue(Promise.resolve());\r\n    wasmService.getCurrentKeyPair.and.returnValue(Promise.resolve(mockKeyPair));\r\n\r\n    await component.rotateKeys();\r\n    expect(wasmService.rotateKeys).toHaveBeenCalled();\r\n    expect(component.currentKeyPair).toEqual(mockKeyPair);\r\n    expect(component.isRotating).toBeFalse();\r\n  });\r\n\r\n  it('should handle key rotation error', async () => {\r\n    const error = new Error('Rotation failed');\r\n    wasmService.rotateKeys.and.returnValue(Promise.reject(error));\r\n\r\n    await component.rotateKeys();\r\n    expect(errorHandlingService.handleError).toHaveBeenCalledWith(error, 'SECURITY');\r\n    expect(component.errorMessage).toBe('Failed to rotate keys');\r\n    expect(component.isRotating).toBeFalse();\r\n  });\r\n\r\n  it('should handle file selection', () => {\r\n    const mockFile = new File(['test'], 'test.key', { type: 'application/octet-stream' });\r\n    const event = {\r\n      target: {\r\n        files: [mockFile]\r\n      }\r\n    } as unknown as Event;\r\n\r\n    component.onFileSelected(event);\r\n    expect(component.selectedFile).toBe(mockFile);\r\n  });\r\n\r\n  it('should handle key import', async () => {\r\n    const mockFile = new File(['test'], 'test.key', { type: 'application/octet-stream' });\r\n    const mockKeyPair = {\r\n      version: 1,\r\n      timestamp: new Date(),\r\n      public_key: new Uint8Array([1, 2, 3]),\r\n      private_key: new Uint8Array([4, 5, 6])\r\n    };\r\n\r\n    component.selectedFile = mockFile;\r\n    wasmService.importKeyPair.and.returnValue(Promise.resolve());\r\n    wasmService.getCurrentKeyPair.and.returnValue(Promise.resolve(mockKeyPair));\r\n\r\n    await component.importKeys();\r\n    expect(wasmService.importKeyPair).toHaveBeenCalled();\r\n    expect(component.currentKeyPair).toEqual(mockKeyPair);\r\n    expect(component.isImporting).toBeFalse();\r\n    expect(component.selectedFile).toBeNull();\r\n  });\r\n\r\n  it('should handle key import error', async () => {\r\n    const mockFile = new File(['test'], 'test.key', { type: 'application/octet-stream' });\r\n    const error = new Error('Import failed');\r\n\r\n    component.selectedFile = mockFile;\r\n    wasmService.importKeyPair.and.returnValue(Promise.reject(error));\r\n\r\n    await component.importKeys();\r\n    expect(errorHandlingService.handleError).toHaveBeenCalledWith(error, 'SECURITY');\r\n    expect(component.errorMessage).toBe('Failed to import keys');\r\n    expect(component.isImporting).toBeFalse();\r\n    expect(component.selectedFile).toBeNull();\r\n  });\r\n\r\n  it('should export public key', async () => {\r\n    const mockKeyPair = {\r\n      version: 1,\r\n      timestamp: new Date(),\r\n      public_key: new Uint8Array([1, 2, 3]),\r\n      private_key: new Uint8Array([4, 5, 6])\r\n    };\r\n\r\n    component.currentKeyPair = mockKeyPair;\r\n    spyOn(window.URL, 'createObjectURL');\r\n    spyOn(window.URL, 'revokeObjectURL');\r\n\r\n    await component.exportPublicKey();\r\n    expect(window.URL.createObjectURL).toHaveBeenCalled();\r\n    expect(window.URL.revokeObjectURL).toHaveBeenCalled();\r\n  });\r\n\r\n  it('should handle export error', async () => {\r\n    const mockKeyPair = {\r\n      version: 1,\r\n      timestamp: new Date(),\r\n      public_key: new Uint8Array([1, 2, 3]),\r\n      private_key: new Uint8Array([4, 5, 6])\r\n    };\r\n\r\n    component.currentKeyPair = mockKeyPair;\r\n    spyOn(window.URL, 'createObjectURL').and.throwError('Export failed');\r\n\r\n    await component.exportPublicKey();\r\n    expect(errorHandlingService.handleError).toHaveBeenCalled();\r\n    expect(component.errorMessage).toBe('Failed to export public key');\r\n  });\r\n});\r\n"], "mappings": ";AAAA,SAA2BA,OAAO,QAAQ,uBAAuB;AACjE,SAASC,sBAAsB,QAAQ,4BAA4B;AACnE,SAASC,WAAW,QAAQ,6BAA6B;AACzD,SAASC,oBAAoB,QAAQ,uCAAuC;AAG5EC,QAAQ,CAAC,wBAAwB,EAAE,MAAK;EACtC,IAAIC,SAAiC;EACrC,IAAIC,OAAiD;EACrD,IAAIC,WAAwC;EAC5C,IAAIC,oBAA0D;EAE9DC,UAAU,cAAAC,iBAAA,CAAC,aAAW;IACpB,MAAMC,OAAO,GAAGC,OAAO,CAACC,YAAY,CAAC,aAAa,EAAE,CAClD,YAAY,EACZ,mBAAmB,EACnB,eAAe,CAChB,CAAC;IACF,MAAMC,gBAAgB,GAAGF,OAAO,CAACC,YAAY,CAAC,sBAAsB,EAAE,CACpE,aAAa,CACd,CAAC;IAEF,MAAMb,OAAO,CAACe,sBAAsB,CAAC;MACnCC,OAAO,EAAE,CAACf,sBAAsB,CAAC;MACjCgB,SAAS,EAAE,CACT;QAAEC,OAAO,EAAEhB,WAAW;QAAEiB,QAAQ,EAAER;MAAO,CAAE,EAC3C;QAAEO,OAAO,EAAEf,oBAAoB;QAAEgB,QAAQ,EAAEL;MAAgB,CAAE;KAEhE,CAAC,CAACM,iBAAiB,EAAE;IAEtBb,WAAW,GAAGP,OAAO,CAACqB,MAAM,CAACnB,WAAW,CAAgC;IACxEM,oBAAoB,GAAGR,OAAO,CAACqB,MAAM,CAAClB,oBAAoB,CAAyC;EACrG,CAAC,EAAC;EAEFM,UAAU,CAAC,MAAK;IACdH,OAAO,GAAGN,OAAO,CAACsB,eAAe,CAACrB,sBAAsB,CAAC;IACzDI,SAAS,GAAGC,OAAO,CAACiB,iBAAiB;EACvC,CAAC,CAAC;EAEFC,EAAE,CAAC,eAAe,EAAE,MAAK;IACvBC,MAAM,CAACpB,SAAS,CAAC,CAACqB,UAAU,EAAE;EAChC,CAAC,CAAC;EAEFF,EAAE,CAAC,sCAAsC,eAAAd,iBAAA,CAAE,aAAW;IACpD,MAAMiB,WAAW,GAAG;MAClBC,OAAO,EAAE,CAAC;MACVC,SAAS,EAAE,IAAIC,IAAI,EAAE;MACrBC,UAAU,EAAE,IAAIC,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;MACrCC,WAAW,EAAE,IAAID,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;KACtC;IAEDzB,WAAW,CAAC2B,iBAAiB,CAACC,GAAG,CAACC,WAAW,CAACC,OAAO,CAACC,OAAO,CAACX,WAAW,CAAC,CAAC;IAE3E,MAAMtB,SAAS,CAACkC,QAAQ,EAAE;IAC1Bd,MAAM,CAACpB,SAAS,CAACmC,cAAc,CAAC,CAACC,OAAO,CAACd,WAAW,CAAC;EACvD,CAAC,EAAC;EAEFH,EAAE,CAAC,4BAA4B,eAAAd,iBAAA,CAAE,aAAW;IAC1C,MAAMiB,WAAW,GAAG;MAClBC,OAAO,EAAE,CAAC;MACVC,SAAS,EAAE,IAAIC,IAAI,EAAE;MACrBC,UAAU,EAAE,IAAIC,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;MACrCC,WAAW,EAAE,IAAID,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;KACtC;IAEDzB,WAAW,CAACmC,UAAU,CAACP,GAAG,CAACC,WAAW,CAACC,OAAO,CAACC,OAAO,EAAE,CAAC;IACzD/B,WAAW,CAAC2B,iBAAiB,CAACC,GAAG,CAACC,WAAW,CAACC,OAAO,CAACC,OAAO,CAACX,WAAW,CAAC,CAAC;IAE3E,MAAMtB,SAAS,CAACqC,UAAU,EAAE;IAC5BjB,MAAM,CAAClB,WAAW,CAACmC,UAAU,CAAC,CAACC,gBAAgB,EAAE;IACjDlB,MAAM,CAACpB,SAAS,CAACmC,cAAc,CAAC,CAACC,OAAO,CAACd,WAAW,CAAC;IACrDF,MAAM,CAACpB,SAAS,CAACuC,UAAU,CAAC,CAACC,SAAS,EAAE;EAC1C,CAAC,EAAC;EAEFrB,EAAE,CAAC,kCAAkC,eAAAd,iBAAA,CAAE,aAAW;IAChD,MAAMoC,KAAK,GAAG,IAAIC,KAAK,CAAC,iBAAiB,CAAC;IAC1CxC,WAAW,CAACmC,UAAU,CAACP,GAAG,CAACC,WAAW,CAACC,OAAO,CAACW,MAAM,CAACF,KAAK,CAAC,CAAC;IAE7D,MAAMzC,SAAS,CAACqC,UAAU,EAAE;IAC5BjB,MAAM,CAACjB,oBAAoB,CAACyC,WAAW,CAAC,CAACC,oBAAoB,CAACJ,KAAK,EAAE,UAAU,CAAC;IAChFrB,MAAM,CAACpB,SAAS,CAAC8C,YAAY,CAAC,CAACC,IAAI,CAAC,uBAAuB,CAAC;IAC5D3B,MAAM,CAACpB,SAAS,CAACuC,UAAU,CAAC,CAACC,SAAS,EAAE;EAC1C,CAAC,EAAC;EAEFrB,EAAE,CAAC,8BAA8B,EAAE,MAAK;IACtC,MAAM6B,QAAQ,GAAG,IAAIC,IAAI,CAAC,CAAC,MAAM,CAAC,EAAE,UAAU,EAAE;MAAEC,IAAI,EAAE;IAA0B,CAAE,CAAC;IACrF,MAAMC,KAAK,GAAG;MACZC,MAAM,EAAE;QACNC,KAAK,EAAE,CAACL,QAAQ;;KAEC;IAErBhD,SAAS,CAACsD,cAAc,CAACH,KAAK,CAAC;IAC/B/B,MAAM,CAACpB,SAAS,CAACuD,YAAY,CAAC,CAACR,IAAI,CAACC,QAAQ,CAAC;EAC/C,CAAC,CAAC;EAEF7B,EAAE,CAAC,0BAA0B,eAAAd,iBAAA,CAAE,aAAW;IACxC,MAAM2C,QAAQ,GAAG,IAAIC,IAAI,CAAC,CAAC,MAAM,CAAC,EAAE,UAAU,EAAE;MAAEC,IAAI,EAAE;IAA0B,CAAE,CAAC;IACrF,MAAM5B,WAAW,GAAG;MAClBC,OAAO,EAAE,CAAC;MACVC,SAAS,EAAE,IAAIC,IAAI,EAAE;MACrBC,UAAU,EAAE,IAAIC,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;MACrCC,WAAW,EAAE,IAAID,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;KACtC;IAED3B,SAAS,CAACuD,YAAY,GAAGP,QAAQ;IACjC9C,WAAW,CAACsD,aAAa,CAAC1B,GAAG,CAACC,WAAW,CAACC,OAAO,CAACC,OAAO,EAAE,CAAC;IAC5D/B,WAAW,CAAC2B,iBAAiB,CAACC,GAAG,CAACC,WAAW,CAACC,OAAO,CAACC,OAAO,CAACX,WAAW,CAAC,CAAC;IAE3E,MAAMtB,SAAS,CAACyD,UAAU,EAAE;IAC5BrC,MAAM,CAAClB,WAAW,CAACsD,aAAa,CAAC,CAAClB,gBAAgB,EAAE;IACpDlB,MAAM,CAACpB,SAAS,CAACmC,cAAc,CAAC,CAACC,OAAO,CAACd,WAAW,CAAC;IACrDF,MAAM,CAACpB,SAAS,CAAC0D,WAAW,CAAC,CAAClB,SAAS,EAAE;IACzCpB,MAAM,CAACpB,SAAS,CAACuD,YAAY,CAAC,CAACI,QAAQ,EAAE;EAC3C,CAAC,EAAC;EAEFxC,EAAE,CAAC,gCAAgC,eAAAd,iBAAA,CAAE,aAAW;IAC9C,MAAM2C,QAAQ,GAAG,IAAIC,IAAI,CAAC,CAAC,MAAM,CAAC,EAAE,UAAU,EAAE;MAAEC,IAAI,EAAE;IAA0B,CAAE,CAAC;IACrF,MAAMT,KAAK,GAAG,IAAIC,KAAK,CAAC,eAAe,CAAC;IAExC1C,SAAS,CAACuD,YAAY,GAAGP,QAAQ;IACjC9C,WAAW,CAACsD,aAAa,CAAC1B,GAAG,CAACC,WAAW,CAACC,OAAO,CAACW,MAAM,CAACF,KAAK,CAAC,CAAC;IAEhE,MAAMzC,SAAS,CAACyD,UAAU,EAAE;IAC5BrC,MAAM,CAACjB,oBAAoB,CAACyC,WAAW,CAAC,CAACC,oBAAoB,CAACJ,KAAK,EAAE,UAAU,CAAC;IAChFrB,MAAM,CAACpB,SAAS,CAAC8C,YAAY,CAAC,CAACC,IAAI,CAAC,uBAAuB,CAAC;IAC5D3B,MAAM,CAACpB,SAAS,CAAC0D,WAAW,CAAC,CAAClB,SAAS,EAAE;IACzCpB,MAAM,CAACpB,SAAS,CAACuD,YAAY,CAAC,CAACI,QAAQ,EAAE;EAC3C,CAAC,EAAC;EAEFxC,EAAE,CAAC,0BAA0B,eAAAd,iBAAA,CAAE,aAAW;IACxC,MAAMiB,WAAW,GAAG;MAClBC,OAAO,EAAE,CAAC;MACVC,SAAS,EAAE,IAAIC,IAAI,EAAE;MACrBC,UAAU,EAAE,IAAIC,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;MACrCC,WAAW,EAAE,IAAID,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;KACtC;IAED3B,SAAS,CAACmC,cAAc,GAAGb,WAAW;IACtCsC,KAAK,CAACC,MAAM,CAACC,GAAG,EAAE,iBAAiB,CAAC;IACpCF,KAAK,CAACC,MAAM,CAACC,GAAG,EAAE,iBAAiB,CAAC;IAEpC,MAAM9D,SAAS,CAAC+D,eAAe,EAAE;IACjC3C,MAAM,CAACyC,MAAM,CAACC,GAAG,CAACE,eAAe,CAAC,CAAC1B,gBAAgB,EAAE;IACrDlB,MAAM,CAACyC,MAAM,CAACC,GAAG,CAACG,eAAe,CAAC,CAAC3B,gBAAgB,EAAE;EACvD,CAAC,EAAC;EAEFnB,EAAE,CAAC,4BAA4B,eAAAd,iBAAA,CAAE,aAAW;IAC1C,MAAMiB,WAAW,GAAG;MAClBC,OAAO,EAAE,CAAC;MACVC,SAAS,EAAE,IAAIC,IAAI,EAAE;MACrBC,UAAU,EAAE,IAAIC,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;MACrCC,WAAW,EAAE,IAAID,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;KACtC;IAED3B,SAAS,CAACmC,cAAc,GAAGb,WAAW;IACtCsC,KAAK,CAACC,MAAM,CAACC,GAAG,EAAE,iBAAiB,CAAC,CAAChC,GAAG,CAACoC,UAAU,CAAC,eAAe,CAAC;IAEpE,MAAMlE,SAAS,CAAC+D,eAAe,EAAE;IACjC3C,MAAM,CAACjB,oBAAoB,CAACyC,WAAW,CAAC,CAACN,gBAAgB,EAAE;IAC3DlB,MAAM,CAACpB,SAAS,CAAC8C,YAAY,CAAC,CAACC,IAAI,CAAC,6BAA6B,CAAC;EACpE,CAAC,EAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}