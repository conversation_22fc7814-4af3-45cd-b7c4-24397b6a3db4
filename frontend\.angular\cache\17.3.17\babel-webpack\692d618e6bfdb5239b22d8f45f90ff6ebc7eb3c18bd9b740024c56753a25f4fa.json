{"ast": null, "code": "import { inject } from '@angular/core';\nimport { Router } from '@angular/router';\nimport { map, take } from 'rxjs/operators';\nimport { AuthService } from '../services/auth.service';\nexport const authGuard = (route, state) => {\n  const authService = inject(AuthService);\n  const router = inject(Router);\n  return authService.authState$.pipe(take(1), map(authState => {\n    if (authState.isAuthenticated && authState.user) {\n      return true;\n    } else {\n      // Store the attempted URL for redirecting after login\n      router.navigate(['/login'], {\n        queryParams: {\n          returnUrl: state.url\n        }\n      });\n      return false;\n    }\n  }));\n};\nexport const loginGuard = (route, state) => {\n  const authService = inject(AuthService);\n  const router = inject(Router);\n  return authService.authState$.pipe(take(1), map(authState => {\n    if (authState.isAuthenticated) {\n      // User is already authenticated, redirect to main\n      router.navigate(['/main']);\n      return false;\n    } else {\n      return true;\n    }\n  }));\n};", "map": {"version": 3, "names": ["inject", "Router", "map", "take", "AuthService", "<PERSON>th<PERSON><PERSON>", "route", "state", "authService", "router", "authState$", "pipe", "authState", "isAuthenticated", "user", "navigate", "queryParams", "returnUrl", "url", "loginGuard"], "sources": ["C:\\Users\\<USER>\\Projects\\QSC1\\frontend\\src\\app\\guards\\auth.guard.ts"], "sourcesContent": ["import { inject } from '@angular/core';\nimport { Router } from '@angular/router';\nimport { map, take } from 'rxjs/operators';\nimport { AuthService } from '../services/auth.service';\nimport { CanActivateFn } from '@angular/router';\n\nexport const authGuard: CanActivateFn = (route, state) => {\n  const authService = inject(AuthService);\n  const router = inject(Router);\n\n  return authService.authState$.pipe(\n    take(1),\n    map(authState => {\n      if (authState.isAuthenticated && authState.user) {\n        return true;\n      } else {\n        // Store the attempted URL for redirecting after login\n        router.navigate(['/login'], {\n          queryParams: { returnUrl: state.url }\n        });\n        return false;\n      }\n    })\n  );\n};\n\nexport const loginGuard: CanActivateFn = (route, state) => {\n  const authService = inject(AuthService);\n  const router = inject(Router);\n\n  return authService.authState$.pipe(\n    take(1),\n    map(authState => {\n      if (authState.isAuthenticated) {\n        // User is already authenticated, redirect to main\n        router.navigate(['/main']);\n        return false;\n      } else {\n        return true;\n      }\n    })\n  );\n};\n"], "mappings": "AAAA,SAASA,MAAM,QAAQ,eAAe;AACtC,SAASC,MAAM,QAAQ,iBAAiB;AACxC,SAASC,GAAG,EAAEC,IAAI,QAAQ,gBAAgB;AAC1C,SAASC,WAAW,QAAQ,0BAA0B;AAGtD,OAAO,MAAMC,SAAS,GAAkBA,CAACC,KAAK,EAAEC,KAAK,KAAI;EACvD,MAAMC,WAAW,GAAGR,MAAM,CAACI,WAAW,CAAC;EACvC,MAAMK,MAAM,GAAGT,MAAM,CAACC,MAAM,CAAC;EAE7B,OAAOO,WAAW,CAACE,UAAU,CAACC,IAAI,CAChCR,IAAI,CAAC,CAAC,CAAC,EACPD,GAAG,CAACU,SAAS,IAAG;IACd,IAAIA,SAAS,CAACC,eAAe,IAAID,SAAS,CAACE,IAAI,EAAE;MAC/C,OAAO,IAAI;KACZ,MAAM;MACL;MACAL,MAAM,CAACM,QAAQ,CAAC,CAAC,QAAQ,CAAC,EAAE;QAC1BC,WAAW,EAAE;UAAEC,SAAS,EAAEV,KAAK,CAACW;QAAG;OACpC,CAAC;MACF,OAAO,KAAK;;EAEhB,CAAC,CAAC,CACH;AACH,CAAC;AAED,OAAO,MAAMC,UAAU,GAAkBA,CAACb,KAAK,EAAEC,KAAK,KAAI;EACxD,MAAMC,WAAW,GAAGR,MAAM,CAACI,WAAW,CAAC;EACvC,MAAMK,MAAM,GAAGT,MAAM,CAACC,MAAM,CAAC;EAE7B,OAAOO,WAAW,CAACE,UAAU,CAACC,IAAI,CAChCR,IAAI,CAAC,CAAC,CAAC,EACPD,GAAG,CAACU,SAAS,IAAG;IACd,IAAIA,SAAS,CAACC,eAAe,EAAE;MAC7B;MACAJ,MAAM,CAACM,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC;MAC1B,OAAO,KAAK;KACb,MAAM;MACL,OAAO,IAAI;;EAEf,CAAC,CAAC,CACH;AACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}