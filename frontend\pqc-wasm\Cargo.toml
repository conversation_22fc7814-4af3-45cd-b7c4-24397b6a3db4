[package]
name = "pqc-wasm"
version = "0.1.0"
edition = "2021"

[lib]
crate-type = ["cdylib"]

[dependencies]
wasm-bindgen = "0.2"
js-sys = "0.3"
getrandom = { version = "0.2", features = ["js"] }
rand = "0.8"
rand_chacha = "0.3"
pqcrypto = { version = "0.1", features = ["dilithium5"] }
aes-gcm = "0.10"
base64 = "0.21"
serde = { version = "1.0", features = ["derive"] }
serde-wasm-bindgen = "0.6"

[profile.release]
opt-level = 3
lto = true
