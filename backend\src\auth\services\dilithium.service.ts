import { Injectable, Logger } from '@nestjs/common';
import { DilithiumKeyPair, DilithiumSignature, DilithiumJwtPayload } from '../types/dilithium.types';
import { IPQCKeyPair, IPQCSignature } from '@qsc/shared';
import { generateSecureRandom, sanitizeForLogging } from '@qsc/shared';
import { LibOQSService } from '../../security/services/liboqs.service';
import * as crypto from 'crypto';
import * as forge from 'node-forge';

/**
 * Dilithium Service for Post-Quantum Digital Signatures
 *
 * This service implements CRYSTALS-Dilithium digital signatures.
 * Currently using Ed25519 as a placeholder until LibOQS is properly integrated.
 *
 * Security Note: In production, this should use actual CRYSTALS-Dilithium
 * implementation from LibOQS for quantum resistance.
 */
@Injectable()
export class DilithiumService {
  private readonly logger = new Logger(DilithiumService.name);
  private readonly keyPair: DilithiumKeyPair;
  private readonly algorithm = 'dilithium'; // Will be used when LibOQS is integrated

  constructor(private readonly libOQSService: LibOQSService) {
    if (this.libOQSService.isAvailable()) {
      this.logger.log('Using LibOQS for Dilithium operations');
    } else {
      this.logger.warn('Using Ed25519 placeholder for Dilithium. Replace with LibOQS in production.');
    }
    // In production, this should be loaded from a secure HSM
    this.keyPair = this.generateKeyPair();
  }

  /**
   * Generate a new Dilithium key pair
   * TODO: Replace with actual CRYSTALS-Dilithium implementation
   */
  private generateKeyPair(): DilithiumKeyPair {
    try {
      // Placeholder implementation using Ed25519
      // In production, this should use CRYSTALS-Dilithium from LibOQS
      const { publicKey, privateKey } = crypto.generateKeyPairSync('ed25519');

      const keyPair = {
        publicKey: Buffer.from(publicKey.export({ type: 'spki', format: 'pem' })),
        privateKey: Buffer.from(privateKey.export({ type: 'pkcs8', format: 'pem' })),
      };

      this.logger.log('Dilithium key pair generated (Ed25519 placeholder)');
      return keyPair;
    } catch (error) {
      this.logger.error('Failed to generate Dilithium key pair', error);
      throw new Error('Key pair generation failed');
    }
  }

  /**
   * Generate a new key pair for external use
   */
  async generatePQCKeyPair(): Promise<IPQCKeyPair> {
    if (this.libOQSService.isAvailable()) {
      return await this.libOQSService.generateDilithiumKeyPair();
    }

    const keyPair = this.generateKeyPair();
    return {
      publicKey: keyPair.publicKey,
      privateKey: keyPair.privateKey,
      algorithm: 'dilithium',
    };
  }

  /**
   * Sign a payload using Dilithium (ML-DSA)
   */
  async sign(payload: DilithiumJwtPayload): Promise<DilithiumSignature> {
    try {
      const message = Buffer.from(JSON.stringify(payload));

      if (this.libOQSService.isAvailable()) {
        const pqcSignature = await this.libOQSService.signWithDilithium(
          message,
          this.keyPair.privateKey
        );

        this.logger.debug('Message signed with Dilithium using ML-DSA', {
          payloadType: payload.type,
          messageLength: message.length,
          signatureLength: pqcSignature.signature.length,
        });

        return { signature: pqcSignature.signature, message };
      }

      // Fallback implementation using Ed25519
      const privateKeyObject = crypto.createPrivateKey(this.keyPair.privateKey);
      const signature = crypto.sign(null, message, privateKeyObject);

      this.logger.warn('Message signed with Dilithium using Ed25519 fallback', {
        payloadType: payload.type,
        messageLength: message.length,
        signatureLength: signature.length,
      });

      return { signature, message };
    } catch (error) {
      this.logger.error('Failed to sign message with Dilithium', {
        error: sanitizeForLogging(error),
        payload: sanitizeForLogging(payload),
      });
      throw new Error('Signing failed');
    }
  }

  /**
   * Sign arbitrary data using Dilithium (ML-DSA)
   */
  async signData(data: Buffer, privateKey?: Buffer): Promise<IPQCSignature> {
    try {
      const keyToUse = privateKey || this.keyPair.privateKey;

      if (this.libOQSService.isAvailable()) {
        return await this.libOQSService.signWithDilithium(data, keyToUse);
      }

      // Fallback implementation
      const privateKeyObject = crypto.createPrivateKey(keyToUse);
      const signature = crypto.sign(null, data, privateKeyObject);

      this.logger.warn('Data signed with Dilithium using Ed25519 fallback', {
        dataLength: data.length,
        signatureLength: signature.length,
      });

      return {
        signature,
        message: data,
        algorithm: 'dilithium',
      };
    } catch (error) {
      this.logger.error('Failed to sign data with Dilithium', error);
      throw new Error('Data signing failed');
    }
  }

  /**
   * Verify a Dilithium signature using ML-DSA
   */
  async verify(signature: DilithiumSignature): Promise<boolean> {
    try {
      if (this.libOQSService.isAvailable()) {
        const pqcSignature: IPQCSignature = {
          signature: signature.signature,
          message: signature.message,
          algorithm: 'dilithium',
        };

        const isValid = await this.libOQSService.verifyDilithiumSignature(
          pqcSignature,
          this.keyPair.publicKey
        );

        this.logger.debug('Dilithium signature verification result using ML-DSA', {
          isValid,
          messageLength: signature.message.length,
          signatureLength: signature.signature.length,
        });

        return isValid;
      }

      // Fallback implementation using Ed25519
      const publicKeyObject = crypto.createPublicKey(this.keyPair.publicKey);
      const isValid = crypto.verify(
        null,
        signature.message,
        publicKeyObject,
        signature.signature
      );

      this.logger.warn('Dilithium signature verification using Ed25519 fallback', {
        isValid,
        messageLength: signature.message.length,
        signatureLength: signature.signature.length,
      });

      return isValid;
    } catch (error) {
      this.logger.error('Failed to verify Dilithium signature', error);
      return false;
    }
  }

  /**
   * Verify arbitrary data signature using ML-DSA
   */
  async verifyData(signature: IPQCSignature, publicKey?: Buffer): Promise<boolean> {
    try {
      const keyToUse = publicKey || this.keyPair.publicKey;

      if (this.libOQSService.isAvailable()) {
        return await this.libOQSService.verifyDilithiumSignature(signature, keyToUse);
      }

      // Fallback implementation
      const publicKeyObject = crypto.createPublicKey(keyToUse);
      const isValid = crypto.verify(
        null,
        signature.message,
        publicKeyObject,
        signature.signature
      );

      this.logger.warn('Dilithium data signature verification using Ed25519 fallback', {
        isValid,
        algorithm: signature.algorithm,
      });

      return isValid;
    } catch (error) {
      this.logger.error('Failed to verify Dilithium data signature', error);
      return false;
    }
  }

  /**
   * Get the public key
   */
  getPublicKey(): Buffer {
    return this.keyPair.publicKey;
  }

  /**
   * Get the private key (use with caution)
   */
  getPrivateKey(): Buffer {
    this.logger.warn('Private key accessed - ensure this is for legitimate purposes');
    return this.keyPair.privateKey;
  }

  /**
   * Export public key in PEM format
   */
  exportPublicKeyPEM(): string {
    return this.keyPair.publicKey.toString();
  }

  /**
   * Import public key from PEM format
   */
  importPublicKeyPEM(pemKey: string): Buffer {
    try {
      const publicKeyObject = crypto.createPublicKey(pemKey);
      return Buffer.from(publicKeyObject.export({ type: 'spki', format: 'pem' }));
    } catch (error) {
      this.logger.error('Failed to import public key from PEM', error);
      throw new Error('Invalid PEM key format');
    }
  }

  /**
   * Get algorithm information
   */
  getAlgorithmInfo(): { name: string; keySize: number; signatureSize: number } {
    if (this.libOQSService.isAvailable()) {
      const details = this.libOQSService.getAlgorithmDetails('Dilithium3', 'signature');
      return {
        name: 'CRYSTALS-Dilithium (ML-DSA)',
        keySize: details?.public_key_length || 1952,
        signatureSize: details?.signature_length || 3293,
      };
    }

    // Fallback values for Ed25519
    return {
      name: 'CRYSTALS-Dilithium (Ed25519 fallback)',
      keySize: 32, // Ed25519 key size
      signatureSize: 64, // Ed25519 signature size
    };
  }
}