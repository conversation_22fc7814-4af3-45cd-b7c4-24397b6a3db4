"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.createApiResponse = createApiResponse;
exports.createSuccessResponse = createSuccessResponse;
exports.createErrorResponse = createErrorResponse;
exports.createQSCError = createQSCError;
exports.sleep = sleep;
exports.retryWithBackoff = retryWithBackoff;
exports.debounce = debounce;
exports.throttle = throttle;
exports.deepClone = deepClone;
exports.isEmpty = isEmpty;
exports.pick = pick;
exports.omit = omit;
exports.formatDate = formatDate;
exports.formatFileSize = formatFileSize;
exports.generateRandomString = generateRandomString;
exports.truncateString = truncateString;
exports.camelToSnake = camelToSnake;
exports.snakeToCamel = snakeToCamel;
exports.objectKeysToSnake = objectKeysToSnake;
exports.objectKeysToCamel = objectKeysToCamel;
exports.isValidUrl = isValidUrl;
exports.escapeHtml = escapeHtml;
exports.timeAgo = timeAgo;
exports.isDevelopment = isDevelopment;
exports.isProduction = isProduction;
exports.getEnvVar = getEnvVar;
/**
 * Common helper utilities for QSC application
 */
/**
 * Create a standardized API response
 */
function createApiResponse(success, data, error, message) {
    return {
        success,
        data: data,
        error,
        message,
        timestamp: new Date().toISOString(),
    };
}
/**
 * Create a success response
 */
function createSuccessResponse(data, message) {
    return createApiResponse(true, data, undefined, message);
}
/**
 * Create an error response
 */
function createErrorResponse(error, message) {
    return createApiResponse(false, undefined, error, message);
}
/**
 * Create a QSC error object
 */
function createQSCError(code, message, details, userId, requestId) {
    return {
        code,
        message,
        details,
        timestamp: new Date(),
        userId: userId,
        requestId: requestId,
    };
}
/**
 * Sleep for a specified number of milliseconds
 */
function sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}
/**
 * Retry a function with exponential backoff
 */
async function retryWithBackoff(fn, maxRetries = 3, baseDelay = 1000, maxDelay = 10000) {
    let lastError;
    for (let attempt = 0; attempt <= maxRetries; attempt++) {
        try {
            return await fn();
        }
        catch (error) {
            lastError = error;
            if (attempt === maxRetries) {
                throw lastError;
            }
            const delay = Math.min(baseDelay * Math.pow(2, attempt), maxDelay);
            await sleep(delay);
        }
    }
    throw lastError;
}
/**
 * Debounce a function
 */
function debounce(func, wait) {
    let timeout;
    return (...args) => {
        clearTimeout(timeout);
        timeout = setTimeout(() => func(...args), wait);
    };
}
/**
 * Throttle a function
 */
function throttle(func, limit) {
    let inThrottle;
    return (...args) => {
        if (!inThrottle) {
            func(...args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    };
}
/**
 * Deep clone an object (JSON-safe)
 */
function deepClone(obj) {
    if (obj === null || typeof obj !== 'object') {
        return obj;
    }
    if (obj instanceof Date) {
        return new Date(obj.getTime());
    }
    if (obj instanceof Array) {
        return obj.map(item => deepClone(item));
    }
    if (typeof obj === 'object') {
        const cloned = {};
        for (const key in obj) {
            if (obj.hasOwnProperty(key)) {
                cloned[key] = deepClone(obj[key]);
            }
        }
        return cloned;
    }
    return obj;
}
/**
 * Check if an object is empty
 */
function isEmpty(obj) {
    if (obj === null || obj === undefined) {
        return true;
    }
    if (typeof obj === 'string' || Array.isArray(obj)) {
        return obj.length === 0;
    }
    if (typeof obj === 'object') {
        return Object.keys(obj).length === 0;
    }
    return false;
}
/**
 * Pick specific properties from an object
 */
function pick(obj, keys) {
    const result = {};
    for (const key of keys) {
        if (key in obj) {
            result[key] = obj[key];
        }
    }
    return result;
}
/**
 * Omit specific properties from an object
 */
function omit(obj, keys) {
    const result = { ...obj };
    for (const key of keys) {
        delete result[key];
    }
    return result;
}
/**
 * Format a date for display
 */
function formatDate(date, format = 'short') {
    const d = new Date(date);
    if (isNaN(d.getTime())) {
        return 'Invalid Date';
    }
    switch (format) {
        case 'short':
            return d.toLocaleDateString();
        case 'long':
            return d.toLocaleString();
        case 'iso':
            return d.toISOString();
        default:
            return d.toString();
    }
}
/**
 * Format file size in human-readable format
 */
function formatFileSize(bytes) {
    if (bytes === 0)
        return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}
/**
 * Generate a random string of specified length
 */
function generateRandomString(length, charset = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789') {
    let result = '';
    for (let i = 0; i < length; i++) {
        result += charset.charAt(Math.floor(Math.random() * charset.length));
    }
    return result;
}
/**
 * Truncate a string to a specified length
 */
function truncateString(str, maxLength, suffix = '...') {
    if (str.length <= maxLength) {
        return str;
    }
    return str.substring(0, maxLength - suffix.length) + suffix;
}
/**
 * Convert camelCase to snake_case
 */
function camelToSnake(str) {
    return str.replace(/[A-Z]/g, letter => `_${letter.toLowerCase()}`);
}
/**
 * Convert snake_case to camelCase
 */
function snakeToCamel(str) {
    return str.replace(/_([a-z])/g, (_, letter) => letter.toUpperCase());
}
/**
 * Convert object keys from camelCase to snake_case
 */
function objectKeysToSnake(obj) {
    if (obj === null || typeof obj !== 'object') {
        return obj;
    }
    if (Array.isArray(obj)) {
        return obj.map(objectKeysToSnake);
    }
    const result = {};
    for (const [key, value] of Object.entries(obj)) {
        const snakeKey = camelToSnake(key);
        result[snakeKey] = typeof value === 'object' ? objectKeysToSnake(value) : value;
    }
    return result;
}
/**
 * Convert object keys from snake_case to camelCase
 */
function objectKeysToCamel(obj) {
    if (obj === null || typeof obj !== 'object') {
        return obj;
    }
    if (Array.isArray(obj)) {
        return obj.map(objectKeysToCamel);
    }
    const result = {};
    for (const [key, value] of Object.entries(obj)) {
        const camelKey = snakeToCamel(key);
        result[camelKey] = typeof value === 'object' ? objectKeysToCamel(value) : value;
    }
    return result;
}
/**
 * Check if a value is a valid URL
 */
function isValidUrl(url) {
    try {
        new URL(url);
        return true;
    }
    catch {
        return false;
    }
}
/**
 * Escape HTML characters
 */
function escapeHtml(text) {
    const map = {
        '&': '&amp;',
        '<': '&lt;',
        '>': '&gt;',
        '"': '&quot;',
        "'": '&#039;'
    };
    return text.replace(/[&<>"']/g, char => map[char] || char);
}
/**
 * Calculate time difference in human-readable format
 */
function timeAgo(date) {
    const now = new Date();
    const past = new Date(date);
    const diffMs = now.getTime() - past.getTime();
    const diffSeconds = Math.floor(diffMs / 1000);
    const diffMinutes = Math.floor(diffSeconds / 60);
    const diffHours = Math.floor(diffMinutes / 60);
    const diffDays = Math.floor(diffHours / 24);
    if (diffSeconds < 60) {
        return 'just now';
    }
    else if (diffMinutes < 60) {
        return `${diffMinutes} minute${diffMinutes !== 1 ? 's' : ''} ago`;
    }
    else if (diffHours < 24) {
        return `${diffHours} hour${diffHours !== 1 ? 's' : ''} ago`;
    }
    else if (diffDays < 7) {
        return `${diffDays} day${diffDays !== 1 ? 's' : ''} ago`;
    }
    else {
        return formatDate(past, 'short');
    }
}
/**
 * Check if code is running in development environment
 */
function isDevelopment() {
    return process.env.NODE_ENV === 'development';
}
/**
 * Check if code is running in production environment
 */
function isProduction() {
    return process.env.NODE_ENV === 'production';
}
/**
 * Get environment variable with default value
 */
function getEnvVar(key, defaultValue) {
    const value = process.env[key];
    if (value === undefined) {
        if (defaultValue !== undefined) {
            return defaultValue;
        }
        throw new Error(`Environment variable ${key} is not defined`);
    }
    return value;
}
//# sourceMappingURL=helpers.js.map