{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Projects/QSC1/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"./wasm.service\";\nexport let EncryptionService = /*#__PURE__*/(() => {\n  class EncryptionService {\n    constructor(wasmService) {\n      this.wasmService = wasmService;\n    }\n    encryptMessage(message) {\n      var _this = this;\n      return _asyncToGenerator(function* () {\n        // Layer 1: PQC Encryption (WASM)\n        const pqcEncrypted = yield _this.wasmService.encryptMessage(message);\n        // Layer 2: Symmetric Encryption (AES-256)\n        const symmetricEncrypted = yield _this.encryptSymmetric(pqcEncrypted);\n        // Layer 3: Transport Layer (TLS-like)\n        const transportEncrypted = yield _this.encryptTransport(symmetricEncrypted);\n        return {\n          pqcLayer: pqcEncrypted,\n          symmetricLayer: symmetricEncrypted,\n          transportLayer: transportEncrypted\n        };\n      })();\n    }\n    decryptMessage(encrypted) {\n      var _this2 = this;\n      return _asyncToGenerator(function* () {\n        // Layer 3: Transport Decryption\n        const transportDecrypted = yield _this2.decryptTransport(encrypted.transportLayer);\n        // Layer 2: Symmetric Decryption\n        const symmetricDecrypted = yield _this2.decryptSymmetric(transportDecrypted);\n        // Layer 1: PQC Decryption\n        return _this2.wasmService.decryptMessage(symmetricDecrypted);\n      })();\n    }\n    encryptSymmetric(data) {\n      return _asyncToGenerator(function* () {\n        // TODO: Implement AES-256 encryption\n        // This is a placeholder that will be replaced with actual encryption\n        return data;\n      })();\n    }\n    decryptSymmetric(data) {\n      return _asyncToGenerator(function* () {\n        // TODO: Implement AES-256 decryption\n        // This is a placeholder that will be replaced with actual decryption\n        return data;\n      })();\n    }\n    encryptTransport(data) {\n      return _asyncToGenerator(function* () {\n        // TODO: Implement transport layer encryption\n        // This is a placeholder that will be replaced with actual encryption\n        return data;\n      })();\n    }\n    decryptTransport(data) {\n      return _asyncToGenerator(function* () {\n        // TODO: Implement transport layer decryption\n        // This is a placeholder that will be replaced with actual decryption\n        return data;\n      })();\n    }\n    wipeMemory() {\n      this.wasmService.wipeMemory();\n    }\n    static {\n      this.ɵfac = function EncryptionService_Factory(t) {\n        return new (t || EncryptionService)(i0.ɵɵinject(i1.WasmService));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: EncryptionService,\n        factory: EncryptionService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return EncryptionService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}