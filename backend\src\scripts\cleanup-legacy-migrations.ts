#!/usr/bin/env ts-node

/**
 * QSC Legacy Migration Cleanup Script
 * 
 * This script removes conflicting Knex migrations that are incompatible
 * with the standardized TypeORM + username/secretWord authentication system.
 * 
 * The QSC project has standardized on:
 * - TypeORM as the single ORM solution
 * - better-sqlite3 as the database driver
 * - username/secretWord authentication (not email/password)
 * - Post-quantum cryptography compatibility
 */

import { promises as fs } from 'fs';
import { join } from 'path';
import { Logger } from '@nestjs/common';

const logger = new Logger('LegacyMigrationCleanup');

// Legacy Knex migrations that conflict with TypeORM
const LEGACY_MIGRATIONS_TO_REMOVE = [
  '20250601020005_init_users_table.js',
  '20250601020006_create_messages_table.js', 
  '20250601020007_create_session_keys_table.js',
  '20250601020008_add_secret_word_to_users.js',
  '20250601020009_update_messages_for_security.js',
  '20250601020010_add_account_status_fields.js',
  '20250601020011_create_groups_table.js',
  '20250601020012_create_group_members_table.js',
  '20250601020013_create_invites_table.js',
  '20250601020014_create_users_table.js',
  '20250601020015_create_messages_table.js',
  '20250601020016_create_session_keys_table.js',
  '20250601020017_create_session_keys_table.js',
  '20250601020018_create_session_keys_table.js',
];

const MIGRATIONS_DIR = join(process.cwd(), 'migrations');
const BACKUP_DIR = join(process.cwd(), 'migrations-backup');

async function createBackup(): Promise<void> {
  try {
    await fs.mkdir(BACKUP_DIR, { recursive: true });
    logger.log(`Created backup directory: ${BACKUP_DIR}`);
    
    for (const migration of LEGACY_MIGRATIONS_TO_REMOVE) {
      const sourcePath = join(MIGRATIONS_DIR, migration);
      const backupPath = join(BACKUP_DIR, migration);
      
      try {
        await fs.copyFile(sourcePath, backupPath);
        logger.log(`Backed up: ${migration}`);
      } catch (error) {
        if ((error as any).code !== 'ENOENT') {
          logger.warn(`Failed to backup ${migration}: ${error}`);
        }
      }
    }
  } catch (error) {
    logger.error(`Failed to create backup: ${error}`);
    throw error;
  }
}

async function removeLegacyMigrations(): Promise<void> {
  let removedCount = 0;
  
  for (const migration of LEGACY_MIGRATIONS_TO_REMOVE) {
    const migrationPath = join(MIGRATIONS_DIR, migration);
    
    try {
      await fs.unlink(migrationPath);
      logger.log(`Removed legacy migration: ${migration}`);
      removedCount++;
    } catch (error) {
      if ((error as any).code === 'ENOENT') {
        logger.log(`Migration already removed: ${migration}`);
      } else {
        logger.error(`Failed to remove ${migration}: ${error}`);
      }
    }
  }
  
  logger.log(`Removed ${removedCount} legacy migrations`);
}

async function createCleanupSummary(): Promise<void> {
  const summaryPath = join(BACKUP_DIR, 'CLEANUP_SUMMARY.md');
  const summary = `# QSC Legacy Migration Cleanup Summary

## Date: ${new Date().toISOString()}

## Reason for Cleanup
The QSC project has standardized on a single ORM solution (TypeORM) with username/secretWord authentication.
Legacy Knex migrations were creating conflicts with the new authentication system.

## Removed Migrations
${LEGACY_MIGRATIONS_TO_REMOVE.map(m => `- ${m}`).join('\n')}

## Current Database Architecture
- **ORM**: TypeORM (standardized)
- **Database**: better-sqlite3 with SQLCipher encryption
- **Authentication**: username/secretWord (not email/password)
- **User Entity**: \`backend/src/users/entities/user.entity.ts\`
- **Database Config**: \`backend/src/database/data-source.ts\`

## TypeORM Migrations
TypeORM migrations are located in \`backend/src/migrations/\` and handle:
- User entity with secretWordHash field
- Post-quantum cryptography compatibility
- Security-first architecture
- Account status management

## Recovery
If you need to restore any legacy migration, they are backed up in this directory.
However, they are incompatible with the current TypeORM setup and should not be used.
`;

  await fs.writeFile(summaryPath, summary);
  logger.log(`Created cleanup summary: ${summaryPath}`);
}

async function main(): Promise<void> {
  try {
    logger.log('Starting QSC legacy migration cleanup...');
    
    // Create backup of legacy migrations
    await createBackup();
    
    // Remove conflicting migrations
    await removeLegacyMigrations();
    
    // Create summary documentation
    await createCleanupSummary();
    
    logger.log('✅ Legacy migration cleanup completed successfully');
    logger.log('');
    logger.log('📋 Summary:');
    logger.log('   - Legacy Knex migrations backed up and removed');
    logger.log('   - TypeORM is now the single ORM solution');
    logger.log('   - User authentication uses username/secretWord pattern');
    logger.log('   - Database schema managed by TypeORM migrations');
    logger.log('');
    logger.log('🔧 Next steps:');
    logger.log('   1. Run: npm run db:migrate (TypeORM migrations)');
    logger.log('   2. Run: npm run db:seed (Create initial admin)');
    logger.log('   3. Test authentication with username/secretWord');
    
  } catch (error) {
    logger.error('❌ Legacy migration cleanup failed:', error);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

export { main as cleanupLegacyMigrations };
