// QSC Main Component - Minimalistic Modern Design
.qsc-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

// Central Circle
.circle-container {
  position: relative;
  z-index: 1;
}

.qsc-circle {
  width: 200px;
  height: 200px;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);

  &:hover {
    transform: scale(1.05);
    box-shadow: 0 12px 48px rgba(0, 0, 0, 0.4);
  }

  &:active {
    transform: scale(0.98);
  }

  // Guest state - Red
  &.circle-guest {
    background: linear-gradient(135deg, #ff4757 0%, #ff3742 100%);
    border: 3px solid rgba(255, 71, 87, 0.3);

    &:hover {
      box-shadow: 0 12px 48px rgba(255, 71, 87, 0.4);
    }
  }

  // Authenticated state - Blue
  &.circle-authenticated {
    background: linear-gradient(135deg, #3742fa 0%, #2f3542 100%);
    border: 3px solid rgba(55, 66, 250, 0.3);

    &:hover {
      box-shadow: 0 12px 48px rgba(55, 66, 250, 0.4);
    }
  }

  // Unread messages state - Green
  &.circle-unread {
    background: linear-gradient(135deg, #2ed573 0%, #1e90ff 100%);
    border: 3px solid rgba(46, 213, 115, 0.3);
    animation: pulse 2s infinite;

    &:hover {
      box-shadow: 0 12px 48px rgba(46, 213, 115, 0.4);
    }
  }

  // Composing state - Purple
  &.circle-composing {
    background: linear-gradient(135deg, #a55eea 0%, #8854d0 100%);
    border: 3px solid rgba(165, 94, 234, 0.3);

    &:hover {
      box-shadow: 0 12px 48px rgba(165, 94, 234, 0.4);
    }
  }
}

.circle-inner {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

// Wind effect animation
.wind-effect {
  position: absolute;
  top: 20%;
  right: 15%;
  width: 60px;
  height: 60px;
  opacity: 0.3;

  &::before,
  &::after {
    content: '';
    position: absolute;
    background: rgba(255, 255, 255, 0.6);
    border-radius: 50%;
    animation: windFlow 3s ease-in-out infinite;
  }

  &::before {
    width: 8px;
    height: 8px;
    top: 10px;
    left: 0;
    animation-delay: 0s;
  }

  &::after {
    width: 6px;
    height: 6px;
    top: 25px;
    left: 15px;
    animation-delay: 1s;
  }
}

// Unread indicator
.unread-indicator {
  position: absolute;
  top: -10px;
  right: -10px;
  background: #ff4757;
  color: white;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 14px;
  border: 3px solid #0f0f23;
  animation: bounce 1s infinite;
}

// User info
.user-info {
  position: absolute;
  top: 20px;
  right: 20px;
  display: flex;
  align-items: center;
  gap: 10px;
  color: rgba(255, 255, 255, 0.7);
  font-size: 14px;
  z-index: 10;

  .logout-btn {
    background: none;
    border: none;
    color: rgba(255, 255, 255, 0.5);
    font-size: 20px;
    cursor: pointer;
    padding: 5px;
    border-radius: 50%;
    transition: all 0.2s ease;

    &:hover {
      color: #ff4757;
      background: rgba(255, 71, 87, 0.1);
    }
  }
}

// Modal styles - Futuristic Minimalism
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(8px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  animation: fadeIn 0.2s ease;
}

.modal {
  background: #fafafa;
  border-radius: 16px;
  box-shadow:
    0 0 40px rgba(255, 255, 255, 0.1),
    0 8px 32px rgba(0, 0, 0, 0.12),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  max-width: 400px;
  width: 90vw;
  max-height: 80vh;
  border: 1px solid rgba(255, 255, 255, 0.1);
  position: relative;
  animation: slideUp 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  flex-direction: column;

  // Login modal specific styling
  &.login-modal {
    max-width: 400px;

    .close-btn {
      position: absolute;
      top: 1rem;
      right: 1rem;
      background: none;
      border: none;
      font-size: 24px;
      color: #a4b0be;
      cursor: pointer;
      padding: 0;
      width: 30px;
      height: 30px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 50%;
      transition: all 0.2s ease;
      z-index: 10;

      &:hover {
        background: rgba(0, 0, 0, 0.1);
        color: #2f3542;
      }
    }
  }

  // Message modal specific styling
  &.message-modal {
    max-width: 480px;

    .modal-content {
      padding: 1.5rem;
      margin-right: 0; // Remove any right margin
    }

    .close-btn {
      position: absolute;
      top: 1rem;
      right: 1rem;
      background: none;
      border: none;
      font-size: 24px;
      color: #a4b0be;
      cursor: pointer;
      padding: 0;
      width: 30px;
      height: 30px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 50%;
      transition: all 0.2s ease;
      z-index: 10;

      &:hover {
        background: rgba(0, 0, 0, 0.1);
        color: #2f3542;
      }
    }
  }

  // Account settings modal specific styling
  &.account-settings-modal {
    max-width: 500px;

    .close-btn {
      position: absolute;
      top: 1rem;
      right: 1rem;
      background: none;
      border: none;
      font-size: 24px;
      color: #a4b0be;
      cursor: pointer;
      padding: 0;
      width: 30px;
      height: 30px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 50%;
      transition: all 0.2s ease;
      z-index: 10;

      &:hover {
        background: rgba(0, 0, 0, 0.1);
        color: #2f3542;
      }
    }
  }
}

.modal::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(45deg,
    rgba(255, 255, 255, 0.1) 0%,
    rgba(255, 255, 255, 0.05) 50%,
    rgba(255, 255, 255, 0.1) 100%);
  border-radius: 18px;
  z-index: -1;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);

  h2 {
    margin: 0;
    font-size: 20px;
    font-weight: 600;
    color: #2f3542;
  }

  .close-btn {
    background: none;
    border: none;
    font-size: 24px;
    color: #a4b0be;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.2s ease;

    &:hover {
      background: rgba(0, 0, 0, 0.1);
      color: #2f3542;
    }
  }
}

.modal-content {
  padding: 2rem;
  flex: 1;
  overflow-y: auto;

  // Custom scrollbar for modal content
  &::-webkit-scrollbar {
    width: 4px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.1);
    border-radius: 2px;
  }

  &::-webkit-scrollbar-thumb:hover {
    background: rgba(0, 0, 0, 0.2);
  }
}

// Form styles - Futuristic Minimalism
.form-group {
  margin-bottom: 2rem;
  position: relative;

  label {
    display: block;
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
    font-weight: 500;
    color: #6b7280;
    letter-spacing: 0.025em;
    text-transform: uppercase;
  }

  input, textarea {
    width: 100%;
    padding: 0.75rem 0;
    background: transparent;
    border: none;
    border-bottom: 1px solid #e5e7eb;
    color: #1f2937;
    font-size: 1rem;
    font-weight: 400;
    transition: all 0.3s ease;

    &:focus {
      outline: none;
      border-bottom-color: #1e40af;
    }

    &:disabled {
      background: transparent;
      color: #9ca3af;
      border-bottom-color: #f3f4f6;
    }

    &::placeholder {
      color: #9ca3af;
      font-weight: 300;
    }
  }

  textarea {
    resize: vertical;
    min-height: 120px;
    font-family: inherit;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    padding: 0.75rem;

    &:focus {
      border-color: #1e40af;
    }
  }
}

.char-count {
  text-align: right;
  font-size: 12px;
  color: #a4b0be;
  margin-top: 4px;
}

// Button styles - Futuristic Pills (for type selectors)
.type-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.875rem 1.5rem;
  border: none;
  border-radius: 50px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  min-width: 120px;
  gap: 0.5rem;
  background: #e5e7eb;
  color: #6b7280;

  &:hover {
    background: #d1d5db;
    transform: translateY(-1px);
  }

  &.active {
    background: #1e40af;
    color: white;

    &:hover {
      background: #1d4ed8;
      box-shadow: 0 4px 12px rgba(30, 64, 175, 0.3);
    }
  }

  &:active {
    transform: translateY(0);
  }

  .icon {
    width: 16px;
    height: 16px;
    stroke: currentColor;
    fill: none;
    stroke-width: 1.5;
  }
}

// Action buttons - Minimalist underlined style
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.75rem 0;
  border: none;
  background: transparent;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  gap: 0.5rem;
  position: relative;
  color: #374151;

  &::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 1px;
    background: currentColor;
    transition: width 0.3s ease;
  }

  &:hover::after {
    width: 100%;
  }

  &.btn-primary {
    color: #1e40af;

    &:disabled {
      color: #9ca3af;
      cursor: not-allowed;

      &::after {
        display: none;
      }
    }
  }

  &.btn-secondary {
    color: #6b7280;
  }

  .icon {
    width: 16px;
    height: 16px;
    stroke: currentColor;
    fill: none;
    stroke-width: 1.5;
  }
}

// Message type selector
.message-type-selector {
  display: flex;
  gap: 1rem;
  margin-bottom: 2rem;
  justify-content: center;
}

// Recipient selector
.recipient-selector {
  position: relative;
}

.selected-recipient {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px;
  background: rgba(55, 66, 250, 0.1);
  border: 1px solid #3742fa;
  border-radius: 6px;
  margin-top: 8px;

  .recipient-name {
    color: #3742fa;
    font-weight: 500;
    font-size: 14px;
  }

  .clear-recipient {
    background: none;
    border: none;
    color: #3742fa;
    font-size: 16px;
    cursor: pointer;
    padding: 0;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.2s ease;

    &:hover {
      background: rgba(55, 66, 250, 0.2);
    }
  }
}

.recipient-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 1px solid #e1e8ed;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  max-height: 200px;
  overflow-y: auto;
  z-index: 1000;
  margin-top: 4px;
}

.recipient-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  cursor: pointer;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  transition: background 0.2s ease;

  &:hover {
    background: rgba(55, 66, 250, 0.05);
  }

  &:last-child {
    border-bottom: none;
  }
}

.contact-info, .group-info {
  display: flex;
  flex-direction: column;
  flex: 1;

  .contact-name, .group-name {
    font-weight: 500;
    color: #2f3542;
    font-size: 14px;
    margin-bottom: 2px;
  }

  .contact-email, .group-members {
    font-size: 12px;
    color: #a4b0be;
  }
}

.contact-status, .group-status {
  display: flex;
  align-items: center;
  gap: 6px;

  .status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;

    &.online {
      background: #2ed573;
    }

    &.offline {
      background: #a4b0be;
    }

    &.active {
      background: #3742fa;
    }
  }

  .status-text {
    font-size: 12px;
    color: #a4b0be;
  }
}

.no-results {
  padding: 16px;
  text-align: center;
  color: #a4b0be;
  font-size: 14px;
  font-style: italic;
}

.message-actions {
  display: flex;
  gap: 2rem;
  margin-top: 2rem;
  justify-content: center;
}

// Error and info messages
.error-message {
  color: #ff4757;
  font-size: 14px;
  margin-top: 8px;
  padding: 8px 12px;
  background: rgba(255, 71, 87, 0.1);
  border-radius: 6px;
  border-left: 3px solid #ff4757;
}

.auth-info {
  margin-top: 20px;
  text-align: center;

  p {
    margin: 8px 0;
    font-size: 13px;
    color: #57606f;

    &.auto-submit-hint {
      font-style: italic;
      color: #a4b0be;
    }
  }
}

.send-hint {
  margin-top: 16px;
  text-align: center;

  p {
    font-size: 12px;
    color: #a4b0be;
    margin: 0;
  }
}

// Loading states
.loading-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  padding: 20px;

  .spinner {
    width: 20px;
    height: 20px;
    border: 2px solid #e1e8ed;
    border-top: 2px solid #3742fa;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }

  span {
    color: #57606f;
    font-size: 14px;
  }
}

// Messages list
.messages-list {
  max-height: 400px;
  overflow-y: auto;
  margin: -8px;
  padding: 8px;
}

.message-item {
  padding: 16px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);

  &:last-child {
    border-bottom: none;
  }

  .message-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;

    .sender {
      font-weight: 600;
      color: #2f3542;
      font-size: 14px;
    }

    .timestamp {
      font-size: 12px;
      color: #a4b0be;
    }
  }

  .message-content {
    color: #57606f;
    line-height: 1.5;
    word-wrap: break-word;
  }
}

.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: #a4b0be;

  p {
    margin: 8px 0;

    &.hint {
      font-size: 14px;
      font-style: italic;
    }
  }
}

// Context menu
.context-menu {
  position: fixed;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  padding: 8px 0;
  min-width: 180px;
  z-index: 2000;
  animation: contextMenuSlide 0.2s cubic-bezier(0.4, 0, 0.2, 1);

  .context-menu-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 16px;
    cursor: pointer;
    transition: background 0.2s ease;
    color: #2f3542;

    &:hover {
      background: rgba(55, 66, 250, 0.1);
    }

    &.logout-item {
      color: #ff4757;

      &:hover {
        background: rgba(255, 71, 87, 0.1);
      }
    }

    .menu-icon {
      font-size: 16px;
      width: 20px;
      text-align: center;
    }

    .menu-text {
      font-size: 14px;
      font-weight: 500;
    }
  }

  .context-menu-divider {
    height: 1px;
    background: rgba(0, 0, 0, 0.1);
    margin: 4px 0;
  }
}

// Account settings modal
.account-settings-modal {
  max-width: 500px;
  width: 90vw;
}

.avatar-section {
  text-align: center;
  margin-bottom: 32px;

  .avatar-container {
    display: inline-block;
  }

  .avatar-display {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    margin: 0 auto 16px;
    position: relative;
    overflow: hidden;
    border: 4px solid #e1e8ed;

    .avatar-image {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

    .avatar-placeholder {
      width: 100%;
      height: 100%;
      background: linear-gradient(135deg, #3742fa 0%, #2f3542 100%);
      display: flex;
      align-items: center;
      justify-content: center;

      .avatar-initials {
        font-size: 48px;
        font-weight: 600;
        color: white;
      }
    }
  }

  .avatar-actions {
    .avatar-upload-btn {
      display: inline-block;
      padding: 8px 16px;
      background: #3742fa;
      color: white;
      border-radius: 6px;
      font-size: 14px;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.2s ease;

      &:hover {
        background: #2f3542;
        transform: translateY(-1px);
      }
    }
  }
}

.profile-info {
  margin-bottom: 32px;

  .form-group {
    margin-bottom: 20px;

    label {
      display: block;
      margin-bottom: 8px;
      font-weight: 500;
      color: #2f3542;
      font-size: 14px;
    }

    .readonly-field {
      padding: 12px 16px;
      background: #f8f9fa;
      border: 2px solid #e1e8ed;
      border-radius: 8px;
      color: #57606f;

      span {
        display: block;

        &.field-note {
          font-size: 12px;
          color: #a4b0be;
          margin-top: 4px;
          font-style: italic;
        }
      }
    }
  }
}

.security-info {
  background: rgba(55, 66, 250, 0.05);
  border: 1px solid rgba(55, 66, 250, 0.2);
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 24px;

  h3 {
    margin: 0 0 16px 0;
    font-size: 16px;
    color: #2f3542;
    font-weight: 600;
  }

  .security-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;

    &:last-child {
      margin-bottom: 0;
    }

    .security-label {
      font-size: 14px;
      color: #57606f;
      font-weight: 500;
    }

    .security-value {
      font-size: 14px;
      color: #3742fa;
      font-weight: 500;
      text-align: right;
      flex: 1;
      margin-left: 16px;
    }
  }
}

.account-actions {
  text-align: center;
  padding-top: 16px;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
}

// Keyboard hints
.keyboard-hints {
  position: fixed;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  color: rgba(255, 255, 255, 0.4);
  font-size: 12px;
  z-index: 5;
}

// Animations
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.02); }
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
  40% { transform: translateY(-5px); }
  60% { transform: translateY(-3px); }
}

@keyframes windFlow {
  0% { transform: translateX(0) translateY(0) scale(1); opacity: 0.3; }
  50% { transform: translateX(20px) translateY(-10px) scale(0.8); opacity: 0.6; }
  100% { transform: translateX(40px) translateY(-20px) scale(0.5); opacity: 0; }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes contextMenuSlide {
  from {
    opacity: 0;
    transform: scale(0.95) translateY(-10px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

// Responsive design
@media (max-width: 768px) {
  .qsc-circle {
    width: 150px;
    height: 150px;
  }

  .modal {
    width: 95vw;
    margin: 20px;
  }

  .user-info {
    top: 15px;
    right: 15px;
    font-size: 12px;
  }
}

@media (max-width: 480px) {
  .qsc-circle {
    width: 120px;
    height: 120px;
  }

  .modal-content {
    padding: 20px;
  }

  .modal-header {
    padding: 16px 20px;
  }

  .context-menu {
    min-width: 160px;

    .context-menu-item {
      padding: 14px 16px; // Larger touch targets

      .menu-text {
        font-size: 16px; // Larger text for mobile
      }
    }
  }

  .avatar-section {
    .avatar-display {
      width: 100px;
      height: 100px;

      .avatar-initials {
        font-size: 40px;
      }
    }
  }

  .security-info {
    .security-item {
      flex-direction: column;
      align-items: flex-start;
      gap: 4px;

      .security-value {
        text-align: left;
        margin-left: 0;
      }
    }
  }
}
