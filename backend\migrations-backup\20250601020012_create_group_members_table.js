/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = function(knex) {
  return knex.schema.createTable('group_members', function(table) {
    table.uuid('id').primary();
    table.uuid('group_id').references('id').inTable('groups').onDelete('CASCADE');
    table.uuid('user_id').references('id').inTable('users').onDelete('CASCADE');
    table.enum('role', ['member', 'admin']).defaultTo('member');
    table.text('encrypted_master_key').notNullable(); // Master key encrypted with member's public key
    table.string('device_hash').notNullable(); // Member's device fingerprint
    table.timestamp('joined_at').defaultTo(knex.fn.now());
    table.timestamp('last_key_update');
    table.integer('failed_attempts').defaultTo(0);
    table.boolean('is_active').defaultTo(true);
    
    // Ensure unique membership
    table.unique(['group_id', 'user_id']);
    
    // Index for faster lookups
    table.index(['group_id', 'role']);
  });
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function(knex) {
  return knex.schema.dropTable('group_members');
}; 