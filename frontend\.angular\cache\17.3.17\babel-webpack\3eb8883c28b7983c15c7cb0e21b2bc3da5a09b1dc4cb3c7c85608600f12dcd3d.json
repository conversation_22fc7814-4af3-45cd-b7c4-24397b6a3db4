{"ast": null, "code": "// Placeholder WASM module for development\n// This will be replaced with actual LibOQS WASM implementation\n\nexport default function () {\n  console.warn('Using placeholder WASM module - LibOQS not available');\n  return Promise.resolve();\n}\n\n// Placeholder functions for development\nexport function generate_key_pair() {\n  throw new Error('WASM module not available - using fallback implementation');\n}\nexport function encrypt_message() {\n  throw new Error('WASM module not available - using fallback implementation');\n}\nexport function decrypt_message() {\n  throw new Error('WASM module not available - using fallback implementation');\n}\nexport function rotate_keys() {\n  throw new Error('WASM module not available - using fallback implementation');\n}", "map": {"version": 3, "names": ["console", "warn", "Promise", "resolve", "generate_key_pair", "Error", "encrypt_message", "decrypt_message", "rotate_keys"], "sources": ["C:/Users/<USER>/Projects/QSC1/frontend/src/assets/wasm/pqc_wasm.js"], "sourcesContent": ["// Placeholder WASM module for development\n// This will be replaced with actual LibOQS WASM implementation\n\nexport default function() {\n  console.warn('Using placeholder WASM module - LibOQS not available');\n  return Promise.resolve();\n}\n\n// Placeholder functions for development\nexport function generate_key_pair() {\n  throw new Error('WASM module not available - using fallback implementation');\n}\n\nexport function encrypt_message() {\n  throw new Error('WASM module not available - using fallback implementation');\n}\n\nexport function decrypt_message() {\n  throw new Error('WASM module not available - using fallback implementation');\n}\n\nexport function rotate_keys() {\n  throw new Error('WASM module not available - using fallback implementation');\n}\n"], "mappings": "AAAA;AACA;;AAEA,eAAe,YAAW;EACxBA,OAAO,CAACC,IAAI,CAAC,sDAAsD,CAAC;EACpE,OAAOC,OAAO,CAACC,OAAO,CAAC,CAAC;AAC1B;;AAEA;AACA,OAAO,SAASC,iBAAiBA,CAAA,EAAG;EAClC,MAAM,IAAIC,KAAK,CAAC,2DAA2D,CAAC;AAC9E;AAEA,OAAO,SAASC,eAAeA,CAAA,EAAG;EAChC,MAAM,IAAID,KAAK,CAAC,2DAA2D,CAAC;AAC9E;AAEA,OAAO,SAASE,eAAeA,CAAA,EAAG;EAChC,MAAM,IAAIF,KAAK,CAAC,2DAA2D,CAAC;AAC9E;AAEA,OAAO,SAASG,WAAWA,CAAA,EAAG;EAC5B,MAAM,IAAIH,KAAK,CAAC,2DAA2D,CAAC;AAC9E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}