/* You can add global styles to this file, and also import other style files */
:root {
  --primary-color: #007bff;
  --secondary-color: #6c757d;
  --success-color: #28a745;
  --danger-color: #dc3545;
  --warning-color: #ffc107;
  --info-color: #17a2b8;
  --light-color: #f8f9fa;
  --dark-color: #343a40;
  --body-bg: #f8f9fa;
  --body-color: #212529;
  --border-color: #dee2e6;
  --border-radius: 4px;
  --box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  --transition: all 0.2s ease;
}

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  background-color: var(--body-bg);
  color: var(--body-color);
  line-height: 1.5;
}

button {
  cursor: pointer;
  font-family: inherit;
  font-size: 1rem;
  padding: 0.5rem 1rem;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  background-color: white;
  color: var(--body-color);
  transition: var(--transition);
}

button:hover {
  background-color: var(--light-color);
}

button:disabled {
  cursor: not-allowed;
  opacity: 0.6;
}

input, textarea {
  font-family: inherit;
  font-size: 1rem;
  padding: 0.5rem;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  background-color: white;
  color: var(--body-color);
  transition: var(--transition);
}

input:focus, textarea:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(0,123,255,0.25);
}

.error-message {
  color: var(--danger-color);
  padding: 0.5rem;
  margin: 0.5rem 0;
  border: 1px solid var(--danger-color);
  border-radius: var(--border-radius);
  background-color: #f8d7da;
}

.success-message {
  color: var(--success-color);
  padding: 0.5rem;
  margin: 0.5rem 0;
  border: 1px solid var(--success-color);
  border-radius: var(--border-radius);
  background-color: #d4edda;
}

.warning-message {
  color: var(--warning-color);
  padding: 0.5rem;
  margin: 0.5rem 0;
  border: 1px solid var(--warning-color);
  border-radius: var(--border-radius);
  background-color: #fff3cd;
}

.info-message {
  color: var(--info-color);
  padding: 0.5rem;
  margin: 0.5rem 0;
  border: 1px solid var(--info-color);
  border-radius: var(--border-radius);
  background-color: #d1ecf1;
}
