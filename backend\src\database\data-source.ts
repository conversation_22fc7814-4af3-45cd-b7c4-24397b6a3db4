import { DataSource } from 'typeorm';
import { join } from 'path';
import { User } from '../users/entities/user.entity';
import { ChatRoom } from '../chat/entities/chat-room.entity';
import { Message } from '../chat/entities/message.entity';
import { Channel } from '../chat/entities/channel.entity';
import { Group } from '../chat/entities/group.entity';
import { GroupMember } from '../chat/entities/group-member.entity';
import { Invite } from '../chat/entities/invite.entity';
import { SessionKey } from '../chat/entities/session-key.entity';

export const dataSource = new DataSource({
  type: 'better-sqlite3',
  database: process.env.DB_PATH || join(process.cwd(), 'secure_chat.sqlite'),
  entities: [
    User,
    ChatRoom,
    Message,
    Channel,
    Group,
    GroupMember,
    Invite,
    SessionKey,
  ],
  migrations: [join(__dirname, '..', 'migrations', '*{.ts,.js}')],
  synchronize: process.env.NODE_ENV !== 'production',
  logging: process.env.NODE_ENV !== 'production',
  extra: {
    pragma: {
      key: process.env.DB_ENCRYPTION_KEY ? `'${process.env.DB_ENCRYPTION_KEY}'` : null,
      cipher: process.env.DB_ENCRYPTION_KEY ? 'aes-256-gcm' : null,
      kdf_iter: 64000,
      cipher_page_size: 4096,
      cipher_use_hmac: 'ON',
      journal_mode: 'WAL',
      synchronous: 'NORMAL',
      foreign_keys: 'ON',
    },
  },
});