import { 
  <PERSON><PERSON><PERSON>, 
  <PERSON>umn, 
  PrimaryGeneratedColumn, 
  CreateDateColumn, 
  UpdateDateColumn,
  ManyToOne,
  JoinColumn
} from 'typeorm';
import { User } from '../../users/entities/user.entity';
import { Group } from './group.entity';
import { IGroupMember } from '@qsc/shared';

@Entity('group_members')
export class GroupMember implements IGroupMember {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  groupId: string;

  @ManyToOne(() => Group, group => group.members)
  @JoinColumn({ name: 'group_id' })
  group: Group;

  @Column()
  userId: string;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'user_id' })
  user: User;

  @Column({
    type: 'varchar',
    enum: ['admin', 'member', 'viewer'],
    default: 'member'
  })
  role: 'admin' | 'member' | 'viewer';

  @CreateDateColumn()
  joinedAt: Date;

  @Column()
  publicKey: string;

  @Column()
  deviceHash: string;

  @Column({ default: true })
  isActive: boolean;
}
