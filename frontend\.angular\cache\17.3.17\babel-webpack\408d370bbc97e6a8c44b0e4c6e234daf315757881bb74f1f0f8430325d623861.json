{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Projects/QSC1/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"C:/Users/<USER>/Projects/QSC1/frontend/src/app/components/key-management/key-management.component.ts.css?ngResource!=!C:\\\\Users\\\\<USER>\\\\Projects\\\\QSC1\\\\frontend\\\\node_modules\\\\@ngtools\\\\webpack\\\\src\\\\loaders\\\\inline-resource.js?data=CiAgICAua2V5LW1hbmFnZW1lbnQgewogICAgICBwYWRkaW5nOiAyMHB4OwogICAgICBtYXgtd2lkdGg6IDgwMHB4OwogICAgICBtYXJnaW46IDAgYXV0bzsKICAgIH0KCiAgICAua2V5LXN0YXR1cyB7CiAgICAgIGJhY2tncm91bmQ6ICNmNWY1ZjU7CiAgICAgIHBhZGRpbmc6IDIwcHg7CiAgICAgIGJvcmRlci1yYWRpdXM6IDhweDsKICAgICAgbWFyZ2luLWJvdHRvbTogMjBweDsKICAgIH0KCiAgICAua2V5LWFjdGlvbnMgewogICAgICBkaXNwbGF5OiBmbGV4OwogICAgICBnYXA6IDEwcHg7CiAgICAgIG1hcmdpbi10b3A6IDE1cHg7CiAgICB9CgogICAgYnV0dG9uIHsKICAgICAgcGFkZGluZzogOHB4IDE2cHg7CiAgICAgIGJvcmRlcjogbm9uZTsKICAgICAgYm9yZGVyLXJhZGl1czogNHB4OwogICAgICBiYWNrZ3JvdW5kOiAjMDA3YmZmOwogICAgICBjb2xvcjogd2hpdGU7CiAgICAgIGN1cnNvcjogcG9pbnRlcjsKICAgICAgdHJhbnNpdGlvbjogYmFja2dyb3VuZCAwLjJzOwogICAgfQoKICAgIGJ1dHRvbjpkaXNhYmxlZCB7CiAgICAgIGJhY2tncm91bmQ6ICNjY2M7CiAgICAgIGN1cnNvcjogbm90LWFsbG93ZWQ7CiAgICB9CgogICAgYnV0dG9uOmhvdmVyOm5vdCg6ZGlzYWJsZWQpIHsKICAgICAgYmFja2dyb3VuZDogIzAwNTZiMzsKICAgIH0KCiAgICAua2V5LWltcG9ydCB7CiAgICAgIGJhY2tncm91bmQ6ICNmNWY1ZjU7CiAgICAgIHBhZGRpbmc6IDIwcHg7CiAgICAgIGJvcmRlci1yYWRpdXM6IDhweDsKICAgIH0KCiAgICAuaW1wb3J0LWZvcm0gewogICAgICBkaXNwbGF5OiBmbGV4OwogICAgICBnYXA6IDEwcHg7CiAgICAgIG1hcmdpbi10b3A6IDE1cHg7CiAgICB9CgogICAgaW5wdXRbdHlwZT0iZmlsZSJdIHsKICAgICAgZmxleDogMTsKICAgICAgcGFkZGluZzogOHB4OwogICAgICBib3JkZXI6IDFweCBzb2xpZCAjZGRkOwogICAgICBib3JkZXItcmFkaXVzOiA0cHg7CiAgICB9CgogICAgLmVycm9yLW1lc3NhZ2UgewogICAgICBjb2xvcjogI2RjMzU0NTsKICAgICAgbWFyZ2luLXRvcDogMTVweDsKICAgICAgcGFkZGluZzogMTBweDsKICAgICAgYmFja2dyb3VuZDogI2Y4ZDdkYTsKICAgICAgYm9yZGVyLXJhZGl1czogNHB4OwogICAgfQogIA%3D%3D!C:/Users/<USER>/Projects/QSC1/frontend/src/app/components/key-management/key-management.component.ts\";\nimport { Component } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { WasmService } from '../../services/wasm.service';\nimport { ErrorHandlingService } from '../../services/error-handling.service';\nlet KeyManagementComponent = class KeyManagementComponent {\n  constructor(wasmService, errorHandling) {\n    this.wasmService = wasmService;\n    this.errorHandling = errorHandling;\n    this.currentKeyPair = null;\n    this.isRotating = false;\n    this.isImporting = false;\n    this.selectedFile = null;\n    this.errorMessage = '';\n  }\n  ngOnInit() {\n    this.loadCurrentKeyPair();\n  }\n  ngOnDestroy() {\n    // Clean up any subscriptions or resources\n  }\n  loadCurrentKeyPair() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      try {\n        _this.currentKeyPair = _this.wasmService.getCurrentKeyPair();\n      } catch (error) {\n        _this.errorMessage = 'Failed to load current key pair';\n        yield _this.errorHandling.handleError(error instanceof Error ? error : new Error(_this.errorMessage), 'SECURITY');\n      }\n    })();\n  }\n  rotateKeys() {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      if (_this2.isRotating) return;\n      _this2.isRotating = true;\n      _this2.errorMessage = '';\n      try {\n        yield _this2.wasmService.rotateKeys();\n        yield _this2.loadCurrentKeyPair();\n      } catch (error) {\n        _this2.errorMessage = 'Failed to rotate keys';\n        yield _this2.errorHandling.handleError(error instanceof Error ? error : new Error(_this2.errorMessage), 'SECURITY');\n      } finally {\n        _this2.isRotating = false;\n      }\n    })();\n  }\n  onFileSelected(event) {\n    const input = event.target;\n    if (input.files?.length) {\n      this.selectedFile = input.files[0];\n    }\n  }\n  importKeys() {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      if (!_this3.selectedFile || _this3.isImporting) return;\n      _this3.isImporting = true;\n      _this3.errorMessage = '';\n      try {\n        const fileContent = yield _this3.selectedFile.text();\n        // This will be implemented when we have the WASM module\n        // await this.wasmService.importKeyPair(fileContent);\n        yield _this3.loadCurrentKeyPair();\n      } catch (error) {\n        _this3.errorMessage = 'Failed to import keys';\n        yield _this3.errorHandling.handleError(error instanceof Error ? error : new Error(_this3.errorMessage), 'SECURITY');\n      } finally {\n        _this3.isImporting = false;\n        _this3.selectedFile = null;\n      }\n    })();\n  }\n  exportPublicKey() {\n    var _this4 = this;\n    return _asyncToGenerator(function* () {\n      if (!_this4.currentKeyPair) return;\n      try {\n        const publicKey = _this4.currentKeyPair.public_key;\n        const blob = new Blob([publicKey], {\n          type: 'application/octet-stream'\n        });\n        const url = window.URL.createObjectURL(blob);\n        const a = document.createElement('a');\n        a.href = url;\n        a.download = `public-key-v${_this4.currentKeyPair.version}.key`;\n        document.body.appendChild(a);\n        a.click();\n        window.URL.revokeObjectURL(url);\n        document.body.removeChild(a);\n      } catch (error) {\n        _this4.errorMessage = 'Failed to export public key';\n        yield _this4.errorHandling.handleError(error instanceof Error ? error : new Error(_this4.errorMessage), 'SECURITY');\n      }\n    })();\n  }\n  static {\n    this.ctorParameters = () => [{\n      type: WasmService\n    }, {\n      type: ErrorHandlingService\n    }];\n  }\n};\nKeyManagementComponent = __decorate([Component({\n  selector: 'qs-key-management',\n  standalone: true,\n  imports: [CommonModule, FormsModule],\n  template: `\n    <div class=\"key-management\">\n      <h2>Key Management</h2>\n\n      <div class=\"key-status\" *ngIf=\"currentKeyPair\">\n        <h3>Current Key Pair</h3>\n        <p>Version: {{ currentKeyPair.version }}</p>\n        <p>Last Rotation: {{ currentKeyPair.timestamp | date:'medium' }}</p>\n\n        <div class=\"key-actions\">\n          <button (click)=\"rotateKeys()\" [disabled]=\"isRotating\">\n            {{ isRotating ? 'Rotating...' : 'Rotate Keys' }}\n          </button>\n          <button (click)=\"exportPublicKey()\">Export Public Key</button>\n        </div>\n      </div>\n\n      <div class=\"key-import\" *ngIf=\"!currentKeyPair\">\n        <h3>Import Key Pair</h3>\n        <div class=\"import-form\">\n          <input\n            type=\"file\"\n            accept=\".key\"\n            (change)=\"onFileSelected($event)\"\n            [disabled]=\"isImporting\"\n          />\n          <button (click)=\"importKeys()\" [disabled=\"!selectedFile || isImporting\">\n            {{ isImporting ? 'Importing...' : 'Import Keys' }}\n          </button>\n        </div>\n      </div>\n\n      <div class=\"error-message\" *ngIf=\"errorMessage\">\n        {{ errorMessage }}\n      </div>\n    </div>\n  `,\n  styles: [__NG_CLI_RESOURCE__0]\n})], KeyManagementComponent);\nexport { KeyManagementComponent };", "map": {"version": 3, "names": ["Component", "CommonModule", "FormsModule", "WasmService", "ErrorHandlingService", "KeyManagementComponent", "constructor", "wasmService", "errorHandling", "currentKeyPair", "isRotating", "isImporting", "selectedFile", "errorMessage", "ngOnInit", "loadCurrentKeyPair", "ngOnDestroy", "_this", "_asyncToGenerator", "getCurrentKeyPair", "error", "handleError", "Error", "rotateKeys", "_this2", "onFileSelected", "event", "input", "target", "files", "length", "importKeys", "_this3", "fileContent", "text", "exportPublicKey", "_this4", "public<PERSON>ey", "public_key", "blob", "Blob", "type", "url", "window", "URL", "createObjectURL", "a", "document", "createElement", "href", "download", "version", "body", "append<PERSON><PERSON><PERSON>", "click", "revokeObjectURL", "<PERSON><PERSON><PERSON><PERSON>", "__decorate", "selector", "standalone", "imports", "template"], "sources": ["C:\\Users\\<USER>\\Projects\\QSC1\\frontend\\src\\app\\components\\key-management\\key-management.component.ts"], "sourcesContent": ["import { Component, OnInit, OnDestroy } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { FormsModule } from '@angular/forms';\r\nimport { WasmService } from '../../services/wasm.service';\r\nimport { ErrorHandlingService } from '../../services/error-handling.service';\r\n\r\n@Component({\r\n  selector: 'qs-key-management',\r\n  standalone: true,\r\n  imports: [CommonModule, FormsModule],\r\n  template: `\r\n    <div class=\"key-management\">\r\n      <h2>Key Management</h2>\r\n\r\n      <div class=\"key-status\" *ngIf=\"currentKeyPair\">\r\n        <h3>Current Key Pair</h3>\r\n        <p>Version: {{ currentKeyPair.version }}</p>\r\n        <p>Last Rotation: {{ currentKeyPair.timestamp | date:'medium' }}</p>\r\n\r\n        <div class=\"key-actions\">\r\n          <button (click)=\"rotateKeys()\" [disabled]=\"isRotating\">\r\n            {{ isRotating ? 'Rotating...' : 'Rotate Keys' }}\r\n          </button>\r\n          <button (click)=\"exportPublicKey()\">Export Public Key</button>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"key-import\" *ngIf=\"!currentKeyPair\">\r\n        <h3>Import Key Pair</h3>\r\n        <div class=\"import-form\">\r\n          <input\r\n            type=\"file\"\r\n            accept=\".key\"\r\n            (change)=\"onFileSelected($event)\"\r\n            [disabled]=\"isImporting\"\r\n          />\r\n          <button (click)=\"importKeys()\" [disabled=\"!selectedFile || isImporting\">\r\n            {{ isImporting ? 'Importing...' : 'Import Keys' }}\r\n          </button>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"error-message\" *ngIf=\"errorMessage\">\r\n        {{ errorMessage }}\r\n      </div>\r\n    </div>\r\n  `,\r\n  styles: [`\r\n    .key-management {\r\n      padding: 20px;\r\n      max-width: 800px;\r\n      margin: 0 auto;\r\n    }\r\n\r\n    .key-status {\r\n      background: #f5f5f5;\r\n      padding: 20px;\r\n      border-radius: 8px;\r\n      margin-bottom: 20px;\r\n    }\r\n\r\n    .key-actions {\r\n      display: flex;\r\n      gap: 10px;\r\n      margin-top: 15px;\r\n    }\r\n\r\n    button {\r\n      padding: 8px 16px;\r\n      border: none;\r\n      border-radius: 4px;\r\n      background: #007bff;\r\n      color: white;\r\n      cursor: pointer;\r\n      transition: background 0.2s;\r\n    }\r\n\r\n    button:disabled {\r\n      background: #ccc;\r\n      cursor: not-allowed;\r\n    }\r\n\r\n    button:hover:not(:disabled) {\r\n      background: #0056b3;\r\n    }\r\n\r\n    .key-import {\r\n      background: #f5f5f5;\r\n      padding: 20px;\r\n      border-radius: 8px;\r\n    }\r\n\r\n    .import-form {\r\n      display: flex;\r\n      gap: 10px;\r\n      margin-top: 15px;\r\n    }\r\n\r\n    input[type=\"file\"] {\r\n      flex: 1;\r\n      padding: 8px;\r\n      border: 1px solid #ddd;\r\n      border-radius: 4px;\r\n    }\r\n\r\n    .error-message {\r\n      color: #dc3545;\r\n      margin-top: 15px;\r\n      padding: 10px;\r\n      background: #f8d7da;\r\n      border-radius: 4px;\r\n    }\r\n  `]\r\n})\r\nexport class KeyManagementComponent implements OnInit, OnDestroy {\r\n  currentKeyPair: any = null;\r\n  isRotating = false;\r\n  isImporting = false;\r\n  selectedFile: File | null = null;\r\n  errorMessage = '';\r\n\r\n  constructor(\r\n    private wasmService: WasmService,\r\n    private errorHandling: ErrorHandlingService\r\n  ) {}\r\n\r\n  ngOnInit(): void {\r\n    this.loadCurrentKeyPair();\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    // Clean up any subscriptions or resources\r\n  }\r\n\r\n  private async loadCurrentKeyPair(): Promise<void> {\r\n    try {\r\n      this.currentKeyPair = this.wasmService.getCurrentKeyPair();\r\n    } catch (error) {\r\n      this.errorMessage = 'Failed to load current key pair';\r\n      await this.errorHandling.handleError(\r\n        error instanceof Error ? error : new Error(this.errorMessage),\r\n        'SECURITY'\r\n      );\r\n    }\r\n  }\r\n\r\n  async rotateKeys(): Promise<void> {\r\n    if (this.isRotating) return;\r\n\r\n    this.isRotating = true;\r\n    this.errorMessage = '';\r\n\r\n    try {\r\n      await this.wasmService.rotateKeys();\r\n      await this.loadCurrentKeyPair();\r\n    } catch (error) {\r\n      this.errorMessage = 'Failed to rotate keys';\r\n      await this.errorHandling.handleError(\r\n        error instanceof Error ? error : new Error(this.errorMessage),\r\n        'SECURITY'\r\n      );\r\n    } finally {\r\n      this.isRotating = false;\r\n    }\r\n  }\r\n\r\n  onFileSelected(event: Event): void {\r\n    const input = event.target as HTMLInputElement;\r\n    if (input.files?.length) {\r\n      this.selectedFile = input.files[0];\r\n    }\r\n  }\r\n\r\n  async importKeys(): Promise<void> {\r\n    if (!this.selectedFile || this.isImporting) return;\r\n\r\n    this.isImporting = true;\r\n    this.errorMessage = '';\r\n\r\n    try {\r\n      const fileContent = await this.selectedFile.text();\r\n      // This will be implemented when we have the WASM module\r\n      // await this.wasmService.importKeyPair(fileContent);\r\n      await this.loadCurrentKeyPair();\r\n    } catch (error) {\r\n      this.errorMessage = 'Failed to import keys';\r\n      await this.errorHandling.handleError(\r\n        error instanceof Error ? error : new Error(this.errorMessage),\r\n        'SECURITY'\r\n      );\r\n    } finally {\r\n      this.isImporting = false;\r\n      this.selectedFile = null;\r\n    }\r\n  }\r\n\r\n  async exportPublicKey(): Promise<void> {\r\n    if (!this.currentKeyPair) return;\r\n\r\n    try {\r\n      const publicKey = this.currentKeyPair.public_key;\r\n      const blob = new Blob([publicKey], { type: 'application/octet-stream' });\r\n      const url = window.URL.createObjectURL(blob);\r\n      const a = document.createElement('a');\r\n      a.href = url;\r\n      a.download = `public-key-v${this.currentKeyPair.version}.key`;\r\n      document.body.appendChild(a);\r\n      a.click();\r\n      window.URL.revokeObjectURL(url);\r\n      document.body.removeChild(a);\r\n    } catch (error) {\r\n      this.errorMessage = 'Failed to export public key';\r\n      await this.errorHandling.handleError(\r\n        error instanceof Error ? error : new Error(this.errorMessage),\r\n        'SECURITY'\r\n      );\r\n    }\r\n  }\r\n}\r\n"], "mappings": ";;;AAAA,SAASA,SAAS,QAA2B,eAAe;AAC5D,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,WAAW,QAAQ,6BAA6B;AACzD,SAASC,oBAAoB,QAAQ,uCAAuC;AA8GrE,IAAMC,sBAAsB,GAA5B,MAAMA,sBAAsB;EAOjCC,YACUC,WAAwB,EACxBC,aAAmC;IADnC,KAAAD,WAAW,GAAXA,WAAW;IACX,KAAAC,aAAa,GAAbA,aAAa;IARvB,KAAAC,cAAc,GAAQ,IAAI;IAC1B,KAAAC,UAAU,GAAG,KAAK;IAClB,KAAAC,WAAW,GAAG,KAAK;IACnB,KAAAC,YAAY,GAAgB,IAAI;IAChC,KAAAC,YAAY,GAAG,EAAE;EAKd;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACC,kBAAkB,EAAE;EAC3B;EAEAC,WAAWA,CAAA;IACT;EAAA;EAGYD,kBAAkBA,CAAA;IAAA,IAAAE,KAAA;IAAA,OAAAC,iBAAA;MAC9B,IAAI;QACFD,KAAI,CAACR,cAAc,GAAGQ,KAAI,CAACV,WAAW,CAACY,iBAAiB,EAAE;OAC3D,CAAC,OAAOC,KAAK,EAAE;QACdH,KAAI,CAACJ,YAAY,GAAG,iCAAiC;QACrD,MAAMI,KAAI,CAACT,aAAa,CAACa,WAAW,CAClCD,KAAK,YAAYE,KAAK,GAAGF,KAAK,GAAG,IAAIE,KAAK,CAACL,KAAI,CAACJ,YAAY,CAAC,EAC7D,UAAU,CACX;;IACF;EACH;EAEMU,UAAUA,CAAA;IAAA,IAAAC,MAAA;IAAA,OAAAN,iBAAA;MACd,IAAIM,MAAI,CAACd,UAAU,EAAE;MAErBc,MAAI,CAACd,UAAU,GAAG,IAAI;MACtBc,MAAI,CAACX,YAAY,GAAG,EAAE;MAEtB,IAAI;QACF,MAAMW,MAAI,CAACjB,WAAW,CAACgB,UAAU,EAAE;QACnC,MAAMC,MAAI,CAACT,kBAAkB,EAAE;OAChC,CAAC,OAAOK,KAAK,EAAE;QACdI,MAAI,CAACX,YAAY,GAAG,uBAAuB;QAC3C,MAAMW,MAAI,CAAChB,aAAa,CAACa,WAAW,CAClCD,KAAK,YAAYE,KAAK,GAAGF,KAAK,GAAG,IAAIE,KAAK,CAACE,MAAI,CAACX,YAAY,CAAC,EAC7D,UAAU,CACX;OACF,SAAS;QACRW,MAAI,CAACd,UAAU,GAAG,KAAK;;IACxB;EACH;EAEAe,cAAcA,CAACC,KAAY;IACzB,MAAMC,KAAK,GAAGD,KAAK,CAACE,MAA0B;IAC9C,IAAID,KAAK,CAACE,KAAK,EAAEC,MAAM,EAAE;MACvB,IAAI,CAAClB,YAAY,GAAGe,KAAK,CAACE,KAAK,CAAC,CAAC,CAAC;;EAEtC;EAEME,UAAUA,CAAA;IAAA,IAAAC,MAAA;IAAA,OAAAd,iBAAA;MACd,IAAI,CAACc,MAAI,CAACpB,YAAY,IAAIoB,MAAI,CAACrB,WAAW,EAAE;MAE5CqB,MAAI,CAACrB,WAAW,GAAG,IAAI;MACvBqB,MAAI,CAACnB,YAAY,GAAG,EAAE;MAEtB,IAAI;QACF,MAAMoB,WAAW,SAASD,MAAI,CAACpB,YAAY,CAACsB,IAAI,EAAE;QAClD;QACA;QACA,MAAMF,MAAI,CAACjB,kBAAkB,EAAE;OAChC,CAAC,OAAOK,KAAK,EAAE;QACdY,MAAI,CAACnB,YAAY,GAAG,uBAAuB;QAC3C,MAAMmB,MAAI,CAACxB,aAAa,CAACa,WAAW,CAClCD,KAAK,YAAYE,KAAK,GAAGF,KAAK,GAAG,IAAIE,KAAK,CAACU,MAAI,CAACnB,YAAY,CAAC,EAC7D,UAAU,CACX;OACF,SAAS;QACRmB,MAAI,CAACrB,WAAW,GAAG,KAAK;QACxBqB,MAAI,CAACpB,YAAY,GAAG,IAAI;;IACzB;EACH;EAEMuB,eAAeA,CAAA;IAAA,IAAAC,MAAA;IAAA,OAAAlB,iBAAA;MACnB,IAAI,CAACkB,MAAI,CAAC3B,cAAc,EAAE;MAE1B,IAAI;QACF,MAAM4B,SAAS,GAAGD,MAAI,CAAC3B,cAAc,CAAC6B,UAAU;QAChD,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACH,SAAS,CAAC,EAAE;UAAEI,IAAI,EAAE;QAA0B,CAAE,CAAC;QACxE,MAAMC,GAAG,GAAGC,MAAM,CAACC,GAAG,CAACC,eAAe,CAACN,IAAI,CAAC;QAC5C,MAAMO,CAAC,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;QACrCF,CAAC,CAACG,IAAI,GAAGP,GAAG;QACZI,CAAC,CAACI,QAAQ,GAAG,eAAed,MAAI,CAAC3B,cAAc,CAAC0C,OAAO,MAAM;QAC7DJ,QAAQ,CAACK,IAAI,CAACC,WAAW,CAACP,CAAC,CAAC;QAC5BA,CAAC,CAACQ,KAAK,EAAE;QACTX,MAAM,CAACC,GAAG,CAACW,eAAe,CAACb,GAAG,CAAC;QAC/BK,QAAQ,CAACK,IAAI,CAACI,WAAW,CAACV,CAAC,CAAC;OAC7B,CAAC,OAAO1B,KAAK,EAAE;QACdgB,MAAI,CAACvB,YAAY,GAAG,6BAA6B;QACjD,MAAMuB,MAAI,CAAC5B,aAAa,CAACa,WAAW,CAClCD,KAAK,YAAYE,KAAK,GAAGF,KAAK,GAAG,IAAIE,KAAK,CAACc,MAAI,CAACvB,YAAY,CAAC,EAC7D,UAAU,CACX;;IACF;EACH;;;;;;;;;AAvGWR,sBAAsB,GAAAoD,UAAA,EA5GlCzD,SAAS,CAAC;EACT0D,QAAQ,EAAE,mBAAmB;EAC7BC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CAAC3D,YAAY,EAAEC,WAAW,CAAC;EACpC2D,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAoCT;;CAmEF,CAAC,C,EACWxD,sBAAsB,CAwGlC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}