
Quantum-Secure Chat (QSC) - Comprehensive Product Brief
Project Overview
Quantum-Secure Chat (QSC) is a privacy-first, text-only, invite-only chat application designed to provide unmatched security through quantum-resistant encryption. Built to counter future quantum computing threats, QSC leverages NIST-standardized post-quantum cryptography (PQC) algorithms, including CRYSTALS-Kyber (ML-KEM) and CRYSTALS-Dilithium (ML-DSA), to ensure robust end-to-end encryption, forward secrecy, and minimal metadata retention. With a minimalist design and Telegram-like functionality, QSC prioritizes security, simplicity, and performance for users requiring highly secure communication.

The project will be developed and tested by Lovable and Augment Code, adhering to strict security protocols, development standards, and quality assurance processes outlined in the provided context.

Product Vision
QSC aims to deliver a secure, text-only messaging platform that protects user privacy against current and future cryptographic threats, including quantum computing attacks. By combining quantum-resistant cryptography, a minimalist user interface, and a privacy-first architecture, QSC offers a seamless and secure communication experience for individuals and groups in high-stakes environments.

Core Features
Security Features
Quantum-Resistant Cryptography:
Implements NIST-standardized ML-KEM (Kyber768) for key exchange and ML-DSA (Dilithium3) for digital signatures.
Security level equivalent to AES-192 (NIST Level 3).
Triple-layer encryption: PQC, symmetric, and transport layers for maximum security.
End-to-End Encryption:
Ensures messages are only readable by intended recipients.
Forward secrecy prevents compromised keys from exposing past messages.
Minimal Metadata Retention:
Stores only user accounts and routes messages without retaining message content.
Messages are stored in local storage and deleted after reading for both sender and receiver.
Invite-Only Access:
Cryptographically signed, one-time-use invites for secure onboarding.
Admin-controlled user access to prevent unauthorized entry.
Secret Word System:
Minimum 4-character secret word (1 uppercase, 1 lowercase, 1 digit, 1 symbol).
Client-side hashing with argon2id and a maximum of 3 login attempts before auto-wipe.
Compromise Response:
Automatic sender notification on cryptographic failures.
Deletion of compromised message chains and forced re-authentication for affected accounts.
Encrypted File Sharing:
Supports sharing text-based links with hybrid encryption (no media, emojis, or attachments).
User Features
Minimalist Interface:
Single circle in the center of the page, color-coded for status:
Red: Guest (unauthenticated).
Blue: Logged in.
Green: Unread message.
No header, footer, or extraneous UI elements for reduced attack surface.
Authentication:
Pop-up modal for login with email and secret word (no submit button; auto-submits on valid input).
Messaging:
Real-time text messaging via WebSockets.
Group chats and channels with PQC-secured group keys.
Self-destructing messages that are immediately deleted from local storage after decryption and viewing.
RAM wiping after message viewing to prevent forensic recovery.
Text-Only Communication:
Supports plain text and links; no media, emojis, or attachments to minimize vulnerabilities.
Technical Architecture
Tech Stack
Frontend: Angular 16+ with WebAssembly (LibOQS for PQC operations).
Backend: NestJS with RESTful APIs and OpenAPI 3.0 specifications.
Database: Encrypted SQLite with SQLCipher (AES-256) and libsodium for key management.
Authentication: Dilithium-signed JWTs with one-time password (OTP) support.
Messaging: WebSockets with Noise Protocol and Kyber for secure real-time communication.
Deployment: Fly.io for scalable, secure hosting.

Compliance Requirements
Zero message persistence on servers
No metadata retention beyond routing necessities
Weekly dependency audits (npm/cargo)
30-day PQC key rotation

Message Handling
Triple-Layer Encryption:
PQC-secured key exchange
AES-256 message encryption
Transport layer security
Ephemeral Storage:
RAM-only during viewing
LocalStorage auto-wipe post-read
Chain deletion on compromise detection