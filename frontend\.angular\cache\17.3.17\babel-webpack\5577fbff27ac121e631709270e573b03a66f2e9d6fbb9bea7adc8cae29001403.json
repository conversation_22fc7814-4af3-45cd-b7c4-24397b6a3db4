{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./app.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./app.scss?ngResource\";\nimport { Component } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { RouterOutlet, Router } from '@angular/router';\nimport { AuthService } from './services/auth.service';\nlet App = class App {\n  constructor(authService, router) {\n    this.authService = authService;\n    this.router = router;\n    this.title = 'QSC';\n  }\n  ngOnInit() {\n    // Check authentication state and redirect accordingly\n    this.authService.authState$.subscribe(state => {\n      if (!state.isAuthenticated && !this.router.url.includes('/login')) {\n        this.router.navigate(['/login']);\n      }\n    });\n  }\n  static {\n    this.ctorParameters = () => [{\n      type: AuthService\n    }, {\n      type: Router\n    }];\n  }\n};\nApp = __decorate([Component({\n  selector: 'app-root',\n  standalone: true,\n  imports: [CommonModule, RouterOutlet],\n  template: __NG_CLI_RESOURCE__0,\n  styles: [__NG_CLI_RESOURCE__1]\n})], App);\nexport { App };", "map": {"version": 3, "names": ["Component", "CommonModule", "RouterOutlet", "Router", "AuthService", "App", "constructor", "authService", "router", "title", "ngOnInit", "authState$", "subscribe", "state", "isAuthenticated", "url", "includes", "navigate", "__decorate", "selector", "standalone", "imports", "template", "__NG_CLI_RESOURCE__0"], "sources": ["C:\\Users\\<USER>\\Projects\\QSC1\\frontend\\src\\app\\app.ts"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { RouterOutlet, Router } from '@angular/router';\r\nimport { AuthService } from './services/auth.service';\r\n\r\n@Component({\r\n  selector: 'app-root',\r\n  standalone: true,\r\n  imports: [CommonModule, RouterOutlet],\r\n  templateUrl: './app.html',\r\n  styleUrl: './app.scss'\r\n})\r\nexport class App implements OnInit {\r\n  public title = 'QSC';\r\n\r\n  constructor(\r\n    private authService: AuthService,\r\n    private router: Router\r\n  ) {}\r\n\r\n  ngOnInit(): void {\r\n    // Check authentication state and redirect accordingly\r\n    this.authService.authState$.subscribe(state => {\r\n      if (!state.isAuthenticated && !this.router.url.includes('/login')) {\r\n        this.router.navigate(['/login']);\r\n      }\r\n    });\r\n  }\r\n}\r\n"], "mappings": ";;;AAAA,SAASA,SAAS,QAAgB,eAAe;AACjD,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,YAAY,EAAEC,MAAM,QAAQ,iBAAiB;AACtD,SAASC,WAAW,QAAQ,yBAAyB;AAS9C,IAAMC,GAAG,GAAT,MAAMA,GAAG;EAGdC,YACUC,WAAwB,EACxBC,MAAc;IADd,KAAAD,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IAJT,KAAAC,KAAK,GAAG,KAAK;EAKjB;EAEHC,QAAQA,CAAA;IACN;IACA,IAAI,CAACH,WAAW,CAACI,UAAU,CAACC,SAAS,CAACC,KAAK,IAAG;MAC5C,IAAI,CAACA,KAAK,CAACC,eAAe,IAAI,CAAC,IAAI,CAACN,MAAM,CAACO,GAAG,CAACC,QAAQ,CAAC,QAAQ,CAAC,EAAE;QACjE,IAAI,CAACR,MAAM,CAACS,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;;IAEpC,CAAC,CAAC;EACJ;;;;;;;;;AAfWZ,GAAG,GAAAa,UAAA,EAPflB,SAAS,CAAC;EACTmB,QAAQ,EAAE,UAAU;EACpBC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CAACpB,YAAY,EAAEC,YAAY,CAAC;EACrCoB,QAAA,EAAAC,oBAAyB;;CAE1B,CAAC,C,EACWlB,GAAG,CAgBf", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}