{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Projects/QSC1/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { __decorate } from \"tslib\";\nimport { Injectable } from '@angular/core';\nimport { WasmService } from './wasm.service';\nimport { ErrorHandlingService } from './error-handling.service';\nlet SecureStorageService = class SecureStorageService {\n  constructor(wasmService, errorHandlingService) {\n    this.wasmService = wasmService;\n    this.errorHandlingService = errorHandlingService;\n    this.db = null;\n    this.DB_NAME = 'quantumshield_db';\n    this.DB_VERSION = 1;\n    this.initDatabase();\n  }\n  initDatabase() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      try {\n        const request = indexedDB.open(_this.DB_NAME, _this.DB_VERSION);\n        request.onerror = event => {\n          _this.errorHandlingService.handleError(new Error('Failed to open database'), 'STORAGE');\n        };\n        request.onupgradeneeded = event => {\n          const db = event.target.result;\n          if (!db.objectStoreNames.contains('notifications')) {\n            db.createObjectStore('notifications', {\n              keyPath: 'id'\n            });\n          }\n          if (!db.objectStoreNames.contains('chain')) {\n            db.createObjectStore('chain', {\n              keyPath: 'id'\n            });\n          }\n          if (!db.objectStoreNames.contains('deletions')) {\n            db.createObjectStore('deletions', {\n              keyPath: 'id'\n            });\n          }\n        };\n        request.onsuccess = event => {\n          _this.db = event.target.result;\n        };\n      } catch (error) {\n        _this.errorHandlingService.handleError(error, 'STORAGE');\n        throw error;\n      }\n    })();\n  }\n  storeSecurely(key, data, store) {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        if (!_this2.db) {\n          throw new Error('Database not initialized');\n        }\n        const encryptedData = yield _this2.wasmService.encryptMessage(JSON.stringify(data));\n        const entry = {\n          id: key,\n          data: encryptedData,\n          timestamp: new Date(),\n          version: 1\n        };\n        const transaction = _this2.db.transaction(store, 'readwrite');\n        const objectStore = transaction.objectStore(store);\n        yield objectStore.put(entry);\n      } catch (error) {\n        _this2.errorHandlingService.handleError(error, 'STORAGE');\n        throw error;\n      }\n    })();\n  }\n  retrieveSecurely(key, store) {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        if (!_this3.db) {\n          throw new Error('Database not initialized');\n        }\n        const transaction = _this3.db.transaction(store, 'readonly');\n        const objectStore = transaction.objectStore(store);\n        const request = objectStore.get(key);\n        return new Promise((resolve, reject) => {\n          request.onsuccess = /*#__PURE__*/_asyncToGenerator(function* () {\n            try {\n              const entry = request.result;\n              if (!entry) {\n                resolve(null);\n                return;\n              }\n              const decryptedData = yield _this3.wasmService.decryptMessage(entry.data);\n              resolve(JSON.parse(decryptedData));\n            } catch (error) {\n              reject(error);\n            }\n          });\n          request.onerror = () => {\n            reject(new Error('Failed to retrieve data'));\n          };\n        });\n      } catch (error) {\n        _this3.errorHandlingService.handleError(error, 'STORAGE');\n        throw error;\n      }\n    })();\n  }\n  deleteSecurely(key, store) {\n    var _this4 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        if (!_this4.db) {\n          throw new Error('Database not initialized');\n        }\n        const transaction = _this4.db.transaction(store, 'readwrite');\n        const objectStore = transaction.objectStore(store);\n        yield objectStore.delete(key);\n      } catch (error) {\n        _this4.errorHandlingService.handleError(error, 'STORAGE');\n        throw error;\n      }\n    })();\n  }\n  rotateKeys() {\n    var _this5 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        if (!_this5.db) {\n          throw new Error('Database not initialized');\n        }\n        const stores = ['notifications', 'chain', 'deletions'];\n        for (const store of stores) {\n          const transaction = _this5.db.transaction(store, 'readonly');\n          const objectStore = transaction.objectStore(store);\n          const request = objectStore.getAll();\n          const entries = yield new Promise((resolve, reject) => {\n            request.onsuccess = () => resolve(request.result);\n            request.onerror = () => reject(new Error('Failed to retrieve entries'));\n          });\n          for (const entry of entries) {\n            const decryptedData = yield _this5.wasmService.decryptMessage(entry.data);\n            yield _this5.storeSecurely(entry.id, JSON.parse(decryptedData), store);\n          }\n        }\n        yield _this5.wasmService.rotateKeys();\n      } catch (error) {\n        _this5.errorHandlingService.handleError(error, 'STORAGE');\n        throw error;\n      }\n    })();\n  }\n  wipeStorage() {\n    var _this6 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        if (!_this6.db) {\n          throw new Error('Database not initialized');\n        }\n        const stores = ['notifications', 'chain', 'deletions'];\n        for (const store of stores) {\n          const transaction = _this6.db.transaction(store, 'readwrite');\n          const objectStore = transaction.objectStore(store);\n          yield objectStore.clear();\n        }\n        _this6.wasmService.wipeMemory();\n      } catch (error) {\n        _this6.errorHandlingService.handleError(error, 'STORAGE');\n        throw error;\n      }\n    })();\n  }\n  static {\n    this.ctorParameters = () => [{\n      type: WasmService\n    }, {\n      type: ErrorHandlingService\n    }];\n  }\n};\nSecureStorageService = __decorate([Injectable({\n  providedIn: 'root'\n})], SecureStorageService);\nexport { SecureStorageService };", "map": {"version": 3, "names": ["Injectable", "WasmService", "ErrorHandlingService", "SecureStorageService", "constructor", "wasmService", "errorHandlingService", "db", "DB_NAME", "DB_VERSION", "initDatabase", "_this", "_asyncToGenerator", "request", "indexedDB", "open", "onerror", "event", "handleError", "Error", "onupgradeneeded", "target", "result", "objectStoreNames", "contains", "createObjectStore", "keyP<PERSON>", "onsuccess", "error", "storeSecurely", "key", "data", "store", "_this2", "encryptedData", "encryptMessage", "JSON", "stringify", "entry", "id", "timestamp", "Date", "version", "transaction", "objectStore", "put", "retrieveS<PERSON>urely", "_this3", "get", "Promise", "resolve", "reject", "decryptedData", "decryptMessage", "parse", "deleteSecurely", "_this4", "delete", "rotateKeys", "_this5", "stores", "getAll", "entries", "wipeStorage", "_this6", "clear", "wipeMemory", "__decorate", "providedIn"], "sources": ["C:\\Users\\<USER>\\Projects\\QSC1\\frontend\\src\\app\\services\\secure-storage.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { WasmService } from './wasm.service';\r\nimport { ErrorHandlingService } from './error-handling.service';\r\n\r\ninterface StorageEntry {\r\n  id: string;\r\n  data: any;\r\n  timestamp: Date;\r\n  version: number;\r\n}\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class SecureStorageService {\r\n  private db: IDBDatabase | null = null;\r\n  private readonly DB_NAME = 'quantumshield_db';\r\n  private readonly DB_VERSION = 1;\r\n\r\n  constructor(\r\n    private wasmService: WasmService,\r\n    private errorHandlingService: ErrorHandlingService\r\n  ) {\r\n    this.initDatabase();\r\n  }\r\n\r\n  private async initDatabase(): Promise<void> {\r\n    try {\r\n      const request = indexedDB.open(this.DB_NAME, this.DB_VERSION);\r\n\r\n      request.onerror = (event) => {\r\n        this.errorHandlingService.handleError(\r\n          new Error('Failed to open database'),\r\n          'STORAGE'\r\n        );\r\n      };\r\n\r\n      request.onupgradeneeded = (event) => {\r\n        const db = (event.target as IDBOpenDBRequest).result;\r\n        if (!db.objectStoreNames.contains('notifications')) {\r\n          db.createObjectStore('notifications', { keyPath: 'id' });\r\n        }\r\n        if (!db.objectStoreNames.contains('chain')) {\r\n          db.createObjectStore('chain', { keyPath: 'id' });\r\n        }\r\n        if (!db.objectStoreNames.contains('deletions')) {\r\n          db.createObjectStore('deletions', { keyPath: 'id' });\r\n        }\r\n      };\r\n\r\n      request.onsuccess = (event) => {\r\n        this.db = (event.target as IDBOpenDBRequest).result;\r\n      };\r\n    } catch (error) {\r\n      this.errorHandlingService.handleError(error as Error, 'STORAGE');\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  async storeSecurely(key: string, data: any, store: string): Promise<void> {\r\n    try {\r\n      if (!this.db) {\r\n        throw new Error('Database not initialized');\r\n      }\r\n\r\n      const encryptedData = await this.wasmService.encryptMessage(JSON.stringify(data));\r\n      const entry: StorageEntry = {\r\n        id: key,\r\n        data: encryptedData,\r\n        timestamp: new Date(),\r\n        version: 1\r\n      };\r\n\r\n      const transaction = this.db.transaction(store, 'readwrite');\r\n      const objectStore = transaction.objectStore(store);\r\n      await objectStore.put(entry);\r\n    } catch (error) {\r\n      this.errorHandlingService.handleError(error as Error, 'STORAGE');\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  async retrieveSecurely(key: string, store: string): Promise<any> {\r\n    try {\r\n      if (!this.db) {\r\n        throw new Error('Database not initialized');\r\n      }\r\n\r\n      const transaction = this.db.transaction(store, 'readonly');\r\n      const objectStore = transaction.objectStore(store);\r\n      const request = objectStore.get(key);\r\n\r\n      return new Promise((resolve, reject) => {\r\n        request.onsuccess = async () => {\r\n          try {\r\n            const entry = request.result as StorageEntry;\r\n            if (!entry) {\r\n              resolve(null);\r\n              return;\r\n            }\r\n\r\n            const decryptedData = await this.wasmService.decryptMessage(entry.data);\r\n            resolve(JSON.parse(decryptedData));\r\n          } catch (error) {\r\n            reject(error);\r\n          }\r\n        };\r\n\r\n        request.onerror = () => {\r\n          reject(new Error('Failed to retrieve data'));\r\n        };\r\n      });\r\n    } catch (error) {\r\n      this.errorHandlingService.handleError(error as Error, 'STORAGE');\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  async deleteSecurely(key: string, store: string): Promise<void> {\r\n    try {\r\n      if (!this.db) {\r\n        throw new Error('Database not initialized');\r\n      }\r\n\r\n      const transaction = this.db.transaction(store, 'readwrite');\r\n      const objectStore = transaction.objectStore(store);\r\n      await objectStore.delete(key);\r\n    } catch (error) {\r\n      this.errorHandlingService.handleError(error as Error, 'STORAGE');\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  async rotateKeys(): Promise<void> {\r\n    try {\r\n      if (!this.db) {\r\n        throw new Error('Database not initialized');\r\n      }\r\n\r\n      const stores = ['notifications', 'chain', 'deletions'];\r\n      for (const store of stores) {\r\n        const transaction = this.db.transaction(store, 'readonly');\r\n        const objectStore = transaction.objectStore(store);\r\n        const request = objectStore.getAll();\r\n\r\n        const entries = await new Promise<StorageEntry[]>((resolve, reject) => {\r\n          request.onsuccess = () => resolve(request.result);\r\n          request.onerror = () => reject(new Error('Failed to retrieve entries'));\r\n        });\r\n\r\n        for (const entry of entries) {\r\n          const decryptedData = await this.wasmService.decryptMessage(entry.data);\r\n          await this.storeSecurely(entry.id, JSON.parse(decryptedData), store);\r\n        }\r\n      }\r\n\r\n      await this.wasmService.rotateKeys();\r\n    } catch (error) {\r\n      this.errorHandlingService.handleError(error as Error, 'STORAGE');\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  async wipeStorage(): Promise<void> {\r\n    try {\r\n      if (!this.db) {\r\n        throw new Error('Database not initialized');\r\n      }\r\n\r\n      const stores = ['notifications', 'chain', 'deletions'];\r\n      for (const store of stores) {\r\n        const transaction = this.db.transaction(store, 'readwrite');\r\n        const objectStore = transaction.objectStore(store);\r\n        await objectStore.clear();\r\n      }\r\n\r\n      this.wasmService.wipeMemory();\r\n    } catch (error) {\r\n      this.errorHandlingService.handleError(error as Error, 'STORAGE');\r\n      throw error;\r\n    }\r\n  }\r\n}\r\n"], "mappings": ";;AAAA,SAASA,UAAU,QAAQ,eAAe;AAC1C,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,oBAAoB,QAAQ,0BAA0B;AAYxD,IAAMC,oBAAoB,GAA1B,MAAMA,oBAAoB;EAK/BC,YACUC,WAAwB,EACxBC,oBAA0C;IAD1C,KAAAD,WAAW,GAAXA,WAAW;IACX,KAAAC,oBAAoB,GAApBA,oBAAoB;IANtB,KAAAC,EAAE,GAAuB,IAAI;IACpB,KAAAC,OAAO,GAAG,kBAAkB;IAC5B,KAAAC,UAAU,GAAG,CAAC;IAM7B,IAAI,CAACC,YAAY,EAAE;EACrB;EAEcA,YAAYA,CAAA;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MACxB,IAAI;QACF,MAAMC,OAAO,GAAGC,SAAS,CAACC,IAAI,CAACJ,KAAI,CAACH,OAAO,EAAEG,KAAI,CAACF,UAAU,CAAC;QAE7DI,OAAO,CAACG,OAAO,GAAIC,KAAK,IAAI;UAC1BN,KAAI,CAACL,oBAAoB,CAACY,WAAW,CACnC,IAAIC,KAAK,CAAC,yBAAyB,CAAC,EACpC,SAAS,CACV;QACH,CAAC;QAEDN,OAAO,CAACO,eAAe,GAAIH,KAAK,IAAI;UAClC,MAAMV,EAAE,GAAIU,KAAK,CAACI,MAA2B,CAACC,MAAM;UACpD,IAAI,CAACf,EAAE,CAACgB,gBAAgB,CAACC,QAAQ,CAAC,eAAe,CAAC,EAAE;YAClDjB,EAAE,CAACkB,iBAAiB,CAAC,eAAe,EAAE;cAAEC,OAAO,EAAE;YAAI,CAAE,CAAC;;UAE1D,IAAI,CAACnB,EAAE,CAACgB,gBAAgB,CAACC,QAAQ,CAAC,OAAO,CAAC,EAAE;YAC1CjB,EAAE,CAACkB,iBAAiB,CAAC,OAAO,EAAE;cAAEC,OAAO,EAAE;YAAI,CAAE,CAAC;;UAElD,IAAI,CAACnB,EAAE,CAACgB,gBAAgB,CAACC,QAAQ,CAAC,WAAW,CAAC,EAAE;YAC9CjB,EAAE,CAACkB,iBAAiB,CAAC,WAAW,EAAE;cAAEC,OAAO,EAAE;YAAI,CAAE,CAAC;;QAExD,CAAC;QAEDb,OAAO,CAACc,SAAS,GAAIV,KAAK,IAAI;UAC5BN,KAAI,CAACJ,EAAE,GAAIU,KAAK,CAACI,MAA2B,CAACC,MAAM;QACrD,CAAC;OACF,CAAC,OAAOM,KAAK,EAAE;QACdjB,KAAI,CAACL,oBAAoB,CAACY,WAAW,CAACU,KAAc,EAAE,SAAS,CAAC;QAChE,MAAMA,KAAK;;IACZ;EACH;EAEMC,aAAaA,CAACC,GAAW,EAAEC,IAAS,EAAEC,KAAa;IAAA,IAAAC,MAAA;IAAA,OAAArB,iBAAA;MACvD,IAAI;QACF,IAAI,CAACqB,MAAI,CAAC1B,EAAE,EAAE;UACZ,MAAM,IAAIY,KAAK,CAAC,0BAA0B,CAAC;;QAG7C,MAAMe,aAAa,SAASD,MAAI,CAAC5B,WAAW,CAAC8B,cAAc,CAACC,IAAI,CAACC,SAAS,CAACN,IAAI,CAAC,CAAC;QACjF,MAAMO,KAAK,GAAiB;UAC1BC,EAAE,EAAET,GAAG;UACPC,IAAI,EAAEG,aAAa;UACnBM,SAAS,EAAE,IAAIC,IAAI,EAAE;UACrBC,OAAO,EAAE;SACV;QAED,MAAMC,WAAW,GAAGV,MAAI,CAAC1B,EAAE,CAACoC,WAAW,CAACX,KAAK,EAAE,WAAW,CAAC;QAC3D,MAAMY,WAAW,GAAGD,WAAW,CAACC,WAAW,CAACZ,KAAK,CAAC;QAClD,MAAMY,WAAW,CAACC,GAAG,CAACP,KAAK,CAAC;OAC7B,CAAC,OAAOV,KAAK,EAAE;QACdK,MAAI,CAAC3B,oBAAoB,CAACY,WAAW,CAACU,KAAc,EAAE,SAAS,CAAC;QAChE,MAAMA,KAAK;;IACZ;EACH;EAEMkB,gBAAgBA,CAAChB,GAAW,EAAEE,KAAa;IAAA,IAAAe,MAAA;IAAA,OAAAnC,iBAAA;MAC/C,IAAI;QACF,IAAI,CAACmC,MAAI,CAACxC,EAAE,EAAE;UACZ,MAAM,IAAIY,KAAK,CAAC,0BAA0B,CAAC;;QAG7C,MAAMwB,WAAW,GAAGI,MAAI,CAACxC,EAAE,CAACoC,WAAW,CAACX,KAAK,EAAE,UAAU,CAAC;QAC1D,MAAMY,WAAW,GAAGD,WAAW,CAACC,WAAW,CAACZ,KAAK,CAAC;QAClD,MAAMnB,OAAO,GAAG+B,WAAW,CAACI,GAAG,CAAClB,GAAG,CAAC;QAEpC,OAAO,IAAImB,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAI;UACrCtC,OAAO,CAACc,SAAS,gBAAAf,iBAAA,CAAG,aAAW;YAC7B,IAAI;cACF,MAAM0B,KAAK,GAAGzB,OAAO,CAACS,MAAsB;cAC5C,IAAI,CAACgB,KAAK,EAAE;gBACVY,OAAO,CAAC,IAAI,CAAC;gBACb;;cAGF,MAAME,aAAa,SAASL,MAAI,CAAC1C,WAAW,CAACgD,cAAc,CAACf,KAAK,CAACP,IAAI,CAAC;cACvEmB,OAAO,CAACd,IAAI,CAACkB,KAAK,CAACF,aAAa,CAAC,CAAC;aACnC,CAAC,OAAOxB,KAAK,EAAE;cACduB,MAAM,CAACvB,KAAK,CAAC;;UAEjB,CAAC;UAEDf,OAAO,CAACG,OAAO,GAAG,MAAK;YACrBmC,MAAM,CAAC,IAAIhC,KAAK,CAAC,yBAAyB,CAAC,CAAC;UAC9C,CAAC;QACH,CAAC,CAAC;OACH,CAAC,OAAOS,KAAK,EAAE;QACdmB,MAAI,CAACzC,oBAAoB,CAACY,WAAW,CAACU,KAAc,EAAE,SAAS,CAAC;QAChE,MAAMA,KAAK;;IACZ;EACH;EAEM2B,cAAcA,CAACzB,GAAW,EAAEE,KAAa;IAAA,IAAAwB,MAAA;IAAA,OAAA5C,iBAAA;MAC7C,IAAI;QACF,IAAI,CAAC4C,MAAI,CAACjD,EAAE,EAAE;UACZ,MAAM,IAAIY,KAAK,CAAC,0BAA0B,CAAC;;QAG7C,MAAMwB,WAAW,GAAGa,MAAI,CAACjD,EAAE,CAACoC,WAAW,CAACX,KAAK,EAAE,WAAW,CAAC;QAC3D,MAAMY,WAAW,GAAGD,WAAW,CAACC,WAAW,CAACZ,KAAK,CAAC;QAClD,MAAMY,WAAW,CAACa,MAAM,CAAC3B,GAAG,CAAC;OAC9B,CAAC,OAAOF,KAAK,EAAE;QACd4B,MAAI,CAAClD,oBAAoB,CAACY,WAAW,CAACU,KAAc,EAAE,SAAS,CAAC;QAChE,MAAMA,KAAK;;IACZ;EACH;EAEM8B,UAAUA,CAAA;IAAA,IAAAC,MAAA;IAAA,OAAA/C,iBAAA;MACd,IAAI;QACF,IAAI,CAAC+C,MAAI,CAACpD,EAAE,EAAE;UACZ,MAAM,IAAIY,KAAK,CAAC,0BAA0B,CAAC;;QAG7C,MAAMyC,MAAM,GAAG,CAAC,eAAe,EAAE,OAAO,EAAE,WAAW,CAAC;QACtD,KAAK,MAAM5B,KAAK,IAAI4B,MAAM,EAAE;UAC1B,MAAMjB,WAAW,GAAGgB,MAAI,CAACpD,EAAE,CAACoC,WAAW,CAACX,KAAK,EAAE,UAAU,CAAC;UAC1D,MAAMY,WAAW,GAAGD,WAAW,CAACC,WAAW,CAACZ,KAAK,CAAC;UAClD,MAAMnB,OAAO,GAAG+B,WAAW,CAACiB,MAAM,EAAE;UAEpC,MAAMC,OAAO,SAAS,IAAIb,OAAO,CAAiB,CAACC,OAAO,EAAEC,MAAM,KAAI;YACpEtC,OAAO,CAACc,SAAS,GAAG,MAAMuB,OAAO,CAACrC,OAAO,CAACS,MAAM,CAAC;YACjDT,OAAO,CAACG,OAAO,GAAG,MAAMmC,MAAM,CAAC,IAAIhC,KAAK,CAAC,4BAA4B,CAAC,CAAC;UACzE,CAAC,CAAC;UAEF,KAAK,MAAMmB,KAAK,IAAIwB,OAAO,EAAE;YAC3B,MAAMV,aAAa,SAASO,MAAI,CAACtD,WAAW,CAACgD,cAAc,CAACf,KAAK,CAACP,IAAI,CAAC;YACvE,MAAM4B,MAAI,CAAC9B,aAAa,CAACS,KAAK,CAACC,EAAE,EAAEH,IAAI,CAACkB,KAAK,CAACF,aAAa,CAAC,EAAEpB,KAAK,CAAC;;;QAIxE,MAAM2B,MAAI,CAACtD,WAAW,CAACqD,UAAU,EAAE;OACpC,CAAC,OAAO9B,KAAK,EAAE;QACd+B,MAAI,CAACrD,oBAAoB,CAACY,WAAW,CAACU,KAAc,EAAE,SAAS,CAAC;QAChE,MAAMA,KAAK;;IACZ;EACH;EAEMmC,WAAWA,CAAA;IAAA,IAAAC,MAAA;IAAA,OAAApD,iBAAA;MACf,IAAI;QACF,IAAI,CAACoD,MAAI,CAACzD,EAAE,EAAE;UACZ,MAAM,IAAIY,KAAK,CAAC,0BAA0B,CAAC;;QAG7C,MAAMyC,MAAM,GAAG,CAAC,eAAe,EAAE,OAAO,EAAE,WAAW,CAAC;QACtD,KAAK,MAAM5B,KAAK,IAAI4B,MAAM,EAAE;UAC1B,MAAMjB,WAAW,GAAGqB,MAAI,CAACzD,EAAE,CAACoC,WAAW,CAACX,KAAK,EAAE,WAAW,CAAC;UAC3D,MAAMY,WAAW,GAAGD,WAAW,CAACC,WAAW,CAACZ,KAAK,CAAC;UAClD,MAAMY,WAAW,CAACqB,KAAK,EAAE;;QAG3BD,MAAI,CAAC3D,WAAW,CAAC6D,UAAU,EAAE;OAC9B,CAAC,OAAOtC,KAAK,EAAE;QACdoC,MAAI,CAAC1D,oBAAoB,CAACY,WAAW,CAACU,KAAc,EAAE,SAAS,CAAC;QAChE,MAAMA,KAAK;;IACZ;EACH;;;;;;;;;AAvKWzB,oBAAoB,GAAAgE,UAAA,EAHhCnE,UAAU,CAAC;EACVoE,UAAU,EAAE;CACb,CAAC,C,EACWjE,oBAAoB,CAwKhC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}