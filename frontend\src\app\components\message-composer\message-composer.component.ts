import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { ApiService } from '../../services/api.service';
import { MessageRequest } from '../../types/api.types';

@Component({
  selector: 'app-message-composer',
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './message-composer.component.html',
  styleUrl: './message-composer.component.scss'
})
export class MessageComposerComponent implements OnInit {
  message: MessageRequest = {
    recipientId: '',
    content: '',
    expiresIn: 24 * 60 * 60 * 1000 // 24 hours default
  };

  isLoading = false;
  errorMessage = '';
  successMessage = '';
  characterCount = 0;
  maxCharacters = 1000;

  expirationOptions = [
    { label: '1 Hour', value: 60 * 60 * 1000 },
    { label: '6 Hours', value: 6 * 60 * 60 * 1000 },
    { label: '24 Hours', value: 24 * 60 * 60 * 1000 },
    { label: '7 Days', value: 7 * 24 * 60 * 60 * 1000 },
    { label: 'Never', value: null }
  ];

  constructor(
    private apiService: ApiService,
    private router: Router
  ) {}

  ngOnInit(): void {
    // Focus on content area when component loads
    setTimeout(() => {
      const contentElement = document.getElementById('message-content');
      if (contentElement) {
        contentElement.focus();
      }
    }, 100);
  }

  onContentChange(): void {
    this.characterCount = this.message.content.length;
    this.errorMessage = '';
  }

  onSend(): void {
    if (!this.validateMessage()) {
      return;
    }

    this.isLoading = true;
    this.errorMessage = '';
    this.successMessage = '';

    this.apiService.post('/chat/messages', this.message).subscribe({
      next: (response) => {
        this.isLoading = false;
        this.successMessage = 'Message sent successfully!';
        
        // Clear form after successful send
        setTimeout(() => {
          this.resetForm();
          this.router.navigate(['/main']);
        }, 1500);
      },
      error: (error) => {
        this.isLoading = false;
        this.errorMessage = error.message || 'Failed to send message. Please try again.';
      }
    });
  }

  private validateMessage(): boolean {
    if (!this.message.recipientId.trim()) {
      this.errorMessage = 'Please enter a recipient ID';
      return false;
    }

    if (!this.message.content.trim()) {
      this.errorMessage = 'Please enter a message';
      return false;
    }

    if (this.message.content.length > this.maxCharacters) {
      this.errorMessage = `Message is too long (${this.message.content.length}/${this.maxCharacters} characters)`;
      return false;
    }

    return true;
  }

  private resetForm(): void {
    this.message = {
      recipientId: '',
      content: '',
      expiresIn: 24 * 60 * 60 * 1000
    };
    this.characterCount = 0;
    this.errorMessage = '';
    this.successMessage = '';
  }

  onCancel(): void {
    this.router.navigate(['/main']);
  }

  onKeyDown(event: KeyboardEvent): void {
    // Ctrl/Cmd + Enter to send
    if ((event.ctrlKey || event.metaKey) && event.key === 'Enter') {
      event.preventDefault();
      this.onSend();
    }
    
    // Escape to cancel
    if (event.key === 'Escape') {
      this.onCancel();
    }
  }
}
