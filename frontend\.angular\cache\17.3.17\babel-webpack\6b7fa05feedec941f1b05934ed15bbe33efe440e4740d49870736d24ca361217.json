{"ast": null, "code": "import { inject } from '@angular/core';\nimport { catchError, switchMap, throwError } from 'rxjs';\nimport { SecureTokenService } from '../services/secure-token.service';\nimport { Router } from '@angular/router';\nimport { AuthService } from '../services/auth.service';\nexport const authInterceptor = (req, next) => {\n  const secureTokenService = inject(SecureTokenService);\n  const router = inject(Router);\n  const authService = inject(AuthService);\n  // Skip auth for login and public endpoints\n  const skipAuth = req.url.includes('/auth/login') || req.url.includes('/auth/register') || req.url.includes('/auth/invite') || req.url.includes('/public/') || req.url.includes('/health');\n  if (skipAuth) {\n    return next(req);\n  }\n  const token = secureTokenService.getToken();\n  if (!token) {\n    // No token available, redirect to login\n    router.navigate(['/login']);\n    return throwError(() => new Error('No authentication token available'));\n  }\n  if (!secureTokenService.isTokenValid()) {\n    // Token is invalid, try to refresh\n    const refreshToken = secureTokenService.getRefreshToken();\n    if (refreshToken) {\n      return authService.refreshToken(refreshToken).pipe(switchMap(response => {\n        // Update tokens\n        secureTokenService.setTokens({\n          token: response.accessToken,\n          refreshToken: response.refreshToken,\n          expiresAt: Date.now() + response.expiresIn * 1000,\n          tokenType: response.tokenType\n        });\n        // Retry the original request with new token\n        const authReq = req.clone({\n          setHeaders: {\n            Authorization: `Bearer ${response.accessToken}`,\n            'Content-Type': 'application/json',\n            'X-Requested-With': 'XMLHttpRequest'\n          }\n        });\n        return next(authReq);\n      }), catchError(error => {\n        // Refresh failed, logout user\n        secureTokenService.clearTokens();\n        router.navigate(['/login']);\n        return throwError(() => error);\n      }));\n    } else {\n      secureTokenService.clearTokens();\n      router.navigate(['/login']);\n      return throwError(() => new Error('Token expired'));\n    }\n  }\n  // Clone the request and add the authorization header\n  const authReq = req.clone({\n    setHeaders: {\n      Authorization: `Bearer ${token}`,\n      'Content-Type': 'application/json',\n      'X-Requested-With': 'XMLHttpRequest'\n    }\n  });\n  return next(authReq).pipe(catchError(error => {\n    // Handle 401 Unauthorized responses\n    if (error.status === 401) {\n      secureTokenService.clearTokens();\n      router.navigate(['/login']);\n    }\n    // Handle 403 Forbidden responses (CSRF issues)\n    if (error.status === 403) {\n      console.error('CSRF token validation failed');\n    }\n    return throwError(() => error);\n  }));\n};", "map": {"version": 3, "names": ["inject", "catchError", "switchMap", "throwError", "SecureTokenService", "Router", "AuthService", "authInterceptor", "req", "next", "secureTokenService", "router", "authService", "<PERSON><PERSON><PERSON>", "url", "includes", "token", "getToken", "navigate", "Error", "isTokenValid", "refreshToken", "getRefreshToken", "pipe", "response", "setTokens", "accessToken", "expiresAt", "Date", "now", "expiresIn", "tokenType", "authReq", "clone", "setHeaders", "Authorization", "error", "clearTokens", "status", "console"], "sources": ["D:\\TCL1\\Projects\\Projects\\QSC1\\frontend\\src\\app\\interceptors\\auth.interceptor.ts"], "sourcesContent": ["import { HttpInterceptorFn, HttpRequest, HttpHandlerFn } from '@angular/common/http';\nimport { inject } from '@angular/core';\nimport { catchError, switchMap, throwError } from 'rxjs';\nimport { SecureTokenService } from '../services/secure-token.service';\nimport { Router } from '@angular/router';\nimport { AuthService } from '../services/auth.service';\n\nexport const authInterceptor: HttpInterceptorFn = (req: HttpRequest<unknown>, next: HttpHandlerFn) => {\n  const secureTokenService = inject(SecureTokenService);\n  const router = inject(Router);\n  const authService = inject(AuthService);\n\n  // Skip auth for login and public endpoints\n  const skipAuth = req.url.includes('/auth/login') ||\n                   req.url.includes('/auth/register') ||\n                   req.url.includes('/auth/invite') ||\n                   req.url.includes('/public/') ||\n                   req.url.includes('/health');\n\n  if (skipAuth) {\n    return next(req);\n  }\n\n  const token = secureTokenService.getToken();\n\n  if (!token) {\n    // No token available, redirect to login\n    router.navigate(['/login']);\n    return throwError(() => new Error('No authentication token available'));\n  }\n\n  if (!secureTokenService.isTokenValid()) {\n    // Token is invalid, try to refresh\n    const refreshToken = secureTokenService.getRefreshToken();\n    if (refreshToken) {\n      return authService.refreshToken(refreshToken).pipe(\n        switchMap(response => {\n          // Update tokens\n          secureTokenService.setTokens({\n            token: response.accessToken,\n            refreshToken: response.refreshToken,\n            expiresAt: Date.now() + (response.expiresIn * 1000),\n            tokenType: response.tokenType\n          });\n\n          // Retry the original request with new token\n          const authReq = req.clone({\n            setHeaders: {\n              Authorization: `Bearer ${response.accessToken}`,\n              'Content-Type': 'application/json',\n              'X-Requested-With': 'XMLHttpRequest'\n            }\n          });\n          return next(authReq);\n        }),\n        catchError(error => {\n          // Refresh failed, logout user\n          secureTokenService.clearTokens();\n          router.navigate(['/login']);\n          return throwError(() => error);\n        })\n      );\n    } else {\n      secureTokenService.clearTokens();\n      router.navigate(['/login']);\n      return throwError(() => new Error('Token expired'));\n    }\n  }\n\n  // Clone the request and add the authorization header\n  const authReq = req.clone({\n    setHeaders: {\n      Authorization: `Bearer ${token}`,\n      'Content-Type': 'application/json',\n      'X-Requested-With': 'XMLHttpRequest'\n    }\n  });\n\n  return next(authReq).pipe(\n    catchError((error) => {\n      // Handle 401 Unauthorized responses\n      if (error.status === 401) {\n        secureTokenService.clearTokens();\n        router.navigate(['/login']);\n      }\n\n      // Handle 403 Forbidden responses (CSRF issues)\n      if (error.status === 403) {\n        console.error('CSRF token validation failed');\n      }\n\n      return throwError(() => error);\n    })\n  );\n};\n"], "mappings": "AACA,SAASA,MAAM,QAAQ,eAAe;AACtC,SAASC,UAAU,EAAEC,SAAS,EAAEC,UAAU,QAAQ,MAAM;AACxD,SAASC,kBAAkB,QAAQ,kCAAkC;AACrE,SAASC,MAAM,QAAQ,iBAAiB;AACxC,SAASC,WAAW,QAAQ,0BAA0B;AAEtD,OAAO,MAAMC,eAAe,GAAsBA,CAACC,GAAyB,EAAEC,IAAmB,KAAI;EACnG,MAAMC,kBAAkB,GAAGV,MAAM,CAACI,kBAAkB,CAAC;EACrD,MAAMO,MAAM,GAAGX,MAAM,CAACK,MAAM,CAAC;EAC7B,MAAMO,WAAW,GAAGZ,MAAM,CAACM,WAAW,CAAC;EAEvC;EACA,MAAMO,QAAQ,GAAGL,GAAG,CAACM,GAAG,CAACC,QAAQ,CAAC,aAAa,CAAC,IAC/BP,GAAG,CAACM,GAAG,CAACC,QAAQ,CAAC,gBAAgB,CAAC,IAClCP,GAAG,CAACM,GAAG,CAACC,QAAQ,CAAC,cAAc,CAAC,IAChCP,GAAG,CAACM,GAAG,CAACC,QAAQ,CAAC,UAAU,CAAC,IAC5BP,GAAG,CAACM,GAAG,CAACC,QAAQ,CAAC,SAAS,CAAC;EAE5C,IAAIF,QAAQ,EAAE;IACZ,OAAOJ,IAAI,CAACD,GAAG,CAAC;;EAGlB,MAAMQ,KAAK,GAAGN,kBAAkB,CAACO,QAAQ,EAAE;EAE3C,IAAI,CAACD,KAAK,EAAE;IACV;IACAL,MAAM,CAACO,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;IAC3B,OAAOf,UAAU,CAAC,MAAM,IAAIgB,KAAK,CAAC,mCAAmC,CAAC,CAAC;;EAGzE,IAAI,CAACT,kBAAkB,CAACU,YAAY,EAAE,EAAE;IACtC;IACA,MAAMC,YAAY,GAAGX,kBAAkB,CAACY,eAAe,EAAE;IACzD,IAAID,YAAY,EAAE;MAChB,OAAOT,WAAW,CAACS,YAAY,CAACA,YAAY,CAAC,CAACE,IAAI,CAChDrB,SAAS,CAACsB,QAAQ,IAAG;QACnB;QACAd,kBAAkB,CAACe,SAAS,CAAC;UAC3BT,KAAK,EAAEQ,QAAQ,CAACE,WAAW;UAC3BL,YAAY,EAAEG,QAAQ,CAACH,YAAY;UACnCM,SAAS,EAAEC,IAAI,CAACC,GAAG,EAAE,GAAIL,QAAQ,CAACM,SAAS,GAAG,IAAK;UACnDC,SAAS,EAAEP,QAAQ,CAACO;SACrB,CAAC;QAEF;QACA,MAAMC,OAAO,GAAGxB,GAAG,CAACyB,KAAK,CAAC;UACxBC,UAAU,EAAE;YACVC,aAAa,EAAE,UAAUX,QAAQ,CAACE,WAAW,EAAE;YAC/C,cAAc,EAAE,kBAAkB;YAClC,kBAAkB,EAAE;;SAEvB,CAAC;QACF,OAAOjB,IAAI,CAACuB,OAAO,CAAC;MACtB,CAAC,CAAC,EACF/B,UAAU,CAACmC,KAAK,IAAG;QACjB;QACA1B,kBAAkB,CAAC2B,WAAW,EAAE;QAChC1B,MAAM,CAACO,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;QAC3B,OAAOf,UAAU,CAAC,MAAMiC,KAAK,CAAC;MAChC,CAAC,CAAC,CACH;KACF,MAAM;MACL1B,kBAAkB,CAAC2B,WAAW,EAAE;MAChC1B,MAAM,CAACO,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;MAC3B,OAAOf,UAAU,CAAC,MAAM,IAAIgB,KAAK,CAAC,eAAe,CAAC,CAAC;;;EAIvD;EACA,MAAMa,OAAO,GAAGxB,GAAG,CAACyB,KAAK,CAAC;IACxBC,UAAU,EAAE;MACVC,aAAa,EAAE,UAAUnB,KAAK,EAAE;MAChC,cAAc,EAAE,kBAAkB;MAClC,kBAAkB,EAAE;;GAEvB,CAAC;EAEF,OAAOP,IAAI,CAACuB,OAAO,CAAC,CAACT,IAAI,CACvBtB,UAAU,CAAEmC,KAAK,IAAI;IACnB;IACA,IAAIA,KAAK,CAACE,MAAM,KAAK,GAAG,EAAE;MACxB5B,kBAAkB,CAAC2B,WAAW,EAAE;MAChC1B,MAAM,CAACO,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;;IAG7B;IACA,IAAIkB,KAAK,CAACE,MAAM,KAAK,GAAG,EAAE;MACxBC,OAAO,CAACH,KAAK,CAAC,8BAA8B,CAAC;;IAG/C,OAAOjC,UAAU,CAAC,MAAMiC,KAAK,CAAC;EAChC,CAAC,CAAC,CACH;AACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}