// Shared types for QSC application
// This replaces the @qsc/shared package imports

export interface IApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
  timestamp: string;
}

export interface ILoginRequest {
  username: string;
  secretWord: string;
  deviceId?: string;
}

export interface IRegisterRequest {
  username: string;
  email?: string;
  secretWord: string;
  inviteCode: string;
}

export interface IAuthResponse {
  accessToken: string;
  refreshToken: string;
  expiresIn: number;
  tokenType: string;
  user: IUserProfile;
}

export interface IUserProfile {
  id: string;
  username: string;
  email?: string;
  phone?: string;
  publicKey?: string;
  isAdmin: boolean;
  isActive: boolean;
  isVerified: boolean;
  accountStatus: 'active' | 'deactivated' | 'compromised';
  createdAt: Date;
  updatedAt: Date;
  lastLoginAt?: Date;
  deviceIds: string[];
}

export interface ITokenRefreshRequest {
  refresh_token: string;
}

export interface ITokenRefreshResponse {
  access_token: string;
  refresh_token: string;
  expires_in: number;
  token_type: string;
}

export interface IAuthError {
  code: string;
  message: string;
  details?: any;
}

export interface ISecretWordValidation {
  isValid: boolean;
  requirements: {
    minLength: boolean;
    hasUppercase: boolean;
    hasLowercase: boolean;
    hasDigit: boolean;
    hasSymbol: boolean;
  };
  strength: 'weak' | 'medium' | 'strong';
}

// Post-Quantum Cryptography types
export interface IPQCKeyPair {
  publicKey: Uint8Array;
  privateKey: Uint8Array;
  algorithm: 'dilithium' | 'kyber';
}

export interface IPQCSignature {
  signature: Uint8Array;
  message: Uint8Array;
  algorithm: string;
}

export interface IKyberKeyExchange {
  publicKey: Uint8Array;
  privateKey: Uint8Array;
  sharedSecret: Uint8Array;
  ciphertext: Uint8Array;
}

// Message types
export interface IMessage {
  id: string;
  content: string;
  timestamp: Date;
  sender: string;
  recipient?: string;
  groupId?: string;
  read?: boolean;
  encrypted?: boolean;
}

// Security types
export interface ISecurityEvent {
  id: string;
  type: 'COMPROMISE' | 'DELETION' | 'AUTH_FAILURE' | 'KEY_ROTATION';
  message: string;
  timestamp: Date;
  severity: 'low' | 'medium' | 'high' | 'critical';
  metadata?: any;
}

// Utility functions for secure operations
export function generateSecureRandom(length: number): Uint8Array {
  return crypto.getRandomValues(new Uint8Array(length));
}

export function sanitizeForLogging(data: any): any {
  if (typeof data === 'string') {
    // Remove sensitive patterns
    return data.replace(/password|token|secret|key/gi, '[REDACTED]');
  }

  if (typeof data === 'object' && data !== null) {
    const sanitized: any = {};
    for (const [key, value] of Object.entries(data)) {
      if (/password|token|secret|key/i.test(key)) {
        sanitized[key] = '[REDACTED]';
      } else {
        sanitized[key] = sanitizeForLogging(value);
      }
    }
    return sanitized;
  }

  return data;
}
