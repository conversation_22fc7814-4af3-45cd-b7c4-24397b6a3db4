import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateChatRoomsTable1710000000001 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      CREATE TABLE IF NOT EXISTS chat_rooms (
        id VARCHAR(36) PRIMARY KEY,
        name VA<PERSON>HAR(255) NOT NULL,
        pqc_group_key TEXT NOT NULL,
        is_private BOOLEAN NOT NULL DEFAULT false,
        is_deleted BOOLEAN NOT NULL DEFAULT false,
        deleted_at TIMESTAMP,
        created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
      )
    `);

    await queryRunner.query(`
      CREATE TABLE IF NOT EXISTS chat_room_members (
        room_id VARCHAR(36) NOT NULL,
        user_id VARCHAR(36) NOT NULL,
        PRIMARY KEY (room_id, user_id),
        <PERSON>OR<PERSON><PERSON>N KEY (room_id) REFERENCES chat_rooms(id) ON DELETE CASCADE,
        FOREIG<PERSON> KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
      )
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP TABLE IF EXISTS chat_room_members`);
    await queryRunner.query(`DROP TABLE IF EXISTS chat_rooms`);
  }
} 