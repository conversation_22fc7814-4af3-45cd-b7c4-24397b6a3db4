{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"./api.service\";\nexport let GroupService = /*#__PURE__*/(() => {\n  class GroupService {\n    constructor(apiService) {\n      this.apiService = apiService;\n    }\n    createGroup(groupData) {\n      return this.apiService.post('/groups', groupData);\n    }\n    getUserGroups() {\n      // This would need to be implemented in the backend\n      return this.apiService.get('/groups/user');\n    }\n    getGroup(groupId) {\n      return this.apiService.get(`/groups/${groupId}`);\n    }\n    createInvite(groupId, inviteData) {\n      return this.apiService.post(`/groups/${groupId}/invites`, inviteData);\n    }\n    joinGroup(joinData) {\n      return this.apiService.post('/groups/join', joinData);\n    }\n    getGroupMembers(groupId) {\n      return this.apiService.get(`/groups/${groupId}/members`);\n    }\n    leaveGroup(groupId) {\n      return this.apiService.delete(`/groups/${groupId}/leave`);\n    }\n    rotateGroupKeys(groupId, newMasterKey) {\n      return this.apiService.post(`/groups/${groupId}/rotate-keys`, {\n        newMasterKey\n      });\n    }\n    static {\n      this.ɵfac = function GroupService_Factory(t) {\n        return new (t || GroupService)(i0.ɵɵinject(i1.ApiService));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: GroupService,\n        factory: GroupService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return GroupService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}