import { HttpInterceptorFn, HttpRequest, HttpHandlerFn } from '@angular/common/http';
import { inject } from '@angular/core';
import { catchError, switchMap, throwError } from 'rxjs';
import { SecureTokenService } from '../services/secure-token.service';
import { Router } from '@angular/router';
import { AuthService } from '../services/auth.service';

export const authInterceptor: HttpInterceptorFn = (req: HttpRequest<unknown>, next: HttpHandlerFn) => {
  const secureTokenService = inject(SecureTokenService);
  const router = inject(Router);
  const authService = inject(AuthService);

  // Skip auth for login and public endpoints
  const skipAuth = req.url.includes('/auth/login') ||
                   req.url.includes('/auth/register') ||
                   req.url.includes('/auth/invite') ||
                   req.url.includes('/public/') ||
                   req.url.includes('/health');

  if (skipAuth) {
    return next(req);
  }

  const token = secureTokenService.getToken();

  if (!token) {
    // No token available, redirect to login
    router.navigate(['/login']);
    return throwError(() => new Error('No authentication token available'));
  }

  if (!secureTokenService.isTokenValid()) {
    // Token is invalid, try to refresh
    const refreshToken = secureTokenService.getRefreshToken();
    if (refreshToken) {
      return authService.refreshToken(refreshToken).pipe(
        switchMap(response => {
          // Update tokens
          secureTokenService.setTokens({
            token: response.accessToken,
            refreshToken: response.refreshToken,
            expiresAt: Date.now() + (response.expiresIn * 1000),
            tokenType: response.tokenType
          });

          // Retry the original request with new token
          const authReq = req.clone({
            setHeaders: {
              Authorization: `Bearer ${response.accessToken}`,
              'Content-Type': 'application/json',
              'X-Requested-With': 'XMLHttpRequest'
            }
          });
          return next(authReq);
        }),
        catchError(error => {
          // Refresh failed, logout user
          secureTokenService.clearTokens();
          router.navigate(['/login']);
          return throwError(() => error);
        })
      );
    } else {
      secureTokenService.clearTokens();
      router.navigate(['/login']);
      return throwError(() => new Error('Token expired'));
    }
  }

  // Clone the request and add the authorization header
  const authReq = req.clone({
    setHeaders: {
      Authorization: `Bearer ${token}`,
      'Content-Type': 'application/json',
      'X-Requested-With': 'XMLHttpRequest'
    }
  });

  return next(authReq).pipe(
    catchError((error) => {
      // Handle 401 Unauthorized responses
      if (error.status === 401) {
        secureTokenService.clearTokens();
        router.navigate(['/login']);
      }

      // Handle 403 Forbidden responses (CSRF issues)
      if (error.status === 403) {
        console.error('CSRF token validation failed');
      }

      return throwError(() => error);
    })
  );
};
