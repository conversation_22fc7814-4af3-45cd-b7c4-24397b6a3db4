import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { TypeOrmModuleOptions, TypeOrmOptionsFactory } from '@nestjs/typeorm';
import { join } from 'path';
import { registerAs } from '@nestjs/config';

@Injectable()
export class DatabaseConfigService implements TypeOrmOptionsFactory {
  constructor(private configService: ConfigService) {}

  createTypeOrmOptions(): TypeOrmModuleOptions {
    const dbPath = process.env.DB_PATH || join(process.cwd(), 'secure_chat.sqlite');

    return {
      type: 'better-sqlite3',
      database: dbPath,
      synchronize: process.env.NODE_ENV !== 'production',
      logging: process.env.NODE_ENV !== 'production',
      entities: [join(__dirname, '..', '**', '*.entity{.ts,.js}')],
      migrations: [join(__dirname, '..', 'migrations', '*{.ts,.js}')],
      migrationsRun: true,
      // SQLCipher configuration for encryption
      extra: {
        pragma: {
          key: process.env.DB_ENCRYPTION_KEY ? `'${process.env.DB_ENCRYPTION_KEY}'` : null,
          cipher: process.env.DB_ENCRYPTION_KEY ? 'aes-256-gcm' : null,
          kdf_iter: 64000,
          cipher_page_size: 4096,
          cipher_use_hmac: 'ON',
          journal_mode: 'WAL',
          synchronous: 'NORMAL',
          foreign_keys: 'ON',
        },
      },
    };
  }
}

export const databaseConfig = (): TypeOrmModuleOptions => {
  const dbPath = process.env.DB_PATH || join(process.cwd(), 'secure_chat.sqlite');

  return {
    type: 'better-sqlite3',
    database: dbPath,
    synchronize: process.env.NODE_ENV !== 'production',
    logging: process.env.NODE_ENV !== 'production',
    entities: [join(__dirname, '..', '**', '*.entity{.ts,.js}')],
    migrations: [join(__dirname, '..', 'migrations', '*{.ts,.js}')],
    migrationsRun: false, // Disable migrations for development, use synchronize instead
    // SQLCipher configuration for encryption (disabled for development)
    extra: {
      pragma: {
        // Disable SQLCipher for development since better-sqlite3 doesn't support it
        // key: process.env.DB_ENCRYPTION_KEY ? `'${process.env.DB_ENCRYPTION_KEY}'` : null,
        // cipher: process.env.DB_ENCRYPTION_KEY ? 'aes-256-gcm' : null,
        // kdf_iter: 64000,
        // cipher_page_size: 4096,
        // cipher_use_hmac: 'ON',
        journal_mode: 'WAL',
        synchronous: 'NORMAL',
        foreign_keys: 'ON',
      },
    },
  };
};