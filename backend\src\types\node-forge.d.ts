declare module 'node-forge' {
  export namespace pki {
    interface KeyPair {
      publicKey: any;
      privateKey: any;
    }
    
    function generateKeyPair(options: any): Promise<KeyPair>;
    function publicKeyFromPem(pem: string): any;
    function privateKeyFromPem(pem: string): any;
  }

  export namespace util {
    function encode64(bytes: Uint8Array): string;
    function decode64(str: string): Uint8Array;
  }

  export namespace random {
    function getBytesSync(count: number): Uint8Array;
  }
} 