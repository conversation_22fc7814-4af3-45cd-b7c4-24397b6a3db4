{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"quantumshield-frontend": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss", "standalone": true}, "@schematics/angular:directive": {"standalone": true}, "@schematics/angular:pipe": {"standalone": true}}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:browser", "options": {"outputPath": "dist/quantumshield-frontend", "index": "src/index.html", "main": "src/main.ts", "polyfills": "src/polyfills.ts", "tsConfig": "tsconfig.app.json", "assets": ["src/favicon.ico", "src/assets", {"glob": "**/*", "input": "src/assets/wasm", "output": "/assets/wasm"}], "styles": ["src/styles.scss"], "scripts": [], "allowedCommonJsDependencies": ["pqc_wasm"]}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "2mb", "maximumError": "5mb"}, {"type": "anyComponentStyle", "maximumWarning": "6kb", "maximumError": "10kb"}], "outputHashing": "all", "optimization": true, "sourceMap": false, "namedChunks": false, "extractLicenses": true, "vendorChunk": false, "buildOptimizer": true}, "development": {"buildOptimizer": false, "optimization": false, "vendorChunk": true, "extractLicenses": false, "sourceMap": true, "namedChunks": true}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "configurations": {"production": {"buildTarget": "quantumshield-frontend:build:production"}, "development": {"buildTarget": "quantumshield-frontend:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"buildTarget": "quantumshield-frontend:build"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "src/test.ts", "polyfills": "src/polyfills.ts", "tsConfig": "tsconfig.spec.json", "karmaConfig": "karma.conf.js", "assets": ["src/favicon.ico", "src/assets"], "styles": ["src/styles.scss"], "scripts": []}}}}}, "cli": {"analytics": false, "schematicCollections": ["@angular-eslint/schematics"]}}