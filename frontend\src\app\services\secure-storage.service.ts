import { Injectable } from '@angular/core';
import { ErrorHandlingService } from './error-handling.service';

interface StorageEntry {
  id: string;
  data: any;
  timestamp: Date;
  version: number;
}

@Injectable({
  providedIn: 'root'
})
export class SecureStorageService {
  private db: IDBDatabase | null = null;
  private readonly DB_NAME = 'quantumshield_db';
  private readonly DB_VERSION = 1;

  constructor(
    private errorHandlingService: ErrorHandlingService
  ) {
    this.initDatabase();
  }

  private async initDatabase(): Promise<void> {
    try {
      const request = indexedDB.open(this.DB_NAME, this.DB_VERSION);

      request.onerror = (event) => {
        this.errorHandlingService.handleError(
          new Error('Failed to open database'),
          'STORAGE'
        );
      };

      request.onupgradeneeded = (event) => {
        const db = (event.target as IDBOpenDBRequest).result;
        if (!db.objectStoreNames.contains('notifications')) {
          db.createObjectStore('notifications', { keyPath: 'id' });
        }
        if (!db.objectStoreNames.contains('chain')) {
          db.createObjectStore('chain', { keyPath: 'id' });
        }
        if (!db.objectStoreNames.contains('deletions')) {
          db.createObjectStore('deletions', { keyPath: 'id' });
        }
      };

      request.onsuccess = (event) => {
        this.db = (event.target as IDBOpenDBRequest).result;
      };
    } catch (error) {
      this.errorHandlingService.handleError(error as Error, 'STORAGE');
      throw error;
    }
  }

  async storeSecurely(key: string, data: any, store: string): Promise<void> {
    try {
      if (!this.db) {
        throw new Error('Database not initialized');
      }

      // Use browser's built-in encryption for storage
      const encryptedData = await this.encryptData(JSON.stringify(data));
      const entry: StorageEntry = {
        id: key,
        data: encryptedData,
        timestamp: new Date(),
        version: 1
      };

      const transaction = this.db.transaction(store, 'readwrite');
      const objectStore = transaction.objectStore(store);
      await objectStore.put(entry);
    } catch (error) {
      this.errorHandlingService.handleError(error as Error, 'STORAGE');
      throw error;
    }
  }

  async retrieveSecurely(key: string, store: string): Promise<any> {
    try {
      if (!this.db) {
        throw new Error('Database not initialized');
      }

      const transaction = this.db.transaction(store, 'readonly');
      const objectStore = transaction.objectStore(store);
      const request = objectStore.get(key);

      return new Promise((resolve, reject) => {
        request.onsuccess = async () => {
          try {
            const entry = request.result as StorageEntry;
            if (!entry) {
              resolve(null);
              return;
            }

            const decryptedData = await this.decryptData(entry.data);
            resolve(JSON.parse(decryptedData));
          } catch (error) {
            reject(error);
          }
        };

        request.onerror = () => {
          reject(new Error('Failed to retrieve data'));
        };
      });
    } catch (error) {
      this.errorHandlingService.handleError(error as Error, 'STORAGE');
      throw error;
    }
  }

  async deleteSecurely(key: string, store: string): Promise<void> {
    try {
      if (!this.db) {
        throw new Error('Database not initialized');
      }

      const transaction = this.db.transaction(store, 'readwrite');
      const objectStore = transaction.objectStore(store);
      await objectStore.delete(key);
    } catch (error) {
      this.errorHandlingService.handleError(error as Error, 'STORAGE');
      throw error;
    }
  }

  async rotateKeys(): Promise<void> {
    try {
      if (!this.db) {
        throw new Error('Database not initialized');
      }

      const stores = ['notifications', 'chain', 'deletions'];
      for (const store of stores) {
        const transaction = this.db.transaction(store, 'readonly');
        const objectStore = transaction.objectStore(store);
        const request = objectStore.getAll();

        const entries = await new Promise<StorageEntry[]>((resolve, reject) => {
          request.onsuccess = () => resolve(request.result);
          request.onerror = () => reject(new Error('Failed to retrieve entries'));
        });

        for (const entry of entries) {
          const decryptedData = await this.decryptData(entry.data);
          await this.storeSecurely(entry.id, JSON.parse(decryptedData), store);
        }
      }
    } catch (error) {
      this.errorHandlingService.handleError(error as Error, 'STORAGE');
      throw error;
    }
  }

  async wipeStorage(): Promise<void> {
    try {
      if (!this.db) {
        throw new Error('Database not initialized');
      }

      const stores = ['notifications', 'chain', 'deletions'];
      for (const store of stores) {
        const transaction = this.db.transaction(store, 'readwrite');
        const objectStore = transaction.objectStore(store);
        await objectStore.clear();
      }

      // Memory wiped with storage
    } catch (error) {
      this.errorHandlingService.handleError(error as Error, 'STORAGE');
      throw error;
    }
  }

  /**
   * Encrypt data using Web Crypto API
   */
  private async encryptData(data: string): Promise<ArrayBuffer> {
    try {
      const key = await this.getOrCreateKey();
      const encoder = new TextEncoder();
      const dataBuffer = encoder.encode(data);
      const iv = crypto.getRandomValues(new Uint8Array(12)); // 96-bit IV for AES-GCM

      const encrypted = await crypto.subtle.encrypt(
        { name: 'AES-GCM', iv: iv },
        key,
        dataBuffer
      );

      // Combine IV and encrypted data
      const result = new Uint8Array(iv.length + encrypted.byteLength);
      result.set(iv);
      result.set(new Uint8Array(encrypted), iv.length);

      return result.buffer;
    } catch (error) {
      throw new Error('Encryption failed');
    }
  }

  /**
   * Decrypt data using Web Crypto API
   */
  private async decryptData(encryptedData: ArrayBuffer): Promise<string> {
    try {
      const key = await this.getOrCreateKey();
      const dataArray = new Uint8Array(encryptedData);
      const iv = dataArray.slice(0, 12); // Extract IV
      const encrypted = dataArray.slice(12); // Extract encrypted data

      const decrypted = await crypto.subtle.decrypt(
        { name: 'AES-GCM', iv: iv },
        key,
        encrypted
      );

      const decoder = new TextDecoder();
      return decoder.decode(decrypted);
    } catch (error) {
      throw new Error('Decryption failed');
    }
  }

  /**
   * Get or create encryption key
   */
  private async getOrCreateKey(): Promise<CryptoKey> {
    // In a real implementation, this key should be derived from user credentials
    // For now, we'll use a static key for simplicity
    const keyMaterial = await crypto.subtle.importKey(
      'raw',
      new TextEncoder().encode('quantumshield-storage-key-32b'),
      { name: 'PBKDF2' },
      false,
      ['deriveKey']
    );

    return crypto.subtle.deriveKey(
      {
        name: 'PBKDF2',
        salt: new TextEncoder().encode('qsc-salt'),
        iterations: 100000,
        hash: 'SHA-256'
      },
      keyMaterial,
      { name: 'AES-GCM', length: 256 },
      false,
      ['encrypt', 'decrypt']
    );
  }
}
