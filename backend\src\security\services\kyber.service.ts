import { Injectable, Logger } from '@nestjs/common';
import * as crypto from 'crypto';
import * as forge from 'node-forge';
import { IKyberKeyExchange, IPQCKeyPair } from '@qsc/shared';
import { generateSecureRandom, sanitizeForLogging } from '@qsc/shared';
import { LibOQSService } from './liboqs.service';

/**
 * Kyber Service for Post-Quantum Key Exchange
 * 
 * This service implements CRYSTALS-Kyber key encapsulation mechanism (KEM).
 * Currently using X25519 as a placeholder until LibOQS is properly integrated.
 * 
 * Security Note: In production, this should use actual CRYSTALS-Kyber
 * implementation from LibOQS for quantum resistance.
 */
@Injectable()
export class KyberService {
  private readonly logger = new Logger(KyberService.name);
  private readonly algorithm = 'kyber'; // Will be used when LibOQS is integrated

  constructor(private readonly libOQSService: LibOQSService) {
    if (this.libOQSService.isAvailable()) {
      this.logger.log('Using LibOQS for Kyber operations');
    } else {
      this.logger.warn('Using X25519 placeholder for Kyber. Replace with LibOQS in production.');
    }
  }

  /**
   * Generate a new Kyber key pair using ML-KEM
   */
  async generateKeyPair(): Promise<IPQCKeyPair> {
    try {
      if (this.libOQSService.isAvailable()) {
        const keyPair = await this.libOQSService.generateKyberKeyPair();
        this.logger.log('Kyber key pair generated using ML-KEM');
        return keyPair;
      }

      // Fallback implementation using X25519
      const { publicKey, privateKey } = crypto.generateKeyPairSync('x25519');

      const keyPair = {
        publicKey: Buffer.from(publicKey.export({ type: 'spki', format: 'pem' })),
        privateKey: Buffer.from(privateKey.export({ type: 'pkcs8', format: 'pem' })),
        algorithm: 'kyber' as const,
      };

      this.logger.warn('Kyber key pair generated using X25519 fallback');
      return keyPair;
    } catch (error) {
      this.logger.error('Failed to generate Kyber key pair', error);
      throw new Error('Kyber key pair generation failed');
    }
  }

  /**
   * Encapsulate a shared secret using Kyber (ML-KEM)
   */
  async encapsulate(publicKey: Buffer): Promise<IKyberKeyExchange> {
    try {
      if (this.libOQSService.isAvailable()) {
        const result = await this.libOQSService.kyberEncapsulate(publicKey);
        this.logger.debug('Kyber encapsulation completed using ML-KEM', {
          publicKeyLength: publicKey.length,
          sharedSecretLength: result.sharedSecret.length,
          ciphertextLength: result.ciphertext.length,
        });

        return {
          publicKey: publicKey,
          privateKey: Buffer.alloc(0), // Not used in encapsulation
          sharedSecret: result.sharedSecret,
          ciphertext: result.ciphertext,
        };
      }

      // Fallback implementation using X25519
      // Generate ephemeral key pair
      const ephemeralKeyPair = crypto.generateKeyPairSync('x25519');
      const ephemeralPrivateKey = ephemeralKeyPair.privateKey;
      const ephemeralPublicKey = ephemeralKeyPair.publicKey;

      // Create public key object from provided key
      const recipientPublicKey = crypto.createPublicKey(publicKey);

      // Perform ECDH to get shared secret
      const sharedSecret = crypto.diffieHellman({
        privateKey: ephemeralPrivateKey,
        publicKey: recipientPublicKey,
      });

      // The ephemeral public key serves as the "ciphertext" in KEM terminology
      const ciphertext = Buffer.from(ephemeralPublicKey.export({ type: 'spki', format: 'pem' }));

      this.logger.warn('Kyber encapsulation completed using X25519 fallback', {
        publicKeyLength: publicKey.length,
        sharedSecretLength: sharedSecret.length,
        ciphertextLength: ciphertext.length,
      });

      return {
        publicKey: Buffer.from(ephemeralPublicKey.export({ type: 'spki', format: 'pem' })),
        privateKey: Buffer.from(ephemeralPrivateKey.export({ type: 'pkcs8', format: 'pem' })),
        sharedSecret,
        ciphertext,
      };
    } catch (error) {
      this.logger.error('Failed to encapsulate with Kyber', {
        error: sanitizeForLogging(error),
      });
      throw new Error('Kyber encapsulation failed');
    }
  }

  /**
   * Decapsulate a shared secret using Kyber (ML-KEM)
   */
  async decapsulate(privateKey: Buffer, ciphertext: Buffer): Promise<Buffer> {
    try {
      if (this.libOQSService.isAvailable()) {
        const sharedSecret = await this.libOQSService.kyberDecapsulate(privateKey, ciphertext);
        this.logger.debug('Kyber decapsulation completed using ML-KEM', {
          privateKeyLength: privateKey.length,
          ciphertextLength: ciphertext.length,
          sharedSecretLength: sharedSecret.length,
        });
        return sharedSecret;
      }

      // Fallback implementation using X25519
      // Create private key object
      const recipientPrivateKey = crypto.createPrivateKey(privateKey);

      // Create public key object from ciphertext (ephemeral public key)
      const ephemeralPublicKey = crypto.createPublicKey(ciphertext);

      // Perform ECDH to get shared secret
      const sharedSecret = crypto.diffieHellman({
        privateKey: recipientPrivateKey,
        publicKey: ephemeralPublicKey,
      });

      this.logger.warn('Kyber decapsulation completed using X25519 fallback', {
        privateKeyLength: privateKey.length,
        ciphertextLength: ciphertext.length,
        sharedSecretLength: sharedSecret.length,
      });

      return sharedSecret;
    } catch (error) {
      this.logger.error('Failed to decapsulate with Kyber', {
        error: sanitizeForLogging(error),
      });
      throw new Error('Kyber decapsulation failed');
    }
  }

  /**
   * Generate a session key using Kyber key exchange
   */
  async generateSessionKey(recipientPublicKey: Buffer): Promise<{
    sessionKey: Buffer;
    encapsulatedKey: Buffer;
  }> {
    try {
      const keyExchange = await this.encapsulate(recipientPublicKey);
      
      // Derive a session key from the shared secret using HKDF
      const sessionKeyBuffer = crypto.hkdfSync(
        'sha256',
        keyExchange.sharedSecret,
        Buffer.alloc(0), // No salt
        Buffer.from('QSC-Session-Key'), // Info parameter
        32 // 256-bit key
      );

      const sessionKey = Buffer.from(sessionKeyBuffer);

      this.logger.debug('Session key generated using Kyber', {
        sessionKeyLength: sessionKey.length,
        encapsulatedKeyLength: keyExchange.ciphertext.length,
      });

      return {
        sessionKey,
        encapsulatedKey: keyExchange.ciphertext,
      };
    } catch (error) {
      this.logger.error('Failed to generate session key with Kyber', error);
      throw new Error('Session key generation failed');
    }
  }

  /**
   * Derive a session key from encapsulated key
   */
  async deriveSessionKey(privateKey: Buffer, encapsulatedKey: Buffer): Promise<Buffer> {
    try {
      const sharedSecret = await this.decapsulate(privateKey, encapsulatedKey);
      
      // Derive a session key from the shared secret using HKDF
      const sessionKeyBuffer = crypto.hkdfSync(
        'sha256',
        sharedSecret,
        Buffer.alloc(0), // No salt
        Buffer.from('QSC-Session-Key'), // Info parameter
        32 // 256-bit key
      );

      const sessionKey = Buffer.from(sessionKeyBuffer);

      this.logger.debug('Session key derived from encapsulated key', {
        sessionKeyLength: sessionKey.length,
      });

      return sessionKey;
    } catch (error) {
      this.logger.error('Failed to derive session key from encapsulated key', error);
      throw new Error('Session key derivation failed');
    }
  }

  /**
   * Export public key in PEM format
   */
  exportPublicKeyPEM(publicKey: Buffer): string {
    return publicKey.toString();
  }

  /**
   * Import public key from PEM format
   */
  importPublicKeyPEM(pemKey: string): Buffer {
    try {
      const publicKeyObject = crypto.createPublicKey(pemKey);
      return Buffer.from(publicKeyObject.export({ type: 'spki', format: 'pem' }));
    } catch (error) {
      this.logger.error('Failed to import public key from PEM', error);
      throw new Error('Invalid PEM key format');
    }
  }

  /**
   * Get algorithm information
   */
  getAlgorithmInfo(): { name: string; keySize: number; ciphertextSize: number; sharedSecretSize: number } {
    if (this.libOQSService.isAvailable()) {
      const details = this.libOQSService.getAlgorithmDetails('Kyber768', 'kem');
      return {
        name: 'CRYSTALS-Kyber (ML-KEM)',
        keySize: details?.public_key_length || 1184,
        ciphertextSize: details?.ciphertext_length || 1088,
        sharedSecretSize: details?.shared_secret_length || 32,
      };
    }

    // Fallback values for X25519
    return {
      name: 'CRYSTALS-Kyber (X25519 fallback)',
      keySize: 32, // X25519 key size
      ciphertextSize: 32, // X25519 public key size (used as ciphertext)
      sharedSecretSize: 32, // X25519 shared secret size
    };
  }

  /**
   * Validate key format
   */
  validateKeyFormat(key: Buffer): boolean {
    try {
      // Try to create a key object to validate format
      crypto.createPublicKey(key);
      return true;
    } catch {
      try {
        crypto.createPrivateKey(key);
        return true;
      } catch {
        return false;
      }
    }
  }

  /**
   * Generate multiple session keys for group communication
   */
  async generateGroupSessionKeys(publicKeys: Buffer[]): Promise<{
    sessionKeys: Buffer[];
    encapsulatedKeys: Buffer[];
  }> {
    try {
      const sessionKeys: Buffer[] = [];
      const encapsulatedKeys: Buffer[] = [];

      for (const publicKey of publicKeys) {
        const { sessionKey, encapsulatedKey } = await this.generateSessionKey(publicKey);
        sessionKeys.push(sessionKey);
        encapsulatedKeys.push(encapsulatedKey);
      }

      this.logger.debug('Group session keys generated', {
        keyCount: sessionKeys.length,
      });

      return { sessionKeys, encapsulatedKeys };
    } catch (error) {
      this.logger.error('Failed to generate group session keys', error);
      throw new Error('Group session key generation failed');
    }
  }
}
