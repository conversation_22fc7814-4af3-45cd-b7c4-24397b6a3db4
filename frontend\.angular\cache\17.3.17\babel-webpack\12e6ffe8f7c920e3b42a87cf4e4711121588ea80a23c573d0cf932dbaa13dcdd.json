{"ast": null, "code": "import { bootstrapApplication } from '@angular/platform-browser';\nimport { App } from './app/app';\nimport { appConfig } from './app/app.config';\nbootstrapApplication(App, appConfig).catch(err => console.error(err));", "map": {"version": 3, "names": ["bootstrapApplication", "App", "appConfig", "catch", "err", "console", "error"], "sources": ["C:\\Users\\<USER>\\Projects\\QSC1\\frontend\\src\\main.ts"], "sourcesContent": ["import { bootstrapApplication } from '@angular/platform-browser';\r\nimport { App } from './app/app';\r\nimport { appConfig } from './app/app.config';\r\n\r\nbootstrapApplication(App, appConfig)\r\n  .catch(err => console.error(err));\r\n"], "mappings": "AAAA,SAASA,oBAAoB,QAAQ,2BAA2B;AAChE,SAASC,GAAG,QAAQ,WAAW;AAC/B,SAASC,SAAS,QAAQ,kBAAkB;AAE5CF,oBAAoB,CAACC,GAAG,EAAEC,SAAS,CAAC,CACjCC,KAAK,CAACC,GAAG,IAAIC,OAAO,CAACC,KAAK,CAACF,GAAG,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}