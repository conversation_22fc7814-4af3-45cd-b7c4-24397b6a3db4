import { Injectable } from '@nestjs/common';
import * as sodium from 'libsodium-wrappers';

@Injectable()
export class KeyManagementService {
  private readonly keyLength = 32; // 256 bits for AES-256
  private readonly saltLength = 16;
  private readonly opsLimit = 3;
  private readonly memLimit = 67108864; // 64MB

  constructor() {
    // Initialize libsodium
    sodium.ready.then(() => {
      console.log('Libsodium initialized');
    });
  }

  async generateMasterKey(): Promise<{ key: string; salt: string }> {
    await sodium.ready;
    const salt = sodium.randombytes_buf(this.saltLength);
    const key = sodium.randombytes_buf(this.keyLength);
    
    return {
      key: sodium.to_base64(key),
      salt: sodium.to_base64(salt)
    };
  }

  async deriveKey(password: string, salt: string): Promise<string> {
    await sodium.ready;
    const key = sodium.crypto_pwhash(
      this.keyLength,
      password,
      sodium.from_base64(salt),
      this.opsLimit,
      this.memLimit,
      sodium.crypto_pwhash_ALG_ARGON2ID13
    );
    
    return sodium.to_base64(key);
  }

  async encryptKey(key: string, masterKey: string): Promise<{ encryptedKey: string; nonce: string }> {
    await sodium.ready;
    const nonce = sodium.randombytes_buf(sodium.crypto_secretbox_NONCEBYTES);
    const encryptedKey = sodium.crypto_secretbox_easy(
      sodium.from_base64(key),
      nonce,
      sodium.from_base64(masterKey)
    );

    return {
      encryptedKey: sodium.to_base64(encryptedKey),
      nonce: sodium.to_base64(nonce)
    };
  }

  async decryptKey(encryptedKey: string, nonce: string, masterKey: string): Promise<string> {
    await sodium.ready;
    const decryptedKey = sodium.crypto_secretbox_open_easy(
      sodium.from_base64(encryptedKey),
      sodium.from_base64(nonce),
      sodium.from_base64(masterKey)
    );

    return sodium.to_base64(decryptedKey);
  }

  async rotateKey(oldKey: string, masterKey: string): Promise<{ newKey: string; encryptedNewKey: string; nonce: string }> {
    await sodium.ready;
    const newKey = sodium.randombytes_buf(this.keyLength);
    const { encryptedKey, nonce } = await this.encryptKey(
      sodium.to_base64(newKey),
      masterKey
    );

    return {
      newKey: sodium.to_base64(newKey),
      encryptedNewKey: encryptedKey,
      nonce
    };
  }

  async secureWipe(buffer: Buffer): Promise<void> {
    await sodium.ready;
    sodium.memzero(buffer);
  }
} 