/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = function(knex) {
  return knex.schema.createTable('messages', function(table) {
    table.uuid('id').primary();
    table.uuid('sender_id').references('id').inTable('users').onDelete('CASCADE');
    table.uuid('recipient_id').references('id').inTable('users').onDelete('CASCADE');
    table.text('encrypted_content').notNullable(); // Encrypted message content
    table.timestamp('created_at').defaultTo(knex.fn.now());
    table.timestamp('expires_at'); // For self-destructing messages
    table.boolean('is_deleted').defaultTo(false);
    table.string('message_hash').notNullable(); // For message integrity verification
  });
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function(knex) {
  return knex.schema.dropTable('messages');
}; 