/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = function(knex) {
  return knex.schema.alterTable('users', function(table) {
    table.enum('account_status', ['active', 'deactivated', 'compromised']).defaultTo('active');
    table.uuid('deactivated_by').references('id').inTable('users');
    table.timestamp('deactivated_at');
    table.text('deactivation_reason');
    table.uuid('reinvited_by').references('id').inTable('users');
    table.timestamp('reinvited_at');
    table.string('new_invite_code').unique();
  });
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function(knex) {
  return knex.schema.alterTable('users', function(table) {
    table.dropColumn('account_status');
    table.dropColumn('deactivated_by');
    table.dropColumn('deactivated_at');
    table.dropColumn('deactivation_reason');
    table.dropColumn('reinvited_by');
    table.dropColumn('reinvited_at');
    table.dropColumn('new_invite_code');
  });
}; 