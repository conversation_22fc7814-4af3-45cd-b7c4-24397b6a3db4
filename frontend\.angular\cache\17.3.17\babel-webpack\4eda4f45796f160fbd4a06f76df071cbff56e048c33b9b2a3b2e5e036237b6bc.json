{"ast": null, "code": "import { inject } from '@angular/core';\nimport { Router } from '@angular/router';\nimport { map, take } from 'rxjs/operators';\nimport { AuthService } from '../services/auth.service';\nexport const authGuard = (route, state) => {\n  const authService = inject(AuthService);\n  const router = inject(Router);\n  return authService.authState$.pipe(take(1), map(authState => {\n    if (authState.isAuthenticated) {\n      return true;\n    } else {\n      // Store the attempted URL for redirecting after login\n      router.navigate(['/login'], {\n        queryParams: {\n          returnUrl: state.url\n        }\n      });\n      return false;\n    }\n  }));\n};\nexport const loginGuard = (route, state) => {\n  const authService = inject(AuthService);\n  const router = inject(Router);\n  return authService.authState$.pipe(take(1), map(authState => {\n    if (authState.isAuthenticated) {\n      // User is already authenticated, redirect to main\n      router.navigate(['/main']);\n      return false;\n    } else {\n      return true;\n    }\n  }));\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}