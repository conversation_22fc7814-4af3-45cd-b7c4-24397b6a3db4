import { ComponentFixture, TestBed } from '@angular/core/testing';
import { KeyManagementComponent } from './key-management.component';
import { WasmService } from '../../services/wasm.service';
import { ErrorHandlingService } from '../../services/error-handling.service';
import { of, throwError } from 'rxjs';

describe('KeyManagementComponent', () => {
  let component: KeyManagementComponent;
  let fixture: ComponentFixture<KeyManagementComponent>;
  let wasmService: jasmine.SpyObj<WasmService>;
  let errorHandlingService: jasmine.SpyObj<ErrorHandlingService>;

  beforeEach(async () => {
    const wasmSpy = jasmine.createSpyObj('WasmService', [
      'rotateKeys',
      'getCurrentKeyPair',
      'importKeyPair'
    ]);
    const errorHandlingSpy = jasmine.createSpyObj('ErrorHandlingService', [
      'handleError'
    ]);

    await TestBed.configureTestingModule({
      imports: [KeyManagementComponent],
      providers: [
        { provide: WasmService, useValue: wasmSpy },
        { provide: ErrorHandlingService, useValue: errorHandlingSpy }
      ]
    }).compileComponents();

    wasmService = TestBed.inject(WasmService) as jasmine.SpyObj<WasmService>;
    errorHandlingService = TestBed.inject(ErrorHandlingService) as jasmine.SpyObj<ErrorHandlingService>;
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(KeyManagementComponent);
    component = fixture.componentInstance;
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should load current key pair on init', async () => {
    const mockKeyPair = {
      version: 1,
      timestamp: new Date(),
      public_key: new Uint8Array([1, 2, 3]),
      private_key: new Uint8Array([4, 5, 6])
    };

    wasmService.getCurrentKeyPair.and.returnValue(mockKeyPair);

    await component.ngOnInit();
    expect(component.currentKeyPair).toEqual(mockKeyPair);
  });

  it('should handle key rotation', async () => {
    const mockKeyPair = {
      version: 2,
      timestamp: new Date(),
      public_key: new Uint8Array([1, 2, 3]),
      private_key: new Uint8Array([4, 5, 6])
    };

    wasmService.rotateKeys.and.returnValue(Promise.resolve());
    wasmService.getCurrentKeyPair.and.returnValue(mockKeyPair);

    await component.rotateKeys();
    expect(wasmService.rotateKeys).toHaveBeenCalled();
    expect(component.currentKeyPair).toEqual(mockKeyPair);
    expect(component.isRotating).toBeFalse();
  });

  it('should handle key rotation error', async () => {
    const error = new Error('Rotation failed');
    wasmService.rotateKeys.and.returnValue(Promise.reject(error));

    await component.rotateKeys();
    expect(errorHandlingService.handleError).toHaveBeenCalledWith(error, 'SECURITY');
    expect(component.errorMessage).toBe('Failed to rotate keys');
    expect(component.isRotating).toBeFalse();
  });

  it('should handle file selection', () => {
    const mockFile = new File(['test'], 'test.key', { type: 'application/octet-stream' });
    const event = {
      target: {
        files: [mockFile]
      }
    } as unknown as Event;

    component.onFileSelected(event);
    expect(component.selectedFile).toBe(mockFile);
  });

  it('should handle key import', async () => {
    const keyPairData = {
      version: 1,
      public_key: [1, 2, 3],
      private_key: [4, 5, 6]
    };
    const mockFile = new File([JSON.stringify(keyPairData)], 'test.key', { type: 'application/octet-stream' });
    const mockKeyPair = {
      version: 1,
      timestamp: new Date(),
      public_key: new Uint8Array([1, 2, 3]),
      private_key: new Uint8Array([4, 5, 6])
    };

    component.selectedFile = mockFile;
    wasmService.importKeyPair.and.returnValue(Promise.resolve());
    wasmService.getCurrentKeyPair.and.returnValue(mockKeyPair);

    await component.importKeys();
    expect(wasmService.importKeyPair).toHaveBeenCalledWith(jasmine.any(Object));
    expect(component.currentKeyPair).toEqual(mockKeyPair);
    expect(component.isImporting).toBeFalse();
    expect(component.selectedFile).toBeNull();
  });

  it('should handle key import error', async () => {
    const keyPairData = { version: 1, public_key: [1, 2, 3], private_key: [4, 5, 6] };
    const mockFile = new File([JSON.stringify(keyPairData)], 'test.key', { type: 'application/octet-stream' });
    const error = new Error('Import failed');

    component.selectedFile = mockFile;
    wasmService.importKeyPair.and.returnValue(Promise.reject(error));

    await component.importKeys();
    expect(errorHandlingService.handleError).toHaveBeenCalledWith(error, 'SECURITY');
    expect(component.errorMessage).toBe('Failed to import keys');
    expect(component.isImporting).toBeFalse();
    expect(component.selectedFile).toBeNull();
  });

  it('should export public key', async () => {
    const mockKeyPair = {
      version: 1,
      timestamp: new Date(),
      public_key: new Uint8Array([1, 2, 3]),
      private_key: new Uint8Array([4, 5, 6])
    };

    component.currentKeyPair = mockKeyPair;
    spyOn(window.URL, 'createObjectURL');
    spyOn(window.URL, 'revokeObjectURL');

    await component.exportPublicKey();
    expect(window.URL.createObjectURL).toHaveBeenCalled();
    expect(window.URL.revokeObjectURL).toHaveBeenCalled();
  });

  it('should handle export error', async () => {
    const mockKeyPair = {
      version: 1,
      timestamp: new Date(),
      public_key: new Uint8Array([1, 2, 3]),
      private_key: new Uint8Array([4, 5, 6])
    };

    component.currentKeyPair = mockKeyPair;
    spyOn(window.URL, 'createObjectURL').and.throwError('Export failed');

    await component.exportPublicKey();
    expect(errorHandlingService.handleError).toHaveBeenCalled();
    expect(component.errorMessage).toBe('Failed to export public key');
  });
});
