{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Projects/QSC1/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { __decorate } from \"tslib\";\nimport { Injectable } from '@angular/core';\nimport { SecureStorageService } from './secure-storage.service';\nimport { ErrorHandlingService } from './error-handling.service';\nlet NotificationService = class NotificationService {\n  constructor(secureStorage, errorHandling) {\n    this.secureStorage = secureStorage;\n    this.errorHandling = errorHandling;\n  }\n  getNotifications() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      try {\n        const notifications = yield _this.secureStorage.retrieveSecurely('notifications', 'notifications');\n        return notifications || [];\n      } catch (error) {\n        _this.errorHandling.handleError(error, 'STORAGE');\n        throw error;\n      }\n    })();\n  }\n  markAsRead(id) {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        const notifications = yield _this2.getNotifications();\n        const notification = notifications.find(n => n.id === id);\n        if (notification) {\n          notification.read = true;\n          yield _this2.secureStorage.storeSecurely('notifications', notifications, 'notifications');\n        }\n      } catch (error) {\n        _this2.errorHandling.handleError(error, 'STORAGE');\n        throw error;\n      }\n    })();\n  }\n  createCompromiseNotification(messageIds) {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        const notifications = yield _this3.getNotifications();\n        const newNotification = {\n          id: crypto.randomUUID(),\n          type: 'COMPROMISE',\n          message: `Messages ${messageIds.join(', ')} may have been compromised`,\n          timestamp: new Date(),\n          read: false\n        };\n        notifications.push(newNotification);\n        yield _this3.secureStorage.storeSecurely('notifications', notifications, 'notifications');\n      } catch (error) {\n        _this3.errorHandling.handleError(error, 'STORAGE');\n        throw error;\n      }\n    })();\n  }\n  createDeletionNotification(messageId) {\n    var _this4 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        const notifications = yield _this4.getNotifications();\n        const newNotification = {\n          id: crypto.randomUUID(),\n          type: 'DELETION',\n          message: `Message ${messageId} has been deleted from the chain`,\n          timestamp: new Date(),\n          read: false\n        };\n        notifications.push(newNotification);\n        yield _this4.secureStorage.storeSecurely('notifications', notifications, 'notifications');\n      } catch (error) {\n        _this4.errorHandling.handleError(error, 'STORAGE');\n        throw error;\n      }\n    })();\n  }\n  static {\n    this.ctorParameters = () => [{\n      type: SecureStorageService\n    }, {\n      type: ErrorHandlingService\n    }];\n  }\n};\nNotificationService = __decorate([Injectable({\n  providedIn: 'root'\n})], NotificationService);\nexport { NotificationService };", "map": {"version": 3, "names": ["Injectable", "SecureStorageService", "ErrorHandlingService", "NotificationService", "constructor", "secureStorage", "errorHandling", "getNotifications", "_this", "_asyncToGenerator", "notifications", "retrieveS<PERSON>urely", "error", "handleError", "mark<PERSON><PERSON><PERSON>", "id", "_this2", "notification", "find", "n", "read", "storeSecurely", "createCompromiseNotification", "messageIds", "_this3", "newNotification", "crypto", "randomUUID", "type", "message", "join", "timestamp", "Date", "push", "createDeletionNotification", "messageId", "_this4", "__decorate", "providedIn"], "sources": ["C:\\Users\\<USER>\\Projects\\QSC1\\frontend\\src\\app\\services\\notification.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { SecureStorageService } from './secure-storage.service';\r\nimport { ErrorHandlingService } from './error-handling.service';\r\n\r\nexport interface Notification {\r\n  id: string;\r\n  type: 'DELETION' | 'COMPROMISE';\r\n  message: string;\r\n  timestamp: Date;\r\n  read: boolean;\r\n}\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class NotificationService {\r\n  constructor(\r\n    private secureStorage: SecureStorageService,\r\n    private errorHandling: ErrorHandlingService\r\n  ) {}\r\n\r\n  async getNotifications(): Promise<Notification[]> {\r\n    try {\r\n      const notifications = await this.secureStorage.retrieveSecurely('notifications', 'notifications');\r\n      return notifications || [];\r\n    } catch (error) {\r\n      this.errorHandling.handleError(error as Error, 'STORAGE');\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  async markAsRead(id: string): Promise<void> {\r\n    try {\r\n      const notifications = await this.getNotifications();\r\n      const notification = notifications.find(n => n.id === id);\r\n      if (notification) {\r\n        notification.read = true;\r\n        await this.secureStorage.storeSecurely('notifications', notifications, 'notifications');\r\n      }\r\n    } catch (error) {\r\n      this.errorHandling.handleError(error as Error, 'STORAGE');\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  async createCompromiseNotification(messageIds: string[]): Promise<void> {\r\n    try {\r\n      const notifications = await this.getNotifications();\r\n      const newNotification: Notification = {\r\n        id: crypto.randomUUID(),\r\n        type: 'COMPROMISE',\r\n        message: `Messages ${messageIds.join(', ')} may have been compromised`,\r\n        timestamp: new Date(),\r\n        read: false\r\n      };\r\n      notifications.push(newNotification);\r\n      await this.secureStorage.storeSecurely('notifications', notifications, 'notifications');\r\n    } catch (error) {\r\n      this.errorHandling.handleError(error as Error, 'STORAGE');\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  async createDeletionNotification(messageId: string): Promise<void> {\r\n    try {\r\n      const notifications = await this.getNotifications();\r\n      const newNotification: Notification = {\r\n        id: crypto.randomUUID(),\r\n        type: 'DELETION',\r\n        message: `Message ${messageId} has been deleted from the chain`,\r\n        timestamp: new Date(),\r\n        read: false\r\n      };\r\n      notifications.push(newNotification);\r\n      await this.secureStorage.storeSecurely('notifications', notifications, 'notifications');\r\n    } catch (error) {\r\n      this.errorHandling.handleError(error as Error, 'STORAGE');\r\n      throw error;\r\n    }\r\n  }\r\n}\r\n"], "mappings": ";;AAAA,SAASA,UAAU,QAAQ,eAAe;AAC1C,SAASC,oBAAoB,QAAQ,0BAA0B;AAC/D,SAASC,oBAAoB,QAAQ,0BAA0B;AAaxD,IAAMC,mBAAmB,GAAzB,MAAMA,mBAAmB;EAC9BC,YACUC,aAAmC,EACnCC,aAAmC;IADnC,KAAAD,aAAa,GAAbA,aAAa;IACb,KAAAC,aAAa,GAAbA,aAAa;EACpB;EAEGC,gBAAgBA,CAAA;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MACpB,IAAI;QACF,MAAMC,aAAa,SAASF,KAAI,CAACH,aAAa,CAACM,gBAAgB,CAAC,eAAe,EAAE,eAAe,CAAC;QACjG,OAAOD,aAAa,IAAI,EAAE;OAC3B,CAAC,OAAOE,KAAK,EAAE;QACdJ,KAAI,CAACF,aAAa,CAACO,WAAW,CAACD,KAAc,EAAE,SAAS,CAAC;QACzD,MAAMA,KAAK;;IACZ;EACH;EAEME,UAAUA,CAACC,EAAU;IAAA,IAAAC,MAAA;IAAA,OAAAP,iBAAA;MACzB,IAAI;QACF,MAAMC,aAAa,SAASM,MAAI,CAACT,gBAAgB,EAAE;QACnD,MAAMU,YAAY,GAAGP,aAAa,CAACQ,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACJ,EAAE,KAAKA,EAAE,CAAC;QACzD,IAAIE,YAAY,EAAE;UAChBA,YAAY,CAACG,IAAI,GAAG,IAAI;UACxB,MAAMJ,MAAI,CAACX,aAAa,CAACgB,aAAa,CAAC,eAAe,EAAEX,aAAa,EAAE,eAAe,CAAC;;OAE1F,CAAC,OAAOE,KAAK,EAAE;QACdI,MAAI,CAACV,aAAa,CAACO,WAAW,CAACD,KAAc,EAAE,SAAS,CAAC;QACzD,MAAMA,KAAK;;IACZ;EACH;EAEMU,4BAA4BA,CAACC,UAAoB;IAAA,IAAAC,MAAA;IAAA,OAAAf,iBAAA;MACrD,IAAI;QACF,MAAMC,aAAa,SAASc,MAAI,CAACjB,gBAAgB,EAAE;QACnD,MAAMkB,eAAe,GAAiB;UACpCV,EAAE,EAAEW,MAAM,CAACC,UAAU,EAAE;UACvBC,IAAI,EAAE,YAAY;UAClBC,OAAO,EAAE,YAAYN,UAAU,CAACO,IAAI,CAAC,IAAI,CAAC,4BAA4B;UACtEC,SAAS,EAAE,IAAIC,IAAI,EAAE;UACrBZ,IAAI,EAAE;SACP;QACDV,aAAa,CAACuB,IAAI,CAACR,eAAe,CAAC;QACnC,MAAMD,MAAI,CAACnB,aAAa,CAACgB,aAAa,CAAC,eAAe,EAAEX,aAAa,EAAE,eAAe,CAAC;OACxF,CAAC,OAAOE,KAAK,EAAE;QACdY,MAAI,CAAClB,aAAa,CAACO,WAAW,CAACD,KAAc,EAAE,SAAS,CAAC;QACzD,MAAMA,KAAK;;IACZ;EACH;EAEMsB,0BAA0BA,CAACC,SAAiB;IAAA,IAAAC,MAAA;IAAA,OAAA3B,iBAAA;MAChD,IAAI;QACF,MAAMC,aAAa,SAAS0B,MAAI,CAAC7B,gBAAgB,EAAE;QACnD,MAAMkB,eAAe,GAAiB;UACpCV,EAAE,EAAEW,MAAM,CAACC,UAAU,EAAE;UACvBC,IAAI,EAAE,UAAU;UAChBC,OAAO,EAAE,WAAWM,SAAS,kCAAkC;UAC/DJ,SAAS,EAAE,IAAIC,IAAI,EAAE;UACrBZ,IAAI,EAAE;SACP;QACDV,aAAa,CAACuB,IAAI,CAACR,eAAe,CAAC;QACnC,MAAMW,MAAI,CAAC/B,aAAa,CAACgB,aAAa,CAAC,eAAe,EAAEX,aAAa,EAAE,eAAe,CAAC;OACxF,CAAC,OAAOE,KAAK,EAAE;QACdwB,MAAI,CAAC9B,aAAa,CAACO,WAAW,CAACD,KAAc,EAAE,SAAS,CAAC;QACzD,MAAMA,KAAK;;IACZ;EACH;;;;;;;;;AAhEWT,mBAAmB,GAAAkC,UAAA,EAH/BrC,UAAU,CAAC;EACVsC,UAAU,EAAE;CACb,CAAC,C,EACWnC,mBAAmB,CAiE/B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}