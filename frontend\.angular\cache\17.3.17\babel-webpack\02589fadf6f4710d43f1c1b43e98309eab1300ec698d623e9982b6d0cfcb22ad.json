{"ast": null, "code": "import _asyncToGenerator from \"D:/TCL1/Projects/Projects/QSC1/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { BehaviorSubject, Subject } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"./secure-storage.service\";\nimport * as i2 from \"./error-handling.service\";\nexport class NotificationService {\n  constructor(secureStorage, errorHandling) {\n    this.secureStorage = secureStorage;\n    this.errorHandling = errorHandling;\n    this.toastNotificationsSubject = new BehaviorSubject([]);\n    this.newToastSubject = new Subject();\n    this.toastNotifications$ = this.toastNotificationsSubject.asObservable();\n    this.newToast$ = this.newToastSubject.asObservable();\n    this.defaultDuration = 5000; // 5 seconds\n    // Request notification permission on service initialization\n    this.requestNotificationPermission();\n  }\n  getNotifications() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      try {\n        const notifications = yield _this.secureStorage.retrieveSecurely('notifications', 'notifications');\n        return notifications || [];\n      } catch (error) {\n        _this.errorHandling.handleError(error, 'STORAGE');\n        throw error;\n      }\n    })();\n  }\n  markAsRead(id) {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        const notifications = yield _this2.getNotifications();\n        const notification = notifications.find(n => n.id === id);\n        if (notification) {\n          notification.read = true;\n          yield _this2.secureStorage.storeSecurely('notifications', notifications, 'notifications');\n        }\n      } catch (error) {\n        _this2.errorHandling.handleError(error, 'STORAGE');\n        throw error;\n      }\n    })();\n  }\n  createCompromiseNotification(messageIds) {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        const notifications = yield _this3.getNotifications();\n        const newNotification = {\n          id: crypto.randomUUID(),\n          type: 'COMPROMISE',\n          message: `Messages ${messageIds.join(', ')} may have been compromised`,\n          timestamp: new Date(),\n          read: false\n        };\n        notifications.push(newNotification);\n        yield _this3.secureStorage.storeSecurely('notifications', notifications, 'notifications');\n      } catch (error) {\n        _this3.errorHandling.handleError(error, 'STORAGE');\n        throw error;\n      }\n    })();\n  }\n  createDeletionNotification(messageId) {\n    var _this4 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        const notifications = yield _this4.getNotifications();\n        const newNotification = {\n          id: crypto.randomUUID(),\n          type: 'DELETION',\n          message: `Message ${messageId} has been deleted from the chain`,\n          timestamp: new Date(),\n          read: false\n        };\n        notifications.push(newNotification);\n        yield _this4.secureStorage.storeSecurely('notifications', notifications, 'notifications');\n      } catch (error) {\n        _this4.errorHandling.handleError(error, 'STORAGE');\n        throw error;\n      }\n    })();\n  }\n  /**\n   * Show a toast notification (for UI feedback)\n   */\n  showNotification(message, type = 'info', duration) {\n    const notification = {\n      id: this.generateToastId(),\n      message,\n      type,\n      duration: duration || this.defaultDuration,\n      timestamp: new Date()\n    };\n    // Add to toast notifications list\n    const currentToasts = this.toastNotificationsSubject.value;\n    this.toastNotificationsSubject.next([notification, ...currentToasts]);\n    // Emit new toast\n    this.newToastSubject.next(notification);\n    // Show browser notification if permission granted\n    this.showBrowserNotification(message, type);\n    // Auto-remove notification after duration\n    if (notification.duration) {\n      setTimeout(() => {\n        this.removeToastNotification(notification.id);\n      }, notification.duration);\n    }\n  }\n  /**\n   * Show success notification\n   */\n  showSuccess(message, duration) {\n    this.showNotification(message, 'success', duration);\n  }\n  /**\n   * Show error notification\n   */\n  showError(message, duration) {\n    this.showNotification(message, 'error', duration || 8000);\n  }\n  /**\n   * Show warning notification\n   */\n  showWarning(message, duration) {\n    this.showNotification(message, 'warning', duration || 6000);\n  }\n  /**\n   * Show info notification\n   */\n  showInfo(message, duration) {\n    this.showNotification(message, 'info', duration);\n  }\n  /**\n   * Remove a specific toast notification\n   */\n  removeToastNotification(id) {\n    const currentToasts = this.toastNotificationsSubject.value;\n    const updatedToasts = currentToasts.filter(n => n.id !== id);\n    this.toastNotificationsSubject.next(updatedToasts);\n  }\n  /**\n   * Clear all toast notifications\n   */\n  clearAllToasts() {\n    this.toastNotificationsSubject.next([]);\n  }\n  /**\n   * Request browser notification permission\n   */\n  requestNotificationPermission() {\n    if ('Notification' in window && Notification.permission === 'default') {\n      Notification.requestPermission().then(permission => {\n        console.log('Notification permission:', permission);\n      });\n    }\n  }\n  /**\n   * Show browser notification\n   */\n  showBrowserNotification(message, type) {\n    if ('Notification' in window && Notification.permission === 'granted') {\n      const options = {\n        body: message,\n        icon: '/assets/icons/qsc-icon.png',\n        badge: '/assets/icons/qsc-badge.png',\n        tag: 'qsc-notification',\n        requireInteraction: type === 'error',\n        silent: false\n      };\n      const notification = new Notification('QSC', options);\n      // Auto-close after 5 seconds (except for errors)\n      if (type !== 'error') {\n        setTimeout(() => {\n          notification.close();\n        }, 5000);\n      }\n      // Handle notification click\n      notification.onclick = () => {\n        window.focus();\n        notification.close();\n      };\n    }\n  }\n  /**\n   * Generate unique ID for toast notifications\n   */\n  generateToastId() {\n    return `toast_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\n  }\n  static {\n    this.ɵfac = function NotificationService_Factory(t) {\n      return new (t || NotificationService)(i0.ɵɵinject(i1.SecureStorageService), i0.ɵɵinject(i2.ErrorHandlingService));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: NotificationService,\n      factory: NotificationService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["BehaviorSubject", "Subject", "NotificationService", "constructor", "secureStorage", "errorHandling", "toastNotificationsSubject", "newToastSubject", "toastNotifications$", "asObservable", "newToast$", "defaultDuration", "requestNotificationPermission", "getNotifications", "_this", "_asyncToGenerator", "notifications", "retrieveS<PERSON>urely", "error", "handleError", "mark<PERSON><PERSON><PERSON>", "id", "_this2", "notification", "find", "n", "read", "storeSecurely", "createCompromiseNotification", "messageIds", "_this3", "newNotification", "crypto", "randomUUID", "type", "message", "join", "timestamp", "Date", "push", "createDeletionNotification", "messageId", "_this4", "showNotification", "duration", "generateToastId", "currentToasts", "value", "next", "showBrowserNotification", "setTimeout", "removeToastNotification", "showSuccess", "showError", "showWarning", "showInfo", "updatedToasts", "filter", "clearAllToasts", "window", "Notification", "permission", "requestPermission", "then", "console", "log", "options", "body", "icon", "badge", "tag", "requireInteraction", "silent", "close", "onclick", "focus", "now", "Math", "random", "toString", "substr", "i0", "ɵɵinject", "i1", "SecureStorageService", "i2", "ErrorHandlingService", "factory", "ɵfac", "providedIn"], "sources": ["D:\\TCL1\\Projects\\Projects\\QSC1\\frontend\\src\\app\\services\\notification.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { BehaviorSubject, Subject } from 'rxjs';\r\nimport { SecureStorageService } from './secure-storage.service';\r\nimport { ErrorHandlingService } from './error-handling.service';\r\n\r\nexport interface Notification {\r\n  id: string;\r\n  type: 'DELETION' | 'COMPROMISE' | 'success' | 'error' | 'warning' | 'info';\r\n  message: string;\r\n  timestamp: Date;\r\n  read: boolean;\r\n}\r\n\r\nexport interface ToastNotification {\r\n  id: string;\r\n  message: string;\r\n  type: 'success' | 'error' | 'warning' | 'info';\r\n  duration?: number;\r\n  timestamp: Date;\r\n}\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class NotificationService {\r\n  private toastNotificationsSubject = new BehaviorSubject<ToastNotification[]>([]);\r\n  private newToastSubject = new Subject<ToastNotification>();\r\n\r\n  public toastNotifications$ = this.toastNotificationsSubject.asObservable();\r\n  public newToast$ = this.newToastSubject.asObservable();\r\n\r\n  private defaultDuration = 5000; // 5 seconds\r\n\r\n  constructor(\r\n    private secureStorage: SecureStorageService,\r\n    private errorHandling: ErrorHandlingService\r\n  ) {\r\n    // Request notification permission on service initialization\r\n    this.requestNotificationPermission();\r\n  }\r\n\r\n  async getNotifications(): Promise<Notification[]> {\r\n    try {\r\n      const notifications = await this.secureStorage.retrieveSecurely('notifications', 'notifications');\r\n      return notifications || [];\r\n    } catch (error) {\r\n      this.errorHandling.handleError(error as Error, 'STORAGE');\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  async markAsRead(id: string): Promise<void> {\r\n    try {\r\n      const notifications = await this.getNotifications();\r\n      const notification = notifications.find(n => n.id === id);\r\n      if (notification) {\r\n        notification.read = true;\r\n        await this.secureStorage.storeSecurely('notifications', notifications, 'notifications');\r\n      }\r\n    } catch (error) {\r\n      this.errorHandling.handleError(error as Error, 'STORAGE');\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  async createCompromiseNotification(messageIds: string[]): Promise<void> {\r\n    try {\r\n      const notifications = await this.getNotifications();\r\n      const newNotification: Notification = {\r\n        id: crypto.randomUUID(),\r\n        type: 'COMPROMISE',\r\n        message: `Messages ${messageIds.join(', ')} may have been compromised`,\r\n        timestamp: new Date(),\r\n        read: false\r\n      };\r\n      notifications.push(newNotification);\r\n      await this.secureStorage.storeSecurely('notifications', notifications, 'notifications');\r\n    } catch (error) {\r\n      this.errorHandling.handleError(error as Error, 'STORAGE');\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  async createDeletionNotification(messageId: string): Promise<void> {\r\n    try {\r\n      const notifications = await this.getNotifications();\r\n      const newNotification: Notification = {\r\n        id: crypto.randomUUID(),\r\n        type: 'DELETION',\r\n        message: `Message ${messageId} has been deleted from the chain`,\r\n        timestamp: new Date(),\r\n        read: false\r\n      };\r\n      notifications.push(newNotification);\r\n      await this.secureStorage.storeSecurely('notifications', notifications, 'notifications');\r\n    } catch (error) {\r\n      this.errorHandling.handleError(error as Error, 'STORAGE');\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Show a toast notification (for UI feedback)\r\n   */\r\n  showNotification(\r\n    message: string,\r\n    type: 'success' | 'error' | 'warning' | 'info' = 'info',\r\n    duration?: number\r\n  ): void {\r\n    const notification: ToastNotification = {\r\n      id: this.generateToastId(),\r\n      message,\r\n      type,\r\n      duration: duration || this.defaultDuration,\r\n      timestamp: new Date()\r\n    };\r\n\r\n    // Add to toast notifications list\r\n    const currentToasts = this.toastNotificationsSubject.value;\r\n    this.toastNotificationsSubject.next([notification, ...currentToasts]);\r\n\r\n    // Emit new toast\r\n    this.newToastSubject.next(notification);\r\n\r\n    // Show browser notification if permission granted\r\n    this.showBrowserNotification(message, type);\r\n\r\n    // Auto-remove notification after duration\r\n    if (notification.duration) {\r\n      setTimeout(() => {\r\n        this.removeToastNotification(notification.id);\r\n      }, notification.duration);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Show success notification\r\n   */\r\n  showSuccess(message: string, duration?: number): void {\r\n    this.showNotification(message, 'success', duration);\r\n  }\r\n\r\n  /**\r\n   * Show error notification\r\n   */\r\n  showError(message: string, duration?: number): void {\r\n    this.showNotification(message, 'error', duration || 8000);\r\n  }\r\n\r\n  /**\r\n   * Show warning notification\r\n   */\r\n  showWarning(message: string, duration?: number): void {\r\n    this.showNotification(message, 'warning', duration || 6000);\r\n  }\r\n\r\n  /**\r\n   * Show info notification\r\n   */\r\n  showInfo(message: string, duration?: number): void {\r\n    this.showNotification(message, 'info', duration);\r\n  }\r\n\r\n  /**\r\n   * Remove a specific toast notification\r\n   */\r\n  removeToastNotification(id: string): void {\r\n    const currentToasts = this.toastNotificationsSubject.value;\r\n    const updatedToasts = currentToasts.filter(n => n.id !== id);\r\n    this.toastNotificationsSubject.next(updatedToasts);\r\n  }\r\n\r\n  /**\r\n   * Clear all toast notifications\r\n   */\r\n  clearAllToasts(): void {\r\n    this.toastNotificationsSubject.next([]);\r\n  }\r\n\r\n  /**\r\n   * Request browser notification permission\r\n   */\r\n  private requestNotificationPermission(): void {\r\n    if ('Notification' in window && Notification.permission === 'default') {\r\n      Notification.requestPermission().then(permission => {\r\n        console.log('Notification permission:', permission);\r\n      });\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Show browser notification\r\n   */\r\n  private showBrowserNotification(message: string, type: string): void {\r\n    if ('Notification' in window && Notification.permission === 'granted') {\r\n      const options: NotificationOptions = {\r\n        body: message,\r\n        icon: '/assets/icons/qsc-icon.png',\r\n        badge: '/assets/icons/qsc-badge.png',\r\n        tag: 'qsc-notification',\r\n        requireInteraction: type === 'error',\r\n        silent: false\r\n      };\r\n\r\n      const notification = new Notification('QSC', options);\r\n\r\n      // Auto-close after 5 seconds (except for errors)\r\n      if (type !== 'error') {\r\n        setTimeout(() => {\r\n          notification.close();\r\n        }, 5000);\r\n      }\r\n\r\n      // Handle notification click\r\n      notification.onclick = () => {\r\n        window.focus();\r\n        notification.close();\r\n      };\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Generate unique ID for toast notifications\r\n   */\r\n  private generateToastId(): string {\r\n    return `toast_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\r\n  }\r\n}\r\n"], "mappings": ";AACA,SAASA,eAAe,EAAEC,OAAO,QAAQ,MAAM;;;;AAuB/C,OAAM,MAAOC,mBAAmB;EAS9BC,YACUC,aAAmC,EACnCC,aAAmC;IADnC,KAAAD,aAAa,GAAbA,aAAa;IACb,KAAAC,aAAa,GAAbA,aAAa;IAVf,KAAAC,yBAAyB,GAAG,IAAIN,eAAe,CAAsB,EAAE,CAAC;IACxE,KAAAO,eAAe,GAAG,IAAIN,OAAO,EAAqB;IAEnD,KAAAO,mBAAmB,GAAG,IAAI,CAACF,yBAAyB,CAACG,YAAY,EAAE;IACnE,KAAAC,SAAS,GAAG,IAAI,CAACH,eAAe,CAACE,YAAY,EAAE;IAE9C,KAAAE,eAAe,GAAG,IAAI,CAAC,CAAC;IAM9B;IACA,IAAI,CAACC,6BAA6B,EAAE;EACtC;EAEMC,gBAAgBA,CAAA;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MACpB,IAAI;QACF,MAAMC,aAAa,SAASF,KAAI,CAACV,aAAa,CAACa,gBAAgB,CAAC,eAAe,EAAE,eAAe,CAAC;QACjG,OAAOD,aAAa,IAAI,EAAE;OAC3B,CAAC,OAAOE,KAAK,EAAE;QACdJ,KAAI,CAACT,aAAa,CAACc,WAAW,CAACD,KAAc,EAAE,SAAS,CAAC;QACzD,MAAMA,KAAK;;IACZ;EACH;EAEME,UAAUA,CAACC,EAAU;IAAA,IAAAC,MAAA;IAAA,OAAAP,iBAAA;MACzB,IAAI;QACF,MAAMC,aAAa,SAASM,MAAI,CAACT,gBAAgB,EAAE;QACnD,MAAMU,YAAY,GAAGP,aAAa,CAACQ,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACJ,EAAE,KAAKA,EAAE,CAAC;QACzD,IAAIE,YAAY,EAAE;UAChBA,YAAY,CAACG,IAAI,GAAG,IAAI;UACxB,MAAMJ,MAAI,CAAClB,aAAa,CAACuB,aAAa,CAAC,eAAe,EAAEX,aAAa,EAAE,eAAe,CAAC;;OAE1F,CAAC,OAAOE,KAAK,EAAE;QACdI,MAAI,CAACjB,aAAa,CAACc,WAAW,CAACD,KAAc,EAAE,SAAS,CAAC;QACzD,MAAMA,KAAK;;IACZ;EACH;EAEMU,4BAA4BA,CAACC,UAAoB;IAAA,IAAAC,MAAA;IAAA,OAAAf,iBAAA;MACrD,IAAI;QACF,MAAMC,aAAa,SAASc,MAAI,CAACjB,gBAAgB,EAAE;QACnD,MAAMkB,eAAe,GAAiB;UACpCV,EAAE,EAAEW,MAAM,CAACC,UAAU,EAAE;UACvBC,IAAI,EAAE,YAAY;UAClBC,OAAO,EAAE,YAAYN,UAAU,CAACO,IAAI,CAAC,IAAI,CAAC,4BAA4B;UACtEC,SAAS,EAAE,IAAIC,IAAI,EAAE;UACrBZ,IAAI,EAAE;SACP;QACDV,aAAa,CAACuB,IAAI,CAACR,eAAe,CAAC;QACnC,MAAMD,MAAI,CAAC1B,aAAa,CAACuB,aAAa,CAAC,eAAe,EAAEX,aAAa,EAAE,eAAe,CAAC;OACxF,CAAC,OAAOE,KAAK,EAAE;QACdY,MAAI,CAACzB,aAAa,CAACc,WAAW,CAACD,KAAc,EAAE,SAAS,CAAC;QACzD,MAAMA,KAAK;;IACZ;EACH;EAEMsB,0BAA0BA,CAACC,SAAiB;IAAA,IAAAC,MAAA;IAAA,OAAA3B,iBAAA;MAChD,IAAI;QACF,MAAMC,aAAa,SAAS0B,MAAI,CAAC7B,gBAAgB,EAAE;QACnD,MAAMkB,eAAe,GAAiB;UACpCV,EAAE,EAAEW,MAAM,CAACC,UAAU,EAAE;UACvBC,IAAI,EAAE,UAAU;UAChBC,OAAO,EAAE,WAAWM,SAAS,kCAAkC;UAC/DJ,SAAS,EAAE,IAAIC,IAAI,EAAE;UACrBZ,IAAI,EAAE;SACP;QACDV,aAAa,CAACuB,IAAI,CAACR,eAAe,CAAC;QACnC,MAAMW,MAAI,CAACtC,aAAa,CAACuB,aAAa,CAAC,eAAe,EAAEX,aAAa,EAAE,eAAe,CAAC;OACxF,CAAC,OAAOE,KAAK,EAAE;QACdwB,MAAI,CAACrC,aAAa,CAACc,WAAW,CAACD,KAAc,EAAE,SAAS,CAAC;QACzD,MAAMA,KAAK;;IACZ;EACH;EAEA;;;EAGAyB,gBAAgBA,CACdR,OAAe,EACfD,IAAA,GAAiD,MAAM,EACvDU,QAAiB;IAEjB,MAAMrB,YAAY,GAAsB;MACtCF,EAAE,EAAE,IAAI,CAACwB,eAAe,EAAE;MAC1BV,OAAO;MACPD,IAAI;MACJU,QAAQ,EAAEA,QAAQ,IAAI,IAAI,CAACjC,eAAe;MAC1C0B,SAAS,EAAE,IAAIC,IAAI;KACpB;IAED;IACA,MAAMQ,aAAa,GAAG,IAAI,CAACxC,yBAAyB,CAACyC,KAAK;IAC1D,IAAI,CAACzC,yBAAyB,CAAC0C,IAAI,CAAC,CAACzB,YAAY,EAAE,GAAGuB,aAAa,CAAC,CAAC;IAErE;IACA,IAAI,CAACvC,eAAe,CAACyC,IAAI,CAACzB,YAAY,CAAC;IAEvC;IACA,IAAI,CAAC0B,uBAAuB,CAACd,OAAO,EAAED,IAAI,CAAC;IAE3C;IACA,IAAIX,YAAY,CAACqB,QAAQ,EAAE;MACzBM,UAAU,CAAC,MAAK;QACd,IAAI,CAACC,uBAAuB,CAAC5B,YAAY,CAACF,EAAE,CAAC;MAC/C,CAAC,EAAEE,YAAY,CAACqB,QAAQ,CAAC;;EAE7B;EAEA;;;EAGAQ,WAAWA,CAACjB,OAAe,EAAES,QAAiB;IAC5C,IAAI,CAACD,gBAAgB,CAACR,OAAO,EAAE,SAAS,EAAES,QAAQ,CAAC;EACrD;EAEA;;;EAGAS,SAASA,CAAClB,OAAe,EAAES,QAAiB;IAC1C,IAAI,CAACD,gBAAgB,CAACR,OAAO,EAAE,OAAO,EAAES,QAAQ,IAAI,IAAI,CAAC;EAC3D;EAEA;;;EAGAU,WAAWA,CAACnB,OAAe,EAAES,QAAiB;IAC5C,IAAI,CAACD,gBAAgB,CAACR,OAAO,EAAE,SAAS,EAAES,QAAQ,IAAI,IAAI,CAAC;EAC7D;EAEA;;;EAGAW,QAAQA,CAACpB,OAAe,EAAES,QAAiB;IACzC,IAAI,CAACD,gBAAgB,CAACR,OAAO,EAAE,MAAM,EAAES,QAAQ,CAAC;EAClD;EAEA;;;EAGAO,uBAAuBA,CAAC9B,EAAU;IAChC,MAAMyB,aAAa,GAAG,IAAI,CAACxC,yBAAyB,CAACyC,KAAK;IAC1D,MAAMS,aAAa,GAAGV,aAAa,CAACW,MAAM,CAAChC,CAAC,IAAIA,CAAC,CAACJ,EAAE,KAAKA,EAAE,CAAC;IAC5D,IAAI,CAACf,yBAAyB,CAAC0C,IAAI,CAACQ,aAAa,CAAC;EACpD;EAEA;;;EAGAE,cAAcA,CAAA;IACZ,IAAI,CAACpD,yBAAyB,CAAC0C,IAAI,CAAC,EAAE,CAAC;EACzC;EAEA;;;EAGQpC,6BAA6BA,CAAA;IACnC,IAAI,cAAc,IAAI+C,MAAM,IAAIC,YAAY,CAACC,UAAU,KAAK,SAAS,EAAE;MACrED,YAAY,CAACE,iBAAiB,EAAE,CAACC,IAAI,CAACF,UAAU,IAAG;QACjDG,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEJ,UAAU,CAAC;MACrD,CAAC,CAAC;;EAEN;EAEA;;;EAGQZ,uBAAuBA,CAACd,OAAe,EAAED,IAAY;IAC3D,IAAI,cAAc,IAAIyB,MAAM,IAAIC,YAAY,CAACC,UAAU,KAAK,SAAS,EAAE;MACrE,MAAMK,OAAO,GAAwB;QACnCC,IAAI,EAAEhC,OAAO;QACbiC,IAAI,EAAE,4BAA4B;QAClCC,KAAK,EAAE,6BAA6B;QACpCC,GAAG,EAAE,kBAAkB;QACvBC,kBAAkB,EAAErC,IAAI,KAAK,OAAO;QACpCsC,MAAM,EAAE;OACT;MAED,MAAMjD,YAAY,GAAG,IAAIqC,YAAY,CAAC,KAAK,EAAEM,OAAO,CAAC;MAErD;MACA,IAAIhC,IAAI,KAAK,OAAO,EAAE;QACpBgB,UAAU,CAAC,MAAK;UACd3B,YAAY,CAACkD,KAAK,EAAE;QACtB,CAAC,EAAE,IAAI,CAAC;;MAGV;MACAlD,YAAY,CAACmD,OAAO,GAAG,MAAK;QAC1Bf,MAAM,CAACgB,KAAK,EAAE;QACdpD,YAAY,CAACkD,KAAK,EAAE;MACtB,CAAC;;EAEL;EAEA;;;EAGQ5B,eAAeA,CAAA;IACrB,OAAO,SAASP,IAAI,CAACsC,GAAG,EAAE,IAAIC,IAAI,CAACC,MAAM,EAAE,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;EACzE;;;uBA1MW9E,mBAAmB,EAAA+E,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,oBAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,oBAAA;IAAA;EAAA;;;aAAnBpF,mBAAmB;MAAAqF,OAAA,EAAnBrF,mBAAmB,CAAAsF,IAAA;MAAAC,UAAA,EAFlB;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}