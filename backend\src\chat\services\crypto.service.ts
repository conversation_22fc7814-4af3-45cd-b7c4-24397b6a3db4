import { Injectable } from '@nestjs/common';
import { LibOQSService } from '../../security/services/liboqs.service';
import * as crypto from 'crypto';

@Injectable()
export class CryptoService {
  constructor(private readonly libOQSService: LibOQSService) {}

  // PQC Layer using ML-DSA (Dilithium)
  async generatePQCKeyPair(): Promise<{ publicKey: Buffer; privateKey: Buffer }> {
    if (this.libOQSService.isAvailable()) {
      const keyPair = await this.libOQSService.generateDilithiumKeyPair();
      return {
        publicKey: keyPair.publicKey,
        privateKey: keyPair.privateKey,
      };
    }

    // Fallback to Ed25519
    const { publicKey, privateKey } = crypto.generateKeyPairSync('ed25519');
    return {
      publicKey: Buffer.from(publicKey.export({ type: 'spki', format: 'pem' })),
      privateKey: Buffer.from(privateKey.export({ type: 'pkcs8', format: 'pem' })),
    };
  }

  async signWithPQC(message: B<PERSON><PERSON>, privateKey: Buffer): Promise<Buffer> {
    if (this.libOQSService.isAvailable()) {
      const signature = await this.libOQSService.signWithDilithium(message, privateKey);
      return signature.signature;
    }

    // Fallback to Ed25519
    const keyObject = crypto.createPrivateKey(privateKey);
    return crypto.sign(null, message, keyObject);
  }

  async verifyPQC(message: Buffer, signature: Buffer, publicKey: Buffer): Promise<boolean> {
    const keyObject = crypto.createPublicKey(publicKey);
    return crypto.verify(null, message, keyObject, signature);
  }

  // Symmetric Layer (AES-256-GCM)
  generateSymmetricKey(): Buffer {
    return crypto.randomBytes(32);
  }

  encryptWithSymmetricKey(data: Buffer, key: Buffer): { encrypted: Buffer; iv: Buffer; authTag: Buffer } {
    const iv = crypto.randomBytes(12);
    const cipher = crypto.createCipheriv('aes-256-gcm', key, iv);
    
    const encrypted = Buffer.concat([
      cipher.update(data),
      cipher.final(),
    ]);

    return {
      encrypted,
      iv,
      authTag: cipher.getAuthTag(),
    };
  }

  decryptWithSymmetricKey(
    encrypted: Buffer,
    key: Buffer,
    iv: Buffer,
    authTag: Buffer,
  ): Buffer {
    const decipher = crypto.createDecipheriv('aes-256-gcm', key, iv);
    decipher.setAuthTag(authTag);
    
    return Buffer.concat([
      decipher.update(encrypted),
      decipher.final(),
    ]);
  }

  // Transport Layer (using Noise Protocol Framework)
  async generateNoiseKeyPair(): Promise<{ publicKey: Buffer; privateKey: Buffer }> {
    const { publicKey, privateKey } = crypto.generateKeyPairSync('x25519');
    return {
      publicKey: Buffer.from(publicKey.export({ type: 'spki', format: 'pem' })),
      privateKey: Buffer.from(privateKey.export({ type: 'pkcs8', format: 'pem' })),
    };
  }

  async encryptForTransport(
    data: Buffer,
    recipientPublicKey: Buffer,
    senderPrivateKey: Buffer,
  ): Promise<{ encrypted: Buffer; ephemeralPublicKey: Buffer }> {
    // Generate ephemeral key pair for this message
    const { publicKey: ephemeralPublicKey, privateKey: ephemeralPrivateKey } = 
      await this.generateNoiseKeyPair();

    // Derive shared secret
    const sharedSecret = crypto.diffieHellman({
      privateKey: crypto.createPrivateKey(ephemeralPrivateKey),
      publicKey: crypto.createPublicKey(recipientPublicKey),
    });

    // Use shared secret to encrypt the message
    const key = crypto.createHash('sha256').update(sharedSecret).digest();
    const { encrypted, iv, authTag } = this.encryptWithSymmetricKey(data, key);

    // Combine all components
    return {
      encrypted: Buffer.concat([iv, authTag, encrypted]),
      ephemeralPublicKey,
    };
  }

  async decryptFromTransport(
    encrypted: Buffer,
    ephemeralPublicKey: Buffer,
    recipientPrivateKey: Buffer,
  ): Promise<Buffer> {
    // Extract components
    const iv = encrypted.slice(0, 12);
    const authTag = encrypted.slice(12, 28);
    const ciphertext = encrypted.slice(28);

    // Derive shared secret
    const sharedSecret = crypto.diffieHellman({
      privateKey: crypto.createPrivateKey(recipientPrivateKey),
      publicKey: crypto.createPublicKey(ephemeralPublicKey),
    });

    // Use shared secret to decrypt the message
    const key = crypto.createHash('sha256').update(sharedSecret).digest();
    return this.decryptWithSymmetricKey(ciphertext, key, iv, authTag);
  }
} 