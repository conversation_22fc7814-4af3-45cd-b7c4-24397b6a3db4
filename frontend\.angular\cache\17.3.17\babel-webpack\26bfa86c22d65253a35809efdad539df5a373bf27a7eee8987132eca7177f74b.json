{"ast": null, "code": "import { BehaviorSubject, tap, switchMap, combineLatest, catchError, throwError } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"./api.service\";\nimport * as i2 from \"./secure-token.service\";\nexport class AuthService {\n  constructor(apiService, secureTokenService) {\n    this.apiService = apiService;\n    this.secureTokenService = secureTokenService;\n    this.userSubject = new BehaviorSubject(null);\n    this.loadingSubject = new BehaviorSubject(false);\n    this.errorSubject = new BehaviorSubject(null);\n    // Combine token, user, loading, and error observables to create auth state\n    this.authState$ = combineLatest([this.secureTokenService.token$, this.userSubject.asObservable(), this.loadingSubject.asObservable(), this.errorSubject.asObservable()]).pipe(tap(([token, user, isLoading, error]) => {\n      // Auto-refresh token if it's expiring soon\n      if (token && this.secureTokenService.isTokenExpiringSoon()) {\n        this.refreshTokenIfNeeded();\n      }\n    }), switchMap(([token, user, isLoading, error]) => {\n      const isAuthenticated = !!token && this.secureTokenService.isTokenValid();\n      return [{\n        isAuthenticated,\n        user,\n        isLoading,\n        error\n      }];\n    }));\n    this.initializeAuthState();\n  }\n  initializeAuthState() {\n    // Check for existing tokens and migrate from localStorage if needed\n    const legacyToken = localStorage.getItem('qsc_token');\n    const legacyUserStr = localStorage.getItem('qsc_user');\n    if (legacyToken && legacyUserStr) {\n      try {\n        const user = JSON.parse(legacyUserStr);\n        // Migrate to secure storage\n        this.secureTokenService.setTokens({\n          token: legacyToken,\n          expiresAt: this.getTokenExpiration(legacyToken) || Date.now() + 3600000,\n          tokenType: 'Bearer'\n        });\n        this.userSubject.next(user);\n        // Clean up legacy storage\n        localStorage.removeItem('qsc_token');\n        localStorage.removeItem('qsc_user');\n      } catch (error) {\n        console.error('Error migrating legacy auth data:', error);\n        this.logout();\n      }\n    } else {\n      // Check if we have a valid token in secure storage\n      const currentToken = this.secureTokenService.getToken();\n      if (currentToken && this.secureTokenService.isTokenValid()) {\n        // Fetch current user data\n        this.getCurrentUserProfile().subscribe({\n          next: user => this.userSubject.next(user),\n          error: () => this.logout()\n        });\n      }\n    }\n  }\n  login(emailOrCredentials, secretWord) {\n    let credentials;\n    if (typeof emailOrCredentials === 'string') {\n      credentials = {\n        email: emailOrCredentials,\n        password: secretWord\n      };\n    } else {\n      credentials = emailOrCredentials;\n    }\n    return this.apiService.post('/auth/login', credentials).pipe(tap(response => {\n      // Store tokens securely\n      const tokenData = {\n        token: response.accessToken,\n        refreshToken: response.refreshToken,\n        expiresAt: Date.now() + response.expiresIn * 1000,\n        tokenType: response.tokenType\n      };\n      this.secureTokenService.setTokens(tokenData);\n      // Update user state\n      this.userSubject.next(response.user);\n    }), catchError(error => {\n      console.error('Login failed:', error);\n      return throwError(() => error);\n    }));\n  }\n  register(registerData) {\n    return this.apiService.post('/auth/register', registerData).pipe(tap(response => {\n      // Store tokens securely after successful registration\n      const tokenData = {\n        token: response.accessToken,\n        refreshToken: response.refreshToken,\n        expiresAt: Date.now() + response.expiresIn * 1000,\n        tokenType: response.tokenType\n      };\n      this.secureTokenService.setTokens(tokenData);\n      // Update user state\n      this.userSubject.next(response.user);\n    }), catchError(error => {\n      console.error('Registration failed:', error);\n      return throwError(() => error);\n    }));\n  }\n  logout() {\n    // Clear all tokens\n    this.secureTokenService.clearTokens();\n    this.userSubject.next(null);\n    // Optionally call logout endpoint\n    this.apiService.post('/auth/logout', {}).subscribe({\n      error: error => console.warn('Logout endpoint failed:', error)\n    });\n  }\n  getCurrentUser() {\n    return this.userSubject.value;\n  }\n  getCurrentUserProfile() {\n    return this.apiService.get('/users/profile');\n  }\n  isAuthenticated() {\n    const token = this.secureTokenService.getToken();\n    return !!token && this.secureTokenService.isTokenValid();\n  }\n  getToken() {\n    return this.secureTokenService.getToken();\n  }\n  getUser() {\n    return this.userSubject.value;\n  }\n  /**\n   * Refresh token if needed\n   */\n  refreshTokenIfNeeded() {\n    const refreshToken = this.secureTokenService.getRefreshToken();\n    if (refreshToken) {\n      this.apiService.post('/auth/refresh', {\n        refresh_token: refreshToken\n      }).subscribe({\n        next: response => {\n          this.secureTokenService.setTokens({\n            token: response.accessToken,\n            refreshToken: response.refreshToken,\n            expiresAt: this.getTokenExpiration(response.accessToken) || Date.now() + 3600000,\n            tokenType: response.tokenType\n          });\n        },\n        error: () => {\n          // Refresh failed, logout user\n          this.logout();\n        }\n      });\n    }\n  }\n  /**\n   * Extract expiration time from JWT token\n   */\n  getTokenExpiration(token) {\n    try {\n      const payload = JSON.parse(atob(token.split('.')[1]));\n      return payload.exp;\n    } catch {\n      return null;\n    }\n  }\n  static {\n    this.ɵfac = function AuthService_Factory(t) {\n      return new (t || AuthService)(i0.ɵɵinject(i1.ApiService), i0.ɵɵinject(i2.SecureTokenService));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: AuthService,\n      factory: AuthService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["BehaviorSubject", "tap", "switchMap", "combineLatest", "catchError", "throwError", "AuthService", "constructor", "apiService", "secureTokenService", "userSubject", "loadingSubject", "errorSubject", "authState$", "token$", "asObservable", "pipe", "token", "user", "isLoading", "error", "isTokenExpiringSoon", "refreshTokenIfNeeded", "isAuthenticated", "isTokenValid", "initializeAuthState", "legacyToken", "localStorage", "getItem", "legacyUserStr", "JSON", "parse", "setTokens", "expiresAt", "getTokenExpiration", "Date", "now", "tokenType", "next", "removeItem", "console", "logout", "currentToken", "getToken", "getCurrentUserProfile", "subscribe", "login", "emailOrCredentials", "secretWord", "credentials", "email", "password", "post", "response", "tokenData", "accessToken", "refreshToken", "expiresIn", "register", "registerData", "clearTokens", "warn", "getCurrentUser", "value", "get", "getUser", "getRefreshToken", "refresh_token", "payload", "atob", "split", "exp", "i0", "ɵɵinject", "i1", "ApiService", "i2", "SecureTokenService", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Projects\\QSC1\\frontend\\src\\app\\services\\auth.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { BehaviorSubject, Observable, tap, switchMap, combineLatest, catchError, throwError } from 'rxjs';\nimport { ApiService } from './api.service';\nimport { SecureTokenService } from './secure-token.service';\nimport { LoginRequest, LoginResponse, RegisterRequest, User, AuthState, TokenData, AuthError } from '../types/auth.types';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class AuthService {\n  private userSubject = new BehaviorSubject<User | null>(null);\n  private loadingSubject = new BehaviorSubject<boolean>(false);\n  private errorSubject = new BehaviorSubject<AuthError | null>(null);\n  public authState$: Observable<AuthState>;\n\n  constructor(\n    private apiService: ApiService,\n    private secureTokenService: SecureTokenService\n  ) {\n    // Combine token, user, loading, and error observables to create auth state\n    this.authState$ = combineLatest([\n      this.secureTokenService.token$,\n      this.userSubject.asObservable(),\n      this.loadingSubject.asObservable(),\n      this.errorSubject.asObservable()\n    ]).pipe(\n      tap(([token, user, isLoading, error]) => {\n        // Auto-refresh token if it's expiring soon\n        if (token && this.secureTokenService.isTokenExpiringSoon()) {\n          this.refreshTokenIfNeeded();\n        }\n      }),\n      switchMap(([token, user, isLoading, error]) => {\n        const isAuthenticated = !!token && this.secureTokenService.isTokenValid();\n        return [{ isAuthenticated, user, isLoading, error }];\n      })\n    );\n\n    this.initializeAuthState();\n  }\n\n  private initializeAuthState(): void {\n    // Check for existing tokens and migrate from localStorage if needed\n    const legacyToken = localStorage.getItem('qsc_token');\n    const legacyUserStr = localStorage.getItem('qsc_user');\n\n    if (legacyToken && legacyUserStr) {\n      try {\n        const user = JSON.parse(legacyUserStr);\n        // Migrate to secure storage\n        this.secureTokenService.setTokens({\n          token: legacyToken,\n          expiresAt: this.getTokenExpiration(legacyToken) || Date.now() + 3600000, // 1 hour default\n          tokenType: 'Bearer'\n        });\n        this.userSubject.next(user);\n\n        // Clean up legacy storage\n        localStorage.removeItem('qsc_token');\n        localStorage.removeItem('qsc_user');\n      } catch (error) {\n        console.error('Error migrating legacy auth data:', error);\n        this.logout();\n      }\n    } else {\n      // Check if we have a valid token in secure storage\n      const currentToken = this.secureTokenService.getToken();\n      if (currentToken && this.secureTokenService.isTokenValid()) {\n        // Fetch current user data\n        this.getCurrentUserProfile().subscribe({\n          next: (user) => this.userSubject.next(user),\n          error: () => this.logout()\n        });\n      }\n    }\n  }\n\n  login(email: string, secretWord: string): Observable<LoginResponse>;\n  login(credentials: LoginRequest): Observable<LoginResponse>;\n  login(emailOrCredentials: string | LoginRequest, secretWord?: string): Observable<LoginResponse> {\n    let credentials: LoginRequest;\n\n    if (typeof emailOrCredentials === 'string') {\n      credentials = { email: emailOrCredentials, password: secretWord! };\n    } else {\n      credentials = emailOrCredentials;\n    }\n\n    return this.apiService.post<LoginResponse>('/auth/login', credentials).pipe(\n      tap(response => {\n        // Store tokens securely\n        const tokenData: TokenData = {\n          token: response.accessToken,\n          refreshToken: response.refreshToken,\n          expiresAt: Date.now() + (response.expiresIn * 1000),\n          tokenType: response.tokenType\n        };\n\n        this.secureTokenService.setTokens(tokenData);\n\n        // Update user state\n        this.userSubject.next(response.user);\n      }),\n      catchError(error => {\n        console.error('Login failed:', error);\n        return throwError(() => error);\n      })\n    );\n  }\n\n  register(registerData: RegisterRequest): Observable<LoginResponse> {\n    return this.apiService.post<LoginResponse>('/auth/register', registerData).pipe(\n      tap(response => {\n        // Store tokens securely after successful registration\n        const tokenData: TokenData = {\n          token: response.accessToken,\n          refreshToken: response.refreshToken,\n          expiresAt: Date.now() + (response.expiresIn * 1000),\n          tokenType: response.tokenType\n        };\n\n        this.secureTokenService.setTokens(tokenData);\n\n        // Update user state\n        this.userSubject.next(response.user);\n      }),\n      catchError(error => {\n        console.error('Registration failed:', error);\n        return throwError(() => error);\n      })\n    );\n  }\n\n  logout(): void {\n    // Clear all tokens\n    this.secureTokenService.clearTokens();\n    this.userSubject.next(null);\n\n    // Optionally call logout endpoint\n    this.apiService.post('/auth/logout', {}).subscribe({\n      error: (error) => console.warn('Logout endpoint failed:', error)\n    });\n  }\n\n  getCurrentUser(): User | null {\n    return this.userSubject.value;\n  }\n\n  getCurrentUserProfile(): Observable<User> {\n    return this.apiService.get<User>('/users/profile');\n  }\n\n  isAuthenticated(): boolean {\n    const token = this.secureTokenService.getToken();\n    return !!token && this.secureTokenService.isTokenValid();\n  }\n\n  getToken(): string | null {\n    return this.secureTokenService.getToken();\n  }\n\n  getUser(): User | null {\n    return this.userSubject.value;\n  }\n\n  /**\n   * Refresh token if needed\n   */\n  private refreshTokenIfNeeded(): void {\n    const refreshToken = this.secureTokenService.getRefreshToken();\n    if (refreshToken) {\n      this.apiService.post<LoginResponse>('/auth/refresh', { refresh_token: refreshToken })\n        .subscribe({\n          next: (response) => {\n            this.secureTokenService.setTokens({\n              token: response.accessToken,\n              refreshToken: response.refreshToken,\n              expiresAt: this.getTokenExpiration(response.accessToken) || Date.now() + 3600000,\n              tokenType: response.tokenType\n            });\n          },\n          error: () => {\n            // Refresh failed, logout user\n            this.logout();\n          }\n        });\n    }\n  }\n\n  /**\n   * Extract expiration time from JWT token\n   */\n  private getTokenExpiration(token: string): number | null {\n    try {\n      const payload = JSON.parse(atob(token.split('.')[1]));\n      return payload.exp;\n    } catch {\n      return null;\n    }\n  }\n}\n"], "mappings": "AACA,SAASA,eAAe,EAAcC,GAAG,EAAEC,SAAS,EAAEC,aAAa,EAAEC,UAAU,EAAEC,UAAU,QAAQ,MAAM;;;;AAQzG,OAAM,MAAOC,WAAW;EAMtBC,YACUC,UAAsB,EACtBC,kBAAsC;IADtC,KAAAD,UAAU,GAAVA,UAAU;IACV,KAAAC,kBAAkB,GAAlBA,kBAAkB;IAPpB,KAAAC,WAAW,GAAG,IAAIV,eAAe,CAAc,IAAI,CAAC;IACpD,KAAAW,cAAc,GAAG,IAAIX,eAAe,CAAU,KAAK,CAAC;IACpD,KAAAY,YAAY,GAAG,IAAIZ,eAAe,CAAmB,IAAI,CAAC;IAOhE;IACA,IAAI,CAACa,UAAU,GAAGV,aAAa,CAAC,CAC9B,IAAI,CAACM,kBAAkB,CAACK,MAAM,EAC9B,IAAI,CAACJ,WAAW,CAACK,YAAY,EAAE,EAC/B,IAAI,CAACJ,cAAc,CAACI,YAAY,EAAE,EAClC,IAAI,CAACH,YAAY,CAACG,YAAY,EAAE,CACjC,CAAC,CAACC,IAAI,CACLf,GAAG,CAAC,CAAC,CAACgB,KAAK,EAAEC,IAAI,EAAEC,SAAS,EAAEC,KAAK,CAAC,KAAI;MACtC;MACA,IAAIH,KAAK,IAAI,IAAI,CAACR,kBAAkB,CAACY,mBAAmB,EAAE,EAAE;QAC1D,IAAI,CAACC,oBAAoB,EAAE;;IAE/B,CAAC,CAAC,EACFpB,SAAS,CAAC,CAAC,CAACe,KAAK,EAAEC,IAAI,EAAEC,SAAS,EAAEC,KAAK,CAAC,KAAI;MAC5C,MAAMG,eAAe,GAAG,CAAC,CAACN,KAAK,IAAI,IAAI,CAACR,kBAAkB,CAACe,YAAY,EAAE;MACzE,OAAO,CAAC;QAAED,eAAe;QAAEL,IAAI;QAAEC,SAAS;QAAEC;MAAK,CAAE,CAAC;IACtD,CAAC,CAAC,CACH;IAED,IAAI,CAACK,mBAAmB,EAAE;EAC5B;EAEQA,mBAAmBA,CAAA;IACzB;IACA,MAAMC,WAAW,GAAGC,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC;IACrD,MAAMC,aAAa,GAAGF,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC;IAEtD,IAAIF,WAAW,IAAIG,aAAa,EAAE;MAChC,IAAI;QACF,MAAMX,IAAI,GAAGY,IAAI,CAACC,KAAK,CAACF,aAAa,CAAC;QACtC;QACA,IAAI,CAACpB,kBAAkB,CAACuB,SAAS,CAAC;UAChCf,KAAK,EAAES,WAAW;UAClBO,SAAS,EAAE,IAAI,CAACC,kBAAkB,CAACR,WAAW,CAAC,IAAIS,IAAI,CAACC,GAAG,EAAE,GAAG,OAAO;UACvEC,SAAS,EAAE;SACZ,CAAC;QACF,IAAI,CAAC3B,WAAW,CAAC4B,IAAI,CAACpB,IAAI,CAAC;QAE3B;QACAS,YAAY,CAACY,UAAU,CAAC,WAAW,CAAC;QACpCZ,YAAY,CAACY,UAAU,CAAC,UAAU,CAAC;OACpC,CAAC,OAAOnB,KAAK,EAAE;QACdoB,OAAO,CAACpB,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;QACzD,IAAI,CAACqB,MAAM,EAAE;;KAEhB,MAAM;MACL;MACA,MAAMC,YAAY,GAAG,IAAI,CAACjC,kBAAkB,CAACkC,QAAQ,EAAE;MACvD,IAAID,YAAY,IAAI,IAAI,CAACjC,kBAAkB,CAACe,YAAY,EAAE,EAAE;QAC1D;QACA,IAAI,CAACoB,qBAAqB,EAAE,CAACC,SAAS,CAAC;UACrCP,IAAI,EAAGpB,IAAI,IAAK,IAAI,CAACR,WAAW,CAAC4B,IAAI,CAACpB,IAAI,CAAC;UAC3CE,KAAK,EAAEA,CAAA,KAAM,IAAI,CAACqB,MAAM;SACzB,CAAC;;;EAGR;EAIAK,KAAKA,CAACC,kBAAyC,EAAEC,UAAmB;IAClE,IAAIC,WAAyB;IAE7B,IAAI,OAAOF,kBAAkB,KAAK,QAAQ,EAAE;MAC1CE,WAAW,GAAG;QAAEC,KAAK,EAAEH,kBAAkB;QAAEI,QAAQ,EAAEH;MAAW,CAAE;KACnE,MAAM;MACLC,WAAW,GAAGF,kBAAkB;;IAGlC,OAAO,IAAI,CAACvC,UAAU,CAAC4C,IAAI,CAAgB,aAAa,EAAEH,WAAW,CAAC,CAACjC,IAAI,CACzEf,GAAG,CAACoD,QAAQ,IAAG;MACb;MACA,MAAMC,SAAS,GAAc;QAC3BrC,KAAK,EAAEoC,QAAQ,CAACE,WAAW;QAC3BC,YAAY,EAAEH,QAAQ,CAACG,YAAY;QACnCvB,SAAS,EAAEE,IAAI,CAACC,GAAG,EAAE,GAAIiB,QAAQ,CAACI,SAAS,GAAG,IAAK;QACnDpB,SAAS,EAAEgB,QAAQ,CAAChB;OACrB;MAED,IAAI,CAAC5B,kBAAkB,CAACuB,SAAS,CAACsB,SAAS,CAAC;MAE5C;MACA,IAAI,CAAC5C,WAAW,CAAC4B,IAAI,CAACe,QAAQ,CAACnC,IAAI,CAAC;IACtC,CAAC,CAAC,EACFd,UAAU,CAACgB,KAAK,IAAG;MACjBoB,OAAO,CAACpB,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;MACrC,OAAOf,UAAU,CAAC,MAAMe,KAAK,CAAC;IAChC,CAAC,CAAC,CACH;EACH;EAEAsC,QAAQA,CAACC,YAA6B;IACpC,OAAO,IAAI,CAACnD,UAAU,CAAC4C,IAAI,CAAgB,gBAAgB,EAAEO,YAAY,CAAC,CAAC3C,IAAI,CAC7Ef,GAAG,CAACoD,QAAQ,IAAG;MACb;MACA,MAAMC,SAAS,GAAc;QAC3BrC,KAAK,EAAEoC,QAAQ,CAACE,WAAW;QAC3BC,YAAY,EAAEH,QAAQ,CAACG,YAAY;QACnCvB,SAAS,EAAEE,IAAI,CAACC,GAAG,EAAE,GAAIiB,QAAQ,CAACI,SAAS,GAAG,IAAK;QACnDpB,SAAS,EAAEgB,QAAQ,CAAChB;OACrB;MAED,IAAI,CAAC5B,kBAAkB,CAACuB,SAAS,CAACsB,SAAS,CAAC;MAE5C;MACA,IAAI,CAAC5C,WAAW,CAAC4B,IAAI,CAACe,QAAQ,CAACnC,IAAI,CAAC;IACtC,CAAC,CAAC,EACFd,UAAU,CAACgB,KAAK,IAAG;MACjBoB,OAAO,CAACpB,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5C,OAAOf,UAAU,CAAC,MAAMe,KAAK,CAAC;IAChC,CAAC,CAAC,CACH;EACH;EAEAqB,MAAMA,CAAA;IACJ;IACA,IAAI,CAAChC,kBAAkB,CAACmD,WAAW,EAAE;IACrC,IAAI,CAAClD,WAAW,CAAC4B,IAAI,CAAC,IAAI,CAAC;IAE3B;IACA,IAAI,CAAC9B,UAAU,CAAC4C,IAAI,CAAC,cAAc,EAAE,EAAE,CAAC,CAACP,SAAS,CAAC;MACjDzB,KAAK,EAAGA,KAAK,IAAKoB,OAAO,CAACqB,IAAI,CAAC,yBAAyB,EAAEzC,KAAK;KAChE,CAAC;EACJ;EAEA0C,cAAcA,CAAA;IACZ,OAAO,IAAI,CAACpD,WAAW,CAACqD,KAAK;EAC/B;EAEAnB,qBAAqBA,CAAA;IACnB,OAAO,IAAI,CAACpC,UAAU,CAACwD,GAAG,CAAO,gBAAgB,CAAC;EACpD;EAEAzC,eAAeA,CAAA;IACb,MAAMN,KAAK,GAAG,IAAI,CAACR,kBAAkB,CAACkC,QAAQ,EAAE;IAChD,OAAO,CAAC,CAAC1B,KAAK,IAAI,IAAI,CAACR,kBAAkB,CAACe,YAAY,EAAE;EAC1D;EAEAmB,QAAQA,CAAA;IACN,OAAO,IAAI,CAAClC,kBAAkB,CAACkC,QAAQ,EAAE;EAC3C;EAEAsB,OAAOA,CAAA;IACL,OAAO,IAAI,CAACvD,WAAW,CAACqD,KAAK;EAC/B;EAEA;;;EAGQzC,oBAAoBA,CAAA;IAC1B,MAAMkC,YAAY,GAAG,IAAI,CAAC/C,kBAAkB,CAACyD,eAAe,EAAE;IAC9D,IAAIV,YAAY,EAAE;MAChB,IAAI,CAAChD,UAAU,CAAC4C,IAAI,CAAgB,eAAe,EAAE;QAAEe,aAAa,EAAEX;MAAY,CAAE,CAAC,CAClFX,SAAS,CAAC;QACTP,IAAI,EAAGe,QAAQ,IAAI;UACjB,IAAI,CAAC5C,kBAAkB,CAACuB,SAAS,CAAC;YAChCf,KAAK,EAAEoC,QAAQ,CAACE,WAAW;YAC3BC,YAAY,EAAEH,QAAQ,CAACG,YAAY;YACnCvB,SAAS,EAAE,IAAI,CAACC,kBAAkB,CAACmB,QAAQ,CAACE,WAAW,CAAC,IAAIpB,IAAI,CAACC,GAAG,EAAE,GAAG,OAAO;YAChFC,SAAS,EAAEgB,QAAQ,CAAChB;WACrB,CAAC;QACJ,CAAC;QACDjB,KAAK,EAAEA,CAAA,KAAK;UACV;UACA,IAAI,CAACqB,MAAM,EAAE;QACf;OACD,CAAC;;EAER;EAEA;;;EAGQP,kBAAkBA,CAACjB,KAAa;IACtC,IAAI;MACF,MAAMmD,OAAO,GAAGtC,IAAI,CAACC,KAAK,CAACsC,IAAI,CAACpD,KAAK,CAACqD,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACrD,OAAOF,OAAO,CAACG,GAAG;KACnB,CAAC,MAAM;MACN,OAAO,IAAI;;EAEf;;;uBA9LWjE,WAAW,EAAAkE,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,kBAAA;IAAA;EAAA;;;aAAXvE,WAAW;MAAAwE,OAAA,EAAXxE,WAAW,CAAAyE,IAAA;MAAAC,UAAA,EAFV;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}