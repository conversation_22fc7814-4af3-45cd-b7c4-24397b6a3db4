import { <PERSON><PERSON><PERSON>, Column, PrimaryGeneratedC<PERSON>umn, CreateDateColumn, UpdateDateColumn, ManyToMany, JoinTable } from 'typeorm';
import { User } from '../../users/entities/user.entity';

@Entity('channels')
export class Channel {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  name: string;

  @Column({ nullable: true })
  description: string;

  @Column({ default: false })
  isPrivate: boolean;

  @Column({ nullable: true })
  inviteCode: string;

  @ManyToMany(() => User)
  @JoinTable({
    name: 'channel_members',
    joinColumn: { name: 'channel_id', referencedColumnName: 'id' },
    inverseJoinColumn: { name: 'user_id', referencedColumnName: 'id' },
  })
  members: User[];

  @ManyToMany(() => User)
  @JoinTable({
    name: 'channel_admins',
    joinColumn: { name: 'channel_id', referencedColumnName: 'id' },
    inverseJoinColumn: { name: 'user_id', referencedColumnName: 'id' },
  })
  admins: User[];

  @Column({ type: 'text', default: '', nullable: false })
  memberIds: string;

  @Column({ type: 'text', default: '', nullable: false })
  adminIds: string;

  @Column({ nullable: true })
  groupKey: string; // Encrypted group key for PQC-secured group messages

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @Column({ default: true })
  isActive: boolean;
} 