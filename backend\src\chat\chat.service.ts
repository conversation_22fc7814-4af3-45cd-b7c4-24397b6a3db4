import { Injectable, NotFoundException, UnauthorizedException, BadRequestException, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Message } from './entities/message.entity';
import { Channel } from './entities/channel.entity';
import { User } from '../users/entities/user.entity';
import { UserService } from '../user/user.service';
import { WebSocketServer } from '@nestjs/websockets';
import { Server } from 'socket.io';
import { SessionKey } from './entities/session-key.entity';
// import { KyberService } from '../security/kyber.service';
import { createHash, timingSafeEqual } from 'crypto';
import { v4 as uuidv4 } from 'uuid';
import * as crypto from 'crypto';

@Injectable()
export class ChatService {
  private readonly logger = new Logger(ChatService.name);

  @WebSocketServer()
  server: Server;

  constructor(
    @InjectRepository(Message)
    private messageRepository: Repository<Message>,
    @InjectRepository(Channel)
    private channelRepository: Repository<Channel>,
    private userService: UserService,
    @InjectRepository(SessionKey)
    private sessionKeyRepository: Repository<SessionKey>,
    @InjectRepository(User)
    private userRepository: Repository<User>,
    // private kyberService: KyberService,
  ) {}

  // Channel methods
  async createChannel(
    name: string,
    description: string,
    isPrivate: boolean,
    creatorId: string,
  ): Promise<Channel> {
    const creator = await this.userService.findById(creatorId);
    const channel = this.channelRepository.create({
      name,
      description,
      isPrivate,
      memberIds: creatorId,
      adminIds: creatorId,
    });
    return this.channelRepository.save(channel);
  }

  async getChannel(id: string, userId: string): Promise<Channel> {
    const channel = await this.channelRepository.findOne({ where: { id } });
    if (!channel) {
      throw new NotFoundException('Channel not found');
    }
    const memberIds = channel.memberIds ? channel.memberIds.split(',').filter(id => id) : [];
    if (channel.isPrivate && !memberIds.includes(userId)) {
      throw new UnauthorizedException('Not a member of this channel');
    }
    return channel;
  }

  async getUserChannels(userId: string): Promise<Channel[]> {
    const channels = await this.channelRepository.find();
    return channels.filter(channel => {
      const memberIds = channel.memberIds ? channel.memberIds.split(',').filter(id => id) : [];
      return memberIds.includes(userId) || !channel.isPrivate;
    });
  }

  async addMember(channelId: string, userId: string, adminId: string): Promise<Channel> {
    const channel = await this.getChannel(channelId, adminId);
    const adminIds = channel.adminIds ? channel.adminIds.split(',').filter(id => id) : [];
    if (!adminIds.includes(adminId)) {
      throw new UnauthorizedException('Not an admin of this channel');
    }
    const memberIds = channel.memberIds ? channel.memberIds.split(',').filter(id => id) : [];
    if (!memberIds.includes(userId)) {
      memberIds.push(userId);
      channel.memberIds = memberIds.join(',');
      return this.channelRepository.save(channel);
    }
    return channel;
  }

  async removeMember(channelId: string, userId: string, adminId: string): Promise<Channel> {
    const channel = await this.getChannel(channelId, adminId);
    const adminIds = channel.adminIds ? channel.adminIds.split(',').filter(id => id) : [];
    if (!adminIds.includes(adminId)) {
      throw new UnauthorizedException('Not an admin of this channel');
    }
    const memberIds = channel.memberIds ? channel.memberIds.split(',').filter(id => id) : [];
    channel.memberIds = memberIds.filter(id => id !== userId).join(',');
    channel.adminIds = adminIds.filter(id => id !== userId).join(',');
    return this.channelRepository.save(channel);
  }

  // Message methods
  async sendMessage(
    content: string,
    metadata: any,
    senderId: string,
    channelId?: string,
    recipientId?: string,
    isSelfDestructing?: boolean,
    selfDestructAt?: Date
  ) {
    // Get or create session key (only for direct messages)
    let sessionKey = null;
    if (recipientId && !channelId) {
      sessionKey = await this.getOrCreateSessionKey(senderId, recipientId);
    }

    // Encrypt message with session key or channel key
    const encryptedContent = await this.encryptMessage(content, sessionKey?.encryptedKey || 'default-key');

    // Create message hash for integrity verification
    const messageHash = this.createMessageHash(senderId, recipientId || channelId || '', content);

    // Store encrypted message
    const message = this.messageRepository.create({
      senderId: senderId,
      recipientId: recipientId,
      roomId: channelId,
      encryptedContent: encryptedContent,
      messageHash: messageHash,
      expiresAt: isSelfDestructing ? selfDestructAt : new Date(Date.now() + 24 * 60 * 60 * 1000),
      isDeleted: false,
      isRead: false,
      isCompromised: false,
      compromiseAttempts: 0,
    });

    return this.messageRepository.save(message);
  }

  async verifyAndReadMessage(messageId: string, userId: string, secretWord: string): Promise<string> {
    const message = await this.messageRepository.findOne({ where: { id: messageId } });
    if (!message) {
      throw new NotFoundException('Message not found');
    }

    if (message.recipientId !== userId) {
      throw new UnauthorizedException('Not authorized to read this message');
    }

    if (message.isRead) {
      throw new BadRequestException('Message has already been read');
    }

    const user = await this.userRepository.findOne({ where: { id: userId } });
    if (!user) {
      throw new NotFoundException('User not found');
    }

    if (user.accountStatus !== 'active') {
      throw new UnauthorizedException('Account is not active');
    }

    // Verify secret word
    const secretWordHash = createHash('sha3-512').update(secretWord).digest('hex');
    const isValid = timingSafeEqual(
      Buffer.from(secretWordHash),
      Buffer.from(user.secretWordHash.hash)
    );

    if (!isValid) {
      // Increment failed attempts
      message.compromiseAttempts += 1;
      await this.messageRepository.save(message);

      // If 3 failed attempts, handle compromise
      if (message.compromiseAttempts >= 3) {
        message.isCompromised = true;
        await this.messageRepository.save(message);

        // Handle account deactivation and message cleanup
        await this.handleCompromise(userId, message);

        // Delete message
        await this.messageRepository.remove(message);
        throw new BadRequestException('Account has been deactivated due to security concerns');
      }

      throw new UnauthorizedException('Invalid secret word');
    }

    // Get session key for decryption
    const sessionKey = await this.getOrCreateSessionKey(message.senderId, message.recipientId!);

    // Decrypt message
    const decryptedContent = await this.decryptMessage(
      message.encryptedContent,
      sessionKey.encryptedKey
    );

    // Mark message as read and set read timestamp
    message.isRead = true;
    message.readAt = new Date();
    await this.messageRepository.save(message);

    // Schedule message deletion after reading
    setTimeout(async () => {
      await this.messageRepository.remove(message);
    }, 5000); // Delete after 5 seconds

    return decryptedContent;
  }

  private async handleCompromise(compromisedUserId: string, message: Message) {
    // 1. Deactivate compromised account
    const compromisedUser = await this.userRepository.findOne({ where: { id: compromisedUserId } });
    if (compromisedUser) {
      compromisedUser.accountStatus = 'compromised';
      compromisedUser.deactivatedAt = new Date();
      compromisedUser.deactivationReason = 'Multiple failed secret word attempts';
      await this.userRepository.save(compromisedUser);
    }

    // 2. Delete all messages to/from compromised user
    await this.messageRepository.delete({ senderId: compromisedUserId });
    await this.messageRepository.delete({ recipientId: compromisedUserId });

    // 3. Delete all session keys involving compromised user
    await this.sessionKeyRepository.delete({ user1Id: compromisedUserId });
    await this.sessionKeyRepository.delete({ user2Id: compromisedUserId });

    // 4. Notify sender about compromise and message deletion
    await this.notifyCompromise(message.senderId, compromisedUserId);

    // 5. Generate new invite code for potential reinvitation
    const newInviteCode = uuidv4();
    if (compromisedUser) {
      compromisedUser.newInviteCode = newInviteCode;
      await this.userRepository.save(compromisedUser);
    }

    return newInviteCode;
  }

  private async notifyCompromise(senderId: string, compromisedUserId: string) {
    // Create a system message about the compromise
    const systemMessage = this.messageRepository.create({
      senderId: 'SYSTEM',
      recipientId: senderId,
      encryptedContent: await this.encryptMessage(
        `SECURITY ALERT: User ${compromisedUserId} has been compromised. All messages have been deleted. The account has been deactivated and requires a new invitation to rejoin.`,
        'SYSTEM_KEY'
      ),
      messageHash: this.createMessageHash('SYSTEM', senderId, 'COMPROMISE_WARNING'),
      isRead: false,
      isDeleted: false,
      isCompromised: false,
      compromiseAttempts: 0
    });

    await this.messageRepository.save(systemMessage);

    // Delete the message after 1 minute to ensure the sender sees it
    setTimeout(async () => {
      await this.messageRepository.remove(systemMessage);
    }, 60000);
  }

  async getMessages(userId: string, otherUserId: string, limit: number = 50) {
    const sessionKey = await this.getOrCreateSessionKey(userId, otherUserId);
    
    const messages = await this.messageRepository.find({
      where: [
        { senderId: userId, recipientId: otherUserId },
        { senderId: otherUserId, recipientId: userId }
      ],
      order: { createdAt: 'DESC' },
      take: limit
    });

    // Decrypt messages
    return Promise.all(messages.map(async (message) => {
      const decryptedContent = await this.decryptMessage(
        message.encrypted_content,
        sessionKey.encryptedKey
      );
      return {
        ...message,
        content: decryptedContent,
      };
    }));
  }

  async markMessageAsRead(messageId: string, userId: string): Promise<Message> {
    const message = await this.messageRepository.findOne({ where: { id: messageId } });
    if (!message) {
      throw new NotFoundException('Message not found');
    }

    if (!message.isRead) {
      message.isRead = true;
      message.readAt = new Date();
      return this.messageRepository.save(message);
    }

    return message;
  }

  async deleteMessage(messageId: string, userId: string): Promise<void> {
    const message = await this.messageRepository.findOne({ where: { id: messageId } });
    if (!message) {
      throw new NotFoundException('Message not found');
    }

    if (message.senderId !== userId) {
      throw new UnauthorizedException('Cannot delete another user\'s message');
    }

    await this.messageRepository.remove(message);
  }

  private async getOrCreateSessionKey(user1Id: string, user2Id: string) {
    // Ensure consistent ordering of user IDs
    const [id1, id2] = [user1Id, user2Id].sort();

    let sessionKey = await this.sessionKeyRepository.findOne({
      where: {
        user1Id: id1,
        user2Id: id2,
        isActive: true
      }
    });

    if (!sessionKey || this.isKeyExpired(sessionKey)) {
      // Generate new PQC key pair and shared secret
      const { publicKey, privateKey, sharedSecret } = await this.generatePQCKeys();

      // Store new session key
      sessionKey = this.sessionKeyRepository.create({
        user1Id: id1,
        user2Id: id2,
        encryptedKey: sharedSecret,
        keyHash: createHash('sha256').update(sharedSecret).digest('hex'),
        expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000), // 24 hours
        isActive: true,
      });

      await this.sessionKeyRepository.save(sessionKey);
    }

    return sessionKey;
  }

  private async generatePQCKeys(): Promise<{ publicKey: string; privateKey: string; sharedSecret: string }> {
    try {
      // Generate Kyber key pair for key exchange (placeholder implementation)
      // const keyPair = await this.kyberService.generateKeyPair();

      // Generate a shared secret using the key pair (placeholder implementation)
      // const { sessionKey } = await this.kyberService.generateSessionKey(keyPair.publicKey);
      const sessionKey = crypto.randomBytes(32); // Temporary fallback
      const keyPair = {
        publicKey: crypto.randomBytes(32),
        privateKey: crypto.randomBytes(32)
      };

      return {
        publicKey: keyPair.publicKey.toString('base64'),
        privateKey: keyPair.privateKey.toString('base64'),
        sharedSecret: sessionKey.toString('base64'),
      };
    } catch (error) {
      this.logger.error('Failed to generate PQC keys', error);
      throw new Error('PQC key generation failed');
    }
  }

  private async encryptMessage(content: string, key: string): Promise<string> {
    try {
      // Convert base64 key to buffer
      const keyBuffer = Buffer.from(key, 'base64');

      // Use AES-256-GCM for symmetric encryption with the PQC-derived key
      const iv = crypto.randomBytes(12);
      const cipher = crypto.createCipheriv('aes-256-gcm', keyBuffer, iv);

      let encrypted = cipher.update(content, 'utf8', 'base64');
      encrypted += cipher.final('base64');

      const authTag = cipher.getAuthTag();

      // Combine IV, auth tag, and encrypted content
      const result = {
        iv: iv.toString('base64'),
        authTag: authTag.toString('base64'),
        encrypted: encrypted,
      };

      this.logger.debug('Message encrypted successfully', {
        contentLength: content.length,
        encryptedLength: encrypted.length,
      });

      return JSON.stringify(result);
    } catch (error) {
      this.logger.error('Failed to encrypt message', error);
      throw new Error('Message encryption failed');
    }
  }

  private async decryptMessage(encryptedContent: string, key: string): Promise<string> {
    try {
      // Convert base64 key to buffer
      const keyBuffer = Buffer.from(key, 'base64');

      // Parse the encrypted content
      const { iv, authTag, encrypted } = JSON.parse(encryptedContent);

      // Create decipher
      const decipher = crypto.createDecipheriv('aes-256-gcm', keyBuffer, Buffer.from(iv, 'base64'));
      decipher.setAuthTag(Buffer.from(authTag, 'base64'));

      let decrypted = decipher.update(encrypted, 'base64', 'utf8');
      decrypted += decipher.final('utf8');

      this.logger.debug('Message decrypted successfully', {
        encryptedLength: encrypted.length,
        decryptedLength: decrypted.length,
      });

      return decrypted;
    } catch (error) {
      this.logger.error('Failed to decrypt message', error);
      throw new Error('Message decryption failed');
    }
  }

  private createMessageHash(senderId: string, recipientId: string, content: string): string {
    return createHash('sha3-512')
      .update(`${senderId}:${recipientId}:${content}:${Date.now()}`)
      .digest('hex');
  }

  private isKeyExpired(sessionKey: SessionKey): boolean {
    return new Date(sessionKey.expiresAt) < new Date();
  }

  // Add method to handle reinvitation
  async reinviteUser(inviterId: string, inviteCode: string): Promise<{ userId: string; newSecretWord: string }> {
    const compromisedUser = await this.userRepository.findOne({ 
      where: { 
        newInviteCode: inviteCode,
        accountStatus: 'compromised'
      }
    });

    if (!compromisedUser) {
      throw new NotFoundException('Invalid or expired invite code');
    }

    // Generate new secret word
    const newSecretWord = this.generateSecretWord();
    const secretWordHash = createHash('sha3-512').update(newSecretWord).digest('hex');

    // Update user status
    compromisedUser.accountStatus = 'active';
    compromisedUser.secretWordHash = {
      hash: secretWordHash,
      salt: '',
      attempts: 0,
      lastAttempt: new Date().toISOString()
    };
    compromisedUser.reinvitedBy = inviterId;
    compromisedUser.reinvitedAt = new Date();
    compromisedUser.newInviteCode = null;
    compromisedUser.failedAttempts = 0;
    compromisedUser.isCompromised = false;
    
    await this.userRepository.save(compromisedUser);

    return {
      userId: compromisedUser.id,
      newSecretWord
    };
  }

  private generateSecretWord(): string {
    const uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
    const lowercase = 'abcdefghijklmnopqrstuvwxyz';
    const digits = '**********';
    const symbols = '!@#$%^&*()_+-=[]{}|;:,.<>?';

    const getRandomChar = (str: string) => str[Math.floor(Math.random() * str.length)];

    return [
      getRandomChar(uppercase),
      getRandomChar(lowercase),
      getRandomChar(digits),
      getRandomChar(symbols)
    ].sort(() => Math.random() - 0.5).join('');
  }
} 