{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { Subject, takeUntil } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../services/auth.service\";\nimport * as i2 from \"../../services/message.service\";\nimport * as i3 from \"../../services/notification.service\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/forms\";\nfunction QscMainComponent_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 9);\n  }\n}\nfunction QscMainComponent_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 10);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.unreadCount, \" \");\n  }\n}\nfunction QscMainComponent_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 11)(1, \"span\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 12);\n    i0.ɵɵlistener(\"click\", function QscMainComponent_div_6_Template_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.logout());\n    });\n    i0.ɵɵtext(4, \"\\u00D7\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r0.currentUser.username);\n  }\n}\nfunction QscMainComponent_div_7_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 26);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.loginError, \" \");\n  }\n}\nfunction QscMainComponent_div_7_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 27);\n    i0.ɵɵelement(1, \"div\", 28);\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3, \"Authenticating...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction QscMainComponent_div_7_div_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 29)(1, \"p\");\n    i0.ɵɵtext(2, \"\\uD83D\\uDD12 Protected by post-quantum cryptography\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p\", 30);\n    i0.ɵɵtext(4, \"Form auto-submits when credentials are valid\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction QscMainComponent_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 13);\n    i0.ɵɵlistener(\"click\", function QscMainComponent_div_7_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.closeLoginModal());\n    });\n    i0.ɵɵelementStart(1, \"div\", 14);\n    i0.ɵɵlistener(\"click\", function QscMainComponent_div_7_Template_div_click_1_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelementStart(2, \"div\", 15)(3, \"h2\");\n    i0.ɵɵtext(4, \"Sign In\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 16);\n    i0.ɵɵlistener(\"click\", function QscMainComponent_div_7_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.closeLoginModal());\n    });\n    i0.ɵɵtext(6, \"\\u00D7\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 17)(8, \"div\", 18)(9, \"label\", 19);\n    i0.ɵɵtext(10, \"Email\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"input\", 20);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function QscMainComponent_div_7_Template_input_ngModelChange_11_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r0 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r0.loginCredentials.email, $event) || (ctx_r0.loginCredentials.email = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"input\", function QscMainComponent_div_7_Template_input_input_11_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.onLoginInputChange());\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"div\", 18)(13, \"label\", 21);\n    i0.ɵɵtext(14, \"Secret Word\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"input\", 22);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function QscMainComponent_div_7_Template_input_ngModelChange_15_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r0 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r0.loginCredentials.secretWord, $event) || (ctx_r0.loginCredentials.secretWord = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"input\", function QscMainComponent_div_7_Template_input_input_15_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.onLoginInputChange());\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(16, QscMainComponent_div_7_div_16_Template, 2, 1, \"div\", 23)(17, QscMainComponent_div_7_div_17_Template, 4, 0, \"div\", 24)(18, QscMainComponent_div_7_div_18_Template, 5, 0, \"div\", 25);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(11);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r0.loginCredentials.email);\n    i0.ɵɵproperty(\"disabled\", ctx_r0.isLoading);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r0.loginCredentials.secretWord);\n    i0.ɵɵproperty(\"disabled\", ctx_r0.isLoading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.loginError);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isLoading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.isLoading);\n  }\n}\nfunction QscMainComponent_div_8_div_18_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 46)(1, \"span\", 47);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 48);\n    i0.ɵɵlistener(\"click\", function QscMainComponent_div_8_div_18_Template_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.switchMessageType(ctx_r0.messageType));\n    });\n    i0.ɵɵtext(4, \"\\u00D7\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r0.getSelectedRecipientName());\n  }\n}\nfunction QscMainComponent_div_8_div_19_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 52);\n    i0.ɵɵlistener(\"click\", function QscMainComponent_div_8_div_19_div_1_Template_div_click_0_listener() {\n      const contact_r7 = i0.ɵɵrestoreView(_r6).$implicit;\n      const ctx_r0 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r0.selectContact(contact_r7));\n    });\n    i0.ɵɵelementStart(1, \"div\", 53)(2, \"span\", 54);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 55);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 56);\n    i0.ɵɵelement(7, \"span\", 57);\n    i0.ɵɵelementStart(8, \"span\", 58);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const contact_r7 = ctx.$implicit;\n    const ctx_r0 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"hidden\", ctx_r0.messageType !== \"direct\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(contact_r7.username);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(contact_r7.email);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"online\", contact_r7.isOnline)(\"offline\", !contact_r7.isOnline);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", contact_r7.isOnline ? \"Online\" : \"Offline\", \" \");\n  }\n}\nfunction QscMainComponent_div_8_div_19_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 52);\n    i0.ɵɵlistener(\"click\", function QscMainComponent_div_8_div_19_div_2_Template_div_click_0_listener() {\n      const group_r9 = i0.ɵɵrestoreView(_r8).$implicit;\n      const ctx_r0 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r0.selectGroup(group_r9));\n    });\n    i0.ɵɵelementStart(1, \"div\", 59)(2, \"span\", 60);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 61);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 62);\n    i0.ɵɵelement(7, \"span\", 57);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const group_r9 = ctx.$implicit;\n    const ctx_r0 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"hidden\", ctx_r0.messageType !== \"group\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(group_r9.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", group_r9.members.length, \" members\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"active\", group_r9.isActive);\n  }\n}\nfunction QscMainComponent_div_8_div_19_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 63);\n    i0.ɵɵtext(1, \" No contacts found \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction QscMainComponent_div_8_div_19_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 63);\n    i0.ɵɵtext(1, \" No groups found \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction QscMainComponent_div_8_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 49);\n    i0.ɵɵtemplate(1, QscMainComponent_div_8_div_19_div_1_Template, 10, 8, \"div\", 50)(2, QscMainComponent_div_8_div_19_div_2_Template, 8, 5, \"div\", 50)(3, QscMainComponent_div_8_div_19_div_3_Template, 2, 0, \"div\", 51)(4, QscMainComponent_div_8_div_19_div_4_Template, 2, 0, \"div\", 51);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.filteredContacts);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.filteredGroups);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.messageType === \"direct\" && ctx_r0.filteredContacts.length === 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.messageType === \"group\" && ctx_r0.filteredGroups.length === 0);\n  }\n}\nfunction QscMainComponent_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 13);\n    i0.ɵɵlistener(\"click\", function QscMainComponent_div_8_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.closeMessageComposer());\n    });\n    i0.ɵɵelementStart(1, \"div\", 31);\n    i0.ɵɵlistener(\"click\", function QscMainComponent_div_8_Template_div_click_1_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelementStart(2, \"div\", 15)(3, \"h2\");\n    i0.ɵɵtext(4, \"Compose Message\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 16);\n    i0.ɵɵlistener(\"click\", function QscMainComponent_div_8_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.closeMessageComposer());\n    });\n    i0.ɵɵtext(6, \"\\u00D7\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 17)(8, \"div\", 32)(9, \"button\", 33);\n    i0.ɵɵlistener(\"click\", function QscMainComponent_div_8_Template_button_click_9_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.switchMessageType(\"direct\"));\n    });\n    i0.ɵɵtext(10, \" \\uD83D\\uDC64 Direct \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"button\", 33);\n    i0.ɵɵlistener(\"click\", function QscMainComponent_div_8_Template_button_click_11_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.switchMessageType(\"group\"));\n    });\n    i0.ɵɵtext(12, \" \\uD83D\\uDC65 Group \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"div\", 18)(14, \"label\", 34);\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"div\", 35)(17, \"input\", 36);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function QscMainComponent_div_8_Template_input_ngModelChange_17_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r0 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r0.recipientSearchQuery, $event) || (ctx_r0.recipientSearchQuery = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"input\", function QscMainComponent_div_8_Template_input_input_17_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.onRecipientSearchChange());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(18, QscMainComponent_div_8_div_18_Template, 5, 1, \"div\", 37)(19, QscMainComponent_div_8_div_19_Template, 5, 4, \"div\", 38);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(20, \"div\", 18)(21, \"label\", 39);\n    i0.ɵɵtext(22, \"Message\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"textarea\", 40);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function QscMainComponent_div_8_Template_textarea_ngModelChange_23_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r0 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r0.messageContent, $event) || (ctx_r0.messageContent = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"div\", 41);\n    i0.ɵɵtext(25);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(26, \"div\", 42)(27, \"button\", 43);\n    i0.ɵɵlistener(\"click\", function QscMainComponent_div_8_Template_button_click_27_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.sendMessage());\n    });\n    i0.ɵɵtext(28, \" Send Message \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"button\", 44);\n    i0.ɵɵlistener(\"click\", function QscMainComponent_div_8_Template_button_click_29_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.closeMessageComposer());\n    });\n    i0.ɵɵtext(30, \" Cancel \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(31, \"div\", 45)(32, \"p\");\n    i0.ɵɵtext(33, \"Press Enter to send \\u2022 Shift+Enter for new line\");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(9);\n    i0.ɵɵclassProp(\"active\", ctx_r0.messageType === \"direct\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"active\", ctx_r0.messageType === \"group\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.messageType === \"direct\" ? \"To (Contact)\" : \"To (Group)\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r0.recipientSearchQuery);\n    i0.ɵɵproperty(\"placeholder\", ctx_r0.messageType === \"direct\" ? \"Search contacts...\" : \"Search groups...\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.getSelectedRecipientName());\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.recipientSearchQuery && !ctx_r0.getSelectedRecipientName());\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r0.messageContent);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r0.messageContent.length, \"/1000\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", !ctx_r0.isMessageValid());\n  }\n}\nfunction QscMainComponent_div_9_div_8_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 69)(1, \"div\", 70)(2, \"span\", 71);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 72);\n    i0.ɵɵtext(5);\n    i0.ɵɵpipe(6, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 73);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const message_r11 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(message_r11.sender);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(6, 3, message_r11.timestamp, \"short\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(message_r11.content);\n  }\n}\nfunction QscMainComponent_div_9_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 67);\n    i0.ɵɵtemplate(1, QscMainComponent_div_9_div_8_div_1_Template, 9, 6, \"div\", 68);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.messages)(\"ngForTrackBy\", ctx_r0.trackMessage);\n  }\n}\nfunction QscMainComponent_div_9_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 74)(1, \"p\");\n    i0.ɵɵtext(2, \"No messages yet\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p\", 75);\n    i0.ɵɵtext(4, \"Click the circle to compose your first message\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction QscMainComponent_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 13);\n    i0.ɵɵlistener(\"click\", function QscMainComponent_div_9_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.closeMessagesViewer());\n    });\n    i0.ɵɵelementStart(1, \"div\", 64);\n    i0.ɵɵlistener(\"click\", function QscMainComponent_div_9_Template_div_click_1_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelementStart(2, \"div\", 15)(3, \"h2\");\n    i0.ɵɵtext(4, \"Messages\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 16);\n    i0.ɵɵlistener(\"click\", function QscMainComponent_div_9_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.closeMessagesViewer());\n    });\n    i0.ɵɵtext(6, \"\\u00D7\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 17);\n    i0.ɵɵtemplate(8, QscMainComponent_div_9_div_8_Template, 2, 2, \"div\", 65)(9, QscMainComponent_div_9_div_9_Template, 5, 0, \"div\", 66);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.messages.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.messages.length === 0);\n  }\n}\nfunction QscMainComponent_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 76)(1, \"span\");\n    i0.ɵɵtext(2, \"ESC to close modals\");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport class QscMainComponent {\n  constructor(authService, messageService, notificationService) {\n    this.authService = authService;\n    this.messageService = messageService;\n    this.notificationService = notificationService;\n    this.destroy$ = new Subject();\n    // Circle state management\n    this.circleState = 'guest';\n    // Modal states\n    this.showLoginModal = false;\n    this.showMessageModal = false;\n    this.showMessagesModal = false;\n    this.showContextMenu = false;\n    this.showAccountSettings = false;\n    // Context menu position\n    this.contextMenuPosition = {\n      x: 0,\n      y: 0\n    };\n    // Authentication\n    this.loginCredentials = {\n      email: '',\n      secretWord: ''\n    };\n    this.loginError = '';\n    this.isLoading = false;\n    // Messaging\n    this.messageContent = '';\n    this.selectedRecipient = '';\n    this.selectedGroup = '';\n    this.messageType = 'direct';\n    this.messages = [];\n    this.unreadCount = 0;\n    // Contacts and Groups\n    this.contacts = [];\n    this.groups = [];\n    this.filteredContacts = [];\n    this.filteredGroups = [];\n    this.recipientSearchQuery = '';\n    // User info\n    this.currentUser = null;\n    // Account settings\n    this.userProfile = {\n      avatar: '',\n      email: '',\n      phoneNumber: ''\n    };\n    // Long press handling\n    this.longPressTimer = null;\n    this.longPressDuration = 500; // 500ms for long press\n  }\n  ngOnInit() {\n    this.initializeApp();\n    this.setupMessageListener();\n    this.setupAuthListener();\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  initializeApp() {\n    // Check if user is already authenticated\n    if (this.authService.isAuthenticated()) {\n      this.currentUser = this.authService.getCurrentUser();\n      this.circleState = 'authenticated';\n      this.loadMessages();\n      this.loadContacts();\n      this.loadGroups();\n    } else {\n      this.circleState = 'guest';\n    }\n  }\n  setupAuthListener() {\n    this.authService.authState$.pipe(takeUntil(this.destroy$)).subscribe(user => {\n      if (user) {\n        this.currentUser = user;\n        this.circleState = 'authenticated';\n        this.showLoginModal = false;\n        this.loadMessages();\n      } else {\n        this.currentUser = null;\n        this.circleState = 'guest';\n        this.messages = [];\n        this.unreadCount = 0;\n      }\n    });\n  }\n  setupMessageListener() {\n    this.messageService.messages$.pipe(takeUntil(this.destroy$)).subscribe(messages => {\n      this.messages = messages;\n      this.updateUnreadCount();\n    });\n    this.messageService.newMessage$.pipe(takeUntil(this.destroy$)).subscribe(message => {\n      this.messages.unshift(message);\n      this.updateUnreadCount();\n      this.notificationService.showNotification('New message received');\n    });\n  }\n  updateUnreadCount() {\n    this.unreadCount = this.messages.filter(m => !m.read).length;\n    if (this.unreadCount > 0 && this.circleState === 'authenticated') {\n      this.circleState = 'unread';\n    } else if (this.unreadCount === 0 && this.circleState === 'unread') {\n      this.circleState = 'authenticated';\n    }\n  }\n  loadMessages() {\n    this.messageService.loadMessages().pipe(takeUntil(this.destroy$)).subscribe({\n      next: messages => {\n        this.messages = messages;\n        this.updateUnreadCount();\n      },\n      error: error => {\n        console.error('Failed to load messages:', error);\n      }\n    });\n  }\n  loadContacts() {\n    // TODO: Replace with actual API call\n    this.contacts = [{\n      id: '1',\n      username: 'alice',\n      email: '<EMAIL>',\n      isOnline: true\n    }, {\n      id: '2',\n      username: 'bob',\n      email: '<EMAIL>',\n      isOnline: false,\n      lastSeen: new Date(Date.now() - 300000) // 5 minutes ago\n    }];\n    this.filteredContacts = [...this.contacts];\n  }\n  loadGroups() {\n    // TODO: Replace with actual API call\n    this.groups = [{\n      id: 'group1',\n      name: 'Work Team',\n      members: ['1', '2', 'current-user'],\n      isActive: true\n    }, {\n      id: 'group2',\n      name: 'Family',\n      members: ['3', '4', 'current-user'],\n      isActive: true\n    }];\n    this.filteredGroups = [...this.groups];\n  }\n  // Circle click handler - main interaction point\n  onCircleClick() {\n    // Don't handle click if context menu is showing\n    if (this.showContextMenu) {\n      this.closeContextMenu();\n      return;\n    }\n    switch (this.circleState) {\n      case 'guest':\n        this.openLoginModal();\n        break;\n      case 'authenticated':\n        this.openMessageComposer();\n        break;\n      case 'unread':\n        this.openMessagesViewer();\n        break;\n      case 'composing':\n        // Already composing, do nothing or close\n        break;\n    }\n  }\n  // Right click handler\n  onCircleRightClick(event) {\n    event.preventDefault();\n    // Only show context menu for authenticated users\n    if (this.circleState === 'guest') return;\n    this.showContextMenu = true;\n    this.contextMenuPosition = {\n      x: event.clientX,\n      y: event.clientY\n    };\n  }\n  // Touch event handlers for long press\n  onCircleTouchStart(event) {\n    // Only for authenticated users\n    if (this.circleState === 'guest') return;\n    this.longPressTimer = setTimeout(() => {\n      const touch = event.touches[0];\n      this.showContextMenu = true;\n      this.contextMenuPosition = {\n        x: touch.clientX,\n        y: touch.clientY\n      };\n      // Provide haptic feedback if available\n      if (navigator.vibrate) {\n        navigator.vibrate(50);\n      }\n    }, this.longPressDuration);\n  }\n  onCircleTouchEnd() {\n    if (this.longPressTimer) {\n      clearTimeout(this.longPressTimer);\n      this.longPressTimer = null;\n    }\n  }\n  onCircleTouchMove() {\n    // Cancel long press if user moves finger\n    if (this.longPressTimer) {\n      clearTimeout(this.longPressTimer);\n      this.longPressTimer = null;\n    }\n  }\n  // Authentication methods\n  openLoginModal() {\n    this.showLoginModal = true;\n    this.loginCredentials = {\n      email: '',\n      secretWord: ''\n    };\n    this.loginError = '';\n  }\n  closeLoginModal() {\n    this.showLoginModal = false;\n    this.loginCredentials = {\n      email: '',\n      secretWord: ''\n    };\n    this.loginError = '';\n  }\n  onLoginInputChange() {\n    // Auto-submit when both fields are valid\n    if (this.isValidCredentials()) {\n      this.performLogin();\n    }\n  }\n  isValidCredentials() {\n    const emailValid = this.loginCredentials.email.includes('@') && this.loginCredentials.email.includes('.');\n    const secretWordValid = this.loginCredentials.secretWord.length >= 4 && /[A-Z]/.test(this.loginCredentials.secretWord) && /[a-z]/.test(this.loginCredentials.secretWord) && /[0-9]/.test(this.loginCredentials.secretWord) && /[^A-Za-z0-9]/.test(this.loginCredentials.secretWord);\n    return emailValid && secretWordValid;\n  }\n  performLogin() {\n    if (this.isLoading) return;\n    this.isLoading = true;\n    this.loginError = '';\n    this.authService.login(this.loginCredentials.email, this.loginCredentials.secretWord).pipe(takeUntil(this.destroy$)).subscribe({\n      next: response => {\n        this.isLoading = false;\n        // Auth state will be updated via authState$ subscription\n      },\n      error: error => {\n        this.isLoading = false;\n        this.loginError = error.message || 'Authentication failed';\n      }\n    });\n  }\n  // Message composition methods\n  openMessageComposer() {\n    this.showMessageModal = true;\n    this.messageContent = '';\n    this.selectedRecipient = '';\n    this.selectedGroup = '';\n    this.messageType = 'direct';\n    this.recipientSearchQuery = '';\n    this.filteredContacts = [...this.contacts];\n    this.filteredGroups = [...this.groups];\n    this.circleState = 'composing';\n  }\n  closeMessageComposer() {\n    this.showMessageModal = false;\n    this.messageContent = '';\n    this.selectedRecipient = '';\n    this.selectedGroup = '';\n    this.recipientSearchQuery = '';\n    this.circleState = 'authenticated';\n  }\n  sendMessage() {\n    if (!this.messageContent.trim()) return;\n    // Validate recipient selection\n    if (this.messageType === 'direct' && !this.selectedRecipient) {\n      this.notificationService.showNotification('Please select a recipient', 'warning');\n      return;\n    }\n    if (this.messageType === 'group' && !this.selectedGroup) {\n      this.notificationService.showNotification('Please select a group', 'warning');\n      return;\n    }\n    const message = {\n      content: this.messageContent.trim(),\n      timestamp: new Date(),\n      sender: this.currentUser?.username || 'Unknown',\n      recipient: this.messageType === 'direct' ? this.selectedRecipient : undefined,\n      groupId: this.messageType === 'group' ? this.selectedGroup : undefined\n    };\n    this.messageService.sendMessage(message).pipe(takeUntil(this.destroy$)).subscribe({\n      next: () => {\n        this.closeMessageComposer();\n        this.notificationService.showNotification('Message sent');\n      },\n      error: error => {\n        console.error('Failed to send message:', error);\n        this.notificationService.showNotification('Failed to send message', 'error');\n      }\n    });\n  }\n  // Recipient selection methods\n  onRecipientSearchChange() {\n    const query = this.recipientSearchQuery.toLowerCase();\n    if (this.messageType === 'direct') {\n      this.filteredContacts = this.contacts.filter(contact => contact.username.toLowerCase().includes(query) || contact.email.toLowerCase().includes(query));\n    } else {\n      this.filteredGroups = this.groups.filter(group => group.name.toLowerCase().includes(query));\n    }\n  }\n  selectContact(contact) {\n    this.selectedRecipient = contact.id;\n    this.recipientSearchQuery = contact.username;\n    this.filteredContacts = [];\n  }\n  selectGroup(group) {\n    this.selectedGroup = group.id;\n    this.recipientSearchQuery = group.name;\n    this.filteredGroups = [];\n  }\n  switchMessageType(type) {\n    this.messageType = type;\n    this.selectedRecipient = '';\n    this.selectedGroup = '';\n    this.recipientSearchQuery = '';\n    this.onRecipientSearchChange();\n  }\n  getSelectedRecipientName() {\n    if (this.messageType === 'direct' && this.selectedRecipient) {\n      const contact = this.contacts.find(c => c.id === this.selectedRecipient);\n      return contact?.username || 'Unknown';\n    }\n    if (this.messageType === 'group' && this.selectedGroup) {\n      const group = this.groups.find(g => g.id === this.selectedGroup);\n      return group?.name || 'Unknown Group';\n    }\n    return '';\n  }\n  isMessageValid() {\n    const hasContent = this.messageContent.trim().length > 0;\n    const hasRecipient = this.messageType === 'direct' ? !!this.selectedRecipient : !!this.selectedGroup;\n    return hasContent && hasRecipient;\n  }\n  // Message viewing methods\n  openMessagesViewer() {\n    this.showMessagesModal = true;\n    this.markMessagesAsRead();\n  }\n  closeMessagesViewer() {\n    this.showMessagesModal = false;\n  }\n  markMessagesAsRead() {\n    this.messageService.markAllAsRead().pipe(takeUntil(this.destroy$)).subscribe(() => {\n      this.updateUnreadCount();\n    });\n  }\n  // Context menu methods\n  closeContextMenu() {\n    this.showContextMenu = false;\n  }\n  openAccountSettings() {\n    this.showAccountSettings = true;\n    this.showContextMenu = false;\n    this.loadUserProfile();\n  }\n  closeAccountSettings() {\n    this.showAccountSettings = false;\n  }\n  // Account settings methods\n  loadUserProfile() {\n    if (this.currentUser) {\n      this.userProfile = {\n        avatar: this.currentUser.avatar || '',\n        email: this.currentUser.email || '',\n        phoneNumber: this.currentUser.phoneNumber || ''\n      };\n    }\n  }\n  onAvatarChange(event) {\n    const input = event.target;\n    if (input.files && input.files[0]) {\n      const file = input.files[0];\n      // Validate file type\n      if (!file.type.startsWith('image/')) {\n        this.notificationService.showNotification('Please select an image file', 'error');\n        return;\n      }\n      // Validate file size (max 2MB)\n      if (file.size > 2 * 1024 * 1024) {\n        this.notificationService.showNotification('Image must be smaller than 2MB', 'error');\n        return;\n      }\n      const reader = new FileReader();\n      reader.onload = e => {\n        this.userProfile.avatar = e.target?.result;\n        this.saveAvatarChange();\n      };\n      reader.readAsDataURL(file);\n    }\n  }\n  saveAvatarChange() {\n    // TODO: Implement API call to save avatar\n    this.notificationService.showNotification('Avatar updated successfully', 'success');\n  }\n  // Logout\n  logout() {\n    this.closeContextMenu();\n    this.authService.logout();\n  }\n  // Keyboard shortcuts\n  onKeyDown(event) {\n    // Escape key closes modals\n    if (event.key === 'Escape') {\n      this.closeAllModals();\n    }\n    // Enter key in login modal\n    if (event.key === 'Enter' && this.showLoginModal) {\n      if (this.isValidCredentials()) {\n        this.performLogin();\n      }\n    }\n    // Enter key in message modal\n    if (event.key === 'Enter' && this.showMessageModal && !event.shiftKey) {\n      event.preventDefault();\n      this.sendMessage();\n    }\n  }\n  // Click outside handler to close context menu\n  onDocumentClick(event) {\n    // Close context menu when clicking outside\n    if (this.showContextMenu) {\n      this.closeContextMenu();\n    }\n  }\n  closeAllModals() {\n    this.showLoginModal = false;\n    this.showMessageModal = false;\n    this.showMessagesModal = false;\n    this.showContextMenu = false;\n    this.showAccountSettings = false;\n    if (this.circleState === 'composing') {\n      this.circleState = 'authenticated';\n    }\n  }\n  // Utility methods\n  getCircleClass() {\n    return `circle-${this.circleState}`;\n  }\n  getCircleTitle() {\n    switch (this.circleState) {\n      case 'guest':\n        return 'Click to sign in';\n      case 'authenticated':\n        return 'Click to compose message';\n      case 'unread':\n        return `Click to view ${this.unreadCount} unread message(s)`;\n      case 'composing':\n        return 'Composing message...';\n      default:\n        return '';\n    }\n  }\n  trackMessage(index, message) {\n    return message.id;\n  }\n  static {\n    this.ɵfac = function QscMainComponent_Factory(t) {\n      return new (t || QscMainComponent)(i0.ɵɵdirectiveInject(i1.AuthService), i0.ɵɵdirectiveInject(i2.MessageService), i0.ɵɵdirectiveInject(i3.NotificationService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: QscMainComponent,\n      selectors: [[\"app-qsc-main\"]],\n      hostBindings: function QscMainComponent_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"keydown\", function QscMainComponent_keydown_HostBindingHandler($event) {\n            return ctx.onKeyDown($event);\n          }, false, i0.ɵɵresolveDocument)(\"click\", function QscMainComponent_click_HostBindingHandler($event) {\n            return ctx.onDocumentClick($event);\n          }, false, i0.ɵɵresolveDocument);\n        }\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 11,\n      vars: 10,\n      consts: [[1, \"qsc-container\"], [1, \"circle-container\"], [1, \"qsc-circle\", 3, \"click\", \"contextmenu\", \"touchstart\", \"touchend\", \"touchmove\", \"title\"], [1, \"circle-inner\"], [\"class\", \"wind-effect\", 4, \"ngIf\"], [\"class\", \"unread-indicator\", 4, \"ngIf\"], [\"class\", \"user-info\", 4, \"ngIf\"], [\"class\", \"modal-overlay\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"keyboard-hints\", 4, \"ngIf\"], [1, \"wind-effect\"], [1, \"unread-indicator\"], [1, \"user-info\"], [\"title\", \"Logout\", 1, \"logout-btn\", 3, \"click\"], [1, \"modal-overlay\", 3, \"click\"], [1, \"modal\", \"login-modal\", 3, \"click\"], [1, \"modal-header\"], [1, \"close-btn\", 3, \"click\"], [1, \"modal-content\"], [1, \"form-group\"], [\"for\", \"email\"], [\"type\", \"email\", \"id\", \"email\", \"placeholder\", \"Enter your email\", \"autocomplete\", \"email\", 3, \"ngModelChange\", \"input\", \"ngModel\", \"disabled\"], [\"for\", \"secretWord\"], [\"type\", \"password\", \"id\", \"secretWord\", \"placeholder\", \"4+ chars: A-Z, a-z, 0-9, symbol\", \"autocomplete\", \"current-password\", 3, \"ngModelChange\", \"input\", \"ngModel\", \"disabled\"], [\"class\", \"error-message\", 4, \"ngIf\"], [\"class\", \"loading-indicator\", 4, \"ngIf\"], [\"class\", \"auth-info\", 4, \"ngIf\"], [1, \"error-message\"], [1, \"loading-indicator\"], [1, \"spinner\"], [1, \"auth-info\"], [1, \"auto-submit-hint\"], [1, \"modal\", \"message-modal\", 3, \"click\"], [1, \"message-type-selector\"], [1, \"type-btn\", 3, \"click\"], [\"for\", \"recipientSearch\"], [1, \"recipient-selector\"], [\"type\", \"text\", \"id\", \"recipientSearch\", \"autocomplete\", \"off\", 3, \"ngModelChange\", \"input\", \"ngModel\", \"placeholder\"], [\"class\", \"selected-recipient\", 4, \"ngIf\"], [\"class\", \"recipient-dropdown\", 4, \"ngIf\"], [\"for\", \"messageContent\"], [\"id\", \"messageContent\", \"placeholder\", \"Type your message here...\", \"rows\", \"6\", \"maxlength\", \"1000\", 3, \"ngModelChange\", \"ngModel\"], [1, \"char-count\"], [1, \"message-actions\"], [1, \"btn\", \"btn-primary\", 3, \"click\", \"disabled\"], [1, \"btn\", \"btn-secondary\", 3, \"click\"], [1, \"send-hint\"], [1, \"selected-recipient\"], [1, \"recipient-name\"], [\"title\", \"Clear selection\", 1, \"clear-recipient\", 3, \"click\"], [1, \"recipient-dropdown\"], [\"class\", \"recipient-item\", 3, \"hidden\", \"click\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"no-results\", 4, \"ngIf\"], [1, \"recipient-item\", 3, \"click\", \"hidden\"], [1, \"contact-info\"], [1, \"contact-name\"], [1, \"contact-email\"], [1, \"contact-status\"], [1, \"status-indicator\"], [1, \"status-text\"], [1, \"group-info\"], [1, \"group-name\"], [1, \"group-members\"], [1, \"group-status\"], [1, \"no-results\"], [1, \"modal\", \"messages-modal\", 3, \"click\"], [\"class\", \"messages-list\", 4, \"ngIf\"], [\"class\", \"empty-state\", 4, \"ngIf\"], [1, \"messages-list\"], [\"class\", \"message-item\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [1, \"message-item\"], [1, \"message-header\"], [1, \"sender\"], [1, \"timestamp\"], [1, \"message-content\"], [1, \"empty-state\"], [1, \"hint\"], [1, \"keyboard-hints\"]],\n      template: function QscMainComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2);\n          i0.ɵɵlistener(\"click\", function QscMainComponent_Template_div_click_2_listener() {\n            return ctx.onCircleClick();\n          })(\"contextmenu\", function QscMainComponent_Template_div_contextmenu_2_listener($event) {\n            return ctx.onCircleRightClick($event);\n          })(\"touchstart\", function QscMainComponent_Template_div_touchstart_2_listener($event) {\n            return ctx.onCircleTouchStart($event);\n          })(\"touchend\", function QscMainComponent_Template_div_touchend_2_listener() {\n            return ctx.onCircleTouchEnd();\n          })(\"touchmove\", function QscMainComponent_Template_div_touchmove_2_listener() {\n            return ctx.onCircleTouchMove();\n          });\n          i0.ɵɵelementStart(3, \"div\", 3);\n          i0.ɵɵtemplate(4, QscMainComponent_div_4_Template, 1, 0, \"div\", 4)(5, QscMainComponent_div_5_Template, 2, 1, \"div\", 5);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(6, QscMainComponent_div_6_Template, 5, 1, \"div\", 6);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(7, QscMainComponent_div_7_Template, 19, 7, \"div\", 7)(8, QscMainComponent_div_8_Template, 34, 12, \"div\", 7)(9, QscMainComponent_div_9_Template, 10, 2, \"div\", 7)(10, QscMainComponent_div_10_Template, 3, 0, \"div\", 8);\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(2);\n          i0.ɵɵclassMap(ctx.getCircleClass());\n          i0.ɵɵproperty(\"title\", ctx.getCircleTitle());\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.circleState !== \"guest\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.circleState === \"unread\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.currentUser);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.showLoginModal);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.showMessageModal);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.showMessagesModal);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.showLoginModal && !ctx.showMessageModal && !ctx.showMessagesModal);\n        }\n      },\n      dependencies: [CommonModule, i4.NgForOf, i4.NgIf, i4.DatePipe, FormsModule, i5.DefaultValueAccessor, i5.NgControlStatus, i5.MaxLengthValidator, i5.NgModel],\n      styles: [\".qsc-container[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  width: 100vw;\\n  height: 100vh;\\n  background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  overflow: hidden;\\n  font-family: \\\"Inter\\\", -apple-system, BlinkMacSystemFont, sans-serif;\\n}\\n\\n.circle-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  z-index: 1;\\n}\\n\\n.qsc-circle[_ngcontent-%COMP%] {\\n  width: 200px;\\n  height: 200px;\\n  border-radius: 50%;\\n  cursor: pointer;\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n  position: relative;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);\\n}\\n.qsc-circle[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.05);\\n  box-shadow: 0 12px 48px rgba(0, 0, 0, 0.4);\\n}\\n.qsc-circle[_ngcontent-%COMP%]:active {\\n  transform: scale(0.98);\\n}\\n.qsc-circle.circle-guest[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #ff4757 0%, #ff3742 100%);\\n  border: 3px solid rgba(255, 71, 87, 0.3);\\n}\\n.qsc-circle.circle-guest[_ngcontent-%COMP%]:hover {\\n  box-shadow: 0 12px 48px rgba(255, 71, 87, 0.4);\\n}\\n.qsc-circle.circle-authenticated[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #3742fa 0%, #2f3542 100%);\\n  border: 3px solid rgba(55, 66, 250, 0.3);\\n}\\n.qsc-circle.circle-authenticated[_ngcontent-%COMP%]:hover {\\n  box-shadow: 0 12px 48px rgba(55, 66, 250, 0.4);\\n}\\n.qsc-circle.circle-unread[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #2ed573 0%, #1e90ff 100%);\\n  border: 3px solid rgba(46, 213, 115, 0.3);\\n  animation: _ngcontent-%COMP%_pulse 2s infinite;\\n}\\n.qsc-circle.circle-unread[_ngcontent-%COMP%]:hover {\\n  box-shadow: 0 12px 48px rgba(46, 213, 115, 0.4);\\n}\\n.qsc-circle.circle-composing[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #a55eea 0%, #8854d0 100%);\\n  border: 3px solid rgba(165, 94, 234, 0.3);\\n}\\n.qsc-circle.circle-composing[_ngcontent-%COMP%]:hover {\\n  box-shadow: 0 12px 48px rgba(165, 94, 234, 0.4);\\n}\\n\\n.circle-inner[_ngcontent-%COMP%] {\\n  position: relative;\\n  width: 100%;\\n  height: 100%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n\\n.wind-effect[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 20%;\\n  right: 15%;\\n  width: 60px;\\n  height: 60px;\\n  opacity: 0.3;\\n}\\n.wind-effect[_ngcontent-%COMP%]::before, .wind-effect[_ngcontent-%COMP%]::after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  background: rgba(255, 255, 255, 0.6);\\n  border-radius: 50%;\\n  animation: _ngcontent-%COMP%_windFlow 3s ease-in-out infinite;\\n}\\n.wind-effect[_ngcontent-%COMP%]::before {\\n  width: 8px;\\n  height: 8px;\\n  top: 10px;\\n  left: 0;\\n  animation-delay: 0s;\\n}\\n.wind-effect[_ngcontent-%COMP%]::after {\\n  width: 6px;\\n  height: 6px;\\n  top: 25px;\\n  left: 15px;\\n  animation-delay: 1s;\\n}\\n\\n.unread-indicator[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: -10px;\\n  right: -10px;\\n  background: #ff4757;\\n  color: white;\\n  border-radius: 50%;\\n  width: 40px;\\n  height: 40px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  font-weight: 600;\\n  font-size: 14px;\\n  border: 3px solid #0f0f23;\\n  animation: _ngcontent-%COMP%_bounce 1s infinite;\\n}\\n\\n.user-info[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 20px;\\n  right: 20px;\\n  display: flex;\\n  align-items: center;\\n  gap: 10px;\\n  color: rgba(255, 255, 255, 0.7);\\n  font-size: 14px;\\n  z-index: 10;\\n}\\n.user-info[_ngcontent-%COMP%]   .logout-btn[_ngcontent-%COMP%] {\\n  background: none;\\n  border: none;\\n  color: rgba(255, 255, 255, 0.5);\\n  font-size: 20px;\\n  cursor: pointer;\\n  padding: 5px;\\n  border-radius: 50%;\\n  transition: all 0.2s ease;\\n}\\n.user-info[_ngcontent-%COMP%]   .logout-btn[_ngcontent-%COMP%]:hover {\\n  color: #ff4757;\\n  background: rgba(255, 71, 87, 0.1);\\n}\\n\\n.modal-overlay[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  width: 100vw;\\n  height: 100vh;\\n  background: rgba(0, 0, 0, 0.8);\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  z-index: 1000;\\n  animation: _ngcontent-%COMP%_fadeIn 0.2s ease;\\n}\\n\\n.modal[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.95);\\n  -webkit-backdrop-filter: blur(20px);\\n          backdrop-filter: blur(20px);\\n  border-radius: 16px;\\n  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);\\n  max-width: 400px;\\n  width: 90vw;\\n  max-height: 80vh;\\n  overflow: hidden;\\n  animation: _ngcontent-%COMP%_slideUp 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n}\\n\\n.modal-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 20px 24px;\\n  border-bottom: 1px solid rgba(0, 0, 0, 0.1);\\n}\\n.modal-header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 20px;\\n  font-weight: 600;\\n  color: #2f3542;\\n}\\n.modal-header[_ngcontent-%COMP%]   .close-btn[_ngcontent-%COMP%] {\\n  background: none;\\n  border: none;\\n  font-size: 24px;\\n  color: #a4b0be;\\n  cursor: pointer;\\n  padding: 0;\\n  width: 30px;\\n  height: 30px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  border-radius: 50%;\\n  transition: all 0.2s ease;\\n}\\n.modal-header[_ngcontent-%COMP%]   .close-btn[_ngcontent-%COMP%]:hover {\\n  background: rgba(0, 0, 0, 0.1);\\n  color: #2f3542;\\n}\\n\\n.modal-content[_ngcontent-%COMP%] {\\n  padding: 24px;\\n}\\n\\n.form-group[_ngcontent-%COMP%] {\\n  margin-bottom: 20px;\\n}\\n.form-group[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  display: block;\\n  margin-bottom: 8px;\\n  font-weight: 500;\\n  color: #2f3542;\\n  font-size: 14px;\\n}\\n.form-group[_ngcontent-%COMP%]   input[_ngcontent-%COMP%], .form-group[_ngcontent-%COMP%]   textarea[_ngcontent-%COMP%] {\\n  width: 100%;\\n  padding: 12px 16px;\\n  border: 2px solid #e1e8ed;\\n  border-radius: 8px;\\n  font-size: 16px;\\n  transition: all 0.2s ease;\\n  background: white;\\n}\\n.form-group[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]:focus, .form-group[_ngcontent-%COMP%]   textarea[_ngcontent-%COMP%]:focus {\\n  outline: none;\\n  border-color: #3742fa;\\n  box-shadow: 0 0 0 3px rgba(55, 66, 250, 0.1);\\n}\\n.form-group[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]:disabled, .form-group[_ngcontent-%COMP%]   textarea[_ngcontent-%COMP%]:disabled {\\n  background: #f8f9fa;\\n  color: #a4b0be;\\n}\\n.form-group[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]::placeholder, .form-group[_ngcontent-%COMP%]   textarea[_ngcontent-%COMP%]::placeholder {\\n  color: #a4b0be;\\n}\\n.form-group[_ngcontent-%COMP%]   textarea[_ngcontent-%COMP%] {\\n  resize: vertical;\\n  min-height: 120px;\\n  font-family: inherit;\\n}\\n\\n.char-count[_ngcontent-%COMP%] {\\n  text-align: right;\\n  font-size: 12px;\\n  color: #a4b0be;\\n  margin-top: 4px;\\n}\\n\\n.btn[_ngcontent-%COMP%] {\\n  padding: 12px 24px;\\n  border: none;\\n  border-radius: 8px;\\n  font-size: 14px;\\n  font-weight: 500;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n}\\n.btn.btn-primary[_ngcontent-%COMP%] {\\n  background: #3742fa;\\n  color: white;\\n}\\n.btn.btn-primary[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  background: #2f3542;\\n  transform: translateY(-1px);\\n}\\n.btn.btn-primary[_ngcontent-%COMP%]:disabled {\\n  background: #a4b0be;\\n  cursor: not-allowed;\\n}\\n.btn.btn-secondary[_ngcontent-%COMP%] {\\n  background: #f1f2f6;\\n  color: #2f3542;\\n}\\n.btn.btn-secondary[_ngcontent-%COMP%]:hover {\\n  background: #e1e8ed;\\n}\\n\\n.message-type-selector[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 8px;\\n  margin-bottom: 20px;\\n}\\n.message-type-selector[_ngcontent-%COMP%]   .type-btn[_ngcontent-%COMP%] {\\n  flex: 1;\\n  padding: 10px 16px;\\n  border: 2px solid #e1e8ed;\\n  border-radius: 8px;\\n  background: white;\\n  color: #57606f;\\n  font-size: 14px;\\n  font-weight: 500;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n}\\n.message-type-selector[_ngcontent-%COMP%]   .type-btn[_ngcontent-%COMP%]:hover {\\n  border-color: #3742fa;\\n  background: rgba(55, 66, 250, 0.05);\\n}\\n.message-type-selector[_ngcontent-%COMP%]   .type-btn.active[_ngcontent-%COMP%] {\\n  border-color: #3742fa;\\n  background: #3742fa;\\n  color: white;\\n}\\n\\n.recipient-selector[_ngcontent-%COMP%] {\\n  position: relative;\\n}\\n\\n.selected-recipient[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  padding: 8px 12px;\\n  background: rgba(55, 66, 250, 0.1);\\n  border: 1px solid #3742fa;\\n  border-radius: 6px;\\n  margin-top: 8px;\\n}\\n.selected-recipient[_ngcontent-%COMP%]   .recipient-name[_ngcontent-%COMP%] {\\n  color: #3742fa;\\n  font-weight: 500;\\n  font-size: 14px;\\n}\\n.selected-recipient[_ngcontent-%COMP%]   .clear-recipient[_ngcontent-%COMP%] {\\n  background: none;\\n  border: none;\\n  color: #3742fa;\\n  font-size: 16px;\\n  cursor: pointer;\\n  padding: 0;\\n  width: 20px;\\n  height: 20px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  border-radius: 50%;\\n  transition: all 0.2s ease;\\n}\\n.selected-recipient[_ngcontent-%COMP%]   .clear-recipient[_ngcontent-%COMP%]:hover {\\n  background: rgba(55, 66, 250, 0.2);\\n}\\n\\n.recipient-dropdown[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 100%;\\n  left: 0;\\n  right: 0;\\n  background: white;\\n  border: 1px solid #e1e8ed;\\n  border-radius: 8px;\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\\n  max-height: 200px;\\n  overflow-y: auto;\\n  z-index: 1000;\\n  margin-top: 4px;\\n}\\n\\n.recipient-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  padding: 12px 16px;\\n  cursor: pointer;\\n  border-bottom: 1px solid rgba(0, 0, 0, 0.05);\\n  transition: background 0.2s ease;\\n}\\n.recipient-item[_ngcontent-%COMP%]:hover {\\n  background: rgba(55, 66, 250, 0.05);\\n}\\n.recipient-item[_ngcontent-%COMP%]:last-child {\\n  border-bottom: none;\\n}\\n\\n.contact-info[_ngcontent-%COMP%], .group-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  flex: 1;\\n}\\n.contact-info[_ngcontent-%COMP%]   .contact-name[_ngcontent-%COMP%], .contact-info[_ngcontent-%COMP%]   .group-name[_ngcontent-%COMP%], .group-info[_ngcontent-%COMP%]   .contact-name[_ngcontent-%COMP%], .group-info[_ngcontent-%COMP%]   .group-name[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: #2f3542;\\n  font-size: 14px;\\n  margin-bottom: 2px;\\n}\\n.contact-info[_ngcontent-%COMP%]   .contact-email[_ngcontent-%COMP%], .contact-info[_ngcontent-%COMP%]   .group-members[_ngcontent-%COMP%], .group-info[_ngcontent-%COMP%]   .contact-email[_ngcontent-%COMP%], .group-info[_ngcontent-%COMP%]   .group-members[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: #a4b0be;\\n}\\n\\n.contact-status[_ngcontent-%COMP%], .group-status[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 6px;\\n}\\n.contact-status[_ngcontent-%COMP%]   .status-indicator[_ngcontent-%COMP%], .group-status[_ngcontent-%COMP%]   .status-indicator[_ngcontent-%COMP%] {\\n  width: 8px;\\n  height: 8px;\\n  border-radius: 50%;\\n}\\n.contact-status[_ngcontent-%COMP%]   .status-indicator.online[_ngcontent-%COMP%], .group-status[_ngcontent-%COMP%]   .status-indicator.online[_ngcontent-%COMP%] {\\n  background: #2ed573;\\n}\\n.contact-status[_ngcontent-%COMP%]   .status-indicator.offline[_ngcontent-%COMP%], .group-status[_ngcontent-%COMP%]   .status-indicator.offline[_ngcontent-%COMP%] {\\n  background: #a4b0be;\\n}\\n.contact-status[_ngcontent-%COMP%]   .status-indicator.active[_ngcontent-%COMP%], .group-status[_ngcontent-%COMP%]   .status-indicator.active[_ngcontent-%COMP%] {\\n  background: #3742fa;\\n}\\n.contact-status[_ngcontent-%COMP%]   .status-text[_ngcontent-%COMP%], .group-status[_ngcontent-%COMP%]   .status-text[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: #a4b0be;\\n}\\n\\n.no-results[_ngcontent-%COMP%] {\\n  padding: 16px;\\n  text-align: center;\\n  color: #a4b0be;\\n  font-size: 14px;\\n  font-style: italic;\\n}\\n\\n.message-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 12px;\\n  margin-top: 20px;\\n}\\n\\n.error-message[_ngcontent-%COMP%] {\\n  color: #ff4757;\\n  font-size: 14px;\\n  margin-top: 8px;\\n  padding: 8px 12px;\\n  background: rgba(255, 71, 87, 0.1);\\n  border-radius: 6px;\\n  border-left: 3px solid #ff4757;\\n}\\n\\n.auth-info[_ngcontent-%COMP%] {\\n  margin-top: 20px;\\n  text-align: center;\\n}\\n.auth-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 8px 0;\\n  font-size: 13px;\\n  color: #57606f;\\n}\\n.auth-info[_ngcontent-%COMP%]   p.auto-submit-hint[_ngcontent-%COMP%] {\\n  font-style: italic;\\n  color: #a4b0be;\\n}\\n\\n.send-hint[_ngcontent-%COMP%] {\\n  margin-top: 16px;\\n  text-align: center;\\n}\\n.send-hint[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: #a4b0be;\\n  margin: 0;\\n}\\n\\n.loading-indicator[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  gap: 12px;\\n  padding: 20px;\\n}\\n.loading-indicator[_ngcontent-%COMP%]   .spinner[_ngcontent-%COMP%] {\\n  width: 20px;\\n  height: 20px;\\n  border: 2px solid #e1e8ed;\\n  border-top: 2px solid #3742fa;\\n  border-radius: 50%;\\n  animation: _ngcontent-%COMP%_spin 1s linear infinite;\\n}\\n.loading-indicator[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  color: #57606f;\\n  font-size: 14px;\\n}\\n\\n.messages-list[_ngcontent-%COMP%] {\\n  max-height: 400px;\\n  overflow-y: auto;\\n  margin: -8px;\\n  padding: 8px;\\n}\\n\\n.message-item[_ngcontent-%COMP%] {\\n  padding: 16px;\\n  border-bottom: 1px solid rgba(0, 0, 0, 0.05);\\n}\\n.message-item[_ngcontent-%COMP%]:last-child {\\n  border-bottom: none;\\n}\\n.message-item[_ngcontent-%COMP%]   .message-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 8px;\\n}\\n.message-item[_ngcontent-%COMP%]   .message-header[_ngcontent-%COMP%]   .sender[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #2f3542;\\n  font-size: 14px;\\n}\\n.message-item[_ngcontent-%COMP%]   .message-header[_ngcontent-%COMP%]   .timestamp[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: #a4b0be;\\n}\\n.message-item[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%] {\\n  color: #57606f;\\n  line-height: 1.5;\\n  word-wrap: break-word;\\n}\\n\\n.empty-state[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 40px 20px;\\n  color: #a4b0be;\\n}\\n.empty-state[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 8px 0;\\n}\\n.empty-state[_ngcontent-%COMP%]   p.hint[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-style: italic;\\n}\\n\\n.keyboard-hints[_ngcontent-%COMP%] {\\n  position: fixed;\\n  bottom: 20px;\\n  left: 50%;\\n  transform: translateX(-50%);\\n  color: rgba(255, 255, 255, 0.4);\\n  font-size: 12px;\\n  z-index: 5;\\n}\\n\\n@keyframes _ngcontent-%COMP%_fadeIn {\\n  from {\\n    opacity: 0;\\n  }\\n  to {\\n    opacity: 1;\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_slideUp {\\n  from {\\n    opacity: 0;\\n    transform: translateY(20px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_pulse {\\n  0%, 100% {\\n    transform: scale(1);\\n  }\\n  50% {\\n    transform: scale(1.02);\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_bounce {\\n  0%, 20%, 50%, 80%, 100% {\\n    transform: translateY(0);\\n  }\\n  40% {\\n    transform: translateY(-5px);\\n  }\\n  60% {\\n    transform: translateY(-3px);\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_windFlow {\\n  0% {\\n    transform: translateX(0) translateY(0) scale(1);\\n    opacity: 0.3;\\n  }\\n  50% {\\n    transform: translateX(20px) translateY(-10px) scale(0.8);\\n    opacity: 0.6;\\n  }\\n  100% {\\n    transform: translateX(40px) translateY(-20px) scale(0.5);\\n    opacity: 0;\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_spin {\\n  0% {\\n    transform: rotate(0deg);\\n  }\\n  100% {\\n    transform: rotate(360deg);\\n  }\\n}\\n@media (max-width: 768px) {\\n  .qsc-circle[_ngcontent-%COMP%] {\\n    width: 150px;\\n    height: 150px;\\n  }\\n  .modal[_ngcontent-%COMP%] {\\n    width: 95vw;\\n    margin: 20px;\\n  }\\n  .user-info[_ngcontent-%COMP%] {\\n    top: 15px;\\n    right: 15px;\\n    font-size: 12px;\\n  }\\n}\\n@media (max-width: 480px) {\\n  .qsc-circle[_ngcontent-%COMP%] {\\n    width: 120px;\\n    height: 120px;\\n  }\\n  .modal-content[_ngcontent-%COMP%] {\\n    padding: 20px;\\n  }\\n  .modal-header[_ngcontent-%COMP%] {\\n    padding: 16px 20px;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "FormsModule", "Subject", "takeUntil", "i0", "ɵɵelement", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r0", "unreadCount", "ɵɵlistener", "QscMainComponent_div_6_Template_button_click_3_listener", "ɵɵrestoreView", "_r2", "ɵɵnextContext", "ɵɵresetView", "logout", "ɵɵtextInterpolate", "currentUser", "username", "loginError", "QscMainComponent_div_7_Template_div_click_0_listener", "_r3", "closeLoginModal", "QscMainComponent_div_7_Template_div_click_1_listener", "$event", "stopPropagation", "QscMainComponent_div_7_Template_button_click_5_listener", "ɵɵtwoWayListener", "QscMainComponent_div_7_Template_input_ngModelChange_11_listener", "ɵɵtwoWayBindingSet", "loginCredentials", "email", "QscMainComponent_div_7_Template_input_input_11_listener", "onLoginInputChange", "QscMainComponent_div_7_Template_input_ngModelChange_15_listener", "secretWord", "QscMainComponent_div_7_Template_input_input_15_listener", "ɵɵtemplate", "QscMainComponent_div_7_div_16_Template", "QscMainComponent_div_7_div_17_Template", "QscMainComponent_div_7_div_18_Template", "ɵɵtwoWayProperty", "ɵɵproperty", "isLoading", "QscMainComponent_div_8_div_18_Template_button_click_3_listener", "_r5", "switchMessageType", "messageType", "getSelectedRecipientName", "QscMainComponent_div_8_div_19_div_1_Template_div_click_0_listener", "contact_r7", "_r6", "$implicit", "selectContact", "ɵɵclassProp", "isOnline", "QscMainComponent_div_8_div_19_div_2_Template_div_click_0_listener", "group_r9", "_r8", "selectGroup", "name", "members", "length", "isActive", "QscMainComponent_div_8_div_19_div_1_Template", "QscMainComponent_div_8_div_19_div_2_Template", "QscMainComponent_div_8_div_19_div_3_Template", "QscMainComponent_div_8_div_19_div_4_Template", "filteredContacts", "filteredGroups", "QscMainComponent_div_8_Template_div_click_0_listener", "_r4", "closeMessageComposer", "QscMainComponent_div_8_Template_div_click_1_listener", "QscMainComponent_div_8_Template_button_click_5_listener", "QscMainComponent_div_8_Template_button_click_9_listener", "QscMainComponent_div_8_Template_button_click_11_listener", "QscMainComponent_div_8_Template_input_ngModelChange_17_listener", "recipient<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "QscMainComponent_div_8_Template_input_input_17_listener", "onRecipientSearchChange", "QscMainComponent_div_8_div_18_Template", "QscMainComponent_div_8_div_19_Template", "QscMainComponent_div_8_Template_textarea_ngModelChange_23_listener", "messageContent", "QscMainComponent_div_8_Template_button_click_27_listener", "sendMessage", "QscMainComponent_div_8_Template_button_click_29_listener", "isMessageValid", "message_r11", "sender", "ɵɵpipeBind2", "timestamp", "content", "QscMainComponent_div_9_div_8_div_1_Template", "messages", "trackMessage", "QscMainComponent_div_9_Template_div_click_0_listener", "_r10", "closeMessagesViewer", "QscMainComponent_div_9_Template_div_click_1_listener", "QscMainComponent_div_9_Template_button_click_5_listener", "QscMainComponent_div_9_div_8_Template", "QscMainComponent_div_9_div_9_Template", "QscMainComponent", "constructor", "authService", "messageService", "notificationService", "destroy$", "circleState", "showLoginModal", "showMessageModal", "showMessagesModal", "showContextMenu", "showAccountSettings", "contextMenuPosition", "x", "y", "selected<PERSON><PERSON><PERSON><PERSON>", "selectedGroup", "contacts", "groups", "userProfile", "avatar", "phoneNumber", "longPressTimer", "longPressDuration", "ngOnInit", "initializeApp", "setupMessageListener", "setupAuthListener", "ngOnDestroy", "next", "complete", "isAuthenticated", "getCurrentUser", "loadMessages", "loadContacts", "loadGroups", "authState$", "pipe", "subscribe", "user", "messages$", "updateUnreadCount", "newMessage$", "message", "unshift", "showNotification", "filter", "m", "read", "error", "console", "id", "lastSeen", "Date", "now", "onCircleClick", "closeContextMenu", "openLoginModal", "openMessageComposer", "openMessagesViewer", "onCircleRightClick", "event", "preventDefault", "clientX", "clientY", "onCircleTouchStart", "setTimeout", "touch", "touches", "navigator", "vibrate", "onCircleTouchEnd", "clearTimeout", "onCircleTouchMove", "isValidCredentials", "performLogin", "emailValid", "includes", "secretWordValid", "test", "login", "response", "trim", "recipient", "undefined", "groupId", "query", "toLowerCase", "contact", "group", "type", "find", "c", "g", "<PERSON><PERSON><PERSON><PERSON>", "hasRecipient", "markMessagesAsRead", "markAllAsRead", "openAccountSettings", "loadUserProfile", "closeAccountSettings", "onAvatarChange", "input", "target", "files", "file", "startsWith", "size", "reader", "FileReader", "onload", "e", "result", "saveAvatarChange", "readAsDataURL", "onKeyDown", "key", "closeAllModals", "shift<PERSON>ey", "onDocumentClick", "getCircleClass", "getCircleTitle", "index", "ɵɵdirectiveInject", "i1", "AuthService", "i2", "MessageService", "i3", "NotificationService", "selectors", "hostBindings", "QscMainComponent_HostBindings", "rf", "ctx", "QscMainComponent_keydown_HostBindingHandler", "ɵɵresolveDocument", "QscMainComponent_click_HostBindingHandler", "QscMainComponent_Template_div_click_2_listener", "QscMainComponent_Template_div_contextmenu_2_listener", "QscMainComponent_Template_div_touchstart_2_listener", "QscMainComponent_Template_div_touchend_2_listener", "QscMainComponent_Template_div_touchmove_2_listener", "QscMainComponent_div_4_Template", "QscMainComponent_div_5_Template", "QscMainComponent_div_6_Template", "QscMainComponent_div_7_Template", "QscMainComponent_div_8_Template", "QscMainComponent_div_9_Template", "QscMainComponent_div_10_Template", "ɵɵclassMap", "i4", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "DatePipe", "i5", "DefaultValueAccessor", "NgControlStatus", "MaxLengthValidator", "NgModel", "styles"], "sources": ["C:\\Users\\<USER>\\Projects\\QSC1\\frontend\\src\\app\\components\\qsc-main\\qsc-main.component.ts", "C:\\Users\\<USER>\\Projects\\QSC1\\frontend\\src\\app\\components\\qsc-main\\qsc-main.component.html"], "sourcesContent": ["import { Component, OnInit, On<PERSON><PERSON>roy, HostListener } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { Subject, takeUntil } from 'rxjs';\nimport { AuthService } from '../../services/auth.service';\nimport { MessageService } from '../../services/message.service';\nimport { NotificationService } from '../../services/notification.service';\nimport { sanitizeForLogging } from '../../types/shared.types';\n\nexport type CircleState = 'guest' | 'authenticated' | 'unread' | 'composing';\n\ninterface LoginCredentials {\n  email: string;\n  secretWord: string;\n}\n\ninterface Message {\n  id: string;\n  content: string;\n  timestamp: Date;\n  sender: string;\n  recipient?: string;\n  groupId?: string;\n  read?: boolean;\n}\n\ninterface Contact {\n  id: string;\n  username: string;\n  email: string;\n  publicKey?: string;\n  isOnline?: boolean;\n  lastSeen?: Date;\n}\n\ninterface Group {\n  id: string;\n  name: string;\n  members: string[];\n  isActive: boolean;\n}\n\n@Component({\n  selector: 'app-qsc-main',\n  standalone: true,\n  imports: [CommonModule, FormsModule],\n  templateUrl: './qsc-main.component.html',\n  styleUrls: ['./qsc-main.component.scss']\n})\nexport class QscMainComponent implements OnInit, OnDestroy {\n  private destroy$ = new Subject<void>();\n\n  // Circle state management\n  circleState: CircleState = 'guest';\n\n  // Modal states\n  showLoginModal = false;\n  showMessageModal = false;\n  showMessagesModal = false;\n  showContextMenu = false;\n  showAccountSettings = false;\n\n  // Context menu position\n  contextMenuPosition = { x: 0, y: 0 };\n\n  // Authentication\n  loginCredentials: LoginCredentials = { email: '', secretWord: '' };\n  loginError = '';\n  isLoading = false;\n\n  // Messaging\n  messageContent = '';\n  selectedRecipient = '';\n  selectedGroup = '';\n  messageType: 'direct' | 'group' = 'direct';\n  messages: Message[] = [];\n  unreadCount = 0;\n\n  // Contacts and Groups\n  contacts: Contact[] = [];\n  groups: Group[] = [];\n  filteredContacts: Contact[] = [];\n  filteredGroups: Group[] = [];\n  recipientSearchQuery = '';\n\n  // User info\n  currentUser: any = null;\n\n  // Account settings\n  userProfile = {\n    avatar: '',\n    email: '',\n    phoneNumber: ''\n  };\n\n  // Long press handling\n  private longPressTimer: any = null;\n  private longPressDuration = 500; // 500ms for long press\n\n  constructor(\n    private authService: AuthService,\n    private messageService: MessageService,\n    private notificationService: NotificationService\n  ) {}\n\n  ngOnInit() {\n    this.initializeApp();\n    this.setupMessageListener();\n    this.setupAuthListener();\n  }\n\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n\n  private initializeApp() {\n    // Check if user is already authenticated\n    if (this.authService.isAuthenticated()) {\n      this.currentUser = this.authService.getCurrentUser();\n      this.circleState = 'authenticated';\n      this.loadMessages();\n      this.loadContacts();\n      this.loadGroups();\n    } else {\n      this.circleState = 'guest';\n    }\n  }\n\n  private setupAuthListener() {\n    this.authService.authState$\n      .pipe(takeUntil(this.destroy$))\n      .subscribe(user => {\n        if (user) {\n          this.currentUser = user;\n          this.circleState = 'authenticated';\n          this.showLoginModal = false;\n          this.loadMessages();\n        } else {\n          this.currentUser = null;\n          this.circleState = 'guest';\n          this.messages = [];\n          this.unreadCount = 0;\n        }\n      });\n  }\n\n  private setupMessageListener() {\n    this.messageService.messages$\n      .pipe(takeUntil(this.destroy$))\n      .subscribe(messages => {\n        this.messages = messages;\n        this.updateUnreadCount();\n      });\n\n    this.messageService.newMessage$\n      .pipe(takeUntil(this.destroy$))\n      .subscribe(message => {\n        this.messages.unshift(message);\n        this.updateUnreadCount();\n        this.notificationService.showNotification('New message received');\n      });\n  }\n\n  private updateUnreadCount() {\n    this.unreadCount = this.messages.filter(m => !m.read).length;\n    if (this.unreadCount > 0 && this.circleState === 'authenticated') {\n      this.circleState = 'unread';\n    } else if (this.unreadCount === 0 && this.circleState === 'unread') {\n      this.circleState = 'authenticated';\n    }\n  }\n\n  private loadMessages() {\n    this.messageService.loadMessages()\n      .pipe(takeUntil(this.destroy$))\n      .subscribe({\n        next: (messages) => {\n          this.messages = messages;\n          this.updateUnreadCount();\n        },\n        error: (error) => {\n          console.error('Failed to load messages:', error);\n        }\n      });\n  }\n\n  private loadContacts() {\n    // TODO: Replace with actual API call\n    this.contacts = [\n      {\n        id: '1',\n        username: 'alice',\n        email: '<EMAIL>',\n        isOnline: true\n      },\n      {\n        id: '2',\n        username: 'bob',\n        email: '<EMAIL>',\n        isOnline: false,\n        lastSeen: new Date(Date.now() - 300000) // 5 minutes ago\n      }\n    ];\n    this.filteredContacts = [...this.contacts];\n  }\n\n  private loadGroups() {\n    // TODO: Replace with actual API call\n    this.groups = [\n      {\n        id: 'group1',\n        name: 'Work Team',\n        members: ['1', '2', 'current-user'],\n        isActive: true\n      },\n      {\n        id: 'group2',\n        name: 'Family',\n        members: ['3', '4', 'current-user'],\n        isActive: true\n      }\n    ];\n    this.filteredGroups = [...this.groups];\n  }\n\n  // Circle click handler - main interaction point\n  onCircleClick() {\n    // Don't handle click if context menu is showing\n    if (this.showContextMenu) {\n      this.closeContextMenu();\n      return;\n    }\n\n    switch (this.circleState) {\n      case 'guest':\n        this.openLoginModal();\n        break;\n      case 'authenticated':\n        this.openMessageComposer();\n        break;\n      case 'unread':\n        this.openMessagesViewer();\n        break;\n      case 'composing':\n        // Already composing, do nothing or close\n        break;\n    }\n  }\n\n  // Right click handler\n  onCircleRightClick(event: MouseEvent) {\n    event.preventDefault();\n\n    // Only show context menu for authenticated users\n    if (this.circleState === 'guest') return;\n\n    this.showContextMenu = true;\n    this.contextMenuPosition = {\n      x: event.clientX,\n      y: event.clientY\n    };\n  }\n\n  // Touch event handlers for long press\n  onCircleTouchStart(event: TouchEvent) {\n    // Only for authenticated users\n    if (this.circleState === 'guest') return;\n\n    this.longPressTimer = setTimeout(() => {\n      const touch = event.touches[0];\n      this.showContextMenu = true;\n      this.contextMenuPosition = {\n        x: touch.clientX,\n        y: touch.clientY\n      };\n\n      // Provide haptic feedback if available\n      if (navigator.vibrate) {\n        navigator.vibrate(50);\n      }\n    }, this.longPressDuration);\n  }\n\n  onCircleTouchEnd() {\n    if (this.longPressTimer) {\n      clearTimeout(this.longPressTimer);\n      this.longPressTimer = null;\n    }\n  }\n\n  onCircleTouchMove() {\n    // Cancel long press if user moves finger\n    if (this.longPressTimer) {\n      clearTimeout(this.longPressTimer);\n      this.longPressTimer = null;\n    }\n  }\n\n  // Authentication methods\n  openLoginModal() {\n    this.showLoginModal = true;\n    this.loginCredentials = { email: '', secretWord: '' };\n    this.loginError = '';\n  }\n\n  closeLoginModal() {\n    this.showLoginModal = false;\n    this.loginCredentials = { email: '', secretWord: '' };\n    this.loginError = '';\n  }\n\n  onLoginInputChange() {\n    // Auto-submit when both fields are valid\n    if (this.isValidCredentials()) {\n      this.performLogin();\n    }\n  }\n\n  private isValidCredentials(): boolean {\n    const emailValid = this.loginCredentials.email.includes('@') &&\n                      this.loginCredentials.email.includes('.');\n    const secretWordValid = this.loginCredentials.secretWord.length >= 4 &&\n                           /[A-Z]/.test(this.loginCredentials.secretWord) &&\n                           /[a-z]/.test(this.loginCredentials.secretWord) &&\n                           /[0-9]/.test(this.loginCredentials.secretWord) &&\n                           /[^A-Za-z0-9]/.test(this.loginCredentials.secretWord);\n\n    return emailValid && secretWordValid;\n  }\n\n  private performLogin() {\n    if (this.isLoading) return;\n\n    this.isLoading = true;\n    this.loginError = '';\n\n    this.authService.login(this.loginCredentials.email, this.loginCredentials.secretWord)\n      .pipe(takeUntil(this.destroy$))\n      .subscribe({\n        next: (response) => {\n          this.isLoading = false;\n          // Auth state will be updated via authState$ subscription\n        },\n        error: (error) => {\n          this.isLoading = false;\n          this.loginError = error.message || 'Authentication failed';\n        }\n      });\n  }\n\n  // Message composition methods\n  openMessageComposer() {\n    this.showMessageModal = true;\n    this.messageContent = '';\n    this.selectedRecipient = '';\n    this.selectedGroup = '';\n    this.messageType = 'direct';\n    this.recipientSearchQuery = '';\n    this.filteredContacts = [...this.contacts];\n    this.filteredGroups = [...this.groups];\n    this.circleState = 'composing';\n  }\n\n  closeMessageComposer() {\n    this.showMessageModal = false;\n    this.messageContent = '';\n    this.selectedRecipient = '';\n    this.selectedGroup = '';\n    this.recipientSearchQuery = '';\n    this.circleState = 'authenticated';\n  }\n\n  sendMessage() {\n    if (!this.messageContent.trim()) return;\n\n    // Validate recipient selection\n    if (this.messageType === 'direct' && !this.selectedRecipient) {\n      this.notificationService.showNotification('Please select a recipient', 'warning');\n      return;\n    }\n\n    if (this.messageType === 'group' && !this.selectedGroup) {\n      this.notificationService.showNotification('Please select a group', 'warning');\n      return;\n    }\n\n    const message: Partial<Message> = {\n      content: this.messageContent.trim(),\n      timestamp: new Date(),\n      sender: this.currentUser?.username || 'Unknown',\n      recipient: this.messageType === 'direct' ? this.selectedRecipient : undefined,\n      groupId: this.messageType === 'group' ? this.selectedGroup : undefined\n    };\n\n    this.messageService.sendMessage(message)\n      .pipe(takeUntil(this.destroy$))\n      .subscribe({\n        next: () => {\n          this.closeMessageComposer();\n          this.notificationService.showNotification('Message sent');\n        },\n        error: (error) => {\n          console.error('Failed to send message:', error);\n          this.notificationService.showNotification('Failed to send message', 'error');\n        }\n      });\n  }\n\n  // Recipient selection methods\n  onRecipientSearchChange() {\n    const query = this.recipientSearchQuery.toLowerCase();\n\n    if (this.messageType === 'direct') {\n      this.filteredContacts = this.contacts.filter(contact =>\n        contact.username.toLowerCase().includes(query) ||\n        contact.email.toLowerCase().includes(query)\n      );\n    } else {\n      this.filteredGroups = this.groups.filter(group =>\n        group.name.toLowerCase().includes(query)\n      );\n    }\n  }\n\n  selectContact(contact: Contact) {\n    this.selectedRecipient = contact.id;\n    this.recipientSearchQuery = contact.username;\n    this.filteredContacts = [];\n  }\n\n  selectGroup(group: Group) {\n    this.selectedGroup = group.id;\n    this.recipientSearchQuery = group.name;\n    this.filteredGroups = [];\n  }\n\n  switchMessageType(type: 'direct' | 'group') {\n    this.messageType = type;\n    this.selectedRecipient = '';\n    this.selectedGroup = '';\n    this.recipientSearchQuery = '';\n    this.onRecipientSearchChange();\n  }\n\n  getSelectedRecipientName(): string {\n    if (this.messageType === 'direct' && this.selectedRecipient) {\n      const contact = this.contacts.find(c => c.id === this.selectedRecipient);\n      return contact?.username || 'Unknown';\n    }\n\n    if (this.messageType === 'group' && this.selectedGroup) {\n      const group = this.groups.find(g => g.id === this.selectedGroup);\n      return group?.name || 'Unknown Group';\n    }\n\n    return '';\n  }\n\n  isMessageValid(): boolean {\n    const hasContent = this.messageContent.trim().length > 0;\n    const hasRecipient = this.messageType === 'direct' ? !!this.selectedRecipient : !!this.selectedGroup;\n    return hasContent && hasRecipient;\n  }\n\n  // Message viewing methods\n  openMessagesViewer() {\n    this.showMessagesModal = true;\n    this.markMessagesAsRead();\n  }\n\n  closeMessagesViewer() {\n    this.showMessagesModal = false;\n  }\n\n  private markMessagesAsRead() {\n    this.messageService.markAllAsRead()\n      .pipe(takeUntil(this.destroy$))\n      .subscribe(() => {\n        this.updateUnreadCount();\n      });\n  }\n\n  // Context menu methods\n  closeContextMenu() {\n    this.showContextMenu = false;\n  }\n\n  openAccountSettings() {\n    this.showAccountSettings = true;\n    this.showContextMenu = false;\n    this.loadUserProfile();\n  }\n\n  closeAccountSettings() {\n    this.showAccountSettings = false;\n  }\n\n  // Account settings methods\n  loadUserProfile() {\n    if (this.currentUser) {\n      this.userProfile = {\n        avatar: this.currentUser.avatar || '',\n        email: this.currentUser.email || '',\n        phoneNumber: this.currentUser.phoneNumber || ''\n      };\n    }\n  }\n\n  onAvatarChange(event: Event) {\n    const input = event.target as HTMLInputElement;\n    if (input.files && input.files[0]) {\n      const file = input.files[0];\n\n      // Validate file type\n      if (!file.type.startsWith('image/')) {\n        this.notificationService.showNotification('Please select an image file', 'error');\n        return;\n      }\n\n      // Validate file size (max 2MB)\n      if (file.size > 2 * 1024 * 1024) {\n        this.notificationService.showNotification('Image must be smaller than 2MB', 'error');\n        return;\n      }\n\n      const reader = new FileReader();\n      reader.onload = (e) => {\n        this.userProfile.avatar = e.target?.result as string;\n        this.saveAvatarChange();\n      };\n      reader.readAsDataURL(file);\n    }\n  }\n\n  saveAvatarChange() {\n    // TODO: Implement API call to save avatar\n    this.notificationService.showNotification('Avatar updated successfully', 'success');\n  }\n\n  // Logout\n  logout() {\n    this.closeContextMenu();\n    this.authService.logout();\n  }\n\n  // Keyboard shortcuts\n  @HostListener('document:keydown', ['$event'])\n  onKeyDown(event: KeyboardEvent) {\n    // Escape key closes modals\n    if (event.key === 'Escape') {\n      this.closeAllModals();\n    }\n\n    // Enter key in login modal\n    if (event.key === 'Enter' && this.showLoginModal) {\n      if (this.isValidCredentials()) {\n        this.performLogin();\n      }\n    }\n\n    // Enter key in message modal\n    if (event.key === 'Enter' && this.showMessageModal && !event.shiftKey) {\n      event.preventDefault();\n      this.sendMessage();\n    }\n  }\n\n  // Click outside handler to close context menu\n  @HostListener('document:click', ['$event'])\n  onDocumentClick(event: Event) {\n    // Close context menu when clicking outside\n    if (this.showContextMenu) {\n      this.closeContextMenu();\n    }\n  }\n\n  private closeAllModals() {\n    this.showLoginModal = false;\n    this.showMessageModal = false;\n    this.showMessagesModal = false;\n    this.showContextMenu = false;\n    this.showAccountSettings = false;\n    if (this.circleState === 'composing') {\n      this.circleState = 'authenticated';\n    }\n  }\n\n  // Utility methods\n  getCircleClass(): string {\n    return `circle-${this.circleState}`;\n  }\n\n  getCircleTitle(): string {\n    switch (this.circleState) {\n      case 'guest': return 'Click to sign in';\n      case 'authenticated': return 'Click to compose message';\n      case 'unread': return `Click to view ${this.unreadCount} unread message(s)`;\n      case 'composing': return 'Composing message...';\n      default: return '';\n    }\n  }\n\n  trackMessage(index: number, message: Message): string {\n    return message.id;\n  }\n}\n", "<!-- Main Container -->\n<div class=\"qsc-container\">\n  <!-- Central Circle -->\n  <div class=\"circle-container\">\n    <div\n      class=\"qsc-circle\"\n      [class]=\"getCircleClass()\"\n      [title]=\"getCircleTitle()\"\n      (click)=\"onCircleClick()\"\n      (contextmenu)=\"onCircleRightClick($event)\"\n      (touchstart)=\"onCircleTouchStart($event)\"\n      (touchend)=\"onCircleTouchEnd()\"\n      (touchmove)=\"onCircleTouchMove()\"\n    >\n      <div class=\"circle-inner\">\n        <div class=\"wind-effect\" *ngIf=\"circleState !== 'guest'\"></div>\n        <div class=\"unread-indicator\" *ngIf=\"circleState === 'unread'\">\n          {{ unreadCount }}\n        </div>\n      </div>\n    </div>\n  </div>\n\n  <!-- User Info (subtle, top-right) -->\n  <div class=\"user-info\" *ngIf=\"currentUser\">\n    <span>{{ currentUser.username }}</span>\n    <button class=\"logout-btn\" (click)=\"logout()\" title=\"Logout\">×</button>\n  </div>\n</div>\n\n<!-- Login Modal -->\n<div class=\"modal-overlay\" *ngIf=\"showLoginModal\" (click)=\"closeLoginModal()\">\n  <div class=\"modal login-modal\" (click)=\"$event.stopPropagation()\">\n    <div class=\"modal-header\">\n      <h2>Sign In</h2>\n      <button class=\"close-btn\" (click)=\"closeLoginModal()\">×</button>\n    </div>\n\n    <div class=\"modal-content\">\n      <div class=\"form-group\">\n        <label for=\"email\">Email</label>\n        <input\n          type=\"email\"\n          id=\"email\"\n          [(ngModel)]=\"loginCredentials.email\"\n          (input)=\"onLoginInputChange()\"\n          placeholder=\"Enter your email\"\n          [disabled]=\"isLoading\"\n          autocomplete=\"email\"\n        />\n      </div>\n\n      <div class=\"form-group\">\n        <label for=\"secretWord\">Secret Word</label>\n        <input\n          type=\"password\"\n          id=\"secretWord\"\n          [(ngModel)]=\"loginCredentials.secretWord\"\n          (input)=\"onLoginInputChange()\"\n          placeholder=\"4+ chars: A-Z, a-z, 0-9, symbol\"\n          [disabled]=\"isLoading\"\n          autocomplete=\"current-password\"\n        />\n      </div>\n\n      <div class=\"error-message\" *ngIf=\"loginError\">\n        {{ loginError }}\n      </div>\n\n      <div class=\"loading-indicator\" *ngIf=\"isLoading\">\n        <div class=\"spinner\"></div>\n        <span>Authenticating...</span>\n      </div>\n\n      <div class=\"auth-info\" *ngIf=\"!isLoading\">\n        <p>🔒 Protected by post-quantum cryptography</p>\n        <p class=\"auto-submit-hint\">Form auto-submits when credentials are valid</p>\n      </div>\n    </div>\n  </div>\n</div>\n\n<!-- Message Composer Modal -->\n<div class=\"modal-overlay\" *ngIf=\"showMessageModal\" (click)=\"closeMessageComposer()\">\n  <div class=\"modal message-modal\" (click)=\"$event.stopPropagation()\">\n    <div class=\"modal-header\">\n      <h2>Compose Message</h2>\n      <button class=\"close-btn\" (click)=\"closeMessageComposer()\">×</button>\n    </div>\n\n    <div class=\"modal-content\">\n      <!-- Message Type Selector -->\n      <div class=\"message-type-selector\">\n        <button\n          class=\"type-btn\"\n          [class.active]=\"messageType === 'direct'\"\n          (click)=\"switchMessageType('direct')\"\n        >\n          👤 Direct\n        </button>\n        <button\n          class=\"type-btn\"\n          [class.active]=\"messageType === 'group'\"\n          (click)=\"switchMessageType('group')\"\n        >\n          👥 Group\n        </button>\n      </div>\n\n      <!-- Recipient Selection -->\n      <div class=\"form-group\">\n        <label for=\"recipientSearch\">\n          {{ messageType === 'direct' ? 'To (Contact)' : 'To (Group)' }}\n        </label>\n        <div class=\"recipient-selector\">\n          <input\n            type=\"text\"\n            id=\"recipientSearch\"\n            [(ngModel)]=\"recipientSearchQuery\"\n            (input)=\"onRecipientSearchChange()\"\n            [placeholder]=\"messageType === 'direct' ? 'Search contacts...' : 'Search groups...'\"\n            autocomplete=\"off\"\n          />\n\n          <!-- Selected Recipient Display -->\n          <div class=\"selected-recipient\" *ngIf=\"getSelectedRecipientName()\">\n            <span class=\"recipient-name\">{{ getSelectedRecipientName() }}</span>\n            <button\n              class=\"clear-recipient\"\n              (click)=\"switchMessageType(messageType)\"\n              title=\"Clear selection\"\n            >×</button>\n          </div>\n\n          <!-- Contact/Group Dropdown -->\n          <div class=\"recipient-dropdown\" *ngIf=\"recipientSearchQuery && !getSelectedRecipientName()\">\n            <!-- Direct Message Contacts -->\n            <div\n              class=\"recipient-item\"\n              *ngFor=\"let contact of filteredContacts\"\n              (click)=\"selectContact(contact)\"\n              [hidden]=\"messageType !== 'direct'\"\n            >\n              <div class=\"contact-info\">\n                <span class=\"contact-name\">{{ contact.username }}</span>\n                <span class=\"contact-email\">{{ contact.email }}</span>\n              </div>\n              <div class=\"contact-status\">\n                <span\n                  class=\"status-indicator\"\n                  [class.online]=\"contact.isOnline\"\n                  [class.offline]=\"!contact.isOnline\"\n                ></span>\n                <span class=\"status-text\">\n                  {{ contact.isOnline ? 'Online' : 'Offline' }}\n                </span>\n              </div>\n            </div>\n\n            <!-- Group Chats -->\n            <div\n              class=\"recipient-item\"\n              *ngFor=\"let group of filteredGroups\"\n              (click)=\"selectGroup(group)\"\n              [hidden]=\"messageType !== 'group'\"\n            >\n              <div class=\"group-info\">\n                <span class=\"group-name\">{{ group.name }}</span>\n                <span class=\"group-members\">{{ group.members.length }} members</span>\n              </div>\n              <div class=\"group-status\">\n                <span\n                  class=\"status-indicator\"\n                  [class.active]=\"group.isActive\"\n                ></span>\n              </div>\n            </div>\n\n            <!-- No Results -->\n            <div class=\"no-results\" *ngIf=\"messageType === 'direct' && filteredContacts.length === 0\">\n              No contacts found\n            </div>\n            <div class=\"no-results\" *ngIf=\"messageType === 'group' && filteredGroups.length === 0\">\n              No groups found\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- Message Content -->\n      <div class=\"form-group\">\n        <label for=\"messageContent\">Message</label>\n        <textarea\n          id=\"messageContent\"\n          [(ngModel)]=\"messageContent\"\n          placeholder=\"Type your message here...\"\n          rows=\"6\"\n          maxlength=\"1000\"\n        ></textarea>\n        <div class=\"char-count\">{{ messageContent.length }}/1000</div>\n      </div>\n\n      <div class=\"message-actions\">\n        <button\n          class=\"btn btn-primary\"\n          (click)=\"sendMessage()\"\n          [disabled]=\"!isMessageValid()\"\n        >\n          Send Message\n        </button>\n        <button class=\"btn btn-secondary\" (click)=\"closeMessageComposer()\">\n          Cancel\n        </button>\n      </div>\n\n      <div class=\"send-hint\">\n        <p>Press Enter to send • Shift+Enter for new line</p>\n      </div>\n    </div>\n  </div>\n</div>\n\n<!-- Messages Viewer Modal -->\n<div class=\"modal-overlay\" *ngIf=\"showMessagesModal\" (click)=\"closeMessagesViewer()\">\n  <div class=\"modal messages-modal\" (click)=\"$event.stopPropagation()\">\n    <div class=\"modal-header\">\n      <h2>Messages</h2>\n      <button class=\"close-btn\" (click)=\"closeMessagesViewer()\">×</button>\n    </div>\n\n    <div class=\"modal-content\">\n      <div class=\"messages-list\" *ngIf=\"messages.length > 0\">\n        <div\n          class=\"message-item\"\n          *ngFor=\"let message of messages; trackBy: trackMessage\"\n        >\n          <div class=\"message-header\">\n            <span class=\"sender\">{{ message.sender }}</span>\n            <span class=\"timestamp\">{{ message.timestamp | date:'short' }}</span>\n          </div>\n          <div class=\"message-content\">{{ message.content }}</div>\n        </div>\n      </div>\n\n      <div class=\"empty-state\" *ngIf=\"messages.length === 0\">\n        <p>No messages yet</p>\n        <p class=\"hint\">Click the circle to compose your first message</p>\n      </div>\n    </div>\n  </div>\n</div>\n\n<!-- Keyboard Hints (bottom) -->\n<div class=\"keyboard-hints\" *ngIf=\"!showLoginModal && !showMessageModal && !showMessagesModal\">\n  <span>ESC to close modals</span>\n</div>\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,OAAO,EAAEC,SAAS,QAAQ,MAAM;;;;;;;;;ICYjCC,EAAA,CAAAC,SAAA,aAA+D;;;;;IAC/DD,EAAA,CAAAE,cAAA,cAA+D;IAC7DF,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;IADJJ,EAAA,CAAAK,SAAA,EACF;IADEL,EAAA,CAAAM,kBAAA,MAAAC,MAAA,CAAAC,WAAA,MACF;;;;;;IAOJR,EADF,CAAAE,cAAA,cAA2C,WACnC;IAAAF,EAAA,CAAAG,MAAA,GAA0B;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACvCJ,EAAA,CAAAE,cAAA,iBAA6D;IAAlCF,EAAA,CAAAS,UAAA,mBAAAC,wDAAA;MAAAV,EAAA,CAAAW,aAAA,CAAAC,GAAA;MAAA,MAAAL,MAAA,GAAAP,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASP,MAAA,CAAAQ,MAAA,EAAQ;IAAA,EAAC;IAAgBf,EAAA,CAAAG,MAAA,aAAC;IAChEH,EADgE,CAAAI,YAAA,EAAS,EACnE;;;;IAFEJ,EAAA,CAAAK,SAAA,GAA0B;IAA1BL,EAAA,CAAAgB,iBAAA,CAAAT,MAAA,CAAAU,WAAA,CAAAC,QAAA,CAA0B;;;;;IAwC9BlB,EAAA,CAAAE,cAAA,cAA8C;IAC5CF,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;IADJJ,EAAA,CAAAK,SAAA,EACF;IADEL,EAAA,CAAAM,kBAAA,MAAAC,MAAA,CAAAY,UAAA,MACF;;;;;IAEAnB,EAAA,CAAAE,cAAA,cAAiD;IAC/CF,EAAA,CAAAC,SAAA,cAA2B;IAC3BD,EAAA,CAAAE,cAAA,WAAM;IAAAF,EAAA,CAAAG,MAAA,wBAAiB;IACzBH,EADyB,CAAAI,YAAA,EAAO,EAC1B;;;;;IAGJJ,EADF,CAAAE,cAAA,cAA0C,QACrC;IAAAF,EAAA,CAAAG,MAAA,0DAAyC;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAChDJ,EAAA,CAAAE,cAAA,YAA4B;IAAAF,EAAA,CAAAG,MAAA,mDAA4C;IAC1EH,EAD0E,CAAAI,YAAA,EAAI,EACxE;;;;;;IA9CZJ,EAAA,CAAAE,cAAA,cAA8E;IAA5BF,EAAA,CAAAS,UAAA,mBAAAW,qDAAA;MAAApB,EAAA,CAAAW,aAAA,CAAAU,GAAA;MAAA,MAAAd,MAAA,GAAAP,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASP,MAAA,CAAAe,eAAA,EAAiB;IAAA,EAAC;IAC3EtB,EAAA,CAAAE,cAAA,cAAkE;IAAnCF,EAAA,CAAAS,UAAA,mBAAAc,qDAAAC,MAAA;MAAAxB,EAAA,CAAAW,aAAA,CAAAU,GAAA;MAAA,OAAArB,EAAA,CAAAc,WAAA,CAASU,MAAA,CAAAC,eAAA,EAAwB;IAAA,EAAC;IAE7DzB,EADF,CAAAE,cAAA,cAA0B,SACpB;IAAAF,EAAA,CAAAG,MAAA,cAAO;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAChBJ,EAAA,CAAAE,cAAA,iBAAsD;IAA5BF,EAAA,CAAAS,UAAA,mBAAAiB,wDAAA;MAAA1B,EAAA,CAAAW,aAAA,CAAAU,GAAA;MAAA,MAAAd,MAAA,GAAAP,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASP,MAAA,CAAAe,eAAA,EAAiB;IAAA,EAAC;IAACtB,EAAA,CAAAG,MAAA,aAAC;IACzDH,EADyD,CAAAI,YAAA,EAAS,EAC5D;IAIFJ,EAFJ,CAAAE,cAAA,cAA2B,cACD,gBACH;IAAAF,EAAA,CAAAG,MAAA,aAAK;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IAChCJ,EAAA,CAAAE,cAAA,iBAQE;IALAF,EAAA,CAAA2B,gBAAA,2BAAAC,gEAAAJ,MAAA;MAAAxB,EAAA,CAAAW,aAAA,CAAAU,GAAA;MAAA,MAAAd,MAAA,GAAAP,EAAA,CAAAa,aAAA;MAAAb,EAAA,CAAA6B,kBAAA,CAAAtB,MAAA,CAAAuB,gBAAA,CAAAC,KAAA,EAAAP,MAAA,MAAAjB,MAAA,CAAAuB,gBAAA,CAAAC,KAAA,GAAAP,MAAA;MAAA,OAAAxB,EAAA,CAAAc,WAAA,CAAAU,MAAA;IAAA,EAAoC;IACpCxB,EAAA,CAAAS,UAAA,mBAAAuB,wDAAA;MAAAhC,EAAA,CAAAW,aAAA,CAAAU,GAAA;MAAA,MAAAd,MAAA,GAAAP,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASP,MAAA,CAAA0B,kBAAA,EAAoB;IAAA,EAAC;IAKlCjC,EATE,CAAAI,YAAA,EAQE,EACE;IAGJJ,EADF,CAAAE,cAAA,eAAwB,iBACE;IAAAF,EAAA,CAAAG,MAAA,mBAAW;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IAC3CJ,EAAA,CAAAE,cAAA,iBAQE;IALAF,EAAA,CAAA2B,gBAAA,2BAAAO,gEAAAV,MAAA;MAAAxB,EAAA,CAAAW,aAAA,CAAAU,GAAA;MAAA,MAAAd,MAAA,GAAAP,EAAA,CAAAa,aAAA;MAAAb,EAAA,CAAA6B,kBAAA,CAAAtB,MAAA,CAAAuB,gBAAA,CAAAK,UAAA,EAAAX,MAAA,MAAAjB,MAAA,CAAAuB,gBAAA,CAAAK,UAAA,GAAAX,MAAA;MAAA,OAAAxB,EAAA,CAAAc,WAAA,CAAAU,MAAA;IAAA,EAAyC;IACzCxB,EAAA,CAAAS,UAAA,mBAAA2B,wDAAA;MAAApC,EAAA,CAAAW,aAAA,CAAAU,GAAA;MAAA,MAAAd,MAAA,GAAAP,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASP,MAAA,CAAA0B,kBAAA,EAAoB;IAAA,EAAC;IAKlCjC,EATE,CAAAI,YAAA,EAQE,EACE;IAWNJ,EATA,CAAAqC,UAAA,KAAAC,sCAAA,kBAA8C,KAAAC,sCAAA,kBAIG,KAAAC,sCAAA,kBAKP;IAMhDxC,EAFI,CAAAI,YAAA,EAAM,EACF,EACF;;;;IApCIJ,EAAA,CAAAK,SAAA,IAAoC;IAApCL,EAAA,CAAAyC,gBAAA,YAAAlC,MAAA,CAAAuB,gBAAA,CAAAC,KAAA,CAAoC;IAGpC/B,EAAA,CAAA0C,UAAA,aAAAnC,MAAA,CAAAoC,SAAA,CAAsB;IAUtB3C,EAAA,CAAAK,SAAA,GAAyC;IAAzCL,EAAA,CAAAyC,gBAAA,YAAAlC,MAAA,CAAAuB,gBAAA,CAAAK,UAAA,CAAyC;IAGzCnC,EAAA,CAAA0C,UAAA,aAAAnC,MAAA,CAAAoC,SAAA,CAAsB;IAKE3C,EAAA,CAAAK,SAAA,EAAgB;IAAhBL,EAAA,CAAA0C,UAAA,SAAAnC,MAAA,CAAAY,UAAA,CAAgB;IAIZnB,EAAA,CAAAK,SAAA,EAAe;IAAfL,EAAA,CAAA0C,UAAA,SAAAnC,MAAA,CAAAoC,SAAA,CAAe;IAKvB3C,EAAA,CAAAK,SAAA,EAAgB;IAAhBL,EAAA,CAAA0C,UAAA,UAAAnC,MAAA,CAAAoC,SAAA,CAAgB;;;;;;IAoDlC3C,EADF,CAAAE,cAAA,cAAmE,eACpC;IAAAF,EAAA,CAAAG,MAAA,GAAgC;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACpEJ,EAAA,CAAAE,cAAA,iBAIC;IAFCF,EAAA,CAAAS,UAAA,mBAAAmC,+DAAA;MAAA5C,EAAA,CAAAW,aAAA,CAAAkC,GAAA;MAAA,MAAAtC,MAAA,GAAAP,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASP,MAAA,CAAAuC,iBAAA,CAAAvC,MAAA,CAAAwC,WAAA,CAA8B;IAAA,EAAC;IAEzC/C,EAAA,CAAAG,MAAA,aAAC;IACJH,EADI,CAAAI,YAAA,EAAS,EACP;;;;IANyBJ,EAAA,CAAAK,SAAA,GAAgC;IAAhCL,EAAA,CAAAgB,iBAAA,CAAAT,MAAA,CAAAyC,wBAAA,GAAgC;;;;;;IAW7DhD,EAAA,CAAAE,cAAA,cAKC;IAFCF,EAAA,CAAAS,UAAA,mBAAAwC,kEAAA;MAAA,MAAAC,UAAA,GAAAlD,EAAA,CAAAW,aAAA,CAAAwC,GAAA,EAAAC,SAAA;MAAA,MAAA7C,MAAA,GAAAP,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASP,MAAA,CAAA8C,aAAA,CAAAH,UAAA,CAAsB;IAAA,EAAC;IAI9BlD,EADF,CAAAE,cAAA,cAA0B,eACG;IAAAF,EAAA,CAAAG,MAAA,GAAsB;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACxDJ,EAAA,CAAAE,cAAA,eAA4B;IAAAF,EAAA,CAAAG,MAAA,GAAmB;IACjDH,EADiD,CAAAI,YAAA,EAAO,EAClD;IACNJ,EAAA,CAAAE,cAAA,cAA4B;IAC1BF,EAAA,CAAAC,SAAA,eAIQ;IACRD,EAAA,CAAAE,cAAA,eAA0B;IACxBF,EAAA,CAAAG,MAAA,GACF;IAEJH,EAFI,CAAAI,YAAA,EAAO,EACH,EACF;;;;;IAhBJJ,EAAA,CAAA0C,UAAA,WAAAnC,MAAA,CAAAwC,WAAA,cAAmC;IAGN/C,EAAA,CAAAK,SAAA,GAAsB;IAAtBL,EAAA,CAAAgB,iBAAA,CAAAkC,UAAA,CAAAhC,QAAA,CAAsB;IACrBlB,EAAA,CAAAK,SAAA,GAAmB;IAAnBL,EAAA,CAAAgB,iBAAA,CAAAkC,UAAA,CAAAnB,KAAA,CAAmB;IAK7C/B,EAAA,CAAAK,SAAA,GAAiC;IACjCL,EADA,CAAAsD,WAAA,WAAAJ,UAAA,CAAAK,QAAA,CAAiC,aAAAL,UAAA,CAAAK,QAAA,CACE;IAGnCvD,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAA4C,UAAA,CAAAK,QAAA,6BACF;;;;;;IAKJvD,EAAA,CAAAE,cAAA,cAKC;IAFCF,EAAA,CAAAS,UAAA,mBAAA+C,kEAAA;MAAA,MAAAC,QAAA,GAAAzD,EAAA,CAAAW,aAAA,CAAA+C,GAAA,EAAAN,SAAA;MAAA,MAAA7C,MAAA,GAAAP,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASP,MAAA,CAAAoD,WAAA,CAAAF,QAAA,CAAkB;IAAA,EAAC;IAI1BzD,EADF,CAAAE,cAAA,cAAwB,eACG;IAAAF,EAAA,CAAAG,MAAA,GAAgB;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAChDJ,EAAA,CAAAE,cAAA,eAA4B;IAAAF,EAAA,CAAAG,MAAA,GAAkC;IAChEH,EADgE,CAAAI,YAAA,EAAO,EACjE;IACNJ,EAAA,CAAAE,cAAA,cAA0B;IACxBF,EAAA,CAAAC,SAAA,eAGQ;IAEZD,EADE,CAAAI,YAAA,EAAM,EACF;;;;;IAZJJ,EAAA,CAAA0C,UAAA,WAAAnC,MAAA,CAAAwC,WAAA,aAAkC;IAGP/C,EAAA,CAAAK,SAAA,GAAgB;IAAhBL,EAAA,CAAAgB,iBAAA,CAAAyC,QAAA,CAAAG,IAAA,CAAgB;IACb5D,EAAA,CAAAK,SAAA,GAAkC;IAAlCL,EAAA,CAAAM,kBAAA,KAAAmD,QAAA,CAAAI,OAAA,CAAAC,MAAA,aAAkC;IAK5D9D,EAAA,CAAAK,SAAA,GAA+B;IAA/BL,EAAA,CAAAsD,WAAA,WAAAG,QAAA,CAAAM,QAAA,CAA+B;;;;;IAMrC/D,EAAA,CAAAE,cAAA,cAA0F;IACxFF,EAAA,CAAAG,MAAA,0BACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;;IACNJ,EAAA,CAAAE,cAAA,cAAuF;IACrFF,EAAA,CAAAG,MAAA,wBACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;;IAjDRJ,EAAA,CAAAE,cAAA,cAA4F;IA+C1FF,EA7CA,CAAAqC,UAAA,IAAA2B,4CAAA,mBAKC,IAAAC,4CAAA,kBAuBA,IAAAC,4CAAA,kBAcyF,IAAAC,4CAAA,kBAGH;IAGzFnE,EAAA,CAAAI,YAAA,EAAM;;;;IA9CkBJ,EAAA,CAAAK,SAAA,EAAmB;IAAnBL,EAAA,CAAA0C,UAAA,YAAAnC,MAAA,CAAA6D,gBAAA,CAAmB;IAuBrBpE,EAAA,CAAAK,SAAA,EAAiB;IAAjBL,EAAA,CAAA0C,UAAA,YAAAnC,MAAA,CAAA8D,cAAA,CAAiB;IAiBZrE,EAAA,CAAAK,SAAA,EAA+D;IAA/DL,EAAA,CAAA0C,UAAA,SAAAnC,MAAA,CAAAwC,WAAA,iBAAAxC,MAAA,CAAA6D,gBAAA,CAAAN,MAAA,OAA+D;IAG/D9D,EAAA,CAAAK,SAAA,EAA4D;IAA5DL,EAAA,CAAA0C,UAAA,SAAAnC,MAAA,CAAAwC,WAAA,gBAAAxC,MAAA,CAAA8D,cAAA,CAAAP,MAAA,OAA4D;;;;;;IAnGjG9D,EAAA,CAAAE,cAAA,cAAqF;IAAjCF,EAAA,CAAAS,UAAA,mBAAA6D,qDAAA;MAAAtE,EAAA,CAAAW,aAAA,CAAA4D,GAAA;MAAA,MAAAhE,MAAA,GAAAP,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASP,MAAA,CAAAiE,oBAAA,EAAsB;IAAA,EAAC;IAClFxE,EAAA,CAAAE,cAAA,cAAoE;IAAnCF,EAAA,CAAAS,UAAA,mBAAAgE,qDAAAjD,MAAA;MAAAxB,EAAA,CAAAW,aAAA,CAAA4D,GAAA;MAAA,OAAAvE,EAAA,CAAAc,WAAA,CAASU,MAAA,CAAAC,eAAA,EAAwB;IAAA,EAAC;IAE/DzB,EADF,CAAAE,cAAA,cAA0B,SACpB;IAAAF,EAAA,CAAAG,MAAA,sBAAe;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACxBJ,EAAA,CAAAE,cAAA,iBAA2D;IAAjCF,EAAA,CAAAS,UAAA,mBAAAiE,wDAAA;MAAA1E,EAAA,CAAAW,aAAA,CAAA4D,GAAA;MAAA,MAAAhE,MAAA,GAAAP,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASP,MAAA,CAAAiE,oBAAA,EAAsB;IAAA,EAAC;IAACxE,EAAA,CAAAG,MAAA,aAAC;IAC9DH,EAD8D,CAAAI,YAAA,EAAS,EACjE;IAKFJ,EAHJ,CAAAE,cAAA,cAA2B,cAEU,iBAKhC;IADCF,EAAA,CAAAS,UAAA,mBAAAkE,wDAAA;MAAA3E,EAAA,CAAAW,aAAA,CAAA4D,GAAA;MAAA,MAAAhE,MAAA,GAAAP,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASP,MAAA,CAAAuC,iBAAA,CAAkB,QAAQ,CAAC;IAAA,EAAC;IAErC9C,EAAA,CAAAG,MAAA,6BACF;IAAAH,EAAA,CAAAI,YAAA,EAAS;IACTJ,EAAA,CAAAE,cAAA,kBAIC;IADCF,EAAA,CAAAS,UAAA,mBAAAmE,yDAAA;MAAA5E,EAAA,CAAAW,aAAA,CAAA4D,GAAA;MAAA,MAAAhE,MAAA,GAAAP,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASP,MAAA,CAAAuC,iBAAA,CAAkB,OAAO,CAAC;IAAA,EAAC;IAEpC9C,EAAA,CAAAG,MAAA,4BACF;IACFH,EADE,CAAAI,YAAA,EAAS,EACL;IAIJJ,EADF,CAAAE,cAAA,eAAwB,iBACO;IAC3BF,EAAA,CAAAG,MAAA,IACF;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IAENJ,EADF,CAAAE,cAAA,eAAgC,iBAQ5B;IAJAF,EAAA,CAAA2B,gBAAA,2BAAAkD,gEAAArD,MAAA;MAAAxB,EAAA,CAAAW,aAAA,CAAA4D,GAAA;MAAA,MAAAhE,MAAA,GAAAP,EAAA,CAAAa,aAAA;MAAAb,EAAA,CAAA6B,kBAAA,CAAAtB,MAAA,CAAAuE,oBAAA,EAAAtD,MAAA,MAAAjB,MAAA,CAAAuE,oBAAA,GAAAtD,MAAA;MAAA,OAAAxB,EAAA,CAAAc,WAAA,CAAAU,MAAA;IAAA,EAAkC;IAClCxB,EAAA,CAAAS,UAAA,mBAAAsE,wDAAA;MAAA/E,EAAA,CAAAW,aAAA,CAAA4D,GAAA;MAAA,MAAAhE,MAAA,GAAAP,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASP,MAAA,CAAAyE,uBAAA,EAAyB;IAAA,EAAC;IAJrChF,EAAA,CAAAI,YAAA,EAOE;IAaFJ,EAVA,CAAAqC,UAAA,KAAA4C,sCAAA,kBAAmE,KAAAC,sCAAA,kBAUyB;IAoDhGlF,EADE,CAAAI,YAAA,EAAM,EACF;IAIJJ,EADF,CAAAE,cAAA,eAAwB,iBACM;IAAAF,EAAA,CAAAG,MAAA,eAAO;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IAC3CJ,EAAA,CAAAE,cAAA,oBAMC;IAJCF,EAAA,CAAA2B,gBAAA,2BAAAwD,mEAAA3D,MAAA;MAAAxB,EAAA,CAAAW,aAAA,CAAA4D,GAAA;MAAA,MAAAhE,MAAA,GAAAP,EAAA,CAAAa,aAAA;MAAAb,EAAA,CAAA6B,kBAAA,CAAAtB,MAAA,CAAA6E,cAAA,EAAA5D,MAAA,MAAAjB,MAAA,CAAA6E,cAAA,GAAA5D,MAAA;MAAA,OAAAxB,EAAA,CAAAc,WAAA,CAAAU,MAAA;IAAA,EAA4B;IAI7BxB,EAAA,CAAAI,YAAA,EAAW;IACZJ,EAAA,CAAAE,cAAA,eAAwB;IAAAF,EAAA,CAAAG,MAAA,IAAgC;IAC1DH,EAD0D,CAAAI,YAAA,EAAM,EAC1D;IAGJJ,EADF,CAAAE,cAAA,eAA6B,kBAK1B;IAFCF,EAAA,CAAAS,UAAA,mBAAA4E,yDAAA;MAAArF,EAAA,CAAAW,aAAA,CAAA4D,GAAA;MAAA,MAAAhE,MAAA,GAAAP,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASP,MAAA,CAAA+E,WAAA,EAAa;IAAA,EAAC;IAGvBtF,EAAA,CAAAG,MAAA,sBACF;IAAAH,EAAA,CAAAI,YAAA,EAAS;IACTJ,EAAA,CAAAE,cAAA,kBAAmE;IAAjCF,EAAA,CAAAS,UAAA,mBAAA8E,yDAAA;MAAAvF,EAAA,CAAAW,aAAA,CAAA4D,GAAA;MAAA,MAAAhE,MAAA,GAAAP,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASP,MAAA,CAAAiE,oBAAA,EAAsB;IAAA,EAAC;IAChExE,EAAA,CAAAG,MAAA,gBACF;IACFH,EADE,CAAAI,YAAA,EAAS,EACL;IAGJJ,EADF,CAAAE,cAAA,eAAuB,SAClB;IAAAF,EAAA,CAAAG,MAAA,2DAA8C;IAIzDH,EAJyD,CAAAI,YAAA,EAAI,EACjD,EACF,EACF,EACF;;;;IA7HIJ,EAAA,CAAAK,SAAA,GAAyC;IAAzCL,EAAA,CAAAsD,WAAA,WAAA/C,MAAA,CAAAwC,WAAA,cAAyC;IAOzC/C,EAAA,CAAAK,SAAA,GAAwC;IAAxCL,EAAA,CAAAsD,WAAA,WAAA/C,MAAA,CAAAwC,WAAA,aAAwC;IAUxC/C,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAC,MAAA,CAAAwC,WAAA,mDACF;IAKI/C,EAAA,CAAAK,SAAA,GAAkC;IAAlCL,EAAA,CAAAyC,gBAAA,YAAAlC,MAAA,CAAAuE,oBAAA,CAAkC;IAElC9E,EAAA,CAAA0C,UAAA,gBAAAnC,MAAA,CAAAwC,WAAA,0DAAoF;IAKrD/C,EAAA,CAAAK,SAAA,EAAgC;IAAhCL,EAAA,CAAA0C,UAAA,SAAAnC,MAAA,CAAAyC,wBAAA,GAAgC;IAUhChD,EAAA,CAAAK,SAAA,EAAyD;IAAzDL,EAAA,CAAA0C,UAAA,SAAAnC,MAAA,CAAAuE,oBAAA,KAAAvE,MAAA,CAAAyC,wBAAA,GAAyD;IA2D1FhD,EAAA,CAAAK,SAAA,GAA4B;IAA5BL,EAAA,CAAAyC,gBAAA,YAAAlC,MAAA,CAAA6E,cAAA,CAA4B;IAKNpF,EAAA,CAAAK,SAAA,GAAgC;IAAhCL,EAAA,CAAAM,kBAAA,KAAAC,MAAA,CAAA6E,cAAA,CAAAtB,MAAA,UAAgC;IAOtD9D,EAAA,CAAAK,SAAA,GAA8B;IAA9BL,EAAA,CAAA0C,UAAA,cAAAnC,MAAA,CAAAiF,cAAA,GAA8B;;;;;IA+B5BxF,EALJ,CAAAE,cAAA,cAGC,cAC6B,eACL;IAAAF,EAAA,CAAAG,MAAA,GAAoB;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAChDJ,EAAA,CAAAE,cAAA,eAAwB;IAAAF,EAAA,CAAAG,MAAA,GAAsC;;IAChEH,EADgE,CAAAI,YAAA,EAAO,EACjE;IACNJ,EAAA,CAAAE,cAAA,cAA6B;IAAAF,EAAA,CAAAG,MAAA,GAAqB;IACpDH,EADoD,CAAAI,YAAA,EAAM,EACpD;;;;IAJmBJ,EAAA,CAAAK,SAAA,GAAoB;IAApBL,EAAA,CAAAgB,iBAAA,CAAAyE,WAAA,CAAAC,MAAA,CAAoB;IACjB1F,EAAA,CAAAK,SAAA,GAAsC;IAAtCL,EAAA,CAAAgB,iBAAA,CAAAhB,EAAA,CAAA2F,WAAA,OAAAF,WAAA,CAAAG,SAAA,WAAsC;IAEnC5F,EAAA,CAAAK,SAAA,GAAqB;IAArBL,EAAA,CAAAgB,iBAAA,CAAAyE,WAAA,CAAAI,OAAA,CAAqB;;;;;IATtD7F,EAAA,CAAAE,cAAA,cAAuD;IACrDF,EAAA,CAAAqC,UAAA,IAAAyD,2CAAA,kBAGC;IAOH9F,EAAA,CAAAI,YAAA,EAAM;;;;IARkBJ,EAAA,CAAAK,SAAA,EAAa;IAAAL,EAAb,CAAA0C,UAAA,YAAAnC,MAAA,CAAAwF,QAAA,CAAa,iBAAAxF,MAAA,CAAAyF,YAAA,CAAqB;;;;;IAWxDhG,EADF,CAAAE,cAAA,cAAuD,QAClD;IAAAF,EAAA,CAAAG,MAAA,sBAAe;IAAAH,EAAA,CAAAI,YAAA,EAAI;IACtBJ,EAAA,CAAAE,cAAA,YAAgB;IAAAF,EAAA,CAAAG,MAAA,qDAA8C;IAChEH,EADgE,CAAAI,YAAA,EAAI,EAC9D;;;;;;IAxBZJ,EAAA,CAAAE,cAAA,cAAqF;IAAhCF,EAAA,CAAAS,UAAA,mBAAAwF,qDAAA;MAAAjG,EAAA,CAAAW,aAAA,CAAAuF,IAAA;MAAA,MAAA3F,MAAA,GAAAP,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASP,MAAA,CAAA4F,mBAAA,EAAqB;IAAA,EAAC;IAClFnG,EAAA,CAAAE,cAAA,cAAqE;IAAnCF,EAAA,CAAAS,UAAA,mBAAA2F,qDAAA5E,MAAA;MAAAxB,EAAA,CAAAW,aAAA,CAAAuF,IAAA;MAAA,OAAAlG,EAAA,CAAAc,WAAA,CAASU,MAAA,CAAAC,eAAA,EAAwB;IAAA,EAAC;IAEhEzB,EADF,CAAAE,cAAA,cAA0B,SACpB;IAAAF,EAAA,CAAAG,MAAA,eAAQ;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACjBJ,EAAA,CAAAE,cAAA,iBAA0D;IAAhCF,EAAA,CAAAS,UAAA,mBAAA4F,wDAAA;MAAArG,EAAA,CAAAW,aAAA,CAAAuF,IAAA;MAAA,MAAA3F,MAAA,GAAAP,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASP,MAAA,CAAA4F,mBAAA,EAAqB;IAAA,EAAC;IAACnG,EAAA,CAAAG,MAAA,aAAC;IAC7DH,EAD6D,CAAAI,YAAA,EAAS,EAChE;IAENJ,EAAA,CAAAE,cAAA,cAA2B;IAczBF,EAbA,CAAAqC,UAAA,IAAAiE,qCAAA,kBAAuD,IAAAC,qCAAA,kBAaA;IAM7DvG,EAFI,CAAAI,YAAA,EAAM,EACF,EACF;;;;IAnB4BJ,EAAA,CAAAK,SAAA,GAAyB;IAAzBL,EAAA,CAAA0C,UAAA,SAAAnC,MAAA,CAAAwF,QAAA,CAAAjC,MAAA,KAAyB;IAa3B9D,EAAA,CAAAK,SAAA,EAA2B;IAA3BL,EAAA,CAAA0C,UAAA,SAAAnC,MAAA,CAAAwF,QAAA,CAAAjC,MAAA,OAA2B;;;;;IAUzD9D,EADF,CAAAE,cAAA,cAA+F,WACvF;IAAAF,EAAA,CAAAG,MAAA,0BAAmB;IAC3BH,EAD2B,CAAAI,YAAA,EAAO,EAC5B;;;AD9MN,OAAM,MAAOoG,gBAAgB;EAkD3BC,YACUC,WAAwB,EACxBC,cAA8B,EAC9BC,mBAAwC;IAFxC,KAAAF,WAAW,GAAXA,WAAW;IACX,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,mBAAmB,GAAnBA,mBAAmB;IApDrB,KAAAC,QAAQ,GAAG,IAAI/G,OAAO,EAAQ;IAEtC;IACA,KAAAgH,WAAW,GAAgB,OAAO;IAElC;IACA,KAAAC,cAAc,GAAG,KAAK;IACtB,KAAAC,gBAAgB,GAAG,KAAK;IACxB,KAAAC,iBAAiB,GAAG,KAAK;IACzB,KAAAC,eAAe,GAAG,KAAK;IACvB,KAAAC,mBAAmB,GAAG,KAAK;IAE3B;IACA,KAAAC,mBAAmB,GAAG;MAAEC,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAC,CAAE;IAEpC;IACA,KAAAxF,gBAAgB,GAAqB;MAAEC,KAAK,EAAE,EAAE;MAAEI,UAAU,EAAE;IAAE,CAAE;IAClE,KAAAhB,UAAU,GAAG,EAAE;IACf,KAAAwB,SAAS,GAAG,KAAK;IAEjB;IACA,KAAAyC,cAAc,GAAG,EAAE;IACnB,KAAAmC,iBAAiB,GAAG,EAAE;IACtB,KAAAC,aAAa,GAAG,EAAE;IAClB,KAAAzE,WAAW,GAAuB,QAAQ;IAC1C,KAAAgD,QAAQ,GAAc,EAAE;IACxB,KAAAvF,WAAW,GAAG,CAAC;IAEf;IACA,KAAAiH,QAAQ,GAAc,EAAE;IACxB,KAAAC,MAAM,GAAY,EAAE;IACpB,KAAAtD,gBAAgB,GAAc,EAAE;IAChC,KAAAC,cAAc,GAAY,EAAE;IAC5B,KAAAS,oBAAoB,GAAG,EAAE;IAEzB;IACA,KAAA7D,WAAW,GAAQ,IAAI;IAEvB;IACA,KAAA0G,WAAW,GAAG;MACZC,MAAM,EAAE,EAAE;MACV7F,KAAK,EAAE,EAAE;MACT8F,WAAW,EAAE;KACd;IAED;IACQ,KAAAC,cAAc,GAAQ,IAAI;IAC1B,KAAAC,iBAAiB,GAAG,GAAG,CAAC,CAAC;EAM9B;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACC,aAAa,EAAE;IACpB,IAAI,CAACC,oBAAoB,EAAE;IAC3B,IAAI,CAACC,iBAAiB,EAAE;EAC1B;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACvB,QAAQ,CAACwB,IAAI,EAAE;IACpB,IAAI,CAACxB,QAAQ,CAACyB,QAAQ,EAAE;EAC1B;EAEQL,aAAaA,CAAA;IACnB;IACA,IAAI,IAAI,CAACvB,WAAW,CAAC6B,eAAe,EAAE,EAAE;MACtC,IAAI,CAACtH,WAAW,GAAG,IAAI,CAACyF,WAAW,CAAC8B,cAAc,EAAE;MACpD,IAAI,CAAC1B,WAAW,GAAG,eAAe;MAClC,IAAI,CAAC2B,YAAY,EAAE;MACnB,IAAI,CAACC,YAAY,EAAE;MACnB,IAAI,CAACC,UAAU,EAAE;KAClB,MAAM;MACL,IAAI,CAAC7B,WAAW,GAAG,OAAO;;EAE9B;EAEQqB,iBAAiBA,CAAA;IACvB,IAAI,CAACzB,WAAW,CAACkC,UAAU,CACxBC,IAAI,CAAC9I,SAAS,CAAC,IAAI,CAAC8G,QAAQ,CAAC,CAAC,CAC9BiC,SAAS,CAACC,IAAI,IAAG;MAChB,IAAIA,IAAI,EAAE;QACR,IAAI,CAAC9H,WAAW,GAAG8H,IAAI;QACvB,IAAI,CAACjC,WAAW,GAAG,eAAe;QAClC,IAAI,CAACC,cAAc,GAAG,KAAK;QAC3B,IAAI,CAAC0B,YAAY,EAAE;OACpB,MAAM;QACL,IAAI,CAACxH,WAAW,GAAG,IAAI;QACvB,IAAI,CAAC6F,WAAW,GAAG,OAAO;QAC1B,IAAI,CAACf,QAAQ,GAAG,EAAE;QAClB,IAAI,CAACvF,WAAW,GAAG,CAAC;;IAExB,CAAC,CAAC;EACN;EAEQ0H,oBAAoBA,CAAA;IAC1B,IAAI,CAACvB,cAAc,CAACqC,SAAS,CAC1BH,IAAI,CAAC9I,SAAS,CAAC,IAAI,CAAC8G,QAAQ,CAAC,CAAC,CAC9BiC,SAAS,CAAC/C,QAAQ,IAAG;MACpB,IAAI,CAACA,QAAQ,GAAGA,QAAQ;MACxB,IAAI,CAACkD,iBAAiB,EAAE;IAC1B,CAAC,CAAC;IAEJ,IAAI,CAACtC,cAAc,CAACuC,WAAW,CAC5BL,IAAI,CAAC9I,SAAS,CAAC,IAAI,CAAC8G,QAAQ,CAAC,CAAC,CAC9BiC,SAAS,CAACK,OAAO,IAAG;MACnB,IAAI,CAACpD,QAAQ,CAACqD,OAAO,CAACD,OAAO,CAAC;MAC9B,IAAI,CAACF,iBAAiB,EAAE;MACxB,IAAI,CAACrC,mBAAmB,CAACyC,gBAAgB,CAAC,sBAAsB,CAAC;IACnE,CAAC,CAAC;EACN;EAEQJ,iBAAiBA,CAAA;IACvB,IAAI,CAACzI,WAAW,GAAG,IAAI,CAACuF,QAAQ,CAACuD,MAAM,CAACC,CAAC,IAAI,CAACA,CAAC,CAACC,IAAI,CAAC,CAAC1F,MAAM;IAC5D,IAAI,IAAI,CAACtD,WAAW,GAAG,CAAC,IAAI,IAAI,CAACsG,WAAW,KAAK,eAAe,EAAE;MAChE,IAAI,CAACA,WAAW,GAAG,QAAQ;KAC5B,MAAM,IAAI,IAAI,CAACtG,WAAW,KAAK,CAAC,IAAI,IAAI,CAACsG,WAAW,KAAK,QAAQ,EAAE;MAClE,IAAI,CAACA,WAAW,GAAG,eAAe;;EAEtC;EAEQ2B,YAAYA,CAAA;IAClB,IAAI,CAAC9B,cAAc,CAAC8B,YAAY,EAAE,CAC/BI,IAAI,CAAC9I,SAAS,CAAC,IAAI,CAAC8G,QAAQ,CAAC,CAAC,CAC9BiC,SAAS,CAAC;MACTT,IAAI,EAAGtC,QAAQ,IAAI;QACjB,IAAI,CAACA,QAAQ,GAAGA,QAAQ;QACxB,IAAI,CAACkD,iBAAiB,EAAE;MAC1B,CAAC;MACDQ,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAClD;KACD,CAAC;EACN;EAEQf,YAAYA,CAAA;IAClB;IACA,IAAI,CAACjB,QAAQ,GAAG,CACd;MACEkC,EAAE,EAAE,GAAG;MACPzI,QAAQ,EAAE,OAAO;MACjBa,KAAK,EAAE,mBAAmB;MAC1BwB,QAAQ,EAAE;KACX,EACD;MACEoG,EAAE,EAAE,GAAG;MACPzI,QAAQ,EAAE,KAAK;MACfa,KAAK,EAAE,iBAAiB;MACxBwB,QAAQ,EAAE,KAAK;MACfqG,QAAQ,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,MAAM,CAAC,CAAC;KACzC,CACF;IACD,IAAI,CAAC1F,gBAAgB,GAAG,CAAC,GAAG,IAAI,CAACqD,QAAQ,CAAC;EAC5C;EAEQkB,UAAUA,CAAA;IAChB;IACA,IAAI,CAACjB,MAAM,GAAG,CACZ;MACEiC,EAAE,EAAE,QAAQ;MACZ/F,IAAI,EAAE,WAAW;MACjBC,OAAO,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,cAAc,CAAC;MACnCE,QAAQ,EAAE;KACX,EACD;MACE4F,EAAE,EAAE,QAAQ;MACZ/F,IAAI,EAAE,QAAQ;MACdC,OAAO,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,cAAc,CAAC;MACnCE,QAAQ,EAAE;KACX,CACF;IACD,IAAI,CAACM,cAAc,GAAG,CAAC,GAAG,IAAI,CAACqD,MAAM,CAAC;EACxC;EAEA;EACAqC,aAAaA,CAAA;IACX;IACA,IAAI,IAAI,CAAC7C,eAAe,EAAE;MACxB,IAAI,CAAC8C,gBAAgB,EAAE;MACvB;;IAGF,QAAQ,IAAI,CAAClD,WAAW;MACtB,KAAK,OAAO;QACV,IAAI,CAACmD,cAAc,EAAE;QACrB;MACF,KAAK,eAAe;QAClB,IAAI,CAACC,mBAAmB,EAAE;QAC1B;MACF,KAAK,QAAQ;QACX,IAAI,CAACC,kBAAkB,EAAE;QACzB;MACF,KAAK,WAAW;QACd;QACA;;EAEN;EAEA;EACAC,kBAAkBA,CAACC,KAAiB;IAClCA,KAAK,CAACC,cAAc,EAAE;IAEtB;IACA,IAAI,IAAI,CAACxD,WAAW,KAAK,OAAO,EAAE;IAElC,IAAI,CAACI,eAAe,GAAG,IAAI;IAC3B,IAAI,CAACE,mBAAmB,GAAG;MACzBC,CAAC,EAAEgD,KAAK,CAACE,OAAO;MAChBjD,CAAC,EAAE+C,KAAK,CAACG;KACV;EACH;EAEA;EACAC,kBAAkBA,CAACJ,KAAiB;IAClC;IACA,IAAI,IAAI,CAACvD,WAAW,KAAK,OAAO,EAAE;IAElC,IAAI,CAACgB,cAAc,GAAG4C,UAAU,CAAC,MAAK;MACpC,MAAMC,KAAK,GAAGN,KAAK,CAACO,OAAO,CAAC,CAAC,CAAC;MAC9B,IAAI,CAAC1D,eAAe,GAAG,IAAI;MAC3B,IAAI,CAACE,mBAAmB,GAAG;QACzBC,CAAC,EAAEsD,KAAK,CAACJ,OAAO;QAChBjD,CAAC,EAAEqD,KAAK,CAACH;OACV;MAED;MACA,IAAIK,SAAS,CAACC,OAAO,EAAE;QACrBD,SAAS,CAACC,OAAO,CAAC,EAAE,CAAC;;IAEzB,CAAC,EAAE,IAAI,CAAC/C,iBAAiB,CAAC;EAC5B;EAEAgD,gBAAgBA,CAAA;IACd,IAAI,IAAI,CAACjD,cAAc,EAAE;MACvBkD,YAAY,CAAC,IAAI,CAAClD,cAAc,CAAC;MACjC,IAAI,CAACA,cAAc,GAAG,IAAI;;EAE9B;EAEAmD,iBAAiBA,CAAA;IACf;IACA,IAAI,IAAI,CAACnD,cAAc,EAAE;MACvBkD,YAAY,CAAC,IAAI,CAAClD,cAAc,CAAC;MACjC,IAAI,CAACA,cAAc,GAAG,IAAI;;EAE9B;EAEA;EACAmC,cAAcA,CAAA;IACZ,IAAI,CAAClD,cAAc,GAAG,IAAI;IAC1B,IAAI,CAACjF,gBAAgB,GAAG;MAAEC,KAAK,EAAE,EAAE;MAAEI,UAAU,EAAE;IAAE,CAAE;IACrD,IAAI,CAAChB,UAAU,GAAG,EAAE;EACtB;EAEAG,eAAeA,CAAA;IACb,IAAI,CAACyF,cAAc,GAAG,KAAK;IAC3B,IAAI,CAACjF,gBAAgB,GAAG;MAAEC,KAAK,EAAE,EAAE;MAAEI,UAAU,EAAE;IAAE,CAAE;IACrD,IAAI,CAAChB,UAAU,GAAG,EAAE;EACtB;EAEAc,kBAAkBA,CAAA;IAChB;IACA,IAAI,IAAI,CAACiJ,kBAAkB,EAAE,EAAE;MAC7B,IAAI,CAACC,YAAY,EAAE;;EAEvB;EAEQD,kBAAkBA,CAAA;IACxB,MAAME,UAAU,GAAG,IAAI,CAACtJ,gBAAgB,CAACC,KAAK,CAACsJ,QAAQ,CAAC,GAAG,CAAC,IAC1C,IAAI,CAACvJ,gBAAgB,CAACC,KAAK,CAACsJ,QAAQ,CAAC,GAAG,CAAC;IAC3D,MAAMC,eAAe,GAAG,IAAI,CAACxJ,gBAAgB,CAACK,UAAU,CAAC2B,MAAM,IAAI,CAAC,IAC7C,OAAO,CAACyH,IAAI,CAAC,IAAI,CAACzJ,gBAAgB,CAACK,UAAU,CAAC,IAC9C,OAAO,CAACoJ,IAAI,CAAC,IAAI,CAACzJ,gBAAgB,CAACK,UAAU,CAAC,IAC9C,OAAO,CAACoJ,IAAI,CAAC,IAAI,CAACzJ,gBAAgB,CAACK,UAAU,CAAC,IAC9C,cAAc,CAACoJ,IAAI,CAAC,IAAI,CAACzJ,gBAAgB,CAACK,UAAU,CAAC;IAE5E,OAAOiJ,UAAU,IAAIE,eAAe;EACtC;EAEQH,YAAYA,CAAA;IAClB,IAAI,IAAI,CAACxI,SAAS,EAAE;IAEpB,IAAI,CAACA,SAAS,GAAG,IAAI;IACrB,IAAI,CAACxB,UAAU,GAAG,EAAE;IAEpB,IAAI,CAACuF,WAAW,CAAC8E,KAAK,CAAC,IAAI,CAAC1J,gBAAgB,CAACC,KAAK,EAAE,IAAI,CAACD,gBAAgB,CAACK,UAAU,CAAC,CAClF0G,IAAI,CAAC9I,SAAS,CAAC,IAAI,CAAC8G,QAAQ,CAAC,CAAC,CAC9BiC,SAAS,CAAC;MACTT,IAAI,EAAGoD,QAAQ,IAAI;QACjB,IAAI,CAAC9I,SAAS,GAAG,KAAK;QACtB;MACF,CAAC;MACD8G,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAAC9G,SAAS,GAAG,KAAK;QACtB,IAAI,CAACxB,UAAU,GAAGsI,KAAK,CAACN,OAAO,IAAI,uBAAuB;MAC5D;KACD,CAAC;EACN;EAEA;EACAe,mBAAmBA,CAAA;IACjB,IAAI,CAAClD,gBAAgB,GAAG,IAAI;IAC5B,IAAI,CAAC5B,cAAc,GAAG,EAAE;IACxB,IAAI,CAACmC,iBAAiB,GAAG,EAAE;IAC3B,IAAI,CAACC,aAAa,GAAG,EAAE;IACvB,IAAI,CAACzE,WAAW,GAAG,QAAQ;IAC3B,IAAI,CAAC+B,oBAAoB,GAAG,EAAE;IAC9B,IAAI,CAACV,gBAAgB,GAAG,CAAC,GAAG,IAAI,CAACqD,QAAQ,CAAC;IAC1C,IAAI,CAACpD,cAAc,GAAG,CAAC,GAAG,IAAI,CAACqD,MAAM,CAAC;IACtC,IAAI,CAACZ,WAAW,GAAG,WAAW;EAChC;EAEAtC,oBAAoBA,CAAA;IAClB,IAAI,CAACwC,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAAC5B,cAAc,GAAG,EAAE;IACxB,IAAI,CAACmC,iBAAiB,GAAG,EAAE;IAC3B,IAAI,CAACC,aAAa,GAAG,EAAE;IACvB,IAAI,CAAC1C,oBAAoB,GAAG,EAAE;IAC9B,IAAI,CAACgC,WAAW,GAAG,eAAe;EACpC;EAEAxB,WAAWA,CAAA;IACT,IAAI,CAAC,IAAI,CAACF,cAAc,CAACsG,IAAI,EAAE,EAAE;IAEjC;IACA,IAAI,IAAI,CAAC3I,WAAW,KAAK,QAAQ,IAAI,CAAC,IAAI,CAACwE,iBAAiB,EAAE;MAC5D,IAAI,CAACX,mBAAmB,CAACyC,gBAAgB,CAAC,2BAA2B,EAAE,SAAS,CAAC;MACjF;;IAGF,IAAI,IAAI,CAACtG,WAAW,KAAK,OAAO,IAAI,CAAC,IAAI,CAACyE,aAAa,EAAE;MACvD,IAAI,CAACZ,mBAAmB,CAACyC,gBAAgB,CAAC,uBAAuB,EAAE,SAAS,CAAC;MAC7E;;IAGF,MAAMF,OAAO,GAAqB;MAChCtD,OAAO,EAAE,IAAI,CAACT,cAAc,CAACsG,IAAI,EAAE;MACnC9F,SAAS,EAAE,IAAIiE,IAAI,EAAE;MACrBnE,MAAM,EAAE,IAAI,CAACzE,WAAW,EAAEC,QAAQ,IAAI,SAAS;MAC/CyK,SAAS,EAAE,IAAI,CAAC5I,WAAW,KAAK,QAAQ,GAAG,IAAI,CAACwE,iBAAiB,GAAGqE,SAAS;MAC7EC,OAAO,EAAE,IAAI,CAAC9I,WAAW,KAAK,OAAO,GAAG,IAAI,CAACyE,aAAa,GAAGoE;KAC9D;IAED,IAAI,CAACjF,cAAc,CAACrB,WAAW,CAAC6D,OAAO,CAAC,CACrCN,IAAI,CAAC9I,SAAS,CAAC,IAAI,CAAC8G,QAAQ,CAAC,CAAC,CAC9BiC,SAAS,CAAC;MACTT,IAAI,EAAEA,CAAA,KAAK;QACT,IAAI,CAAC7D,oBAAoB,EAAE;QAC3B,IAAI,CAACoC,mBAAmB,CAACyC,gBAAgB,CAAC,cAAc,CAAC;MAC3D,CAAC;MACDI,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;QAC/C,IAAI,CAAC7C,mBAAmB,CAACyC,gBAAgB,CAAC,wBAAwB,EAAE,OAAO,CAAC;MAC9E;KACD,CAAC;EACN;EAEA;EACArE,uBAAuBA,CAAA;IACrB,MAAM8G,KAAK,GAAG,IAAI,CAAChH,oBAAoB,CAACiH,WAAW,EAAE;IAErD,IAAI,IAAI,CAAChJ,WAAW,KAAK,QAAQ,EAAE;MACjC,IAAI,CAACqB,gBAAgB,GAAG,IAAI,CAACqD,QAAQ,CAAC6B,MAAM,CAAC0C,OAAO,IAClDA,OAAO,CAAC9K,QAAQ,CAAC6K,WAAW,EAAE,CAACV,QAAQ,CAACS,KAAK,CAAC,IAC9CE,OAAO,CAACjK,KAAK,CAACgK,WAAW,EAAE,CAACV,QAAQ,CAACS,KAAK,CAAC,CAC5C;KACF,MAAM;MACL,IAAI,CAACzH,cAAc,GAAG,IAAI,CAACqD,MAAM,CAAC4B,MAAM,CAAC2C,KAAK,IAC5CA,KAAK,CAACrI,IAAI,CAACmI,WAAW,EAAE,CAACV,QAAQ,CAACS,KAAK,CAAC,CACzC;;EAEL;EAEAzI,aAAaA,CAAC2I,OAAgB;IAC5B,IAAI,CAACzE,iBAAiB,GAAGyE,OAAO,CAACrC,EAAE;IACnC,IAAI,CAAC7E,oBAAoB,GAAGkH,OAAO,CAAC9K,QAAQ;IAC5C,IAAI,CAACkD,gBAAgB,GAAG,EAAE;EAC5B;EAEAT,WAAWA,CAACsI,KAAY;IACtB,IAAI,CAACzE,aAAa,GAAGyE,KAAK,CAACtC,EAAE;IAC7B,IAAI,CAAC7E,oBAAoB,GAAGmH,KAAK,CAACrI,IAAI;IACtC,IAAI,CAACS,cAAc,GAAG,EAAE;EAC1B;EAEAvB,iBAAiBA,CAACoJ,IAAwB;IACxC,IAAI,CAACnJ,WAAW,GAAGmJ,IAAI;IACvB,IAAI,CAAC3E,iBAAiB,GAAG,EAAE;IAC3B,IAAI,CAACC,aAAa,GAAG,EAAE;IACvB,IAAI,CAAC1C,oBAAoB,GAAG,EAAE;IAC9B,IAAI,CAACE,uBAAuB,EAAE;EAChC;EAEAhC,wBAAwBA,CAAA;IACtB,IAAI,IAAI,CAACD,WAAW,KAAK,QAAQ,IAAI,IAAI,CAACwE,iBAAiB,EAAE;MAC3D,MAAMyE,OAAO,GAAG,IAAI,CAACvE,QAAQ,CAAC0E,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACzC,EAAE,KAAK,IAAI,CAACpC,iBAAiB,CAAC;MACxE,OAAOyE,OAAO,EAAE9K,QAAQ,IAAI,SAAS;;IAGvC,IAAI,IAAI,CAAC6B,WAAW,KAAK,OAAO,IAAI,IAAI,CAACyE,aAAa,EAAE;MACtD,MAAMyE,KAAK,GAAG,IAAI,CAACvE,MAAM,CAACyE,IAAI,CAACE,CAAC,IAAIA,CAAC,CAAC1C,EAAE,KAAK,IAAI,CAACnC,aAAa,CAAC;MAChE,OAAOyE,KAAK,EAAErI,IAAI,IAAI,eAAe;;IAGvC,OAAO,EAAE;EACX;EAEA4B,cAAcA,CAAA;IACZ,MAAM8G,UAAU,GAAG,IAAI,CAAClH,cAAc,CAACsG,IAAI,EAAE,CAAC5H,MAAM,GAAG,CAAC;IACxD,MAAMyI,YAAY,GAAG,IAAI,CAACxJ,WAAW,KAAK,QAAQ,GAAG,CAAC,CAAC,IAAI,CAACwE,iBAAiB,GAAG,CAAC,CAAC,IAAI,CAACC,aAAa;IACpG,OAAO8E,UAAU,IAAIC,YAAY;EACnC;EAEA;EACApC,kBAAkBA,CAAA;IAChB,IAAI,CAAClD,iBAAiB,GAAG,IAAI;IAC7B,IAAI,CAACuF,kBAAkB,EAAE;EAC3B;EAEArG,mBAAmBA,CAAA;IACjB,IAAI,CAACc,iBAAiB,GAAG,KAAK;EAChC;EAEQuF,kBAAkBA,CAAA;IACxB,IAAI,CAAC7F,cAAc,CAAC8F,aAAa,EAAE,CAChC5D,IAAI,CAAC9I,SAAS,CAAC,IAAI,CAAC8G,QAAQ,CAAC,CAAC,CAC9BiC,SAAS,CAAC,MAAK;MACd,IAAI,CAACG,iBAAiB,EAAE;IAC1B,CAAC,CAAC;EACN;EAEA;EACAe,gBAAgBA,CAAA;IACd,IAAI,CAAC9C,eAAe,GAAG,KAAK;EAC9B;EAEAwF,mBAAmBA,CAAA;IACjB,IAAI,CAACvF,mBAAmB,GAAG,IAAI;IAC/B,IAAI,CAACD,eAAe,GAAG,KAAK;IAC5B,IAAI,CAACyF,eAAe,EAAE;EACxB;EAEAC,oBAAoBA,CAAA;IAClB,IAAI,CAACzF,mBAAmB,GAAG,KAAK;EAClC;EAEA;EACAwF,eAAeA,CAAA;IACb,IAAI,IAAI,CAAC1L,WAAW,EAAE;MACpB,IAAI,CAAC0G,WAAW,GAAG;QACjBC,MAAM,EAAE,IAAI,CAAC3G,WAAW,CAAC2G,MAAM,IAAI,EAAE;QACrC7F,KAAK,EAAE,IAAI,CAACd,WAAW,CAACc,KAAK,IAAI,EAAE;QACnC8F,WAAW,EAAE,IAAI,CAAC5G,WAAW,CAAC4G,WAAW,IAAI;OAC9C;;EAEL;EAEAgF,cAAcA,CAACxC,KAAY;IACzB,MAAMyC,KAAK,GAAGzC,KAAK,CAAC0C,MAA0B;IAC9C,IAAID,KAAK,CAACE,KAAK,IAAIF,KAAK,CAACE,KAAK,CAAC,CAAC,CAAC,EAAE;MACjC,MAAMC,IAAI,GAAGH,KAAK,CAACE,KAAK,CAAC,CAAC,CAAC;MAE3B;MACA,IAAI,CAACC,IAAI,CAACf,IAAI,CAACgB,UAAU,CAAC,QAAQ,CAAC,EAAE;QACnC,IAAI,CAACtG,mBAAmB,CAACyC,gBAAgB,CAAC,6BAA6B,EAAE,OAAO,CAAC;QACjF;;MAGF;MACA,IAAI4D,IAAI,CAACE,IAAI,GAAG,CAAC,GAAG,IAAI,GAAG,IAAI,EAAE;QAC/B,IAAI,CAACvG,mBAAmB,CAACyC,gBAAgB,CAAC,gCAAgC,EAAE,OAAO,CAAC;QACpF;;MAGF,MAAM+D,MAAM,GAAG,IAAIC,UAAU,EAAE;MAC/BD,MAAM,CAACE,MAAM,GAAIC,CAAC,IAAI;QACpB,IAAI,CAAC5F,WAAW,CAACC,MAAM,GAAG2F,CAAC,CAACR,MAAM,EAAES,MAAgB;QACpD,IAAI,CAACC,gBAAgB,EAAE;MACzB,CAAC;MACDL,MAAM,CAACM,aAAa,CAACT,IAAI,CAAC;;EAE9B;EAEAQ,gBAAgBA,CAAA;IACd;IACA,IAAI,CAAC7G,mBAAmB,CAACyC,gBAAgB,CAAC,6BAA6B,EAAE,SAAS,CAAC;EACrF;EAEA;EACAtI,MAAMA,CAAA;IACJ,IAAI,CAACiJ,gBAAgB,EAAE;IACvB,IAAI,CAACtD,WAAW,CAAC3F,MAAM,EAAE;EAC3B;EAEA;EAEA4M,SAASA,CAACtD,KAAoB;IAC5B;IACA,IAAIA,KAAK,CAACuD,GAAG,KAAK,QAAQ,EAAE;MAC1B,IAAI,CAACC,cAAc,EAAE;;IAGvB;IACA,IAAIxD,KAAK,CAACuD,GAAG,KAAK,OAAO,IAAI,IAAI,CAAC7G,cAAc,EAAE;MAChD,IAAI,IAAI,CAACmE,kBAAkB,EAAE,EAAE;QAC7B,IAAI,CAACC,YAAY,EAAE;;;IAIvB;IACA,IAAId,KAAK,CAACuD,GAAG,KAAK,OAAO,IAAI,IAAI,CAAC5G,gBAAgB,IAAI,CAACqD,KAAK,CAACyD,QAAQ,EAAE;MACrEzD,KAAK,CAACC,cAAc,EAAE;MACtB,IAAI,CAAChF,WAAW,EAAE;;EAEtB;EAEA;EAEAyI,eAAeA,CAAC1D,KAAY;IAC1B;IACA,IAAI,IAAI,CAACnD,eAAe,EAAE;MACxB,IAAI,CAAC8C,gBAAgB,EAAE;;EAE3B;EAEQ6D,cAAcA,CAAA;IACpB,IAAI,CAAC9G,cAAc,GAAG,KAAK;IAC3B,IAAI,CAACC,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAACC,iBAAiB,GAAG,KAAK;IAC9B,IAAI,CAACC,eAAe,GAAG,KAAK;IAC5B,IAAI,CAACC,mBAAmB,GAAG,KAAK;IAChC,IAAI,IAAI,CAACL,WAAW,KAAK,WAAW,EAAE;MACpC,IAAI,CAACA,WAAW,GAAG,eAAe;;EAEtC;EAEA;EACAkH,cAAcA,CAAA;IACZ,OAAO,UAAU,IAAI,CAAClH,WAAW,EAAE;EACrC;EAEAmH,cAAcA,CAAA;IACZ,QAAQ,IAAI,CAACnH,WAAW;MACtB,KAAK,OAAO;QAAE,OAAO,kBAAkB;MACvC,KAAK,eAAe;QAAE,OAAO,0BAA0B;MACvD,KAAK,QAAQ;QAAE,OAAO,iBAAiB,IAAI,CAACtG,WAAW,oBAAoB;MAC3E,KAAK,WAAW;QAAE,OAAO,sBAAsB;MAC/C;QAAS,OAAO,EAAE;;EAEtB;EAEAwF,YAAYA,CAACkI,KAAa,EAAE/E,OAAgB;IAC1C,OAAOA,OAAO,CAACQ,EAAE;EACnB;;;uBA5iBWnD,gBAAgB,EAAAxG,EAAA,CAAAmO,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAArO,EAAA,CAAAmO,iBAAA,CAAAG,EAAA,CAAAC,cAAA,GAAAvO,EAAA,CAAAmO,iBAAA,CAAAK,EAAA,CAAAC,mBAAA;IAAA;EAAA;;;YAAhBjI,gBAAgB;MAAAkI,SAAA;MAAAC,YAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAhB7O,EAAA,CAAAS,UAAA,qBAAAsO,4CAAAvN,MAAA;YAAA,OAAAsN,GAAA,CAAAnB,SAAA,CAAAnM,MAAA,CAAiB;UAAA,UAAAxB,EAAA,CAAAgP,iBAAA,CAAD,mBAAAC,0CAAAzN,MAAA;YAAA,OAAhBsN,GAAA,CAAAf,eAAA,CAAAvM,MAAA,CAAuB;UAAA,UAAAxB,EAAA,CAAAgP,iBAAA,CAAP;;;;;;;;;;UC7CzBhP,EAHJ,CAAAE,cAAA,aAA2B,aAEK,aAU3B;UADCF,EAJA,CAAAS,UAAA,mBAAAyO,+CAAA;YAAA,OAASJ,GAAA,CAAA/E,aAAA,EAAe;UAAA,EAAC,yBAAAoF,qDAAA3N,MAAA;YAAA,OACVsN,GAAA,CAAA1E,kBAAA,CAAA5I,MAAA,CAA0B;UAAA,EAAC,wBAAA4N,oDAAA5N,MAAA;YAAA,OAC5BsN,GAAA,CAAArE,kBAAA,CAAAjJ,MAAA,CAA0B;UAAA,EAAC,sBAAA6N,kDAAA;YAAA,OAC7BP,GAAA,CAAA/D,gBAAA,EAAkB;UAAA,EAAC,uBAAAuE,mDAAA;YAAA,OAClBR,GAAA,CAAA7D,iBAAA,EAAmB;UAAA,EAAC;UAEjCjL,EAAA,CAAAE,cAAA,aAA0B;UAExBF,EADA,CAAAqC,UAAA,IAAAkN,+BAAA,iBAAyD,IAAAC,+BAAA,iBACM;UAKrExP,EAFI,CAAAI,YAAA,EAAM,EACF,EACF;UAGNJ,EAAA,CAAAqC,UAAA,IAAAoN,+BAAA,iBAA2C;UAI7CzP,EAAA,CAAAI,YAAA,EAAM;UAiONJ,EA9NA,CAAAqC,UAAA,IAAAqN,+BAAA,kBAA8E,IAAAC,+BAAA,mBAoDO,IAAAC,+BAAA,kBA4IA,KAAAC,gCAAA,iBA8BU;;;UAvPzF7P,EAAA,CAAAK,SAAA,GAA0B;UAA1BL,EAAA,CAAA8P,UAAA,CAAAhB,GAAA,CAAAd,cAAA,GAA0B;UAC1BhO,EAAA,CAAA0C,UAAA,UAAAoM,GAAA,CAAAb,cAAA,GAA0B;UAQEjO,EAAA,CAAAK,SAAA,GAA6B;UAA7BL,EAAA,CAAA0C,UAAA,SAAAoM,GAAA,CAAAhI,WAAA,aAA6B;UACxB9G,EAAA,CAAAK,SAAA,EAA8B;UAA9BL,EAAA,CAAA0C,UAAA,SAAAoM,GAAA,CAAAhI,WAAA,cAA8B;UAQ3C9G,EAAA,CAAAK,SAAA,EAAiB;UAAjBL,EAAA,CAAA0C,UAAA,SAAAoM,GAAA,CAAA7N,WAAA,CAAiB;UAOfjB,EAAA,CAAAK,SAAA,EAAoB;UAApBL,EAAA,CAAA0C,UAAA,SAAAoM,GAAA,CAAA/H,cAAA,CAAoB;UAoDpB/G,EAAA,CAAAK,SAAA,EAAsB;UAAtBL,EAAA,CAAA0C,UAAA,SAAAoM,GAAA,CAAA9H,gBAAA,CAAsB;UA4ItBhH,EAAA,CAAAK,SAAA,EAAuB;UAAvBL,EAAA,CAAA0C,UAAA,SAAAoM,GAAA,CAAA7H,iBAAA,CAAuB;UA8BtBjH,EAAA,CAAAK,SAAA,EAAgE;UAAhEL,EAAA,CAAA0C,UAAA,UAAAoM,GAAA,CAAA/H,cAAA,KAAA+H,GAAA,CAAA9H,gBAAA,KAAA8H,GAAA,CAAA7H,iBAAA,CAAgE;;;qBDhNjFrH,YAAY,EAAAmQ,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAAF,EAAA,CAAAG,QAAA,EAAErQ,WAAW,EAAAsQ,EAAA,CAAAC,oBAAA,EAAAD,EAAA,CAAAE,eAAA,EAAAF,EAAA,CAAAG,kBAAA,EAAAH,EAAA,CAAAI,OAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}