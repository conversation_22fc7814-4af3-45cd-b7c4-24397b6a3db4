/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = function(knex) {
  return knex.schema.alterTable('users', function(table) {
    table.string('secret_word_hash').notNullable(); // Hashed secret word
    table.integer('failed_attempts').defaultTo(0); // Track failed attempts
    table.timestamp('last_attempt_at'); // Track last attempt time
    table.boolean('is_compromised').defaultTo(false); // Flag for compromised accounts
  });
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function(knex) {
  return knex.schema.alterTable('users', function(table) {
    table.dropColumn('secret_word_hash');
    table.dropColumn('failed_attempts');
    table.dropColumn('last_attempt_at');
    table.dropColumn('is_compromised');
  });
}; 