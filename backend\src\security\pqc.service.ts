import { Injectable } from '@nestjs/common';
import { createCipheriv, createDecipheriv, randomBytes } from 'crypto';
import { promisify } from 'util';
import { exec } from 'child_process';

const execAsync = promisify(exec);

@Injectable()
export class PQCService {
  private readonly algorithm = 'aes-256-gcm';
  private readonly keyLength = 32; // 256 bits

  async generateKeyPair(): Promise<{ publicKey: string; privateKey: string }> {
    try {
      // Using liboqs for Dilithium implementation
      const { stdout } = await execAsync('oqs-dilithium-keygen');
      const [publicKey, privateKey] = stdout.split('\n').filter(Boolean);
      return { publicKey, privateKey };
    } catch (error) {
      throw new Error(`Failed to generate PQC key pair: ${error.message}`);
    }
  }

  async encrypt(data: string, publicKey: string): Promise<{ encryptedData: string; iv: string; authTag: string }> {
    const iv = randomBytes(12);
    const key = randomBytes(this.keyLength);
    
    const cipher = createCipheriv(this.algorithm, key, iv);
    const encryptedData = Buffer.concat([
      cipher.update(data, 'utf8'),
      cipher.final()
    ]);

    return {
      encryptedData: encryptedData.toString('base64'),
      iv: iv.toString('base64'),
      authTag: cipher.getAuthTag().toString('base64')
    };
  }

  async decrypt(encryptedData: string, iv: string, authTag: string, privateKey: string): Promise<string> {
    const decipher = createDecipheriv(
      this.algorithm,
      Buffer.from(privateKey, 'base64'),
      Buffer.from(iv, 'base64')
    );
    
    decipher.setAuthTag(Buffer.from(authTag, 'base64'));
    
    const decrypted = Buffer.concat([
      decipher.update(Buffer.from(encryptedData, 'base64')),
      decipher.final()
    ]);

    return decrypted.toString('utf8');
  }

  async sign(data: string, privateKey: string): Promise<string> {
    try {
      const { stdout } = await execAsync(`echo "${data}" | oqs-dilithium-sign -k "${privateKey}"`);
      return stdout.trim();
    } catch (error) {
      throw new Error(`Failed to sign data: ${error.message}`);
    }
  }

  async verify(data: string, signature: string, publicKey: string): Promise<boolean> {
    try {
      await execAsync(`echo "${data}" | oqs-dilithium-verify -k "${publicKey}" -s "${signature}"`);
      return true;
    } catch (error) {
      return false;
    }
  }
} 