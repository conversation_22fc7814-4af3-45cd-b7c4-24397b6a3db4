{"ast": null, "code": "import { LoginComponent } from './components/login/login.component';\nimport { MainScreenComponent } from './components/main-screen/main-screen.component';\nimport { MessageComposerComponent } from './components/message-composer/message-composer.component';\nimport { GroupManagerComponent } from './components/group-manager/group-manager.component';\nimport { authGuard, loginGuard } from './guards/auth.guard';\nexport const routes = [{\n  path: '',\n  redirectTo: '/main',\n  pathMatch: 'full'\n}, {\n  path: 'login',\n  component: LoginComponent,\n  canActivate: [loginGuard]\n}, {\n  path: 'main',\n  component: MainScreenComponent,\n  canActivate: [authGuard]\n}, {\n  path: 'compose',\n  component: MessageComposerComponent,\n  canActivate: [authGuard]\n}, {\n  path: 'groups/create',\n  component: GroupManagerComponent,\n  canActivate: [authGuard]\n}, {\n  path: 'groups/join',\n  component: GroupManagerComponent,\n  canActivate: [authGuard]\n}, {\n  path: '**',\n  redirectTo: '/login'\n}];", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}