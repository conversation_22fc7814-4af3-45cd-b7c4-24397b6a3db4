import { NestFactory } from '@nestjs/core';
import { AppModule } from '../app.module';
import { UserService } from '../user/user.service';
import { SecretWordService } from '../auth/services/secret-word.service';
import { Logger } from '@nestjs/common';

async function resetQSCUser() {
  const logger = new Logger('ResetQSCUser');

  try {
    const app = await NestFactory.create(AppModule);
    const userService = app.get(UserService);
    const secretWordService = app.get(SecretWordService);

    const username = 'QSC';
    const phone = '+256753068706';

    // Find the QSC user
    let user;
    try {
      user = await userService.findByUsername(username);
    } catch (error) {
      logger.error(`User with username '${username}' not found`);
      await app.close();
      return;
    }

    logger.log(`Found QSC user:`);
    logger.log(`  ID: ${user.id}`);
    logger.log(`  Username: ${user.username}`);
    logger.log(`  Email: ${user.email}`);
    logger.log(`  Current Phone: ${user.phone || 'Not set'}`);
    logger.log(`  Is Admin: ${user.isAdmin}`);
    logger.log('');

    // Generate a new secure 4-character secret word
    function generateSecretWord(): string {
      const uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
      const lowercase = 'abcdefghijklmnopqrstuvwxyz';
      const digits = '0123456789';
      const symbols = '!@#$%^&*()_+-=[]{}|;:,.<>?';

      const getRandomChar = (str: string) => str[Math.floor(Math.random() * str.length)];

      return [
        getRandomChar(uppercase),
        getRandomChar(lowercase),
        getRandomChar(digits),
        getRandomChar(symbols)
      ].sort(() => Math.random() - 0.5).join('');
    }

    const newSecretWord = generateSecretWord();

    // Create new secret word hash
    const newSecretWordHash = await secretWordService.hashSecretWord(newSecretWord);

    // Update the user with new secret word and phone number
    user.secretWordHash = newSecretWordHash;
    user.phone = phone;
    user.failedAttempts = 0; // Reset failed attempts
    user.lastAttemptAt = null; // Clear last attempt time
    user.isCompromised = false; // Ensure not compromised
    user.accountStatus = 'active'; // Ensure active

    const updatedUser = await userService.updatePhone(user.id, phone);
    
    // Save the updated secret word hash manually since updatePhone doesn't handle it
    await userService['userRepository'].save({
      ...user,
      secretWordHash: newSecretWordHash,
    });

    logger.log(`✅ QSC user updated successfully:`);
    logger.log(`   Username: ${user.username}`);
    logger.log(`   Email: ${user.email}`);
    logger.log(`   Phone: ${phone}`);
    logger.log(`   ID: ${user.id}`);
    logger.log(`   New Secret Word: ${newSecretWord}`);
    logger.log('');
    logger.log('🔐 IMPORTANT: Save these credentials securely!');
    logger.log('🔐 The secret word will not be displayed again.');
    logger.log('📱 Phone number has been added to the user profile.');

    await app.close();
  } catch (error) {
    logger.error('Failed to reset QSC user:', error);
    process.exit(1);
  }
}

// Run the script if this file is executed directly
if (require.main === module) {
  resetQSCUser();
}

export { resetQSCUser };
