{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Projects/QSC1/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { TestBed } from '@angular/core/testing';\nimport { Router } from '@angular/router';\nimport { App } from './app';\nimport { AuthService } from './services/auth.service';\ndescribe('App', () => {\n  let mockAuthService;\n  let mockRouter;\n  beforeEach(/*#__PURE__*/_asyncToGenerator(function* () {\n    mockAuthService = jasmine.createSpyObj('AuthService', ['authState$']);\n    mockRouter = jasmine.createSpyObj('Router', ['navigate']);\n    yield TestBed.configureTestingModule({\n      imports: [App],\n      providers: [{\n        provide: AuthService,\n        useValue: mockAuthService\n      }, {\n        provide: Router,\n        useValue: mockRouter\n      }]\n    }).compileComponents();\n  }));\n  it('should create the app', () => {\n    const fixture = TestBed.createComponent(App);\n    const app = fixture.componentInstance;\n    expect(app).toBeTruthy();\n  });\n  it('should have QSC title', () => {\n    const fixture = TestBed.createComponent(App);\n    const app = fixture.componentInstance;\n    expect(app.title).toBe('QSC');\n  });\n});", "map": {"version": 3, "names": ["TestBed", "Router", "App", "AuthService", "describe", "mockAuthService", "mockRouter", "beforeEach", "_asyncToGenerator", "jasmine", "createSpyObj", "configureTestingModule", "imports", "providers", "provide", "useValue", "compileComponents", "it", "fixture", "createComponent", "app", "componentInstance", "expect", "toBeTruthy", "title", "toBe"], "sources": ["C:\\Users\\<USER>\\Projects\\QSC1\\frontend\\src\\app\\app.spec.ts"], "sourcesContent": ["import { TestBed } from '@angular/core/testing';\r\nimport { Router } from '@angular/router';\r\nimport { App } from './app';\r\nimport { AuthService } from './services/auth.service';\r\n\r\ndescribe('App', () => {\r\n  let mockAuthService: jasmine.SpyObj<AuthService>;\r\n  let mockRouter: jasmine.SpyObj<Router>;\r\n\r\n  beforeEach(async () => {\r\n    mockAuthService = jasmine.createSpyObj('AuthService', ['authState$']);\r\n    mockRouter = jasmine.createSpyObj('Router', ['navigate']);\r\n\r\n    await TestBed.configureTestingModule({\r\n      imports: [App],\r\n      providers: [\r\n        { provide: AuthService, useValue: mockAuthService },\r\n        { provide: Router, useValue: mockRouter }\r\n      ]\r\n    }).compileComponents();\r\n  });\r\n\r\n  it('should create the app', () => {\r\n    const fixture = TestBed.createComponent(App);\r\n    const app = fixture.componentInstance;\r\n    expect(app).toBeTruthy();\r\n  });\r\n\r\n  it('should have QSC title', () => {\r\n    const fixture = TestBed.createComponent(App);\r\n    const app = fixture.componentInstance;\r\n    expect(app.title).toBe('QSC');\r\n  });\r\n});\r\n"], "mappings": ";AAAA,SAASA,OAAO,QAAQ,uBAAuB;AAC/C,SAASC,MAAM,QAAQ,iBAAiB;AACxC,SAASC,GAAG,QAAQ,OAAO;AAC3B,SAASC,WAAW,QAAQ,yBAAyB;AAErDC,QAAQ,CAAC,KAAK,EAAE,MAAK;EACnB,IAAIC,eAA4C;EAChD,IAAIC,UAAkC;EAEtCC,UAAU,cAAAC,iBAAA,CAAC,aAAW;IACpBH,eAAe,GAAGI,OAAO,CAACC,YAAY,CAAC,aAAa,EAAE,CAAC,YAAY,CAAC,CAAC;IACrEJ,UAAU,GAAGG,OAAO,CAACC,YAAY,CAAC,QAAQ,EAAE,CAAC,UAAU,CAAC,CAAC;IAEzD,MAAMV,OAAO,CAACW,sBAAsB,CAAC;MACnCC,OAAO,EAAE,CAACV,GAAG,CAAC;MACdW,SAAS,EAAE,CACT;QAAEC,OAAO,EAAEX,WAAW;QAAEY,QAAQ,EAAEV;MAAe,CAAE,EACnD;QAAES,OAAO,EAAEb,MAAM;QAAEc,QAAQ,EAAET;MAAU,CAAE;KAE5C,CAAC,CAACU,iBAAiB,EAAE;EACxB,CAAC,EAAC;EAEFC,EAAE,CAAC,uBAAuB,EAAE,MAAK;IAC/B,MAAMC,OAAO,GAAGlB,OAAO,CAACmB,eAAe,CAACjB,GAAG,CAAC;IAC5C,MAAMkB,GAAG,GAAGF,OAAO,CAACG,iBAAiB;IACrCC,MAAM,CAACF,GAAG,CAAC,CAACG,UAAU,EAAE;EAC1B,CAAC,CAAC;EAEFN,EAAE,CAAC,uBAAuB,EAAE,MAAK;IAC/B,MAAMC,OAAO,GAAGlB,OAAO,CAACmB,eAAe,CAACjB,GAAG,CAAC;IAC5C,MAAMkB,GAAG,GAAGF,OAAO,CAACG,iBAAiB;IACrCC,MAAM,CAACF,GAAG,CAACI,KAAK,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;EAC/B,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}