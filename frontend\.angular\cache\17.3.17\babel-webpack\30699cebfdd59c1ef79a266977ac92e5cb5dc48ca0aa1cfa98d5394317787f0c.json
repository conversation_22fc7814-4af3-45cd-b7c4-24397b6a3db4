{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Projects/QSC1/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { __decorate } from \"tslib\";\nimport { Injectable } from '@angular/core';\nlet ErrorHandlingService = class ErrorHandlingService {\n  constructor(secureStorage) {\n    this.secureStorage = secureStorage;\n    this.MAX_RECOVERY_ATTEMPTS = 3;\n    this.MAX_NETWORK_RETRIES = 5;\n    this.INITIAL_RETRY_DELAY = 1000; // 1 second\n  }\n  handleError(error, type) {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      const errorLog = {\n        id: crypto.randomUUID(),\n        timestamp: new Date(),\n        type,\n        message: error.message,\n        stack: error.stack,\n        recoveryAttempted: false,\n        recovered: false\n      };\n      try {\n        switch (type) {\n          case 'SECURITY':\n            yield _this.handleSecurityError(errorLog);\n            break;\n          case 'STORAGE':\n            yield _this.handleStorageError(errorLog);\n            break;\n          case 'WASM':\n            yield _this.handleWasmError(errorLog);\n            break;\n          case 'NETWORK':\n            yield _this.handleNetworkError(errorLog);\n            break;\n        }\n      } catch (recoveryError) {\n        errorLog.recoveryAttempted = true;\n        errorLog.recovered = false;\n        yield _this.logError(errorLog);\n        throw recoveryError;\n      }\n    })();\n  }\n  handleSecurityError(errorLog) {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      // Force re-authentication and clear sensitive data\n      try {\n        yield _this2.secureStorage.wipeStorage();\n        errorLog.recoveryAttempted = true;\n        errorLog.recovered = true;\n      } catch (error) {\n        errorLog.recoveryAttempted = true;\n        errorLog.recovered = false;\n        throw error;\n      } finally {\n        yield _this2.logError(errorLog);\n      }\n    })();\n  }\n  handleStorageError(errorLog) {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      let attempts = 0;\n      while (attempts < _this3.MAX_RECOVERY_ATTEMPTS) {\n        try {\n          yield _this3.secureStorage.rotateKeys();\n          errorLog.recoveryAttempted = true;\n          errorLog.recovered = true;\n          break;\n        } catch (error) {\n          attempts++;\n          if (attempts === _this3.MAX_RECOVERY_ATTEMPTS) {\n            errorLog.recoveryAttempted = true;\n            errorLog.recovered = false;\n            yield _this3.secureStorage.wipeStorage();\n            throw error;\n          }\n          yield new Promise(resolve => setTimeout(resolve, _this3.INITIAL_RETRY_DELAY * attempts));\n        }\n      }\n      yield _this3.logError(errorLog);\n    })();\n  }\n  handleWasmError(errorLog) {\n    var _this4 = this;\n    return _asyncToGenerator(function* () {\n      let attempts = 0;\n      while (attempts < _this4.MAX_RECOVERY_ATTEMPTS) {\n        try {\n          // Attempt to reload WASM module\n          yield _this4.reloadWasmModule();\n          errorLog.recoveryAttempted = true;\n          errorLog.recovered = true;\n          break;\n        } catch (error) {\n          attempts++;\n          if (attempts === _this4.MAX_RECOVERY_ATTEMPTS) {\n            errorLog.recoveryAttempted = true;\n            errorLog.recovered = false;\n            throw error;\n          }\n          yield new Promise(resolve => setTimeout(resolve, _this4.INITIAL_RETRY_DELAY * attempts));\n        }\n      }\n      yield _this4.logError(errorLog);\n    })();\n  }\n  handleNetworkError(errorLog) {\n    var _this5 = this;\n    return _asyncToGenerator(function* () {\n      let attempts = 0;\n      while (attempts < _this5.MAX_NETWORK_RETRIES) {\n        try {\n          // Implement exponential backoff\n          const delay = _this5.INITIAL_RETRY_DELAY * Math.pow(2, attempts);\n          yield new Promise(resolve => setTimeout(resolve, delay));\n          // Check network connectivity\n          const isConnected = yield _this5.checkNetworkConnectivity();\n          if (isConnected) {\n            errorLog.recoveryAttempted = true;\n            errorLog.recovered = true;\n            break;\n          }\n          attempts++;\n          if (attempts === _this5.MAX_NETWORK_RETRIES) {\n            errorLog.recoveryAttempted = true;\n            errorLog.recovered = false;\n            throw new Error('Network connection failed after maximum retries');\n          }\n        } catch (error) {\n          attempts++;\n          if (attempts === _this5.MAX_NETWORK_RETRIES) {\n            errorLog.recoveryAttempted = true;\n            errorLog.recovered = false;\n            throw error;\n          }\n        }\n      }\n      yield _this5.logError(errorLog);\n    })();\n  }\n  reloadWasmModule() {\n    return _asyncToGenerator(function* () {\n      // Implementation will be provided by WASM service\n      throw new Error('Not implemented');\n    })();\n  }\n  checkNetworkConnectivity() {\n    return _asyncToGenerator(function* () {\n      try {\n        const response = yield fetch('/api/health', {\n          method: 'HEAD'\n        });\n        return response.ok;\n      } catch {\n        return false;\n      }\n    })();\n  }\n  logError(errorLog) {\n    var _this6 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        yield _this6.secureStorage.storeSecurely(errorLog.id, errorLog, 'error_logs');\n      } catch (error) {\n        console.error('Failed to log error:', error);\n      }\n    })();\n  }\n  getErrorLogs() {\n    var _this7 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        const logs = yield _this7.secureStorage.retrieveSecurely('error_logs', 'all');\n        return Object.values(logs);\n      } catch (error) {\n        console.error('Failed to retrieve error logs:', error);\n        return [];\n      }\n    })();\n  }\n  clearErrorLogs() {\n    var _this8 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        yield _this8.secureStorage.deleteSecurely('error_logs', 'all');\n      } catch (error) {\n        console.error('Failed to clear error logs:', error);\n        throw error;\n      }\n    })();\n  }\n};\nErrorHandlingService = __decorate([Injectable({\n  providedIn: 'root'\n})], ErrorHandlingService);\nexport { ErrorHandlingService };", "map": {"version": 3, "names": ["Injectable", "ErrorHandlingService", "constructor", "secureStorage", "MAX_RECOVERY_ATTEMPTS", "MAX_NETWORK_RETRIES", "INITIAL_RETRY_DELAY", "handleError", "error", "type", "_this", "_asyncToGenerator", "errorLog", "id", "crypto", "randomUUID", "timestamp", "Date", "message", "stack", "recoveryAttempted", "recovered", "handleSecurityError", "handleStorageError", "handleWasmError", "handleNetworkError", "recoveryError", "logError", "_this2", "wipeStorage", "_this3", "attempts", "rotateKeys", "Promise", "resolve", "setTimeout", "_this4", "reloadWasmModule", "_this5", "delay", "Math", "pow", "isConnected", "checkNetworkConnectivity", "Error", "response", "fetch", "method", "ok", "_this6", "storeSecurely", "console", "getErrorLogs", "_this7", "logs", "retrieveS<PERSON>urely", "Object", "values", "clearErrorLogs", "_this8", "deleteSecurely", "__decorate", "providedIn"], "sources": ["C:\\Users\\<USER>\\Projects\\QSC1\\frontend\\src\\app\\services\\error-handling.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\n\r\nexport type ErrorType = 'SECURITY' | 'STORAGE' | 'WASM' | 'NETWORK';\r\n\r\nexport interface ErrorLog {\r\n  id: string;\r\n  timestamp: Date;\r\n  type: ErrorType;\r\n  message: string;\r\n  stack?: string;\r\n  recoveryAttempted: boolean;\r\n  recovered: boolean;\r\n}\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class ErrorHandlingService {\r\n  private readonly MAX_RECOVERY_ATTEMPTS = 3;\r\n  private readonly MAX_NETWORK_RETRIES = 5;\r\n  private readonly INITIAL_RETRY_DELAY = 1000; // 1 second\r\n\r\n  constructor(private secureStorage: SecureStorageService) {}\r\n\r\n  public async handleError(error: Error, type: ErrorType): Promise<void> {\r\n    const errorLog: ErrorLog = {\r\n      id: crypto.randomUUID(),\r\n      timestamp: new Date(),\r\n      type,\r\n      message: error.message,\r\n      stack: error.stack,\r\n      recoveryAttempted: false,\r\n      recovered: false\r\n    };\r\n\r\n    try {\r\n      switch (type) {\r\n        case 'SECURITY':\r\n          await this.handleSecurityError(errorLog);\r\n          break;\r\n        case 'STORAGE':\r\n          await this.handleStorageError(errorLog);\r\n          break;\r\n        case 'WASM':\r\n          await this.handleWasmError(errorLog);\r\n          break;\r\n        case 'NETWORK':\r\n          await this.handleNetworkError(errorLog);\r\n          break;\r\n      }\r\n    } catch (recoveryError) {\r\n      errorLog.recoveryAttempted = true;\r\n      errorLog.recovered = false;\r\n      await this.logError(errorLog);\r\n      throw recoveryError;\r\n    }\r\n  }\r\n\r\n  private async handleSecurityError(errorLog: ErrorLog): Promise<void> {\r\n    // Force re-authentication and clear sensitive data\r\n    try {\r\n      await this.secureStorage.wipeStorage();\r\n      errorLog.recoveryAttempted = true;\r\n      errorLog.recovered = true;\r\n    } catch (error) {\r\n      errorLog.recoveryAttempted = true;\r\n      errorLog.recovered = false;\r\n      throw error;\r\n    } finally {\r\n      await this.logError(errorLog);\r\n    }\r\n  }\r\n\r\n  private async handleStorageError(errorLog: ErrorLog): Promise<void> {\r\n    let attempts = 0;\r\n    while (attempts < this.MAX_RECOVERY_ATTEMPTS) {\r\n      try {\r\n        await this.secureStorage.rotateKeys();\r\n        errorLog.recoveryAttempted = true;\r\n        errorLog.recovered = true;\r\n        break;\r\n      } catch (error) {\r\n        attempts++;\r\n        if (attempts === this.MAX_RECOVERY_ATTEMPTS) {\r\n          errorLog.recoveryAttempted = true;\r\n          errorLog.recovered = false;\r\n          await this.secureStorage.wipeStorage();\r\n          throw error;\r\n        }\r\n        await new Promise(resolve => setTimeout(resolve, this.INITIAL_RETRY_DELAY * attempts));\r\n      }\r\n    }\r\n    await this.logError(errorLog);\r\n  }\r\n\r\n  private async handleWasmError(errorLog: ErrorLog): Promise<void> {\r\n    let attempts = 0;\r\n    while (attempts < this.MAX_RECOVERY_ATTEMPTS) {\r\n      try {\r\n        // Attempt to reload WASM module\r\n        await this.reloadWasmModule();\r\n        errorLog.recoveryAttempted = true;\r\n        errorLog.recovered = true;\r\n        break;\r\n      } catch (error) {\r\n        attempts++;\r\n        if (attempts === this.MAX_RECOVERY_ATTEMPTS) {\r\n          errorLog.recoveryAttempted = true;\r\n          errorLog.recovered = false;\r\n          throw error;\r\n        }\r\n        await new Promise(resolve => setTimeout(resolve, this.INITIAL_RETRY_DELAY * attempts));\r\n      }\r\n    }\r\n    await this.logError(errorLog);\r\n  }\r\n\r\n  private async handleNetworkError(errorLog: ErrorLog): Promise<void> {\r\n    let attempts = 0;\r\n    while (attempts < this.MAX_NETWORK_RETRIES) {\r\n      try {\r\n        // Implement exponential backoff\r\n        const delay = this.INITIAL_RETRY_DELAY * Math.pow(2, attempts);\r\n        await new Promise(resolve => setTimeout(resolve, delay));\r\n\r\n        // Check network connectivity\r\n        const isConnected = await this.checkNetworkConnectivity();\r\n        if (isConnected) {\r\n          errorLog.recoveryAttempted = true;\r\n          errorLog.recovered = true;\r\n          break;\r\n        }\r\n\r\n        attempts++;\r\n        if (attempts === this.MAX_NETWORK_RETRIES) {\r\n          errorLog.recoveryAttempted = true;\r\n          errorLog.recovered = false;\r\n          throw new Error('Network connection failed after maximum retries');\r\n        }\r\n      } catch (error) {\r\n        attempts++;\r\n        if (attempts === this.MAX_NETWORK_RETRIES) {\r\n          errorLog.recoveryAttempted = true;\r\n          errorLog.recovered = false;\r\n          throw error;\r\n        }\r\n      }\r\n    }\r\n    await this.logError(errorLog);\r\n  }\r\n\r\n  private async reloadWasmModule(): Promise<void> {\r\n    // Implementation will be provided by WASM service\r\n    throw new Error('Not implemented');\r\n  }\r\n\r\n  private async checkNetworkConnectivity(): Promise<boolean> {\r\n    try {\r\n      const response = await fetch('/api/health', { method: 'HEAD' });\r\n      return response.ok;\r\n    } catch {\r\n      return false;\r\n    }\r\n  }\r\n\r\n  private async logError(errorLog: ErrorLog): Promise<void> {\r\n    try {\r\n      await this.secureStorage.storeSecurely(errorLog.id, errorLog, 'error_logs');\r\n    } catch (error) {\r\n      console.error('Failed to log error:', error);\r\n    }\r\n  }\r\n\r\n  public async getErrorLogs(): Promise<ErrorLog[]> {\r\n    try {\r\n      const logs = await this.secureStorage.retrieveSecurely('error_logs', 'all');\r\n      return Object.values(logs) as ErrorLog[];\r\n    } catch (error) {\r\n      console.error('Failed to retrieve error logs:', error);\r\n      return [];\r\n    }\r\n  }\r\n\r\n  public async clearErrorLogs(): Promise<void> {\r\n    try {\r\n      await this.secureStorage.deleteSecurely('error_logs', 'all');\r\n    } catch (error) {\r\n      console.error('Failed to clear error logs:', error);\r\n      throw error;\r\n    }\r\n  }\r\n}\r\n"], "mappings": ";;AAAA,SAASA,UAAU,QAAQ,eAAe;AAiBnC,IAAMC,oBAAoB,GAA1B,MAAMA,oBAAoB;EAK/BC,YAAoBC,aAAmC;IAAnC,KAAAA,aAAa,GAAbA,aAAa;IAJhB,KAAAC,qBAAqB,GAAG,CAAC;IACzB,KAAAC,mBAAmB,GAAG,CAAC;IACvB,KAAAC,mBAAmB,GAAG,IAAI,CAAC,CAAC;EAEa;EAE7CC,WAAWA,CAACC,KAAY,EAAEC,IAAe;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MACpD,MAAMC,QAAQ,GAAa;QACzBC,EAAE,EAAEC,MAAM,CAACC,UAAU,EAAE;QACvBC,SAAS,EAAE,IAAIC,IAAI,EAAE;QACrBR,IAAI;QACJS,OAAO,EAAEV,KAAK,CAACU,OAAO;QACtBC,KAAK,EAAEX,KAAK,CAACW,KAAK;QAClBC,iBAAiB,EAAE,KAAK;QACxBC,SAAS,EAAE;OACZ;MAED,IAAI;QACF,QAAQZ,IAAI;UACV,KAAK,UAAU;YACb,MAAMC,KAAI,CAACY,mBAAmB,CAACV,QAAQ,CAAC;YACxC;UACF,KAAK,SAAS;YACZ,MAAMF,KAAI,CAACa,kBAAkB,CAACX,QAAQ,CAAC;YACvC;UACF,KAAK,MAAM;YACT,MAAMF,KAAI,CAACc,eAAe,CAACZ,QAAQ,CAAC;YACpC;UACF,KAAK,SAAS;YACZ,MAAMF,KAAI,CAACe,kBAAkB,CAACb,QAAQ,CAAC;YACvC;;OAEL,CAAC,OAAOc,aAAa,EAAE;QACtBd,QAAQ,CAACQ,iBAAiB,GAAG,IAAI;QACjCR,QAAQ,CAACS,SAAS,GAAG,KAAK;QAC1B,MAAMX,KAAI,CAACiB,QAAQ,CAACf,QAAQ,CAAC;QAC7B,MAAMc,aAAa;;IACpB;EACH;EAEcJ,mBAAmBA,CAACV,QAAkB;IAAA,IAAAgB,MAAA;IAAA,OAAAjB,iBAAA;MAClD;MACA,IAAI;QACF,MAAMiB,MAAI,CAACzB,aAAa,CAAC0B,WAAW,EAAE;QACtCjB,QAAQ,CAACQ,iBAAiB,GAAG,IAAI;QACjCR,QAAQ,CAACS,SAAS,GAAG,IAAI;OAC1B,CAAC,OAAOb,KAAK,EAAE;QACdI,QAAQ,CAACQ,iBAAiB,GAAG,IAAI;QACjCR,QAAQ,CAACS,SAAS,GAAG,KAAK;QAC1B,MAAMb,KAAK;OACZ,SAAS;QACR,MAAMoB,MAAI,CAACD,QAAQ,CAACf,QAAQ,CAAC;;IAC9B;EACH;EAEcW,kBAAkBA,CAACX,QAAkB;IAAA,IAAAkB,MAAA;IAAA,OAAAnB,iBAAA;MACjD,IAAIoB,QAAQ,GAAG,CAAC;MAChB,OAAOA,QAAQ,GAAGD,MAAI,CAAC1B,qBAAqB,EAAE;QAC5C,IAAI;UACF,MAAM0B,MAAI,CAAC3B,aAAa,CAAC6B,UAAU,EAAE;UACrCpB,QAAQ,CAACQ,iBAAiB,GAAG,IAAI;UACjCR,QAAQ,CAACS,SAAS,GAAG,IAAI;UACzB;SACD,CAAC,OAAOb,KAAK,EAAE;UACduB,QAAQ,EAAE;UACV,IAAIA,QAAQ,KAAKD,MAAI,CAAC1B,qBAAqB,EAAE;YAC3CQ,QAAQ,CAACQ,iBAAiB,GAAG,IAAI;YACjCR,QAAQ,CAACS,SAAS,GAAG,KAAK;YAC1B,MAAMS,MAAI,CAAC3B,aAAa,CAAC0B,WAAW,EAAE;YACtC,MAAMrB,KAAK;;UAEb,MAAM,IAAIyB,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAEJ,MAAI,CAACxB,mBAAmB,GAAGyB,QAAQ,CAAC,CAAC;;;MAG1F,MAAMD,MAAI,CAACH,QAAQ,CAACf,QAAQ,CAAC;IAAC;EAChC;EAEcY,eAAeA,CAACZ,QAAkB;IAAA,IAAAwB,MAAA;IAAA,OAAAzB,iBAAA;MAC9C,IAAIoB,QAAQ,GAAG,CAAC;MAChB,OAAOA,QAAQ,GAAGK,MAAI,CAAChC,qBAAqB,EAAE;QAC5C,IAAI;UACF;UACA,MAAMgC,MAAI,CAACC,gBAAgB,EAAE;UAC7BzB,QAAQ,CAACQ,iBAAiB,GAAG,IAAI;UACjCR,QAAQ,CAACS,SAAS,GAAG,IAAI;UACzB;SACD,CAAC,OAAOb,KAAK,EAAE;UACduB,QAAQ,EAAE;UACV,IAAIA,QAAQ,KAAKK,MAAI,CAAChC,qBAAqB,EAAE;YAC3CQ,QAAQ,CAACQ,iBAAiB,GAAG,IAAI;YACjCR,QAAQ,CAACS,SAAS,GAAG,KAAK;YAC1B,MAAMb,KAAK;;UAEb,MAAM,IAAIyB,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAEE,MAAI,CAAC9B,mBAAmB,GAAGyB,QAAQ,CAAC,CAAC;;;MAG1F,MAAMK,MAAI,CAACT,QAAQ,CAACf,QAAQ,CAAC;IAAC;EAChC;EAEca,kBAAkBA,CAACb,QAAkB;IAAA,IAAA0B,MAAA;IAAA,OAAA3B,iBAAA;MACjD,IAAIoB,QAAQ,GAAG,CAAC;MAChB,OAAOA,QAAQ,GAAGO,MAAI,CAACjC,mBAAmB,EAAE;QAC1C,IAAI;UACF;UACA,MAAMkC,KAAK,GAAGD,MAAI,CAAChC,mBAAmB,GAAGkC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEV,QAAQ,CAAC;UAC9D,MAAM,IAAIE,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAEK,KAAK,CAAC,CAAC;UAExD;UACA,MAAMG,WAAW,SAASJ,MAAI,CAACK,wBAAwB,EAAE;UACzD,IAAID,WAAW,EAAE;YACf9B,QAAQ,CAACQ,iBAAiB,GAAG,IAAI;YACjCR,QAAQ,CAACS,SAAS,GAAG,IAAI;YACzB;;UAGFU,QAAQ,EAAE;UACV,IAAIA,QAAQ,KAAKO,MAAI,CAACjC,mBAAmB,EAAE;YACzCO,QAAQ,CAACQ,iBAAiB,GAAG,IAAI;YACjCR,QAAQ,CAACS,SAAS,GAAG,KAAK;YAC1B,MAAM,IAAIuB,KAAK,CAAC,iDAAiD,CAAC;;SAErE,CAAC,OAAOpC,KAAK,EAAE;UACduB,QAAQ,EAAE;UACV,IAAIA,QAAQ,KAAKO,MAAI,CAACjC,mBAAmB,EAAE;YACzCO,QAAQ,CAACQ,iBAAiB,GAAG,IAAI;YACjCR,QAAQ,CAACS,SAAS,GAAG,KAAK;YAC1B,MAAMb,KAAK;;;;MAIjB,MAAM8B,MAAI,CAACX,QAAQ,CAACf,QAAQ,CAAC;IAAC;EAChC;EAEcyB,gBAAgBA,CAAA;IAAA,OAAA1B,iBAAA;MAC5B;MACA,MAAM,IAAIiC,KAAK,CAAC,iBAAiB,CAAC;IAAC;EACrC;EAEcD,wBAAwBA,CAAA;IAAA,OAAAhC,iBAAA;MACpC,IAAI;QACF,MAAMkC,QAAQ,SAASC,KAAK,CAAC,aAAa,EAAE;UAAEC,MAAM,EAAE;QAAM,CAAE,CAAC;QAC/D,OAAOF,QAAQ,CAACG,EAAE;OACnB,CAAC,MAAM;QACN,OAAO,KAAK;;IACb;EACH;EAEcrB,QAAQA,CAACf,QAAkB;IAAA,IAAAqC,MAAA;IAAA,OAAAtC,iBAAA;MACvC,IAAI;QACF,MAAMsC,MAAI,CAAC9C,aAAa,CAAC+C,aAAa,CAACtC,QAAQ,CAACC,EAAE,EAAED,QAAQ,EAAE,YAAY,CAAC;OAC5E,CAAC,OAAOJ,KAAK,EAAE;QACd2C,OAAO,CAAC3C,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;;IAC7C;EACH;EAEa4C,YAAYA,CAAA;IAAA,IAAAC,MAAA;IAAA,OAAA1C,iBAAA;MACvB,IAAI;QACF,MAAM2C,IAAI,SAASD,MAAI,CAAClD,aAAa,CAACoD,gBAAgB,CAAC,YAAY,EAAE,KAAK,CAAC;QAC3E,OAAOC,MAAM,CAACC,MAAM,CAACH,IAAI,CAAe;OACzC,CAAC,OAAO9C,KAAK,EAAE;QACd2C,OAAO,CAAC3C,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;QACtD,OAAO,EAAE;;IACV;EACH;EAEakD,cAAcA,CAAA;IAAA,IAAAC,MAAA;IAAA,OAAAhD,iBAAA;MACzB,IAAI;QACF,MAAMgD,MAAI,CAACxD,aAAa,CAACyD,cAAc,CAAC,YAAY,EAAE,KAAK,CAAC;OAC7D,CAAC,OAAOpD,KAAK,EAAE;QACd2C,OAAO,CAAC3C,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;QACnD,MAAMA,KAAK;;IACZ;EACH;CACD;AA9KYP,oBAAoB,GAAA4D,UAAA,EAHhC7D,UAAU,CAAC;EACV8D,UAAU,EAAE;CACb,CAAC,C,EACW7D,oBAAoB,CA8KhC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}