{"ast": null, "code": "import { BehaviorSubject } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nexport class SecureTokenService {\n  constructor() {\n    this.TOKEN_KEY = 'qsc_secure_token';\n    this.REFRESH_TOKEN_KEY = 'qsc_refresh_token';\n    this.tokenSubject = new BehaviorSubject(null);\n    this.refreshTokenSubject = new BehaviorSubject(null);\n    this.initializeTokens();\n  }\n  /**\n   * Initialize tokens from secure storage on service startup\n   */\n  initializeTokens() {\n    try {\n      // Try to get tokens from sessionStorage first (more secure than localStorage)\n      const token = sessionStorage.getItem(this.TOKEN_KEY);\n      const refreshToken = sessionStorage.getItem(this.REFRESH_TOKEN_KEY);\n      if (token && this.isTokenValid(token)) {\n        this.tokenSubject.next(token);\n      }\n      if (refreshToken) {\n        this.refreshTokenSubject.next(refreshToken);\n      }\n    } catch (error) {\n      console.error('Failed to initialize tokens:', error);\n      this.clearTokens();\n    }\n  }\n  /**\n   * Store tokens securely\n   */\n  setTokens(tokenData) {\n    try {\n      // Store in sessionStorage instead of localStorage for better security\n      sessionStorage.setItem(this.TOKEN_KEY, tokenData.token);\n      if (tokenData.refreshToken) {\n        sessionStorage.setItem(this.REFRESH_TOKEN_KEY, tokenData.refreshToken);\n        this.refreshTokenSubject.next(tokenData.refreshToken);\n      }\n      this.tokenSubject.next(tokenData.token);\n      // Set up automatic token cleanup\n      this.scheduleTokenCleanup(tokenData.expiresAt);\n    } catch (error) {\n      console.error('Failed to store tokens:', error);\n      throw new Error('Token storage failed');\n    }\n  }\n  /**\n   * Get current access token\n   */\n  getToken() {\n    return this.tokenSubject.value;\n  }\n  /**\n   * Get current refresh token\n   */\n  getRefreshToken() {\n    return this.refreshTokenSubject.value;\n  }\n  /**\n   * Observable for token changes\n   */\n  get token$() {\n    return this.tokenSubject.asObservable();\n  }\n  /**\n   * Observable for refresh token changes\n   */\n  get refreshToken$() {\n    return this.refreshTokenSubject.asObservable();\n  }\n  /**\n   * Check if current token is valid\n   */\n  isTokenValid(token) {\n    const currentToken = token || this.getToken();\n    if (!currentToken) return false;\n    try {\n      const payload = this.parseJwtPayload(currentToken);\n      const now = Math.floor(Date.now() / 1000);\n      return payload.exp > now;\n    } catch {\n      return false;\n    }\n  }\n  /**\n   * Clear all tokens\n   */\n  clearTokens() {\n    try {\n      sessionStorage.removeItem(this.TOKEN_KEY);\n      sessionStorage.removeItem(this.REFRESH_TOKEN_KEY);\n      // Also clear from localStorage if they exist there (migration)\n      localStorage.removeItem('qsc_token');\n      localStorage.removeItem('qsc_user');\n      this.tokenSubject.next(null);\n      this.refreshTokenSubject.next(null);\n    } catch (error) {\n      console.error('Failed to clear tokens:', error);\n    }\n  }\n  /**\n   * Parse JWT payload without verification (for expiration check)\n   */\n  parseJwtPayload(token) {\n    try {\n      const base64Url = token.split('.')[1];\n      const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');\n      const jsonPayload = decodeURIComponent(atob(base64).split('').map(c => '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2)).join(''));\n      return JSON.parse(jsonPayload);\n    } catch (error) {\n      throw new Error('Invalid JWT token');\n    }\n  }\n  /**\n   * Schedule automatic token cleanup when it expires\n   */\n  scheduleTokenCleanup(expiresAt) {\n    const now = Date.now();\n    const expirationTime = expiresAt * 1000; // Convert to milliseconds\n    const timeUntilExpiration = expirationTime - now;\n    if (timeUntilExpiration > 0) {\n      setTimeout(() => {\n        if (!this.isTokenValid()) {\n          this.clearTokens();\n        }\n      }, timeUntilExpiration);\n    }\n  }\n  /**\n   * Get token expiration time\n   */\n  getTokenExpiration() {\n    const token = this.getToken();\n    if (!token) return null;\n    try {\n      const payload = this.parseJwtPayload(token);\n      return payload.exp * 1000; // Convert to milliseconds\n    } catch {\n      return null;\n    }\n  }\n  /**\n   * Check if token will expire soon (within 5 minutes)\n   */\n  isTokenExpiringSoon() {\n    const expiration = this.getTokenExpiration();\n    if (!expiration) return true;\n    const fiveMinutesFromNow = Date.now() + 5 * 60 * 1000;\n    return expiration < fiveMinutesFromNow;\n  }\n  static {\n    this.ɵfac = function SecureTokenService_Factory(t) {\n      return new (t || SecureTokenService)();\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: SecureTokenService,\n      factory: SecureTokenService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["BehaviorSubject", "SecureTokenService", "constructor", "TOKEN_KEY", "REFRESH_TOKEN_KEY", "tokenSubject", "refreshTokenSubject", "initializeTokens", "token", "sessionStorage", "getItem", "refreshToken", "isTokenValid", "next", "error", "console", "clearTokens", "setTokens", "tokenData", "setItem", "scheduleTokenCleanup", "expiresAt", "Error", "getToken", "value", "getRefreshToken", "token$", "asObservable", "refreshToken$", "currentToken", "payload", "parseJwtPayload", "now", "Math", "floor", "Date", "exp", "removeItem", "localStorage", "base64Url", "split", "base64", "replace", "jsonPayload", "decodeURIComponent", "atob", "map", "c", "charCodeAt", "toString", "slice", "join", "JSON", "parse", "expirationTime", "timeUntilExpiration", "setTimeout", "getTokenExpiration", "isTokenExpiringSoon", "expiration", "fiveMinutesFromNow", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Projects\\QSC1\\frontend\\src\\app\\services\\secure-token.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { BehaviorSubject, Observable } from 'rxjs';\nimport { TokenData } from '../types/auth.types';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class SecureTokenService {\n  private readonly TOKEN_KEY = 'qsc_secure_token';\n  private readonly REFRESH_TOKEN_KEY = 'qsc_refresh_token';\n  private tokenSubject = new BehaviorSubject<string | null>(null);\n  private refreshTokenSubject = new BehaviorSubject<string | null>(null);\n\n  constructor() {\n    this.initializeTokens();\n  }\n\n  /**\n   * Initialize tokens from secure storage on service startup\n   */\n  private initializeTokens(): void {\n    try {\n      // Try to get tokens from sessionStorage first (more secure than localStorage)\n      const token = sessionStorage.getItem(this.TOKEN_KEY);\n      const refreshToken = sessionStorage.getItem(this.REFRESH_TOKEN_KEY);\n\n      if (token && this.isTokenValid(token)) {\n        this.tokenSubject.next(token);\n      }\n\n      if (refreshToken) {\n        this.refreshTokenSubject.next(refreshToken);\n      }\n    } catch (error) {\n      console.error('Failed to initialize tokens:', error);\n      this.clearTokens();\n    }\n  }\n\n  /**\n   * Store tokens securely\n   */\n  setTokens(tokenData: TokenData): void {\n    try {\n      // Store in sessionStorage instead of localStorage for better security\n      sessionStorage.setItem(this.TOKEN_KEY, tokenData.token);\n\n      if (tokenData.refreshToken) {\n        sessionStorage.setItem(this.REFRESH_TOKEN_KEY, tokenData.refreshToken);\n        this.refreshTokenSubject.next(tokenData.refreshToken);\n      }\n\n      this.tokenSubject.next(tokenData.token);\n\n      // Set up automatic token cleanup\n      this.scheduleTokenCleanup(tokenData.expiresAt);\n    } catch (error) {\n      console.error('Failed to store tokens:', error);\n      throw new Error('Token storage failed');\n    }\n  }\n\n  /**\n   * Get current access token\n   */\n  getToken(): string | null {\n    return this.tokenSubject.value;\n  }\n\n  /**\n   * Get current refresh token\n   */\n  getRefreshToken(): string | null {\n    return this.refreshTokenSubject.value;\n  }\n\n  /**\n   * Observable for token changes\n   */\n  get token$(): Observable<string | null> {\n    return this.tokenSubject.asObservable();\n  }\n\n  /**\n   * Observable for refresh token changes\n   */\n  get refreshToken$(): Observable<string | null> {\n    return this.refreshTokenSubject.asObservable();\n  }\n\n  /**\n   * Check if current token is valid\n   */\n  isTokenValid(token?: string): boolean {\n    const currentToken = token || this.getToken();\n    if (!currentToken) return false;\n\n    try {\n      const payload = this.parseJwtPayload(currentToken);\n      const now = Math.floor(Date.now() / 1000);\n      return payload.exp > now;\n    } catch {\n      return false;\n    }\n  }\n\n  /**\n   * Clear all tokens\n   */\n  clearTokens(): void {\n    try {\n      sessionStorage.removeItem(this.TOKEN_KEY);\n      sessionStorage.removeItem(this.REFRESH_TOKEN_KEY);\n\n      // Also clear from localStorage if they exist there (migration)\n      localStorage.removeItem('qsc_token');\n      localStorage.removeItem('qsc_user');\n\n      this.tokenSubject.next(null);\n      this.refreshTokenSubject.next(null);\n    } catch (error) {\n      console.error('Failed to clear tokens:', error);\n    }\n  }\n\n  /**\n   * Parse JWT payload without verification (for expiration check)\n   */\n  private parseJwtPayload(token: string): any {\n    try {\n      const base64Url = token.split('.')[1];\n      const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');\n      const jsonPayload = decodeURIComponent(\n        atob(base64)\n          .split('')\n          .map(c => '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2))\n          .join('')\n      );\n      return JSON.parse(jsonPayload);\n    } catch (error) {\n      throw new Error('Invalid JWT token');\n    }\n  }\n\n  /**\n   * Schedule automatic token cleanup when it expires\n   */\n  private scheduleTokenCleanup(expiresAt: number): void {\n    const now = Date.now();\n    const expirationTime = expiresAt * 1000; // Convert to milliseconds\n    const timeUntilExpiration = expirationTime - now;\n\n    if (timeUntilExpiration > 0) {\n      setTimeout(() => {\n        if (!this.isTokenValid()) {\n          this.clearTokens();\n        }\n      }, timeUntilExpiration);\n    }\n  }\n\n  /**\n   * Get token expiration time\n   */\n  getTokenExpiration(): number | null {\n    const token = this.getToken();\n    if (!token) return null;\n\n    try {\n      const payload = this.parseJwtPayload(token);\n      return payload.exp * 1000; // Convert to milliseconds\n    } catch {\n      return null;\n    }\n  }\n\n  /**\n   * Check if token will expire soon (within 5 minutes)\n   */\n  isTokenExpiringSoon(): boolean {\n    const expiration = this.getTokenExpiration();\n    if (!expiration) return true;\n\n    const fiveMinutesFromNow = Date.now() + (5 * 60 * 1000);\n    return expiration < fiveMinutesFromNow;\n  }\n}\n"], "mappings": "AACA,SAASA,eAAe,QAAoB,MAAM;;AAMlD,OAAM,MAAOC,kBAAkB;EAM7BC,YAAA;IALiB,KAAAC,SAAS,GAAG,kBAAkB;IAC9B,KAAAC,iBAAiB,GAAG,mBAAmB;IAChD,KAAAC,YAAY,GAAG,IAAIL,eAAe,CAAgB,IAAI,CAAC;IACvD,KAAAM,mBAAmB,GAAG,IAAIN,eAAe,CAAgB,IAAI,CAAC;IAGpE,IAAI,CAACO,gBAAgB,EAAE;EACzB;EAEA;;;EAGQA,gBAAgBA,CAAA;IACtB,IAAI;MACF;MACA,MAAMC,KAAK,GAAGC,cAAc,CAACC,OAAO,CAAC,IAAI,CAACP,SAAS,CAAC;MACpD,MAAMQ,YAAY,GAAGF,cAAc,CAACC,OAAO,CAAC,IAAI,CAACN,iBAAiB,CAAC;MAEnE,IAAII,KAAK,IAAI,IAAI,CAACI,YAAY,CAACJ,KAAK,CAAC,EAAE;QACrC,IAAI,CAACH,YAAY,CAACQ,IAAI,CAACL,KAAK,CAAC;;MAG/B,IAAIG,YAAY,EAAE;QAChB,IAAI,CAACL,mBAAmB,CAACO,IAAI,CAACF,YAAY,CAAC;;KAE9C,CAAC,OAAOG,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpD,IAAI,CAACE,WAAW,EAAE;;EAEtB;EAEA;;;EAGAC,SAASA,CAACC,SAAoB;IAC5B,IAAI;MACF;MACAT,cAAc,CAACU,OAAO,CAAC,IAAI,CAAChB,SAAS,EAAEe,SAAS,CAACV,KAAK,CAAC;MAEvD,IAAIU,SAAS,CAACP,YAAY,EAAE;QAC1BF,cAAc,CAACU,OAAO,CAAC,IAAI,CAACf,iBAAiB,EAAEc,SAAS,CAACP,YAAY,CAAC;QACtE,IAAI,CAACL,mBAAmB,CAACO,IAAI,CAACK,SAAS,CAACP,YAAY,CAAC;;MAGvD,IAAI,CAACN,YAAY,CAACQ,IAAI,CAACK,SAAS,CAACV,KAAK,CAAC;MAEvC;MACA,IAAI,CAACY,oBAAoB,CAACF,SAAS,CAACG,SAAS,CAAC;KAC/C,CAAC,OAAOP,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/C,MAAM,IAAIQ,KAAK,CAAC,sBAAsB,CAAC;;EAE3C;EAEA;;;EAGAC,QAAQA,CAAA;IACN,OAAO,IAAI,CAAClB,YAAY,CAACmB,KAAK;EAChC;EAEA;;;EAGAC,eAAeA,CAAA;IACb,OAAO,IAAI,CAACnB,mBAAmB,CAACkB,KAAK;EACvC;EAEA;;;EAGA,IAAIE,MAAMA,CAAA;IACR,OAAO,IAAI,CAACrB,YAAY,CAACsB,YAAY,EAAE;EACzC;EAEA;;;EAGA,IAAIC,aAAaA,CAAA;IACf,OAAO,IAAI,CAACtB,mBAAmB,CAACqB,YAAY,EAAE;EAChD;EAEA;;;EAGAf,YAAYA,CAACJ,KAAc;IACzB,MAAMqB,YAAY,GAAGrB,KAAK,IAAI,IAAI,CAACe,QAAQ,EAAE;IAC7C,IAAI,CAACM,YAAY,EAAE,OAAO,KAAK;IAE/B,IAAI;MACF,MAAMC,OAAO,GAAG,IAAI,CAACC,eAAe,CAACF,YAAY,CAAC;MAClD,MAAMG,GAAG,GAAGC,IAAI,CAACC,KAAK,CAACC,IAAI,CAACH,GAAG,EAAE,GAAG,IAAI,CAAC;MACzC,OAAOF,OAAO,CAACM,GAAG,GAAGJ,GAAG;KACzB,CAAC,MAAM;MACN,OAAO,KAAK;;EAEhB;EAEA;;;EAGAhB,WAAWA,CAAA;IACT,IAAI;MACFP,cAAc,CAAC4B,UAAU,CAAC,IAAI,CAAClC,SAAS,CAAC;MACzCM,cAAc,CAAC4B,UAAU,CAAC,IAAI,CAACjC,iBAAiB,CAAC;MAEjD;MACAkC,YAAY,CAACD,UAAU,CAAC,WAAW,CAAC;MACpCC,YAAY,CAACD,UAAU,CAAC,UAAU,CAAC;MAEnC,IAAI,CAAChC,YAAY,CAACQ,IAAI,CAAC,IAAI,CAAC;MAC5B,IAAI,CAACP,mBAAmB,CAACO,IAAI,CAAC,IAAI,CAAC;KACpC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;;EAEnD;EAEA;;;EAGQiB,eAAeA,CAACvB,KAAa;IACnC,IAAI;MACF,MAAM+B,SAAS,GAAG/B,KAAK,CAACgC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;MACrC,MAAMC,MAAM,GAAGF,SAAS,CAACG,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAACA,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC;MAC9D,MAAMC,WAAW,GAAGC,kBAAkB,CACpCC,IAAI,CAACJ,MAAM,CAAC,CACTD,KAAK,CAAC,EAAE,CAAC,CACTM,GAAG,CAACC,CAAC,IAAI,GAAG,GAAG,CAAC,IAAI,GAAGA,CAAC,CAACC,UAAU,CAAC,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,EAAEC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAC/DC,IAAI,CAAC,EAAE,CAAC,CACZ;MACD,OAAOC,IAAI,CAACC,KAAK,CAACV,WAAW,CAAC;KAC/B,CAAC,OAAO7B,KAAK,EAAE;MACd,MAAM,IAAIQ,KAAK,CAAC,mBAAmB,CAAC;;EAExC;EAEA;;;EAGQF,oBAAoBA,CAACC,SAAiB;IAC5C,MAAMW,GAAG,GAAGG,IAAI,CAACH,GAAG,EAAE;IACtB,MAAMsB,cAAc,GAAGjC,SAAS,GAAG,IAAI,CAAC,CAAC;IACzC,MAAMkC,mBAAmB,GAAGD,cAAc,GAAGtB,GAAG;IAEhD,IAAIuB,mBAAmB,GAAG,CAAC,EAAE;MAC3BC,UAAU,CAAC,MAAK;QACd,IAAI,CAAC,IAAI,CAAC5C,YAAY,EAAE,EAAE;UACxB,IAAI,CAACI,WAAW,EAAE;;MAEtB,CAAC,EAAEuC,mBAAmB,CAAC;;EAE3B;EAEA;;;EAGAE,kBAAkBA,CAAA;IAChB,MAAMjD,KAAK,GAAG,IAAI,CAACe,QAAQ,EAAE;IAC7B,IAAI,CAACf,KAAK,EAAE,OAAO,IAAI;IAEvB,IAAI;MACF,MAAMsB,OAAO,GAAG,IAAI,CAACC,eAAe,CAACvB,KAAK,CAAC;MAC3C,OAAOsB,OAAO,CAACM,GAAG,GAAG,IAAI,CAAC,CAAC;KAC5B,CAAC,MAAM;MACN,OAAO,IAAI;;EAEf;EAEA;;;EAGAsB,mBAAmBA,CAAA;IACjB,MAAMC,UAAU,GAAG,IAAI,CAACF,kBAAkB,EAAE;IAC5C,IAAI,CAACE,UAAU,EAAE,OAAO,IAAI;IAE5B,MAAMC,kBAAkB,GAAGzB,IAAI,CAACH,GAAG,EAAE,GAAI,CAAC,GAAG,EAAE,GAAG,IAAK;IACvD,OAAO2B,UAAU,GAAGC,kBAAkB;EACxC;;;uBAlLW3D,kBAAkB;IAAA;EAAA;;;aAAlBA,kBAAkB;MAAA4D,OAAA,EAAlB5D,kBAAkB,CAAA6D,IAAA;MAAAC,UAAA,EAFjB;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}