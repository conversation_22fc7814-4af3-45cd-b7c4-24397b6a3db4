import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable, tap, switchMap, combineLatest, catchError, throwError } from 'rxjs';
import { ApiService } from './api.service';
import { SecureTokenService } from './secure-token.service';
import { LoginRequest, LoginResponse, RegisterRequest, User, AuthState, TokenData, AuthError } from '../types/auth.types';

@Injectable({
  providedIn: 'root'
})
export class AuthService {
  private userSubject = new BehaviorSubject<User | null>(null);
  private loadingSubject = new BehaviorSubject<boolean>(false);
  private errorSubject = new BehaviorSubject<AuthError | null>(null);
  public authState$: Observable<AuthState>;

  constructor(
    private apiService: ApiService,
    private secureTokenService: SecureTokenService
  ) {
    // Combine token, user, loading, and error observables to create auth state
    this.authState$ = combineLatest([
      this.secureTokenService.token$,
      this.userSubject.asObservable(),
      this.loadingSubject.asObservable(),
      this.errorSubject.asObservable()
    ]).pipe(
      tap(([token, user, isLoading, error]) => {
        // Auto-refresh token if it's expiring soon
        if (token && this.secureTokenService.isTokenExpiringSoon()) {
          this.refreshTokenIfNeeded();
        }
      }),
      switchMap(([token, user, isLoading, error]) => {
        const isAuthenticated = !!token && this.secureTokenService.isTokenValid();
        return [{ isAuthenticated, user, isLoading, error }];
      })
    );

    this.initializeAuthState();
  }

  private initializeAuthState(): void {
    // Check for existing tokens and migrate from localStorage if needed
    const legacyToken = localStorage.getItem('qsc_token');
    const legacyUserStr = localStorage.getItem('qsc_user');

    if (legacyToken && legacyUserStr) {
      try {
        const user = JSON.parse(legacyUserStr);
        // Migrate to secure storage
        this.secureTokenService.setTokens({
          token: legacyToken,
          expiresAt: this.getTokenExpiration(legacyToken) || Date.now() + 3600000, // 1 hour default
          tokenType: 'Bearer'
        });
        this.userSubject.next(user);

        // Clean up legacy storage
        localStorage.removeItem('qsc_token');
        localStorage.removeItem('qsc_user');
      } catch (error) {
        console.error('Error migrating legacy auth data:', error);
        this.logout();
      }
    } else {
      // Check if we have a valid token in secure storage
      const currentToken = this.secureTokenService.getToken();
      if (currentToken && this.secureTokenService.isTokenValid()) {
        // Fetch current user data
        this.getCurrentUserProfile().subscribe({
          next: (user) => this.userSubject.next(user),
          error: () => this.logout()
        });
      }
    }
  }

  login(username: string, secretWord: string, deviceId?: string): Observable<LoginResponse>;
  login(credentials: LoginRequest): Observable<LoginResponse>;
  login(usernameOrCredentials: string | LoginRequest, secretWord?: string, deviceId?: string): Observable<LoginResponse> {
    let credentials: LoginRequest;

    if (typeof usernameOrCredentials === 'string') {
      credentials = { username: usernameOrCredentials, secretWord: secretWord!, deviceId };
    } else {
      credentials = usernameOrCredentials;
    }

    return this.apiService.post<LoginResponse>('/auth/login', credentials).pipe(
      tap(response => {
        // Store tokens securely
        const tokenData: TokenData = {
          token: response.accessToken,
          refreshToken: response.refreshToken,
          expiresAt: Date.now() + (response.expiresIn * 1000),
          tokenType: response.tokenType
        };

        this.secureTokenService.setTokens(tokenData);

        // Update user state
        this.userSubject.next(response.user);
      }),
      catchError(error => {
        console.error('Login failed:', error);
        return throwError(() => error);
      })
    );
  }

  register(registerData: RegisterRequest): Observable<LoginResponse> {
    return this.apiService.post<LoginResponse>('/auth/register', registerData).pipe(
      tap(response => {
        // Store tokens securely after successful registration
        const tokenData: TokenData = {
          token: response.accessToken,
          refreshToken: response.refreshToken,
          expiresAt: Date.now() + (response.expiresIn * 1000),
          tokenType: response.tokenType
        };

        this.secureTokenService.setTokens(tokenData);

        // Update user state
        this.userSubject.next(response.user);
      }),
      catchError(error => {
        console.error('Registration failed:', error);
        return throwError(() => error);
      })
    );
  }

  logout(): void {
    const token = this.secureTokenService.getToken();
    if (token) {
      // Call logout endpoint to blacklist token
      this.apiService.post('/auth/logout', { token }).subscribe({
        error: (error) => console.error('Logout failed:', error)
      });
    }
    this.secureTokenService.clearTokens();
    this.userSubject.next(null);
  }

  getCurrentUser(): User | null {
    return this.userSubject.value;
  }

  getCurrentUserProfile(): Observable<User> {
    return this.apiService.get<User>('/users/profile');
  }

  isAuthenticated(): boolean {
    const token = this.secureTokenService.getToken();
    return !!token && this.secureTokenService.isTokenValid();
  }

  getToken(): string | null {
    return this.secureTokenService.getToken();
  }

  getUser(): User | null {
    return this.userSubject.value;
  }

  /**
   * Refresh token if needed
   */
  private refreshTokenIfNeeded(): void {
    const refreshToken = this.secureTokenService.getRefreshToken();
    if (refreshToken) {
      this.apiService.post<LoginResponse>('/auth/refresh', { refresh_token: refreshToken })
        .subscribe({
          next: (response) => {
            this.secureTokenService.setTokens({
              token: response.accessToken,
              refreshToken: response.refreshToken,
              expiresAt: this.getTokenExpiration(response.accessToken) || Date.now() + 3600000,
              tokenType: response.tokenType
            });
          },
          error: () => {
            // Refresh failed, logout user
            this.logout();
          }
        });
    }
  }

  /**
   * Extract expiration time from JWT token
   */
  private getTokenExpiration(token: string): number | null {
    try {
      const payload = JSON.parse(atob(token.split('.')[1]));
      return payload.exp;
    } catch {
      return null;
    }
  }

  /**
   * Refresh the access token using a refresh token
   */
  refreshToken(refreshToken: string): Observable<LoginResponse> {
    return this.apiService.post<LoginResponse>('/auth/refresh', { refresh_token: refreshToken }).pipe(
      tap(response => {
        const tokenData: TokenData = {
          token: response.accessToken,
          refreshToken: response.refreshToken,
          expiresAt: Date.now() + (response.expiresIn * 1000),
          tokenType: response.tokenType
        };
        this.secureTokenService.setTokens(tokenData);
      }),
      catchError(error => {
        console.error('Token refresh failed:', error);
        this.logout();
        return throwError(() => error);
      })
    );
  }
}
