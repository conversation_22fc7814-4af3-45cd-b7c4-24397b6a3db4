{"ast": null, "code": "import { reduce } from './reduce';\nimport { operate } from '../util/lift';\nconst arrReducer = (arr, value) => (arr.push(value), arr);\nexport function toArray() {\n  return operate((source, subscriber) => {\n    reduce(arrReducer, [])(source).subscribe(subscriber);\n  });\n}", "map": {"version": 3, "names": ["reduce", "operate", "arrReducer", "arr", "value", "push", "toArray", "source", "subscriber", "subscribe"], "sources": ["C:/Users/<USER>/Projects/QSC1/frontend/node_modules/rxjs/dist/esm/internal/operators/toArray.js"], "sourcesContent": ["import { reduce } from './reduce';\nimport { operate } from '../util/lift';\nconst arrReducer = (arr, value) => (arr.push(value), arr);\nexport function toArray() {\n    return operate((source, subscriber) => {\n        reduce(arrReducer, [])(source).subscribe(subscriber);\n    });\n}\n"], "mappings": "AAAA,SAASA,MAAM,QAAQ,UAAU;AACjC,SAASC,OAAO,QAAQ,cAAc;AACtC,MAAMC,UAAU,GAAGA,CAACC,GAAG,EAAEC,KAAK,MAAMD,GAAG,CAACE,IAAI,CAACD,KAAK,CAAC,EAAED,GAAG,CAAC;AACzD,OAAO,SAASG,OAAOA,CAAA,EAAG;EACtB,OAAOL,OAAO,CAAC,CAACM,MAAM,EAAEC,UAAU,KAAK;IACnCR,MAAM,CAACE,UAAU,EAAE,EAAE,CAAC,CAACK,MAAM,CAAC,CAACE,SAAS,CAACD,UAAU,CAAC;EACxD,CAAC,CAAC;AACN", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}