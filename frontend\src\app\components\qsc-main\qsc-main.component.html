<!-- Main Container -->
<div class="qsc-container">
  <!-- Central Circle -->
  <div class="circle-container">
    <div
      class="qsc-circle"
      [class]="getCircleClass()"
      [title]="getCircleTitle()"
      (click)="onCircleClick()"
      (contextmenu)="onCircleRightClick($event)"
      (touchstart)="onCircleTouchStart($event)"
      (touchend)="onCircleTouchEnd()"
      (touchmove)="onCircleTouchMove()"
    >
      <div class="circle-inner">
        <div class="wind-effect" *ngIf="circleState !== 'guest'"></div>
        <div class="unread-indicator" *ngIf="circleState === 'unread'">
          {{ unreadCount }}
        </div>
      </div>
    </div>
  </div>

  <!-- User Info (subtle, top-right) -->
  <div class="user-info" *ngIf="currentUser">
    <span>{{ currentUser.username }}</span>
    <button class="logout-btn" (click)="logout()" title="Logout">×</button>
  </div>

  <!-- Context Menu -->
  <div
    class="context-menu"
    *ngIf="showContextMenu"
    [style.left.px]="contextMenuPosition.x"
    [style.top.px]="contextMenuPosition.y"
    (click)="$event.stopPropagation()"
  >
    <div class="context-menu-item" (click)="openAccountSettings()">
      <span class="menu-icon">👤</span>
      <span class="menu-text">Account Settings</span>
    </div>
    <div class="context-menu-divider"></div>
    <div class="context-menu-item logout-item" (click)="logout()">
      <span class="menu-icon">🚪</span>
      <span class="menu-text">Logout</span>
    </div>
  </div>
</div>

<!-- Login Modal -->
<div class="modal-overlay" *ngIf="showLoginModal" (click)="closeLoginModal()">
  <div class="modal login-modal" (click)="$event.stopPropagation()">
    <button class="close-btn" (click)="closeLoginModal()">×</button>

    <div class="modal-content">
      <div class="form-group">
        <input
          type="text"
          id="username"
          [(ngModel)]="loginCredentials.username"
          (input)="onLoginInputChange()"
          placeholder="Username"
          [disabled]="isLoading"
          autocomplete="username"
        />
      </div>

      <div class="form-group">
        <input
          type="password"
          id="secretWord"
          [(ngModel)]="loginCredentials.secretWord"
          (input)="onLoginInputChange()"
          placeholder="Secret Word (4 chars: A-Z, a-z, 0-9, symbol)"
          [disabled]="isLoading"
          autocomplete="current-password"
        />
      </div>

      <div class="error-message" *ngIf="loginError">
        {{ loginError }}
      </div>

      <div class="loading-indicator" *ngIf="isLoading">
        <div class="spinner"></div>
        <span>Authenticating...</span>
      </div>

      <div class="auth-info" *ngIf="!isLoading">
        <p>🔒 Protected by post-quantum cryptography</p>
        <p class="auto-submit-hint">Form auto-submits when credentials are valid</p>
      </div>
    </div>
  </div>
</div>

<!-- Message Composer Modal -->
<div class="modal-overlay" *ngIf="showMessageModal" (click)="closeMessageComposer()">
  <div class="modal message-modal" (click)="$event.stopPropagation()">
    <button class="close-btn" (click)="closeMessageComposer()" title="Close">×</button>
    <div class="modal-content">
      <!-- Message Type Selector - Only show when user has groups -->
      <div class="message-type-selector" *ngIf="userGroups.length > 0">
        <button
          class="type-btn direct-btn"
          (click)="switchMessageType('direct')"
          [class.active]="messageType === 'direct'"
        >
          <svg class="icon" viewBox="0 0 24 24">
            <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"/>
            <circle cx="12" cy="7" r="4"/>
          </svg>
          Direct
        </button>
        <button
          class="type-btn group-btn"
          (click)="switchMessageType('group')"
          [class.active]="messageType === 'group'"
        >
          <svg class="icon" viewBox="0 0 24 24">
            <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"/>
            <circle cx="9" cy="7" r="4"/>
            <path d="M23 21v-2a4 4 0 0 0-3-3.87"/>
            <path d="M16 3.13a4 4 0 0 1 0 7.75"/>
          </svg>
          Group
        </button>
      </div>

      <!-- Recipient Selection -->
      <div class="form-group">
        <div class="recipient-selector">
          <input
            type="text"
            id="recipientSearch"
            [(ngModel)]="recipientSearchQuery"
            (input)="onRecipientSearchChange()"
            [placeholder]="messageType === 'direct' ? 'Search contacts...' : 'Search groups...'"
            autocomplete="off"
          />

          <!-- Selected Recipient Display -->
          <div class="selected-recipient" *ngIf="getSelectedRecipientName()">
            <span class="recipient-name">{{ getSelectedRecipientName() }}</span>
            <button
              class="clear-recipient"
              (click)="switchMessageType(messageType)"
              title="Clear selection"
            >×</button>
          </div>

          <!-- Contact/Group Dropdown -->
          <div class="recipient-dropdown" *ngIf="recipientSearchQuery && !getSelectedRecipientName()">
            <!-- Direct Message Contacts -->
            <div
              class="recipient-item"
              *ngFor="let contact of filteredContacts"
              (click)="selectContact(contact)"
              [hidden]="messageType !== 'direct'"
            >
              <div class="contact-info">
                <span class="contact-name">{{ contact.username }}</span>
                <span class="contact-email">{{ contact.email }}</span>
              </div>
              <div class="contact-status">
                <span
                  class="status-indicator"
                  [class.online]="contact.isOnline"
                  [class.offline]="!contact.isOnline"
                ></span>
                <span class="status-text">
                  {{ contact.isOnline ? 'Online' : 'Offline' }}
                </span>
              </div>
            </div>

            <!-- Group Chats -->
            <div
              class="recipient-item"
              *ngFor="let group of filteredGroups"
              (click)="selectGroup(group)"
              [hidden]="messageType !== 'group'"
            >
              <div class="group-info">
                <span class="group-name">{{ group.name }}</span>
                <span class="group-members">{{ group.members.length }} members</span>
              </div>
              <div class="group-status">
                <span
                  class="status-indicator"
                  [class.active]="group.isActive"
                ></span>
              </div>
            </div>

            <!-- No Results -->
            <div class="no-results" *ngIf="messageType === 'direct' && filteredContacts.length === 0">
              No contacts found
            </div>
            <div class="no-results" *ngIf="messageType === 'group' && filteredGroups.length === 0">
              No groups found
            </div>
          </div>
        </div>
      </div>

      <!-- Message Content -->
      <div class="form-group">
        <textarea
          id="messageContent"
          [(ngModel)]="messageContent"
          placeholder="Type your message here..."
          rows="6"
          maxlength="1000"
        ></textarea>
        <div class="char-count">{{ messageContent.length }}/1000</div>
      </div>

      <div class="message-actions">
        <button
          class="btn btn-primary"
          (click)="sendMessage()"
          [disabled]="!isMessageValid()"
        >
          <svg class="icon" viewBox="0 0 24 24">
            <line x1="22" y1="2" x2="11" y2="13"/>
            <polygon points="22,2 15,22 11,13 2,9"/>
          </svg>
          Send
        </button>
        <button class="btn btn-secondary" (click)="closeMessageComposer()">
          <svg class="icon" viewBox="0 0 24 24">
            <line x1="18" y1="6" x2="6" y2="18"/>
            <line x1="6" y1="6" x2="18" y2="18"/>
          </svg>
          Cancel
        </button>
      </div>

      <div class="send-hint">
        <p>Press Enter to send • Shift+Enter for new line</p>
      </div>
    </div>
  </div>
</div>

<!-- Messages Viewer Modal -->
<div class="modal-overlay" *ngIf="showMessagesModal" (click)="closeMessagesViewer()">
  <div class="modal messages-modal" (click)="$event.stopPropagation()">
    <button class="close-btn" (click)="closeMessagesViewer()">×</button>

    <div class="modal-content">
      <div class="messages-list" *ngIf="messages.length > 0">
        <div
          class="message-item"
          *ngFor="let message of messages; trackBy: trackMessage"
        >
          <div class="message-header">
            <span class="sender">{{ message.sender }}</span>
            <span class="timestamp">{{ message.timestamp | date:'short' }}</span>
          </div>
          <div class="message-content">{{ message.content }}</div>
        </div>
      </div>

      <div class="empty-state" *ngIf="messages.length === 0">
        <p>No messages yet</p>
        <p class="hint">Click the circle to compose your first message</p>
      </div>
    </div>
  </div>
</div>

<!-- Account Settings Modal -->
<div class="modal-overlay" *ngIf="showAccountSettings" (click)="closeAccountSettings()">
  <div class="modal account-settings-modal" (click)="$event.stopPropagation()">
    <button class="close-btn" (click)="closeAccountSettings()" title="Close">×</button>

    <div class="modal-content">
      <!-- Avatar Section -->
      <div class="avatar-section">
        <div class="avatar-container">
          <div class="avatar-display">
            <img
              *ngIf="userProfile.avatar"
              [src]="userProfile.avatar"
              alt="Profile Avatar"
              class="avatar-image"
            />
            <div *ngIf="!userProfile.avatar" class="avatar-placeholder">
              <span class="avatar-initials">
                {{ currentUser?.username?.charAt(0)?.toUpperCase() || '?' }}
              </span>
            </div>
          </div>
          <div class="avatar-actions">
            <label for="avatarInput" class="avatar-upload-btn">
              📷 Change Avatar
            </label>
            <input
              type="file"
              id="avatarInput"
              accept="image/*"
              (change)="onAvatarChange($event)"
              style="display: none;"
            />
          </div>
        </div>
      </div>

      <!-- Profile Information -->
      <div class="profile-info">
        <div class="form-group">
          <div class="readonly-field">
            <span>{{ currentUser?.username || 'Not set' }}</span>
          </div>
        </div>

        <div class="form-group">
          <div class="readonly-field">
            <span>{{ userProfile.email || 'Not set' }}</span>
            <span class="field-note">Email cannot be changed for security reasons</span>
          </div>
        </div>

        <div class="form-group">
          <div class="readonly-field">
            <span>{{ userProfile.phone || 'Not set' }}</span>
            <span class="field-note">Phone number cannot be changed for security reasons</span>
          </div>
        </div>
      </div>

      <!-- Security Information -->
      <div class="security-info">
        <h3>Security Information</h3>
        <div class="security-item">
          <span class="security-label">🔐 Encryption:</span>
          <span class="security-value">Post-Quantum Cryptography (ML-DSA, ML-KEM)</span>
        </div>
        <div class="security-item">
          <span class="security-label">🛡️ Security Level:</span>
          <span class="security-value">NIST Level 3 (AES-192 equivalent)</span>
        </div>
        <div class="security-item">
          <span class="security-label">🔑 Key Rotation:</span>
          <span class="security-value">Every 30 days</span>
        </div>
      </div>

      <div class="account-actions">
        <button class="btn btn-secondary" (click)="closeAccountSettings()">
          <svg class="icon" viewBox="0 0 24 24">
            <path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4"/>
            <polyline points="16,17 21,12 16,7"/>
            <line x1="21" y1="12" x2="9" y2="12"/>
          </svg>
          Close
        </button>
      </div>
    </div>
  </div>
</div>

<!-- Keyboard Hints (bottom) -->
<div class="keyboard-hints" *ngIf="!showLoginModal && !showMessageModal && !showMessagesModal && !showAccountSettings">
  <span>ESC to close modals • Right-click circle for menu</span>
</div>
