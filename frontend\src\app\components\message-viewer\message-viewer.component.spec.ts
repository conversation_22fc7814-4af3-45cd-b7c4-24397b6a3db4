import { ComponentFixture, TestBed } from '@angular/core/testing';
import { MessageViewerComponent } from './message-viewer.component';
import { StateService } from '../../services/state.service';
import { DomSanitizer } from '@angular/platform-browser';

describe('MessageViewerComponent', () => {
  let component: MessageViewerComponent;
  let fixture: ComponentFixture<MessageViewerComponent>;
  let stateService: StateService;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [MessageViewerComponent],
      providers: [
        StateService,
        {
          provide: DomSanitizer,
          useValue: {
            bypassSecurityTrustHtml: (value: string) => value
          }
        }
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(MessageViewerComponent);
    component = fixture.componentInstance;
    stateService = TestBed.inject(StateService);
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should sanitize content', () => {
    const content = '<script>alert("test")</script>';
    const sanitized = component['sanitizeContent'](content);
    expect(sanitized).toBe(content);
  });

  it('should delete message', () => {
    const messageId = 'test-id';
    const spy = spyOn(stateService, 'removeMessage');
    component['deleteMessage'](messageId);
    expect(spy).toHaveBeenCalledWith(messageId);
  });

  it('should clear messages on destroy', () => {
    const spy = spyOn(stateService, 'clearMessages');
    component.ngOnDestroy();
    expect(spy).toHaveBeenCalled();
  });
});
