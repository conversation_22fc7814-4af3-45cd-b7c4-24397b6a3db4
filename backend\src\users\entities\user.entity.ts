import { <PERSON>tity, Column, PrimaryGeneratedColumn, CreateDateColumn, UpdateDateColumn } from 'typeorm';
import { IUser, ISecretWordHash, AccountStatus } from '@qsc/shared';

@Entity('users')
export class User implements IUser {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ unique: true })
  username: string;

  @Column({ unique: true, nullable: true })
  email?: string;

  @Column({ nullable: true })
  phone?: string;

  @Column({ nullable: true })
  publicKey?: string;

  @Column({ default: false })
  isAdmin: boolean;

  @Column({ default: true })
  isActive: boolean;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @Column({ nullable: true })
  lastLoginAt?: Date;

  @Column({ type: 'json', default: '[]' })
  deviceIds: string[];

  @Column({ type: 'json' })
  secretWordHash: ISecretWordHash;

  @Column({ default: false })
  isVerified: boolean;

  @Column({
    type: 'varchar',
    default: 'active',
    enum: ['active', 'deactivated', 'compromised']
  })
  accountStatus: AccountStatus;

  @Column({ nullable: true })
  deactivatedBy?: string;

  @Column({ nullable: true })
  deactivatedAt?: Date;

  @Column({ nullable: true })
  deactivationReason?: string;

  @Column({ nullable: true })
  reinvitedBy?: string;

  @Column({ nullable: true })
  reinvitedAt?: Date;

  @Column({ nullable: true })
  newInviteCode?: string;

  @Column({ default: 0 })
  failedAttempts: number;

  @Column({ nullable: true })
  lastAttemptAt?: Date;

  @Column({ default: false })
  isCompromised: boolean;
} 