{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Projects/QSC1/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { TestBed } from '@angular/core/testing';\nimport { MessageViewerComponent } from './message-viewer.component';\nimport { StateService } from '../../services/state.service';\nimport { DomSanitizer } from '@angular/platform-browser';\ndescribe('MessageViewerComponent', () => {\n  let component;\n  let fixture;\n  let stateService;\n  beforeEach(/*#__PURE__*/_asyncToGenerator(function* () {\n    yield TestBed.configureTestingModule({\n      imports: [MessageViewerComponent],\n      providers: [StateService, {\n        provide: DomSanitizer,\n        useValue: {\n          bypassSecurityTrustHtml: value => value\n        }\n      }]\n    }).compileComponents();\n    fixture = TestBed.createComponent(MessageViewerComponent);\n    component = fixture.componentInstance;\n    stateService = TestBed.inject(StateService);\n    fixture.detectChanges();\n  }));\n  it('should create', () => {\n    expect(component).toBeTruthy();\n  });\n  it('should sanitize content', () => {\n    const content = '<script>alert(\"test\")</script>';\n    const sanitized = component['sanitizeContent'](content);\n    expect(sanitized).toBe(content);\n  });\n  it('should delete message', () => {\n    const messageId = 'test-id';\n    const spy = spyOn(stateService, 'removeMessage');\n    component['deleteMessage'](messageId);\n    expect(spy).toHaveBeenCalledWith(messageId);\n  });\n  it('should clear messages on destroy', () => {\n    const spy = spyOn(stateService, 'clearMessages');\n    component.ngOnDestroy();\n    expect(spy).toHaveBeenCalled();\n  });\n});", "map": {"version": 3, "names": ["TestBed", "MessageViewerComponent", "StateService", "Dom<PERSON><PERSON><PERSON>zer", "describe", "component", "fixture", "stateService", "beforeEach", "_asyncToGenerator", "configureTestingModule", "imports", "providers", "provide", "useValue", "bypassSecurityTrustHtml", "value", "compileComponents", "createComponent", "componentInstance", "inject", "detectChanges", "it", "expect", "toBeTruthy", "content", "sanitized", "toBe", "messageId", "spy", "spyOn", "toHaveBeenCalledWith", "ngOnDestroy", "toHaveBeenCalled"], "sources": ["C:\\Users\\<USER>\\Projects\\QSC1\\frontend\\src\\app\\components\\message-viewer\\message-viewer.component.spec.ts"], "sourcesContent": ["import { ComponentFixture, TestBed } from '@angular/core/testing';\r\nimport { MessageViewerComponent } from './message-viewer.component';\r\nimport { StateService } from '../../services/state.service';\r\nimport { DomSanitizer } from '@angular/platform-browser';\r\n\r\ndescribe('MessageViewerComponent', () => {\r\n  let component: MessageViewerComponent;\r\n  let fixture: ComponentFixture<MessageViewerComponent>;\r\n  let stateService: StateService;\r\n\r\n  beforeEach(async () => {\r\n    await TestBed.configureTestingModule({\r\n      imports: [MessageViewerComponent],\r\n      providers: [\r\n        StateService,\r\n        {\r\n          provide: DomSanitizer,\r\n          useValue: {\r\n            bypassSecurityTrustHtml: (value: string) => value\r\n          }\r\n        }\r\n      ]\r\n    }).compileComponents();\r\n\r\n    fixture = TestBed.createComponent(MessageViewerComponent);\r\n    component = fixture.componentInstance;\r\n    stateService = TestBed.inject(StateService);\r\n    fixture.detectChanges();\r\n  });\r\n\r\n  it('should create', () => {\r\n    expect(component).toBeTruthy();\r\n  });\r\n\r\n  it('should sanitize content', () => {\r\n    const content = '<script>alert(\"test\")</script>';\r\n    const sanitized = component['sanitizeContent'](content);\r\n    expect(sanitized).toBe(content);\r\n  });\r\n\r\n  it('should delete message', () => {\r\n    const messageId = 'test-id';\r\n    const spy = spyOn(stateService, 'removeMessage');\r\n    component['deleteMessage'](messageId);\r\n    expect(spy).toHaveBeenCalledWith(messageId);\r\n  });\r\n\r\n  it('should clear messages on destroy', () => {\r\n    const spy = spyOn(stateService, 'clearMessages');\r\n    component.ngOnDestroy();\r\n    expect(spy).toHaveBeenCalled();\r\n  });\r\n});\r\n"], "mappings": ";AAAA,SAA2BA,OAAO,QAAQ,uBAAuB;AACjE,SAASC,sBAAsB,QAAQ,4BAA4B;AACnE,SAASC,YAAY,QAAQ,8BAA8B;AAC3D,SAASC,YAAY,QAAQ,2BAA2B;AAExDC,QAAQ,CAAC,wBAAwB,EAAE,MAAK;EACtC,IAAIC,SAAiC;EACrC,IAAIC,OAAiD;EACrD,IAAIC,YAA0B;EAE9BC,UAAU,cAAAC,iBAAA,CAAC,aAAW;IACpB,MAAMT,OAAO,CAACU,sBAAsB,CAAC;MACnCC,OAAO,EAAE,CAACV,sBAAsB,CAAC;MACjCW,SAAS,EAAE,CACTV,YAAY,EACZ;QACEW,OAAO,EAAEV,YAAY;QACrBW,QAAQ,EAAE;UACRC,uBAAuB,EAAGC,KAAa,IAAKA;;OAE/C;KAEJ,CAAC,CAACC,iBAAiB,EAAE;IAEtBX,OAAO,GAAGN,OAAO,CAACkB,eAAe,CAACjB,sBAAsB,CAAC;IACzDI,SAAS,GAAGC,OAAO,CAACa,iBAAiB;IACrCZ,YAAY,GAAGP,OAAO,CAACoB,MAAM,CAAClB,YAAY,CAAC;IAC3CI,OAAO,CAACe,aAAa,EAAE;EACzB,CAAC,EAAC;EAEFC,EAAE,CAAC,eAAe,EAAE,MAAK;IACvBC,MAAM,CAAClB,SAAS,CAAC,CAACmB,UAAU,EAAE;EAChC,CAAC,CAAC;EAEFF,EAAE,CAAC,yBAAyB,EAAE,MAAK;IACjC,MAAMG,OAAO,GAAG,gCAAgC;IAChD,MAAMC,SAAS,GAAGrB,SAAS,CAAC,iBAAiB,CAAC,CAACoB,OAAO,CAAC;IACvDF,MAAM,CAACG,SAAS,CAAC,CAACC,IAAI,CAACF,OAAO,CAAC;EACjC,CAAC,CAAC;EAEFH,EAAE,CAAC,uBAAuB,EAAE,MAAK;IAC/B,MAAMM,SAAS,GAAG,SAAS;IAC3B,MAAMC,GAAG,GAAGC,KAAK,CAACvB,YAAY,EAAE,eAAe,CAAC;IAChDF,SAAS,CAAC,eAAe,CAAC,CAACuB,SAAS,CAAC;IACrCL,MAAM,CAACM,GAAG,CAAC,CAACE,oBAAoB,CAACH,SAAS,CAAC;EAC7C,CAAC,CAAC;EAEFN,EAAE,CAAC,kCAAkC,EAAE,MAAK;IAC1C,MAAMO,GAAG,GAAGC,KAAK,CAACvB,YAAY,EAAE,eAAe,CAAC;IAChDF,SAAS,CAAC2B,WAAW,EAAE;IACvBT,MAAM,CAACM,GAAG,CAAC,CAACI,gBAAgB,EAAE;EAChC,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}