import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterLink } from '@angular/router';
import { ApiService } from '../../services/api.service';

@Component({
  selector: 'app-home',
  standalone: true,
  imports: [CommonModule, RouterLink],
  template: `
    <div class="home-container">
      <h2>Welcome to Quantum Shield</h2>
      <p>A secure messaging platform with post-quantum cryptography</p>

      <div class="connection-status">
        <h3>Backend Connection Status</h3>
        <p *ngIf="healthStatus">Health Check: {{ healthStatus }}</p>
        <p *ngIf="dbStatus">Database: {{ dbStatus }}</p>
        <button (click)="testConnection()" class="button">Test Connection</button>
      </div>

      <div class="actions">
        <a routerLink="/key-management" class="button">Manage Keys</a>
      </div>
    </div>
  `,
  styles: [`
    .home-container {
      text-align: center;
      padding: 2rem;
    }
    h2 {
      color: #2c3e50;
      margin-bottom: 1rem;
    }
    .connection-status {
      margin: 2rem 0;
      padding: 1rem;
      border: 1px solid #ddd;
      border-radius: 4px;
    }
    .actions {
      margin-top: 2rem;
    }
    .button {
      display: inline-block;
      padding: 0.8rem 1.5rem;
      background-color: #3498db;
      color: white;
      text-decoration: none;
      border-radius: 4px;
      transition: background-color 0.3s;
      border: none;
      cursor: pointer;
      margin: 0.5rem;
    }
    .button:hover {
      background-color: #2980b9;
    }
  `]
})
export class HomeComponent implements OnInit {
  healthStatus: string = '';
  dbStatus: string = '';

  constructor(private apiService: ApiService) {}

  ngOnInit() {
    this.testConnection();
  }

  testConnection() {
    this.healthStatus = 'Checking...';
    this.dbStatus = 'Checking...';

    this.apiService.checkHealth().subscribe({
      next: (response) => {
        this.healthStatus = 'Connected';
      },
      error: (error) => {
        this.healthStatus = 'Failed to connect';
        console.error('Health check failed:', error);
      }
    });

    this.apiService.testDatabase().subscribe({
      next: (response) => {
        this.dbStatus = 'Connected';
      },
      error: (error) => {
        this.dbStatus = 'Failed to connect';
        console.error('Database test failed:', error);
      }
    });
  }
}
