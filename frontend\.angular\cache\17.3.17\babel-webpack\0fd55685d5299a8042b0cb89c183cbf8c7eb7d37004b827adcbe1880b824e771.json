{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterOutlet } from '@angular/router';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"./services/wasm.service\";\nimport * as i2 from \"./services/error-handling.service\";\nexport let AppComponent = /*#__PURE__*/(() => {\n  class AppComponent {\n    constructor(wasmService, errorHandling) {\n      this.wasmService = wasmService;\n      this.errorHandling = errorHandling;\n    }\n    static {\n      this.ɵfac = function AppComponent_Factory(t) {\n        return new (t || AppComponent)(i0.ɵɵdirectiveInject(i1.WasmService), i0.ɵɵdirectiveInject(i2.ErrorHandlingService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: AppComponent,\n        selectors: [[\"app-root\"]],\n        standalone: true,\n        features: [i0.ɵɵStandaloneFeature],\n        decls: 1,\n        vars: 0,\n        template: function AppComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelement(0, \"router-outlet\");\n          }\n        },\n        dependencies: [CommonModule, RouterOutlet],\n        encapsulation: 2\n      });\n    }\n  }\n  return AppComponent;\n})();\n// Export as App for compatibility\nexport { AppComponent as App };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}