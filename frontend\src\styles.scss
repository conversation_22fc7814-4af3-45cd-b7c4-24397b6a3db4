/* QSC - Quantum Secure Chat - Global Styles */
/* Minimalistic Modern Design */

// Import Inter font for modern typography
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

// CSS Reset and Base Styles
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  height: 100%;
  overflow: hidden;
}

body {
  height: 100%;
  margin: 0;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background: #0f0f23;
  color: #ffffff;
  overflow: hidden;
  user-select: none; // Prevent text selection for cleaner UI
}

// Remove default button and input styles
button, input, textarea, select {
  font-family: inherit;
  font-size: inherit;
}

button {
  cursor: pointer;
  border: none;
  background: none;
  outline: none;
}

input, textarea {
  outline: none;
}

// Ensure app takes full viewport
app-root {
  display: block;
  width: 100vw;
  height: 100vh;
  overflow: hidden;
}

// Utility classes for the minimalistic design
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

// Smooth transitions for all interactive elements
* {
  transition: opacity 0.2s ease, transform 0.2s ease;
}

// Disable text selection on non-text elements
.no-select {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

// Focus styles for accessibility
*:focus-visible {
  outline: 2px solid #3742fa;
  outline-offset: 2px;
}

// Scrollbar styling for modals
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.3);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.5);
}

// Animation performance optimization
* {
  will-change: auto;
}

.animate {
  will-change: transform, opacity;
}

// Print styles (hide everything for security)
@media print {
  * {
    display: none !important;
  }
}

// High contrast mode support
@media (prefers-contrast: high) {
  body {
    background: #000000;
    color: #ffffff;
  }
}

// Reduced motion support
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Modal Styles - Futuristic Minimalism */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(8px);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  animation: modalFadeIn 0.2s ease-out;
}

.modal-content {
  background: #fafafa;
  padding: 3rem 2.5rem;
  border-radius: 16px;
  box-shadow:
    0 0 40px rgba(255, 255, 255, 0.1),
    0 8px 32px rgba(0, 0, 0, 0.12),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  max-width: 480px;
  width: 90%;
  max-height: 80vh;
  overflow-y: auto;
  border: 1px solid rgba(255, 255, 255, 0.1);
  position: relative;
  animation: modalSlideIn 0.3s ease-out;
}

.modal-content::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(45deg,
    rgba(255, 255, 255, 0.1) 0%,
    rgba(255, 255, 255, 0.05) 50%,
    rgba(255, 255, 255, 0.1) 100%);
  border-radius: 18px;
  z-index: -1;
}

@keyframes modalFadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Futuristic Form Elements */
.form-group {
  margin-bottom: 2rem;
  position: relative;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  color: #6b7280;
  letter-spacing: 0.025em;
  text-transform: uppercase;
}

.form-group input {
  width: 100%;
  padding: 0.75rem 0;
  background: transparent;
  border: none;
  border-bottom: 1px solid #e5e7eb;
  color: #1f2937;
  font-size: 1rem;
  font-weight: 400;
  transition: all 0.3s ease;
}

.form-group input:focus {
  border-bottom-color: #1e40af;
  outline: none;
}

.form-group input::placeholder {
  color: #9ca3af;
  font-weight: 300;
}

/* Futuristic Pill Buttons */
.pill-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.875rem 1.5rem;
  background: #1e40af;
  color: white;
  border: none;
  border-radius: 50px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  min-width: 120px;
  gap: 0.5rem;
}

.pill-button:hover {
  background: #1d4ed8;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(30, 64, 175, 0.3);
}

.pill-button:active {
  transform: translateY(0);
}

.pill-button .icon {
  width: 16px;
  height: 16px;
  stroke: currentColor;
  fill: none;
  stroke-width: 1.5;
}

/* Button Group */
.button-group {
  display: flex;
  gap: 1rem;
  justify-content: center;
  margin-top: 2rem;
}
