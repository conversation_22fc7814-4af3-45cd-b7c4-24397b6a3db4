{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { Subject, takeUntil } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../services/auth.service\";\nimport * as i2 from \"../../services/message.service\";\nimport * as i3 from \"../../services/notification.service\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/forms\";\nfunction QscMainComponent_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 10);\n  }\n}\nfunction QscMainComponent_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 11);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.unreadCount, \" \");\n  }\n}\nfunction QscMainComponent_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 12)(1, \"span\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 13);\n    i0.ɵɵlistener(\"click\", function QscMainComponent_div_6_Template_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.logout());\n    });\n    i0.ɵɵtext(4, \"\\u00D7\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r0.currentUser.username);\n  }\n}\nfunction QscMainComponent_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 14);\n    i0.ɵɵlistener(\"click\", function QscMainComponent_div_7_Template_div_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelementStart(1, \"div\", 15);\n    i0.ɵɵlistener(\"click\", function QscMainComponent_div_7_Template_div_click_1_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.openAccountSettings());\n    });\n    i0.ɵɵelementStart(2, \"span\", 16);\n    i0.ɵɵtext(3, \"\\uD83D\\uDC64\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 17);\n    i0.ɵɵtext(5, \"Account Settings\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(6, \"div\", 18);\n    i0.ɵɵelementStart(7, \"div\", 19);\n    i0.ɵɵlistener(\"click\", function QscMainComponent_div_7_Template_div_click_7_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.logout());\n    });\n    i0.ɵɵelementStart(8, \"span\", 16);\n    i0.ɵɵtext(9, \"\\uD83D\\uDEAA\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"span\", 17);\n    i0.ɵɵtext(11, \"Logout\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵstyleProp(\"left\", ctx_r0.contextMenuPosition.x, \"px\")(\"top\", ctx_r0.contextMenuPosition.y, \"px\");\n  }\n}\nfunction QscMainComponent_div_8_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 33);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.loginError, \" \");\n  }\n}\nfunction QscMainComponent_div_8_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 34);\n    i0.ɵɵelement(1, \"div\", 35);\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3, \"Authenticating...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction QscMainComponent_div_8_div_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 36)(1, \"p\");\n    i0.ɵɵtext(2, \"\\uD83D\\uDD12 Protected by post-quantum cryptography\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p\", 37);\n    i0.ɵɵtext(4, \"Form auto-submits when credentials are valid\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction QscMainComponent_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 20);\n    i0.ɵɵlistener(\"click\", function QscMainComponent_div_8_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.closeLoginModal());\n    });\n    i0.ɵɵelementStart(1, \"div\", 21);\n    i0.ɵɵlistener(\"click\", function QscMainComponent_div_8_Template_div_click_1_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelementStart(2, \"div\", 22)(3, \"h2\");\n    i0.ɵɵtext(4, \"Sign In\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 23);\n    i0.ɵɵlistener(\"click\", function QscMainComponent_div_8_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.closeLoginModal());\n    });\n    i0.ɵɵtext(6, \"\\u00D7\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 24)(8, \"div\", 25)(9, \"label\", 26);\n    i0.ɵɵtext(10, \"Email\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"input\", 27);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function QscMainComponent_div_8_Template_input_ngModelChange_11_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r0 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r0.loginCredentials.email, $event) || (ctx_r0.loginCredentials.email = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"input\", function QscMainComponent_div_8_Template_input_input_11_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.onLoginInputChange());\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"div\", 25)(13, \"label\", 28);\n    i0.ɵɵtext(14, \"Secret Word\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"input\", 29);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function QscMainComponent_div_8_Template_input_ngModelChange_15_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r0 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r0.loginCredentials.secretWord, $event) || (ctx_r0.loginCredentials.secretWord = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"input\", function QscMainComponent_div_8_Template_input_input_15_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.onLoginInputChange());\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(16, QscMainComponent_div_8_div_16_Template, 2, 1, \"div\", 30)(17, QscMainComponent_div_8_div_17_Template, 4, 0, \"div\", 31)(18, QscMainComponent_div_8_div_18_Template, 5, 0, \"div\", 32);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(11);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r0.loginCredentials.email);\n    i0.ɵɵproperty(\"disabled\", ctx_r0.isLoading);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r0.loginCredentials.secretWord);\n    i0.ɵɵproperty(\"disabled\", ctx_r0.isLoading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.loginError);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isLoading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.isLoading);\n  }\n}\nfunction QscMainComponent_div_9_div_18_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 53)(1, \"span\", 54);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 55);\n    i0.ɵɵlistener(\"click\", function QscMainComponent_div_9_div_18_Template_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.switchMessageType(ctx_r0.messageType));\n    });\n    i0.ɵɵtext(4, \"\\u00D7\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r0.getSelectedRecipientName());\n  }\n}\nfunction QscMainComponent_div_9_div_19_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 59);\n    i0.ɵɵlistener(\"click\", function QscMainComponent_div_9_div_19_div_1_Template_div_click_0_listener() {\n      const contact_r8 = i0.ɵɵrestoreView(_r7).$implicit;\n      const ctx_r0 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r0.selectContact(contact_r8));\n    });\n    i0.ɵɵelementStart(1, \"div\", 60)(2, \"span\", 61);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 62);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 63);\n    i0.ɵɵelement(7, \"span\", 64);\n    i0.ɵɵelementStart(8, \"span\", 65);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const contact_r8 = ctx.$implicit;\n    const ctx_r0 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"hidden\", ctx_r0.messageType !== \"direct\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(contact_r8.username);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(contact_r8.email);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"online\", contact_r8.isOnline)(\"offline\", !contact_r8.isOnline);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", contact_r8.isOnline ? \"Online\" : \"Offline\", \" \");\n  }\n}\nfunction QscMainComponent_div_9_div_19_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 59);\n    i0.ɵɵlistener(\"click\", function QscMainComponent_div_9_div_19_div_2_Template_div_click_0_listener() {\n      const group_r10 = i0.ɵɵrestoreView(_r9).$implicit;\n      const ctx_r0 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r0.selectGroup(group_r10));\n    });\n    i0.ɵɵelementStart(1, \"div\", 66)(2, \"span\", 67);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 68);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 69);\n    i0.ɵɵelement(7, \"span\", 64);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const group_r10 = ctx.$implicit;\n    const ctx_r0 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"hidden\", ctx_r0.messageType !== \"group\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(group_r10.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", group_r10.members.length, \" members\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"active\", group_r10.isActive);\n  }\n}\nfunction QscMainComponent_div_9_div_19_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 70);\n    i0.ɵɵtext(1, \" No contacts found \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction QscMainComponent_div_9_div_19_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 70);\n    i0.ɵɵtext(1, \" No groups found \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction QscMainComponent_div_9_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 56);\n    i0.ɵɵtemplate(1, QscMainComponent_div_9_div_19_div_1_Template, 10, 8, \"div\", 57)(2, QscMainComponent_div_9_div_19_div_2_Template, 8, 5, \"div\", 57)(3, QscMainComponent_div_9_div_19_div_3_Template, 2, 0, \"div\", 58)(4, QscMainComponent_div_9_div_19_div_4_Template, 2, 0, \"div\", 58);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.filteredContacts);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.filteredGroups);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.messageType === \"direct\" && ctx_r0.filteredContacts.length === 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.messageType === \"group\" && ctx_r0.filteredGroups.length === 0);\n  }\n}\nfunction QscMainComponent_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 20);\n    i0.ɵɵlistener(\"click\", function QscMainComponent_div_9_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.closeMessageComposer());\n    });\n    i0.ɵɵelementStart(1, \"div\", 38);\n    i0.ɵɵlistener(\"click\", function QscMainComponent_div_9_Template_div_click_1_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelementStart(2, \"div\", 22)(3, \"h2\");\n    i0.ɵɵtext(4, \"Compose Message\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 23);\n    i0.ɵɵlistener(\"click\", function QscMainComponent_div_9_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.closeMessageComposer());\n    });\n    i0.ɵɵtext(6, \"\\u00D7\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 24)(8, \"div\", 39)(9, \"button\", 40);\n    i0.ɵɵlistener(\"click\", function QscMainComponent_div_9_Template_button_click_9_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.switchMessageType(\"direct\"));\n    });\n    i0.ɵɵtext(10, \" \\uD83D\\uDC64 Direct \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"button\", 40);\n    i0.ɵɵlistener(\"click\", function QscMainComponent_div_9_Template_button_click_11_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.switchMessageType(\"group\"));\n    });\n    i0.ɵɵtext(12, \" \\uD83D\\uDC65 Group \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"div\", 25)(14, \"label\", 41);\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"div\", 42)(17, \"input\", 43);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function QscMainComponent_div_9_Template_input_ngModelChange_17_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r0 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r0.recipientSearchQuery, $event) || (ctx_r0.recipientSearchQuery = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"input\", function QscMainComponent_div_9_Template_input_input_17_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.onRecipientSearchChange());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(18, QscMainComponent_div_9_div_18_Template, 5, 1, \"div\", 44)(19, QscMainComponent_div_9_div_19_Template, 5, 4, \"div\", 45);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(20, \"div\", 25)(21, \"label\", 46);\n    i0.ɵɵtext(22, \"Message\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"textarea\", 47);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function QscMainComponent_div_9_Template_textarea_ngModelChange_23_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r0 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r0.messageContent, $event) || (ctx_r0.messageContent = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"div\", 48);\n    i0.ɵɵtext(25);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(26, \"div\", 49)(27, \"button\", 50);\n    i0.ɵɵlistener(\"click\", function QscMainComponent_div_9_Template_button_click_27_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.sendMessage());\n    });\n    i0.ɵɵtext(28, \" Send Message \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"button\", 51);\n    i0.ɵɵlistener(\"click\", function QscMainComponent_div_9_Template_button_click_29_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.closeMessageComposer());\n    });\n    i0.ɵɵtext(30, \" Cancel \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(31, \"div\", 52)(32, \"p\");\n    i0.ɵɵtext(33, \"Press Enter to send \\u2022 Shift+Enter for new line\");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(9);\n    i0.ɵɵclassProp(\"active\", ctx_r0.messageType === \"direct\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"active\", ctx_r0.messageType === \"group\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.messageType === \"direct\" ? \"To (Contact)\" : \"To (Group)\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r0.recipientSearchQuery);\n    i0.ɵɵproperty(\"placeholder\", ctx_r0.messageType === \"direct\" ? \"Search contacts...\" : \"Search groups...\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.getSelectedRecipientName());\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.recipientSearchQuery && !ctx_r0.getSelectedRecipientName());\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r0.messageContent);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r0.messageContent.length, \"/1000\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", !ctx_r0.isMessageValid());\n  }\n}\nfunction QscMainComponent_div_10_div_8_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 76)(1, \"div\", 77)(2, \"span\", 78);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 79);\n    i0.ɵɵtext(5);\n    i0.ɵɵpipe(6, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 80);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const message_r12 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(message_r12.sender);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(6, 3, message_r12.timestamp, \"short\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(message_r12.content);\n  }\n}\nfunction QscMainComponent_div_10_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 74);\n    i0.ɵɵtemplate(1, QscMainComponent_div_10_div_8_div_1_Template, 9, 6, \"div\", 75);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.messages)(\"ngForTrackBy\", ctx_r0.trackMessage);\n  }\n}\nfunction QscMainComponent_div_10_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 81)(1, \"p\");\n    i0.ɵɵtext(2, \"No messages yet\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p\", 82);\n    i0.ɵɵtext(4, \"Click the circle to compose your first message\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction QscMainComponent_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 20);\n    i0.ɵɵlistener(\"click\", function QscMainComponent_div_10_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.closeMessagesViewer());\n    });\n    i0.ɵɵelementStart(1, \"div\", 71);\n    i0.ɵɵlistener(\"click\", function QscMainComponent_div_10_Template_div_click_1_listener($event) {\n      i0.ɵɵrestoreView(_r11);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelementStart(2, \"div\", 22)(3, \"h2\");\n    i0.ɵɵtext(4, \"Messages\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 23);\n    i0.ɵɵlistener(\"click\", function QscMainComponent_div_10_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.closeMessagesViewer());\n    });\n    i0.ɵɵtext(6, \"\\u00D7\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 24);\n    i0.ɵɵtemplate(8, QscMainComponent_div_10_div_8_Template, 2, 2, \"div\", 72)(9, QscMainComponent_div_10_div_9_Template, 5, 0, \"div\", 73);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.messages.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.messages.length === 0);\n  }\n}\nfunction QscMainComponent_div_11_img_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 100);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"src\", ctx_r0.userProfile.avatar, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction QscMainComponent_div_11_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 101)(1, \"span\", 102);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    let tmp_2_0;\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", (ctx_r0.currentUser == null ? null : ctx_r0.currentUser.username == null ? null : (tmp_2_0 = ctx_r0.currentUser.username.charAt(0)) == null ? null : tmp_2_0.toUpperCase()) || \"?\", \" \");\n  }\n}\nfunction QscMainComponent_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 20);\n    i0.ɵɵlistener(\"click\", function QscMainComponent_div_11_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.closeAccountSettings());\n    });\n    i0.ɵɵelementStart(1, \"div\", 83);\n    i0.ɵɵlistener(\"click\", function QscMainComponent_div_11_Template_div_click_1_listener($event) {\n      i0.ɵɵrestoreView(_r13);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelementStart(2, \"div\", 22)(3, \"h2\");\n    i0.ɵɵtext(4, \"Account Settings\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 23);\n    i0.ɵɵlistener(\"click\", function QscMainComponent_div_11_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.closeAccountSettings());\n    });\n    i0.ɵɵtext(6, \"\\u00D7\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 24)(8, \"div\", 84)(9, \"div\", 85)(10, \"div\", 86);\n    i0.ɵɵtemplate(11, QscMainComponent_div_11_img_11_Template, 1, 1, \"img\", 87)(12, QscMainComponent_div_11_div_12_Template, 3, 1, \"div\", 88);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"div\", 89)(14, \"label\", 90);\n    i0.ɵɵtext(15, \" \\uD83D\\uDCF7 Change Avatar \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"input\", 91);\n    i0.ɵɵlistener(\"change\", function QscMainComponent_div_11_Template_input_change_16_listener($event) {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.onAvatarChange($event));\n    });\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(17, \"div\", 92)(18, \"div\", 25)(19, \"label\");\n    i0.ɵɵtext(20, \"Username\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"div\", 93)(22, \"span\");\n    i0.ɵɵtext(23);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(24, \"div\", 25)(25, \"label\");\n    i0.ɵɵtext(26, \"Email Address\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"div\", 93)(28, \"span\");\n    i0.ɵɵtext(29);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(30, \"span\", 94);\n    i0.ɵɵtext(31, \"Email cannot be changed for security reasons\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(32, \"div\", 25)(33, \"label\");\n    i0.ɵɵtext(34, \"Phone Number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(35, \"div\", 93)(36, \"span\");\n    i0.ɵɵtext(37);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(38, \"span\", 94);\n    i0.ɵɵtext(39, \"Phone number cannot be changed for security reasons\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(40, \"div\", 95)(41, \"h3\");\n    i0.ɵɵtext(42, \"Security Information\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(43, \"div\", 96)(44, \"span\", 97);\n    i0.ɵɵtext(45, \"\\uD83D\\uDD10 Encryption:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(46, \"span\", 98);\n    i0.ɵɵtext(47, \"Post-Quantum Cryptography (ML-DSA, ML-KEM)\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(48, \"div\", 96)(49, \"span\", 97);\n    i0.ɵɵtext(50, \"\\uD83D\\uDEE1\\uFE0F Security Level:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(51, \"span\", 98);\n    i0.ɵɵtext(52, \"NIST Level 3 (AES-192 equivalent)\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(53, \"div\", 96)(54, \"span\", 97);\n    i0.ɵɵtext(55, \"\\uD83D\\uDD11 Key Rotation:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(56, \"span\", 98);\n    i0.ɵɵtext(57, \"Every 30 days\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(58, \"div\", 99)(59, \"button\", 51);\n    i0.ɵɵlistener(\"click\", function QscMainComponent_div_11_Template_button_click_59_listener() {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.closeAccountSettings());\n    });\n    i0.ɵɵtext(60, \" Close \");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(11);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.userProfile.avatar);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.userProfile.avatar);\n    i0.ɵɵadvance(11);\n    i0.ɵɵtextInterpolate((ctx_r0.currentUser == null ? null : ctx_r0.currentUser.username) || \"Not set\");\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r0.userProfile.email || \"Not set\");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate(ctx_r0.userProfile.phoneNumber || \"Not set\");\n  }\n}\nfunction QscMainComponent_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 103)(1, \"span\");\n    i0.ɵɵtext(2, \"ESC to close modals \\u2022 Right-click circle for menu\");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport class QscMainComponent {\n  constructor(authService, messageService, notificationService) {\n    this.authService = authService;\n    this.messageService = messageService;\n    this.notificationService = notificationService;\n    this.destroy$ = new Subject();\n    // Circle state management\n    this.circleState = 'guest';\n    // Modal states\n    this.showLoginModal = false;\n    this.showMessageModal = false;\n    this.showMessagesModal = false;\n    this.showContextMenu = false;\n    this.showAccountSettings = false;\n    // Context menu position\n    this.contextMenuPosition = {\n      x: 0,\n      y: 0\n    };\n    // Authentication\n    this.loginCredentials = {\n      email: '',\n      secretWord: ''\n    };\n    this.loginError = '';\n    this.isLoading = false;\n    // Messaging\n    this.messageContent = '';\n    this.selectedRecipient = '';\n    this.selectedGroup = '';\n    this.messageType = 'direct';\n    this.messages = [];\n    this.unreadCount = 0;\n    // Contacts and Groups\n    this.contacts = [];\n    this.groups = [];\n    this.filteredContacts = [];\n    this.filteredGroups = [];\n    this.recipientSearchQuery = '';\n    // User info\n    this.currentUser = null;\n    // Account settings\n    this.userProfile = {\n      avatar: '',\n      email: '',\n      phoneNumber: ''\n    };\n    // Long press handling\n    this.longPressTimer = null;\n    this.longPressDuration = 500; // 500ms for long press\n  }\n  ngOnInit() {\n    this.initializeApp();\n    this.setupMessageListener();\n    this.setupAuthListener();\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  initializeApp() {\n    // Check if user is already authenticated\n    if (this.authService.isAuthenticated()) {\n      this.currentUser = this.authService.getCurrentUser();\n      this.circleState = 'authenticated';\n      this.loadMessages();\n      this.loadContacts();\n      this.loadGroups();\n    } else {\n      this.circleState = 'guest';\n    }\n  }\n  setupAuthListener() {\n    this.authService.authState$.pipe(takeUntil(this.destroy$)).subscribe(user => {\n      if (user) {\n        this.currentUser = user;\n        this.circleState = 'authenticated';\n        this.showLoginModal = false;\n        this.loadMessages();\n      } else {\n        this.currentUser = null;\n        this.circleState = 'guest';\n        this.messages = [];\n        this.unreadCount = 0;\n      }\n    });\n  }\n  setupMessageListener() {\n    this.messageService.messages$.pipe(takeUntil(this.destroy$)).subscribe(messages => {\n      this.messages = messages;\n      this.updateUnreadCount();\n    });\n    this.messageService.newMessage$.pipe(takeUntil(this.destroy$)).subscribe(message => {\n      this.messages.unshift(message);\n      this.updateUnreadCount();\n      this.notificationService.showNotification('New message received');\n    });\n  }\n  updateUnreadCount() {\n    this.unreadCount = this.messages.filter(m => !m.read).length;\n    if (this.unreadCount > 0 && this.circleState === 'authenticated') {\n      this.circleState = 'unread';\n    } else if (this.unreadCount === 0 && this.circleState === 'unread') {\n      this.circleState = 'authenticated';\n    }\n  }\n  loadMessages() {\n    this.messageService.loadMessages().pipe(takeUntil(this.destroy$)).subscribe({\n      next: messages => {\n        this.messages = messages;\n        this.updateUnreadCount();\n      },\n      error: error => {\n        console.error('Failed to load messages:', error);\n      }\n    });\n  }\n  loadContacts() {\n    // TODO: Replace with actual API call\n    this.contacts = [{\n      id: '1',\n      username: 'alice',\n      email: '<EMAIL>',\n      isOnline: true\n    }, {\n      id: '2',\n      username: 'bob',\n      email: '<EMAIL>',\n      isOnline: false,\n      lastSeen: new Date(Date.now() - 300000) // 5 minutes ago\n    }];\n    this.filteredContacts = [...this.contacts];\n  }\n  loadGroups() {\n    // TODO: Replace with actual API call\n    this.groups = [{\n      id: 'group1',\n      name: 'Work Team',\n      members: ['1', '2', 'current-user'],\n      isActive: true\n    }, {\n      id: 'group2',\n      name: 'Family',\n      members: ['3', '4', 'current-user'],\n      isActive: true\n    }];\n    this.filteredGroups = [...this.groups];\n  }\n  // Circle click handler - main interaction point\n  onCircleClick() {\n    // Don't handle click if context menu is showing\n    if (this.showContextMenu) {\n      this.closeContextMenu();\n      return;\n    }\n    switch (this.circleState) {\n      case 'guest':\n        this.openLoginModal();\n        break;\n      case 'authenticated':\n        this.openMessageComposer();\n        break;\n      case 'unread':\n        this.openMessagesViewer();\n        break;\n      case 'composing':\n        // Already composing, do nothing or close\n        break;\n    }\n  }\n  // Right click handler\n  onCircleRightClick(event) {\n    event.preventDefault();\n    // Only show context menu for authenticated users\n    if (this.circleState === 'guest') return;\n    this.showContextMenu = true;\n    this.contextMenuPosition = {\n      x: event.clientX,\n      y: event.clientY\n    };\n  }\n  // Touch event handlers for long press\n  onCircleTouchStart(event) {\n    // Only for authenticated users\n    if (this.circleState === 'guest') return;\n    this.longPressTimer = setTimeout(() => {\n      const touch = event.touches[0];\n      this.showContextMenu = true;\n      this.contextMenuPosition = {\n        x: touch.clientX,\n        y: touch.clientY\n      };\n      // Provide haptic feedback if available\n      if (navigator.vibrate) {\n        navigator.vibrate(50);\n      }\n    }, this.longPressDuration);\n  }\n  onCircleTouchEnd() {\n    if (this.longPressTimer) {\n      clearTimeout(this.longPressTimer);\n      this.longPressTimer = null;\n    }\n  }\n  onCircleTouchMove() {\n    // Cancel long press if user moves finger\n    if (this.longPressTimer) {\n      clearTimeout(this.longPressTimer);\n      this.longPressTimer = null;\n    }\n  }\n  // Authentication methods\n  openLoginModal() {\n    this.showLoginModal = true;\n    this.loginCredentials = {\n      email: '',\n      secretWord: ''\n    };\n    this.loginError = '';\n  }\n  closeLoginModal() {\n    this.showLoginModal = false;\n    this.loginCredentials = {\n      email: '',\n      secretWord: ''\n    };\n    this.loginError = '';\n  }\n  onLoginInputChange() {\n    // Auto-submit when both fields are valid\n    if (this.isValidCredentials()) {\n      this.performLogin();\n    }\n  }\n  isValidCredentials() {\n    const emailValid = this.loginCredentials.email.includes('@') && this.loginCredentials.email.includes('.');\n    const secretWordValid = this.loginCredentials.secretWord.length >= 4 && /[A-Z]/.test(this.loginCredentials.secretWord) && /[a-z]/.test(this.loginCredentials.secretWord) && /[0-9]/.test(this.loginCredentials.secretWord) && /[^A-Za-z0-9]/.test(this.loginCredentials.secretWord);\n    return emailValid && secretWordValid;\n  }\n  performLogin() {\n    if (this.isLoading) return;\n    this.isLoading = true;\n    this.loginError = '';\n    this.authService.login(this.loginCredentials.email, this.loginCredentials.secretWord).pipe(takeUntil(this.destroy$)).subscribe({\n      next: response => {\n        this.isLoading = false;\n        // Auth state will be updated via authState$ subscription\n      },\n      error: error => {\n        this.isLoading = false;\n        this.loginError = error.message || 'Authentication failed';\n      }\n    });\n  }\n  // Message composition methods\n  openMessageComposer() {\n    this.showMessageModal = true;\n    this.messageContent = '';\n    this.selectedRecipient = '';\n    this.selectedGroup = '';\n    this.messageType = 'direct';\n    this.recipientSearchQuery = '';\n    this.filteredContacts = [...this.contacts];\n    this.filteredGroups = [...this.groups];\n    this.circleState = 'composing';\n  }\n  closeMessageComposer() {\n    this.showMessageModal = false;\n    this.messageContent = '';\n    this.selectedRecipient = '';\n    this.selectedGroup = '';\n    this.recipientSearchQuery = '';\n    this.circleState = 'authenticated';\n  }\n  sendMessage() {\n    if (!this.messageContent.trim()) return;\n    // Validate recipient selection\n    if (this.messageType === 'direct' && !this.selectedRecipient) {\n      this.notificationService.showNotification('Please select a recipient', 'warning');\n      return;\n    }\n    if (this.messageType === 'group' && !this.selectedGroup) {\n      this.notificationService.showNotification('Please select a group', 'warning');\n      return;\n    }\n    const message = {\n      content: this.messageContent.trim(),\n      timestamp: new Date(),\n      sender: this.currentUser?.username || 'Unknown',\n      recipient: this.messageType === 'direct' ? this.selectedRecipient : undefined,\n      groupId: this.messageType === 'group' ? this.selectedGroup : undefined\n    };\n    this.messageService.sendMessage(message).pipe(takeUntil(this.destroy$)).subscribe({\n      next: () => {\n        this.closeMessageComposer();\n        this.notificationService.showNotification('Message sent');\n      },\n      error: error => {\n        console.error('Failed to send message:', error);\n        this.notificationService.showNotification('Failed to send message', 'error');\n      }\n    });\n  }\n  // Recipient selection methods\n  onRecipientSearchChange() {\n    const query = this.recipientSearchQuery.toLowerCase();\n    if (this.messageType === 'direct') {\n      this.filteredContacts = this.contacts.filter(contact => contact.username.toLowerCase().includes(query) || contact.email.toLowerCase().includes(query));\n    } else {\n      this.filteredGroups = this.groups.filter(group => group.name.toLowerCase().includes(query));\n    }\n  }\n  selectContact(contact) {\n    this.selectedRecipient = contact.id;\n    this.recipientSearchQuery = contact.username;\n    this.filteredContacts = [];\n  }\n  selectGroup(group) {\n    this.selectedGroup = group.id;\n    this.recipientSearchQuery = group.name;\n    this.filteredGroups = [];\n  }\n  switchMessageType(type) {\n    this.messageType = type;\n    this.selectedRecipient = '';\n    this.selectedGroup = '';\n    this.recipientSearchQuery = '';\n    this.onRecipientSearchChange();\n  }\n  getSelectedRecipientName() {\n    if (this.messageType === 'direct' && this.selectedRecipient) {\n      const contact = this.contacts.find(c => c.id === this.selectedRecipient);\n      return contact?.username || 'Unknown';\n    }\n    if (this.messageType === 'group' && this.selectedGroup) {\n      const group = this.groups.find(g => g.id === this.selectedGroup);\n      return group?.name || 'Unknown Group';\n    }\n    return '';\n  }\n  isMessageValid() {\n    const hasContent = this.messageContent.trim().length > 0;\n    const hasRecipient = this.messageType === 'direct' ? !!this.selectedRecipient : !!this.selectedGroup;\n    return hasContent && hasRecipient;\n  }\n  // Message viewing methods\n  openMessagesViewer() {\n    this.showMessagesModal = true;\n    this.markMessagesAsRead();\n  }\n  closeMessagesViewer() {\n    this.showMessagesModal = false;\n  }\n  markMessagesAsRead() {\n    this.messageService.markAllAsRead().pipe(takeUntil(this.destroy$)).subscribe(() => {\n      this.updateUnreadCount();\n    });\n  }\n  // Context menu methods\n  closeContextMenu() {\n    this.showContextMenu = false;\n  }\n  openAccountSettings() {\n    this.showAccountSettings = true;\n    this.showContextMenu = false;\n    this.loadUserProfile();\n  }\n  closeAccountSettings() {\n    this.showAccountSettings = false;\n  }\n  // Account settings methods\n  loadUserProfile() {\n    if (this.currentUser) {\n      this.userProfile = {\n        avatar: this.currentUser.avatar || '',\n        email: this.currentUser.email || '',\n        phoneNumber: this.currentUser.phoneNumber || ''\n      };\n    }\n  }\n  onAvatarChange(event) {\n    const input = event.target;\n    if (input.files && input.files[0]) {\n      const file = input.files[0];\n      // Validate file type\n      if (!file.type.startsWith('image/')) {\n        this.notificationService.showNotification('Please select an image file', 'error');\n        return;\n      }\n      // Validate file size (max 2MB)\n      if (file.size > 2 * 1024 * 1024) {\n        this.notificationService.showNotification('Image must be smaller than 2MB', 'error');\n        return;\n      }\n      const reader = new FileReader();\n      reader.onload = e => {\n        this.userProfile.avatar = e.target?.result;\n        this.saveAvatarChange();\n      };\n      reader.readAsDataURL(file);\n    }\n  }\n  saveAvatarChange() {\n    // TODO: Implement API call to save avatar\n    this.notificationService.showNotification('Avatar updated successfully', 'success');\n  }\n  // Logout\n  logout() {\n    this.closeContextMenu();\n    this.authService.logout();\n  }\n  // Keyboard shortcuts\n  onKeyDown(event) {\n    // Escape key closes modals\n    if (event.key === 'Escape') {\n      this.closeAllModals();\n    }\n    // Enter key in login modal\n    if (event.key === 'Enter' && this.showLoginModal) {\n      if (this.isValidCredentials()) {\n        this.performLogin();\n      }\n    }\n    // Enter key in message modal\n    if (event.key === 'Enter' && this.showMessageModal && !event.shiftKey) {\n      event.preventDefault();\n      this.sendMessage();\n    }\n  }\n  // Click outside handler to close context menu\n  onDocumentClick(event) {\n    // Close context menu when clicking outside\n    if (this.showContextMenu) {\n      this.closeContextMenu();\n    }\n  }\n  closeAllModals() {\n    this.showLoginModal = false;\n    this.showMessageModal = false;\n    this.showMessagesModal = false;\n    this.showContextMenu = false;\n    this.showAccountSettings = false;\n    if (this.circleState === 'composing') {\n      this.circleState = 'authenticated';\n    }\n  }\n  // Utility methods\n  getCircleClass() {\n    return `circle-${this.circleState}`;\n  }\n  getCircleTitle() {\n    switch (this.circleState) {\n      case 'guest':\n        return 'Click to sign in';\n      case 'authenticated':\n        return 'Click to compose message';\n      case 'unread':\n        return `Click to view ${this.unreadCount} unread message(s)`;\n      case 'composing':\n        return 'Composing message...';\n      default:\n        return '';\n    }\n  }\n  trackMessage(index, message) {\n    return message.id;\n  }\n  static {\n    this.ɵfac = function QscMainComponent_Factory(t) {\n      return new (t || QscMainComponent)(i0.ɵɵdirectiveInject(i1.AuthService), i0.ɵɵdirectiveInject(i2.MessageService), i0.ɵɵdirectiveInject(i3.NotificationService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: QscMainComponent,\n      selectors: [[\"app-qsc-main\"]],\n      hostBindings: function QscMainComponent_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"keydown\", function QscMainComponent_keydown_HostBindingHandler($event) {\n            return ctx.onKeyDown($event);\n          }, false, i0.ɵɵresolveDocument)(\"click\", function QscMainComponent_click_HostBindingHandler($event) {\n            return ctx.onDocumentClick($event);\n          }, false, i0.ɵɵresolveDocument);\n        }\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 13,\n      vars: 12,\n      consts: [[1, \"qsc-container\"], [1, \"circle-container\"], [1, \"qsc-circle\", 3, \"click\", \"contextmenu\", \"touchstart\", \"touchend\", \"touchmove\", \"title\"], [1, \"circle-inner\"], [\"class\", \"wind-effect\", 4, \"ngIf\"], [\"class\", \"unread-indicator\", 4, \"ngIf\"], [\"class\", \"user-info\", 4, \"ngIf\"], [\"class\", \"context-menu\", 3, \"left\", \"top\", \"click\", 4, \"ngIf\"], [\"class\", \"modal-overlay\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"keyboard-hints\", 4, \"ngIf\"], [1, \"wind-effect\"], [1, \"unread-indicator\"], [1, \"user-info\"], [\"title\", \"Logout\", 1, \"logout-btn\", 3, \"click\"], [1, \"context-menu\", 3, \"click\"], [1, \"context-menu-item\", 3, \"click\"], [1, \"menu-icon\"], [1, \"menu-text\"], [1, \"context-menu-divider\"], [1, \"context-menu-item\", \"logout-item\", 3, \"click\"], [1, \"modal-overlay\", 3, \"click\"], [1, \"modal\", \"login-modal\", 3, \"click\"], [1, \"modal-header\"], [1, \"close-btn\", 3, \"click\"], [1, \"modal-content\"], [1, \"form-group\"], [\"for\", \"email\"], [\"type\", \"email\", \"id\", \"email\", \"placeholder\", \"Enter your email\", \"autocomplete\", \"email\", 3, \"ngModelChange\", \"input\", \"ngModel\", \"disabled\"], [\"for\", \"secretWord\"], [\"type\", \"password\", \"id\", \"secretWord\", \"placeholder\", \"4+ chars: A-Z, a-z, 0-9, symbol\", \"autocomplete\", \"current-password\", 3, \"ngModelChange\", \"input\", \"ngModel\", \"disabled\"], [\"class\", \"error-message\", 4, \"ngIf\"], [\"class\", \"loading-indicator\", 4, \"ngIf\"], [\"class\", \"auth-info\", 4, \"ngIf\"], [1, \"error-message\"], [1, \"loading-indicator\"], [1, \"spinner\"], [1, \"auth-info\"], [1, \"auto-submit-hint\"], [1, \"modal\", \"message-modal\", 3, \"click\"], [1, \"message-type-selector\"], [1, \"type-btn\", 3, \"click\"], [\"for\", \"recipientSearch\"], [1, \"recipient-selector\"], [\"type\", \"text\", \"id\", \"recipientSearch\", \"autocomplete\", \"off\", 3, \"ngModelChange\", \"input\", \"ngModel\", \"placeholder\"], [\"class\", \"selected-recipient\", 4, \"ngIf\"], [\"class\", \"recipient-dropdown\", 4, \"ngIf\"], [\"for\", \"messageContent\"], [\"id\", \"messageContent\", \"placeholder\", \"Type your message here...\", \"rows\", \"6\", \"maxlength\", \"1000\", 3, \"ngModelChange\", \"ngModel\"], [1, \"char-count\"], [1, \"message-actions\"], [1, \"btn\", \"btn-primary\", 3, \"click\", \"disabled\"], [1, \"btn\", \"btn-secondary\", 3, \"click\"], [1, \"send-hint\"], [1, \"selected-recipient\"], [1, \"recipient-name\"], [\"title\", \"Clear selection\", 1, \"clear-recipient\", 3, \"click\"], [1, \"recipient-dropdown\"], [\"class\", \"recipient-item\", 3, \"hidden\", \"click\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"no-results\", 4, \"ngIf\"], [1, \"recipient-item\", 3, \"click\", \"hidden\"], [1, \"contact-info\"], [1, \"contact-name\"], [1, \"contact-email\"], [1, \"contact-status\"], [1, \"status-indicator\"], [1, \"status-text\"], [1, \"group-info\"], [1, \"group-name\"], [1, \"group-members\"], [1, \"group-status\"], [1, \"no-results\"], [1, \"modal\", \"messages-modal\", 3, \"click\"], [\"class\", \"messages-list\", 4, \"ngIf\"], [\"class\", \"empty-state\", 4, \"ngIf\"], [1, \"messages-list\"], [\"class\", \"message-item\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [1, \"message-item\"], [1, \"message-header\"], [1, \"sender\"], [1, \"timestamp\"], [1, \"message-content\"], [1, \"empty-state\"], [1, \"hint\"], [1, \"modal\", \"account-settings-modal\", 3, \"click\"], [1, \"avatar-section\"], [1, \"avatar-container\"], [1, \"avatar-display\"], [\"alt\", \"Profile Avatar\", \"class\", \"avatar-image\", 3, \"src\", 4, \"ngIf\"], [\"class\", \"avatar-placeholder\", 4, \"ngIf\"], [1, \"avatar-actions\"], [\"for\", \"avatarInput\", 1, \"avatar-upload-btn\"], [\"type\", \"file\", \"id\", \"avatarInput\", \"accept\", \"image/*\", 2, \"display\", \"none\", 3, \"change\"], [1, \"profile-info\"], [1, \"readonly-field\"], [1, \"field-note\"], [1, \"security-info\"], [1, \"security-item\"], [1, \"security-label\"], [1, \"security-value\"], [1, \"account-actions\"], [\"alt\", \"Profile Avatar\", 1, \"avatar-image\", 3, \"src\"], [1, \"avatar-placeholder\"], [1, \"avatar-initials\"], [1, \"keyboard-hints\"]],\n      template: function QscMainComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2);\n          i0.ɵɵlistener(\"click\", function QscMainComponent_Template_div_click_2_listener() {\n            return ctx.onCircleClick();\n          })(\"contextmenu\", function QscMainComponent_Template_div_contextmenu_2_listener($event) {\n            return ctx.onCircleRightClick($event);\n          })(\"touchstart\", function QscMainComponent_Template_div_touchstart_2_listener($event) {\n            return ctx.onCircleTouchStart($event);\n          })(\"touchend\", function QscMainComponent_Template_div_touchend_2_listener() {\n            return ctx.onCircleTouchEnd();\n          })(\"touchmove\", function QscMainComponent_Template_div_touchmove_2_listener() {\n            return ctx.onCircleTouchMove();\n          });\n          i0.ɵɵelementStart(3, \"div\", 3);\n          i0.ɵɵtemplate(4, QscMainComponent_div_4_Template, 1, 0, \"div\", 4)(5, QscMainComponent_div_5_Template, 2, 1, \"div\", 5);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(6, QscMainComponent_div_6_Template, 5, 1, \"div\", 6)(7, QscMainComponent_div_7_Template, 12, 4, \"div\", 7);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(8, QscMainComponent_div_8_Template, 19, 7, \"div\", 8)(9, QscMainComponent_div_9_Template, 34, 12, \"div\", 8)(10, QscMainComponent_div_10_Template, 10, 2, \"div\", 8)(11, QscMainComponent_div_11_Template, 61, 5, \"div\", 8)(12, QscMainComponent_div_12_Template, 3, 0, \"div\", 9);\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(2);\n          i0.ɵɵclassMap(ctx.getCircleClass());\n          i0.ɵɵproperty(\"title\", ctx.getCircleTitle());\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.circleState !== \"guest\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.circleState === \"unread\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.currentUser);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.showContextMenu);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.showLoginModal);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.showMessageModal);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.showMessagesModal);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.showAccountSettings);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.showLoginModal && !ctx.showMessageModal && !ctx.showMessagesModal && !ctx.showAccountSettings);\n        }\n      },\n      dependencies: [CommonModule, i4.NgForOf, i4.NgIf, i4.DatePipe, FormsModule, i5.DefaultValueAccessor, i5.NgControlStatus, i5.MaxLengthValidator, i5.NgModel],\n      styles: [\".qsc-container[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  width: 100vw;\\n  height: 100vh;\\n  background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  overflow: hidden;\\n  font-family: \\\"Inter\\\", -apple-system, BlinkMacSystemFont, sans-serif;\\n}\\n\\n.circle-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  z-index: 1;\\n}\\n\\n.qsc-circle[_ngcontent-%COMP%] {\\n  width: 200px;\\n  height: 200px;\\n  border-radius: 50%;\\n  cursor: pointer;\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n  position: relative;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);\\n}\\n.qsc-circle[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.05);\\n  box-shadow: 0 12px 48px rgba(0, 0, 0, 0.4);\\n}\\n.qsc-circle[_ngcontent-%COMP%]:active {\\n  transform: scale(0.98);\\n}\\n.qsc-circle.circle-guest[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #ff4757 0%, #ff3742 100%);\\n  border: 3px solid rgba(255, 71, 87, 0.3);\\n}\\n.qsc-circle.circle-guest[_ngcontent-%COMP%]:hover {\\n  box-shadow: 0 12px 48px rgba(255, 71, 87, 0.4);\\n}\\n.qsc-circle.circle-authenticated[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #3742fa 0%, #2f3542 100%);\\n  border: 3px solid rgba(55, 66, 250, 0.3);\\n}\\n.qsc-circle.circle-authenticated[_ngcontent-%COMP%]:hover {\\n  box-shadow: 0 12px 48px rgba(55, 66, 250, 0.4);\\n}\\n.qsc-circle.circle-unread[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #2ed573 0%, #1e90ff 100%);\\n  border: 3px solid rgba(46, 213, 115, 0.3);\\n  animation: _ngcontent-%COMP%_pulse 2s infinite;\\n}\\n.qsc-circle.circle-unread[_ngcontent-%COMP%]:hover {\\n  box-shadow: 0 12px 48px rgba(46, 213, 115, 0.4);\\n}\\n.qsc-circle.circle-composing[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #a55eea 0%, #8854d0 100%);\\n  border: 3px solid rgba(165, 94, 234, 0.3);\\n}\\n.qsc-circle.circle-composing[_ngcontent-%COMP%]:hover {\\n  box-shadow: 0 12px 48px rgba(165, 94, 234, 0.4);\\n}\\n\\n.circle-inner[_ngcontent-%COMP%] {\\n  position: relative;\\n  width: 100%;\\n  height: 100%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n\\n.wind-effect[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 20%;\\n  right: 15%;\\n  width: 60px;\\n  height: 60px;\\n  opacity: 0.3;\\n}\\n.wind-effect[_ngcontent-%COMP%]::before, .wind-effect[_ngcontent-%COMP%]::after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  background: rgba(255, 255, 255, 0.6);\\n  border-radius: 50%;\\n  animation: _ngcontent-%COMP%_windFlow 3s ease-in-out infinite;\\n}\\n.wind-effect[_ngcontent-%COMP%]::before {\\n  width: 8px;\\n  height: 8px;\\n  top: 10px;\\n  left: 0;\\n  animation-delay: 0s;\\n}\\n.wind-effect[_ngcontent-%COMP%]::after {\\n  width: 6px;\\n  height: 6px;\\n  top: 25px;\\n  left: 15px;\\n  animation-delay: 1s;\\n}\\n\\n.unread-indicator[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: -10px;\\n  right: -10px;\\n  background: #ff4757;\\n  color: white;\\n  border-radius: 50%;\\n  width: 40px;\\n  height: 40px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  font-weight: 600;\\n  font-size: 14px;\\n  border: 3px solid #0f0f23;\\n  animation: _ngcontent-%COMP%_bounce 1s infinite;\\n}\\n\\n.user-info[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 20px;\\n  right: 20px;\\n  display: flex;\\n  align-items: center;\\n  gap: 10px;\\n  color: rgba(255, 255, 255, 0.7);\\n  font-size: 14px;\\n  z-index: 10;\\n}\\n.user-info[_ngcontent-%COMP%]   .logout-btn[_ngcontent-%COMP%] {\\n  background: none;\\n  border: none;\\n  color: rgba(255, 255, 255, 0.5);\\n  font-size: 20px;\\n  cursor: pointer;\\n  padding: 5px;\\n  border-radius: 50%;\\n  transition: all 0.2s ease;\\n}\\n.user-info[_ngcontent-%COMP%]   .logout-btn[_ngcontent-%COMP%]:hover {\\n  color: #ff4757;\\n  background: rgba(255, 71, 87, 0.1);\\n}\\n\\n.modal-overlay[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  width: 100vw;\\n  height: 100vh;\\n  background: rgba(0, 0, 0, 0.8);\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  z-index: 1000;\\n  animation: _ngcontent-%COMP%_fadeIn 0.2s ease;\\n}\\n\\n.modal[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.95);\\n  -webkit-backdrop-filter: blur(20px);\\n          backdrop-filter: blur(20px);\\n  border-radius: 16px;\\n  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);\\n  max-width: 400px;\\n  width: 90vw;\\n  max-height: 80vh;\\n  overflow: hidden;\\n  animation: _ngcontent-%COMP%_slideUp 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n}\\n\\n.modal-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 20px 24px;\\n  border-bottom: 1px solid rgba(0, 0, 0, 0.1);\\n}\\n.modal-header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 20px;\\n  font-weight: 600;\\n  color: #2f3542;\\n}\\n.modal-header[_ngcontent-%COMP%]   .close-btn[_ngcontent-%COMP%] {\\n  background: none;\\n  border: none;\\n  font-size: 24px;\\n  color: #a4b0be;\\n  cursor: pointer;\\n  padding: 0;\\n  width: 30px;\\n  height: 30px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  border-radius: 50%;\\n  transition: all 0.2s ease;\\n}\\n.modal-header[_ngcontent-%COMP%]   .close-btn[_ngcontent-%COMP%]:hover {\\n  background: rgba(0, 0, 0, 0.1);\\n  color: #2f3542;\\n}\\n\\n.modal-content[_ngcontent-%COMP%] {\\n  padding: 24px;\\n}\\n\\n.form-group[_ngcontent-%COMP%] {\\n  margin-bottom: 20px;\\n}\\n.form-group[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  display: block;\\n  margin-bottom: 8px;\\n  font-weight: 500;\\n  color: #2f3542;\\n  font-size: 14px;\\n}\\n.form-group[_ngcontent-%COMP%]   input[_ngcontent-%COMP%], .form-group[_ngcontent-%COMP%]   textarea[_ngcontent-%COMP%] {\\n  width: 100%;\\n  padding: 12px 16px;\\n  border: 2px solid #e1e8ed;\\n  border-radius: 8px;\\n  font-size: 16px;\\n  transition: all 0.2s ease;\\n  background: white;\\n}\\n.form-group[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]:focus, .form-group[_ngcontent-%COMP%]   textarea[_ngcontent-%COMP%]:focus {\\n  outline: none;\\n  border-color: #3742fa;\\n  box-shadow: 0 0 0 3px rgba(55, 66, 250, 0.1);\\n}\\n.form-group[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]:disabled, .form-group[_ngcontent-%COMP%]   textarea[_ngcontent-%COMP%]:disabled {\\n  background: #f8f9fa;\\n  color: #a4b0be;\\n}\\n.form-group[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]::placeholder, .form-group[_ngcontent-%COMP%]   textarea[_ngcontent-%COMP%]::placeholder {\\n  color: #a4b0be;\\n}\\n.form-group[_ngcontent-%COMP%]   textarea[_ngcontent-%COMP%] {\\n  resize: vertical;\\n  min-height: 120px;\\n  font-family: inherit;\\n}\\n\\n.char-count[_ngcontent-%COMP%] {\\n  text-align: right;\\n  font-size: 12px;\\n  color: #a4b0be;\\n  margin-top: 4px;\\n}\\n\\n.btn[_ngcontent-%COMP%] {\\n  padding: 12px 24px;\\n  border: none;\\n  border-radius: 8px;\\n  font-size: 14px;\\n  font-weight: 500;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n}\\n.btn.btn-primary[_ngcontent-%COMP%] {\\n  background: #3742fa;\\n  color: white;\\n}\\n.btn.btn-primary[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  background: #2f3542;\\n  transform: translateY(-1px);\\n}\\n.btn.btn-primary[_ngcontent-%COMP%]:disabled {\\n  background: #a4b0be;\\n  cursor: not-allowed;\\n}\\n.btn.btn-secondary[_ngcontent-%COMP%] {\\n  background: #f1f2f6;\\n  color: #2f3542;\\n}\\n.btn.btn-secondary[_ngcontent-%COMP%]:hover {\\n  background: #e1e8ed;\\n}\\n\\n.message-type-selector[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 8px;\\n  margin-bottom: 20px;\\n}\\n.message-type-selector[_ngcontent-%COMP%]   .type-btn[_ngcontent-%COMP%] {\\n  flex: 1;\\n  padding: 10px 16px;\\n  border: 2px solid #e1e8ed;\\n  border-radius: 8px;\\n  background: white;\\n  color: #57606f;\\n  font-size: 14px;\\n  font-weight: 500;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n}\\n.message-type-selector[_ngcontent-%COMP%]   .type-btn[_ngcontent-%COMP%]:hover {\\n  border-color: #3742fa;\\n  background: rgba(55, 66, 250, 0.05);\\n}\\n.message-type-selector[_ngcontent-%COMP%]   .type-btn.active[_ngcontent-%COMP%] {\\n  border-color: #3742fa;\\n  background: #3742fa;\\n  color: white;\\n}\\n\\n.recipient-selector[_ngcontent-%COMP%] {\\n  position: relative;\\n}\\n\\n.selected-recipient[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  padding: 8px 12px;\\n  background: rgba(55, 66, 250, 0.1);\\n  border: 1px solid #3742fa;\\n  border-radius: 6px;\\n  margin-top: 8px;\\n}\\n.selected-recipient[_ngcontent-%COMP%]   .recipient-name[_ngcontent-%COMP%] {\\n  color: #3742fa;\\n  font-weight: 500;\\n  font-size: 14px;\\n}\\n.selected-recipient[_ngcontent-%COMP%]   .clear-recipient[_ngcontent-%COMP%] {\\n  background: none;\\n  border: none;\\n  color: #3742fa;\\n  font-size: 16px;\\n  cursor: pointer;\\n  padding: 0;\\n  width: 20px;\\n  height: 20px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  border-radius: 50%;\\n  transition: all 0.2s ease;\\n}\\n.selected-recipient[_ngcontent-%COMP%]   .clear-recipient[_ngcontent-%COMP%]:hover {\\n  background: rgba(55, 66, 250, 0.2);\\n}\\n\\n.recipient-dropdown[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 100%;\\n  left: 0;\\n  right: 0;\\n  background: white;\\n  border: 1px solid #e1e8ed;\\n  border-radius: 8px;\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\\n  max-height: 200px;\\n  overflow-y: auto;\\n  z-index: 1000;\\n  margin-top: 4px;\\n}\\n\\n.recipient-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  padding: 12px 16px;\\n  cursor: pointer;\\n  border-bottom: 1px solid rgba(0, 0, 0, 0.05);\\n  transition: background 0.2s ease;\\n}\\n.recipient-item[_ngcontent-%COMP%]:hover {\\n  background: rgba(55, 66, 250, 0.05);\\n}\\n.recipient-item[_ngcontent-%COMP%]:last-child {\\n  border-bottom: none;\\n}\\n\\n.contact-info[_ngcontent-%COMP%], .group-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  flex: 1;\\n}\\n.contact-info[_ngcontent-%COMP%]   .contact-name[_ngcontent-%COMP%], .contact-info[_ngcontent-%COMP%]   .group-name[_ngcontent-%COMP%], .group-info[_ngcontent-%COMP%]   .contact-name[_ngcontent-%COMP%], .group-info[_ngcontent-%COMP%]   .group-name[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: #2f3542;\\n  font-size: 14px;\\n  margin-bottom: 2px;\\n}\\n.contact-info[_ngcontent-%COMP%]   .contact-email[_ngcontent-%COMP%], .contact-info[_ngcontent-%COMP%]   .group-members[_ngcontent-%COMP%], .group-info[_ngcontent-%COMP%]   .contact-email[_ngcontent-%COMP%], .group-info[_ngcontent-%COMP%]   .group-members[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: #a4b0be;\\n}\\n\\n.contact-status[_ngcontent-%COMP%], .group-status[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 6px;\\n}\\n.contact-status[_ngcontent-%COMP%]   .status-indicator[_ngcontent-%COMP%], .group-status[_ngcontent-%COMP%]   .status-indicator[_ngcontent-%COMP%] {\\n  width: 8px;\\n  height: 8px;\\n  border-radius: 50%;\\n}\\n.contact-status[_ngcontent-%COMP%]   .status-indicator.online[_ngcontent-%COMP%], .group-status[_ngcontent-%COMP%]   .status-indicator.online[_ngcontent-%COMP%] {\\n  background: #2ed573;\\n}\\n.contact-status[_ngcontent-%COMP%]   .status-indicator.offline[_ngcontent-%COMP%], .group-status[_ngcontent-%COMP%]   .status-indicator.offline[_ngcontent-%COMP%] {\\n  background: #a4b0be;\\n}\\n.contact-status[_ngcontent-%COMP%]   .status-indicator.active[_ngcontent-%COMP%], .group-status[_ngcontent-%COMP%]   .status-indicator.active[_ngcontent-%COMP%] {\\n  background: #3742fa;\\n}\\n.contact-status[_ngcontent-%COMP%]   .status-text[_ngcontent-%COMP%], .group-status[_ngcontent-%COMP%]   .status-text[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: #a4b0be;\\n}\\n\\n.no-results[_ngcontent-%COMP%] {\\n  padding: 16px;\\n  text-align: center;\\n  color: #a4b0be;\\n  font-size: 14px;\\n  font-style: italic;\\n}\\n\\n.message-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 12px;\\n  margin-top: 20px;\\n}\\n\\n.error-message[_ngcontent-%COMP%] {\\n  color: #ff4757;\\n  font-size: 14px;\\n  margin-top: 8px;\\n  padding: 8px 12px;\\n  background: rgba(255, 71, 87, 0.1);\\n  border-radius: 6px;\\n  border-left: 3px solid #ff4757;\\n}\\n\\n.auth-info[_ngcontent-%COMP%] {\\n  margin-top: 20px;\\n  text-align: center;\\n}\\n.auth-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 8px 0;\\n  font-size: 13px;\\n  color: #57606f;\\n}\\n.auth-info[_ngcontent-%COMP%]   p.auto-submit-hint[_ngcontent-%COMP%] {\\n  font-style: italic;\\n  color: #a4b0be;\\n}\\n\\n.send-hint[_ngcontent-%COMP%] {\\n  margin-top: 16px;\\n  text-align: center;\\n}\\n.send-hint[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: #a4b0be;\\n  margin: 0;\\n}\\n\\n.loading-indicator[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  gap: 12px;\\n  padding: 20px;\\n}\\n.loading-indicator[_ngcontent-%COMP%]   .spinner[_ngcontent-%COMP%] {\\n  width: 20px;\\n  height: 20px;\\n  border: 2px solid #e1e8ed;\\n  border-top: 2px solid #3742fa;\\n  border-radius: 50%;\\n  animation: _ngcontent-%COMP%_spin 1s linear infinite;\\n}\\n.loading-indicator[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  color: #57606f;\\n  font-size: 14px;\\n}\\n\\n.messages-list[_ngcontent-%COMP%] {\\n  max-height: 400px;\\n  overflow-y: auto;\\n  margin: -8px;\\n  padding: 8px;\\n}\\n\\n.message-item[_ngcontent-%COMP%] {\\n  padding: 16px;\\n  border-bottom: 1px solid rgba(0, 0, 0, 0.05);\\n}\\n.message-item[_ngcontent-%COMP%]:last-child {\\n  border-bottom: none;\\n}\\n.message-item[_ngcontent-%COMP%]   .message-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 8px;\\n}\\n.message-item[_ngcontent-%COMP%]   .message-header[_ngcontent-%COMP%]   .sender[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #2f3542;\\n  font-size: 14px;\\n}\\n.message-item[_ngcontent-%COMP%]   .message-header[_ngcontent-%COMP%]   .timestamp[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: #a4b0be;\\n}\\n.message-item[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%] {\\n  color: #57606f;\\n  line-height: 1.5;\\n  word-wrap: break-word;\\n}\\n\\n.empty-state[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 40px 20px;\\n  color: #a4b0be;\\n}\\n.empty-state[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 8px 0;\\n}\\n.empty-state[_ngcontent-%COMP%]   p.hint[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-style: italic;\\n}\\n\\n.context-menu[_ngcontent-%COMP%] {\\n  position: fixed;\\n  background: rgba(255, 255, 255, 0.95);\\n  -webkit-backdrop-filter: blur(20px);\\n          backdrop-filter: blur(20px);\\n  border-radius: 12px;\\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);\\n  padding: 8px 0;\\n  min-width: 180px;\\n  z-index: 2000;\\n  animation: _ngcontent-%COMP%_contextMenuSlide 0.2s cubic-bezier(0.4, 0, 0.2, 1);\\n}\\n.context-menu[_ngcontent-%COMP%]   .context-menu-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n  padding: 12px 16px;\\n  cursor: pointer;\\n  transition: background 0.2s ease;\\n  color: #2f3542;\\n}\\n.context-menu[_ngcontent-%COMP%]   .context-menu-item[_ngcontent-%COMP%]:hover {\\n  background: rgba(55, 66, 250, 0.1);\\n}\\n.context-menu[_ngcontent-%COMP%]   .context-menu-item.logout-item[_ngcontent-%COMP%] {\\n  color: #ff4757;\\n}\\n.context-menu[_ngcontent-%COMP%]   .context-menu-item.logout-item[_ngcontent-%COMP%]:hover {\\n  background: rgba(255, 71, 87, 0.1);\\n}\\n.context-menu[_ngcontent-%COMP%]   .context-menu-item[_ngcontent-%COMP%]   .menu-icon[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  width: 20px;\\n  text-align: center;\\n}\\n.context-menu[_ngcontent-%COMP%]   .context-menu-item[_ngcontent-%COMP%]   .menu-text[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 500;\\n}\\n.context-menu[_ngcontent-%COMP%]   .context-menu-divider[_ngcontent-%COMP%] {\\n  height: 1px;\\n  background: rgba(0, 0, 0, 0.1);\\n  margin: 4px 0;\\n}\\n\\n.account-settings-modal[_ngcontent-%COMP%] {\\n  max-width: 500px;\\n  width: 90vw;\\n}\\n\\n.avatar-section[_ngcontent-%COMP%] {\\n  text-align: center;\\n  margin-bottom: 32px;\\n}\\n.avatar-section[_ngcontent-%COMP%]   .avatar-container[_ngcontent-%COMP%] {\\n  display: inline-block;\\n}\\n.avatar-section[_ngcontent-%COMP%]   .avatar-display[_ngcontent-%COMP%] {\\n  width: 120px;\\n  height: 120px;\\n  border-radius: 50%;\\n  margin: 0 auto 16px;\\n  position: relative;\\n  overflow: hidden;\\n  border: 4px solid #e1e8ed;\\n}\\n.avatar-section[_ngcontent-%COMP%]   .avatar-display[_ngcontent-%COMP%]   .avatar-image[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  object-fit: cover;\\n}\\n.avatar-section[_ngcontent-%COMP%]   .avatar-display[_ngcontent-%COMP%]   .avatar-placeholder[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  background: linear-gradient(135deg, #3742fa 0%, #2f3542 100%);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n.avatar-section[_ngcontent-%COMP%]   .avatar-display[_ngcontent-%COMP%]   .avatar-placeholder[_ngcontent-%COMP%]   .avatar-initials[_ngcontent-%COMP%] {\\n  font-size: 48px;\\n  font-weight: 600;\\n  color: white;\\n}\\n.avatar-section[_ngcontent-%COMP%]   .avatar-actions[_ngcontent-%COMP%]   .avatar-upload-btn[_ngcontent-%COMP%] {\\n  display: inline-block;\\n  padding: 8px 16px;\\n  background: #3742fa;\\n  color: white;\\n  border-radius: 6px;\\n  font-size: 14px;\\n  font-weight: 500;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n}\\n.avatar-section[_ngcontent-%COMP%]   .avatar-actions[_ngcontent-%COMP%]   .avatar-upload-btn[_ngcontent-%COMP%]:hover {\\n  background: #2f3542;\\n  transform: translateY(-1px);\\n}\\n\\n.profile-info[_ngcontent-%COMP%] {\\n  margin-bottom: 32px;\\n}\\n.profile-info[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%] {\\n  margin-bottom: 20px;\\n}\\n.profile-info[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  display: block;\\n  margin-bottom: 8px;\\n  font-weight: 500;\\n  color: #2f3542;\\n  font-size: 14px;\\n}\\n.profile-info[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .readonly-field[_ngcontent-%COMP%] {\\n  padding: 12px 16px;\\n  background: #f8f9fa;\\n  border: 2px solid #e1e8ed;\\n  border-radius: 8px;\\n  color: #57606f;\\n}\\n.profile-info[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .readonly-field[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  display: block;\\n}\\n.profile-info[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .readonly-field[_ngcontent-%COMP%]   span.field-note[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: #a4b0be;\\n  margin-top: 4px;\\n  font-style: italic;\\n}\\n\\n.security-info[_ngcontent-%COMP%] {\\n  background: rgba(55, 66, 250, 0.05);\\n  border: 1px solid rgba(55, 66, 250, 0.2);\\n  border-radius: 8px;\\n  padding: 20px;\\n  margin-bottom: 24px;\\n}\\n.security-info[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0 0 16px 0;\\n  font-size: 16px;\\n  color: #2f3542;\\n  font-weight: 600;\\n}\\n.security-info[_ngcontent-%COMP%]   .security-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 12px;\\n}\\n.security-info[_ngcontent-%COMP%]   .security-item[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0;\\n}\\n.security-info[_ngcontent-%COMP%]   .security-item[_ngcontent-%COMP%]   .security-label[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: #57606f;\\n  font-weight: 500;\\n}\\n.security-info[_ngcontent-%COMP%]   .security-item[_ngcontent-%COMP%]   .security-value[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: #3742fa;\\n  font-weight: 500;\\n  text-align: right;\\n  flex: 1;\\n  margin-left: 16px;\\n}\\n\\n.account-actions[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding-top: 16px;\\n  border-top: 1px solid rgba(0, 0, 0, 0.1);\\n}\\n\\n.keyboard-hints[_ngcontent-%COMP%] {\\n  position: fixed;\\n  bottom: 20px;\\n  left: 50%;\\n  transform: translateX(-50%);\\n  color: rgba(255, 255, 255, 0.4);\\n  font-size: 12px;\\n  z-index: 5;\\n}\\n\\n@keyframes _ngcontent-%COMP%_fadeIn {\\n  from {\\n    opacity: 0;\\n  }\\n  to {\\n    opacity: 1;\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_slideUp {\\n  from {\\n    opacity: 0;\\n    transform: translateY(20px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_pulse {\\n  0%, 100% {\\n    transform: scale(1);\\n  }\\n  50% {\\n    transform: scale(1.02);\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_bounce {\\n  0%, 20%, 50%, 80%, 100% {\\n    transform: translateY(0);\\n  }\\n  40% {\\n    transform: translateY(-5px);\\n  }\\n  60% {\\n    transform: translateY(-3px);\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_windFlow {\\n  0% {\\n    transform: translateX(0) translateY(0) scale(1);\\n    opacity: 0.3;\\n  }\\n  50% {\\n    transform: translateX(20px) translateY(-10px) scale(0.8);\\n    opacity: 0.6;\\n  }\\n  100% {\\n    transform: translateX(40px) translateY(-20px) scale(0.5);\\n    opacity: 0;\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_spin {\\n  0% {\\n    transform: rotate(0deg);\\n  }\\n  100% {\\n    transform: rotate(360deg);\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_contextMenuSlide {\\n  from {\\n    opacity: 0;\\n    transform: scale(0.95) translateY(-10px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: scale(1) translateY(0);\\n  }\\n}\\n@media (max-width: 768px) {\\n  .qsc-circle[_ngcontent-%COMP%] {\\n    width: 150px;\\n    height: 150px;\\n  }\\n  .modal[_ngcontent-%COMP%] {\\n    width: 95vw;\\n    margin: 20px;\\n  }\\n  .user-info[_ngcontent-%COMP%] {\\n    top: 15px;\\n    right: 15px;\\n    font-size: 12px;\\n  }\\n}\\n@media (max-width: 480px) {\\n  .qsc-circle[_ngcontent-%COMP%] {\\n    width: 120px;\\n    height: 120px;\\n  }\\n  .modal-content[_ngcontent-%COMP%] {\\n    padding: 20px;\\n  }\\n  .modal-header[_ngcontent-%COMP%] {\\n    padding: 16px 20px;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "FormsModule", "Subject", "takeUntil", "i0", "ɵɵelement", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r0", "unreadCount", "ɵɵlistener", "QscMainComponent_div_6_Template_button_click_3_listener", "ɵɵrestoreView", "_r2", "ɵɵnextContext", "ɵɵresetView", "logout", "ɵɵtextInterpolate", "currentUser", "username", "QscMainComponent_div_7_Template_div_click_0_listener", "$event", "_r3", "stopPropagation", "QscMainComponent_div_7_Template_div_click_1_listener", "openAccountSettings", "QscMainComponent_div_7_Template_div_click_7_listener", "ɵɵstyleProp", "contextMenuPosition", "x", "y", "loginError", "QscMainComponent_div_8_Template_div_click_0_listener", "_r4", "closeLoginModal", "QscMainComponent_div_8_Template_div_click_1_listener", "QscMainComponent_div_8_Template_button_click_5_listener", "ɵɵtwoWayListener", "QscMainComponent_div_8_Template_input_ngModelChange_11_listener", "ɵɵtwoWayBindingSet", "loginCredentials", "email", "QscMainComponent_div_8_Template_input_input_11_listener", "onLoginInputChange", "QscMainComponent_div_8_Template_input_ngModelChange_15_listener", "secretWord", "QscMainComponent_div_8_Template_input_input_15_listener", "ɵɵtemplate", "QscMainComponent_div_8_div_16_Template", "QscMainComponent_div_8_div_17_Template", "QscMainComponent_div_8_div_18_Template", "ɵɵtwoWayProperty", "ɵɵproperty", "isLoading", "QscMainComponent_div_9_div_18_Template_button_click_3_listener", "_r6", "switchMessageType", "messageType", "getSelectedRecipientName", "QscMainComponent_div_9_div_19_div_1_Template_div_click_0_listener", "contact_r8", "_r7", "$implicit", "selectContact", "ɵɵclassProp", "isOnline", "QscMainComponent_div_9_div_19_div_2_Template_div_click_0_listener", "group_r10", "_r9", "selectGroup", "name", "members", "length", "isActive", "QscMainComponent_div_9_div_19_div_1_Template", "QscMainComponent_div_9_div_19_div_2_Template", "QscMainComponent_div_9_div_19_div_3_Template", "QscMainComponent_div_9_div_19_div_4_Template", "filteredContacts", "filteredGroups", "QscMainComponent_div_9_Template_div_click_0_listener", "_r5", "closeMessageComposer", "QscMainComponent_div_9_Template_div_click_1_listener", "QscMainComponent_div_9_Template_button_click_5_listener", "QscMainComponent_div_9_Template_button_click_9_listener", "QscMainComponent_div_9_Template_button_click_11_listener", "QscMainComponent_div_9_Template_input_ngModelChange_17_listener", "recipient<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "QscMainComponent_div_9_Template_input_input_17_listener", "onRecipientSearchChange", "QscMainComponent_div_9_div_18_Template", "QscMainComponent_div_9_div_19_Template", "QscMainComponent_div_9_Template_textarea_ngModelChange_23_listener", "messageContent", "QscMainComponent_div_9_Template_button_click_27_listener", "sendMessage", "QscMainComponent_div_9_Template_button_click_29_listener", "isMessageValid", "message_r12", "sender", "ɵɵpipeBind2", "timestamp", "content", "QscMainComponent_div_10_div_8_div_1_Template", "messages", "trackMessage", "QscMainComponent_div_10_Template_div_click_0_listener", "_r11", "closeMessagesViewer", "QscMainComponent_div_10_Template_div_click_1_listener", "QscMainComponent_div_10_Template_button_click_5_listener", "QscMainComponent_div_10_div_8_Template", "QscMainComponent_div_10_div_9_Template", "userProfile", "avatar", "ɵɵsanitizeUrl", "tmp_2_0", "char<PERSON>t", "toUpperCase", "QscMainComponent_div_11_Template_div_click_0_listener", "_r13", "closeAccountSettings", "QscMainComponent_div_11_Template_div_click_1_listener", "QscMainComponent_div_11_Template_button_click_5_listener", "QscMainComponent_div_11_img_11_Template", "QscMainComponent_div_11_div_12_Template", "QscMainComponent_div_11_Template_input_change_16_listener", "onAvatarChange", "QscMainComponent_div_11_Template_button_click_59_listener", "phoneNumber", "QscMainComponent", "constructor", "authService", "messageService", "notificationService", "destroy$", "circleState", "showLoginModal", "showMessageModal", "showMessagesModal", "showContextMenu", "showAccountSettings", "selected<PERSON><PERSON><PERSON><PERSON>", "selectedGroup", "contacts", "groups", "longPressTimer", "longPressDuration", "ngOnInit", "initializeApp", "setupMessageListener", "setupAuthListener", "ngOnDestroy", "next", "complete", "isAuthenticated", "getCurrentUser", "loadMessages", "loadContacts", "loadGroups", "authState$", "pipe", "subscribe", "user", "messages$", "updateUnreadCount", "newMessage$", "message", "unshift", "showNotification", "filter", "m", "read", "error", "console", "id", "lastSeen", "Date", "now", "onCircleClick", "closeContextMenu", "openLoginModal", "openMessageComposer", "openMessagesViewer", "onCircleRightClick", "event", "preventDefault", "clientX", "clientY", "onCircleTouchStart", "setTimeout", "touch", "touches", "navigator", "vibrate", "onCircleTouchEnd", "clearTimeout", "onCircleTouchMove", "isValidCredentials", "performLogin", "emailValid", "includes", "secretWordValid", "test", "login", "response", "trim", "recipient", "undefined", "groupId", "query", "toLowerCase", "contact", "group", "type", "find", "c", "g", "<PERSON><PERSON><PERSON><PERSON>", "hasRecipient", "markMessagesAsRead", "markAllAsRead", "loadUserProfile", "input", "target", "files", "file", "startsWith", "size", "reader", "FileReader", "onload", "e", "result", "saveAvatarChange", "readAsDataURL", "onKeyDown", "key", "closeAllModals", "shift<PERSON>ey", "onDocumentClick", "getCircleClass", "getCircleTitle", "index", "ɵɵdirectiveInject", "i1", "AuthService", "i2", "MessageService", "i3", "NotificationService", "selectors", "hostBindings", "QscMainComponent_HostBindings", "rf", "ctx", "QscMainComponent_keydown_HostBindingHandler", "ɵɵresolveDocument", "QscMainComponent_click_HostBindingHandler", "QscMainComponent_Template_div_click_2_listener", "QscMainComponent_Template_div_contextmenu_2_listener", "QscMainComponent_Template_div_touchstart_2_listener", "QscMainComponent_Template_div_touchend_2_listener", "QscMainComponent_Template_div_touchmove_2_listener", "QscMainComponent_div_4_Template", "QscMainComponent_div_5_Template", "QscMainComponent_div_6_Template", "QscMainComponent_div_7_Template", "QscMainComponent_div_8_Template", "QscMainComponent_div_9_Template", "QscMainComponent_div_10_Template", "QscMainComponent_div_11_Template", "QscMainComponent_div_12_Template", "ɵɵclassMap", "i4", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "DatePipe", "i5", "DefaultValueAccessor", "NgControlStatus", "MaxLengthValidator", "NgModel", "styles"], "sources": ["C:\\Users\\<USER>\\Projects\\QSC1\\frontend\\src\\app\\components\\qsc-main\\qsc-main.component.ts", "C:\\Users\\<USER>\\Projects\\QSC1\\frontend\\src\\app\\components\\qsc-main\\qsc-main.component.html"], "sourcesContent": ["import { Component, OnInit, On<PERSON><PERSON>roy, HostListener } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { Subject, takeUntil } from 'rxjs';\nimport { AuthService } from '../../services/auth.service';\nimport { MessageService } from '../../services/message.service';\nimport { NotificationService } from '../../services/notification.service';\nimport { sanitizeForLogging } from '../../types/shared.types';\n\nexport type CircleState = 'guest' | 'authenticated' | 'unread' | 'composing';\n\ninterface LoginCredentials {\n  email: string;\n  secretWord: string;\n}\n\ninterface Message {\n  id: string;\n  content: string;\n  timestamp: Date;\n  sender: string;\n  recipient?: string;\n  groupId?: string;\n  read?: boolean;\n}\n\ninterface Contact {\n  id: string;\n  username: string;\n  email: string;\n  publicKey?: string;\n  isOnline?: boolean;\n  lastSeen?: Date;\n}\n\ninterface Group {\n  id: string;\n  name: string;\n  members: string[];\n  isActive: boolean;\n}\n\n@Component({\n  selector: 'app-qsc-main',\n  standalone: true,\n  imports: [CommonModule, FormsModule],\n  templateUrl: './qsc-main.component.html',\n  styleUrls: ['./qsc-main.component.scss']\n})\nexport class QscMainComponent implements OnInit, OnDestroy {\n  private destroy$ = new Subject<void>();\n\n  // Circle state management\n  circleState: CircleState = 'guest';\n\n  // Modal states\n  showLoginModal = false;\n  showMessageModal = false;\n  showMessagesModal = false;\n  showContextMenu = false;\n  showAccountSettings = false;\n\n  // Context menu position\n  contextMenuPosition = { x: 0, y: 0 };\n\n  // Authentication\n  loginCredentials: LoginCredentials = { email: '', secretWord: '' };\n  loginError = '';\n  isLoading = false;\n\n  // Messaging\n  messageContent = '';\n  selectedRecipient = '';\n  selectedGroup = '';\n  messageType: 'direct' | 'group' = 'direct';\n  messages: Message[] = [];\n  unreadCount = 0;\n\n  // Contacts and Groups\n  contacts: Contact[] = [];\n  groups: Group[] = [];\n  filteredContacts: Contact[] = [];\n  filteredGroups: Group[] = [];\n  recipientSearchQuery = '';\n\n  // User info\n  currentUser: any = null;\n\n  // Account settings\n  userProfile = {\n    avatar: '',\n    email: '',\n    phoneNumber: ''\n  };\n\n  // Long press handling\n  private longPressTimer: any = null;\n  private longPressDuration = 500; // 500ms for long press\n\n  constructor(\n    private authService: AuthService,\n    private messageService: MessageService,\n    private notificationService: NotificationService\n  ) {}\n\n  ngOnInit() {\n    this.initializeApp();\n    this.setupMessageListener();\n    this.setupAuthListener();\n  }\n\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n\n  private initializeApp() {\n    // Check if user is already authenticated\n    if (this.authService.isAuthenticated()) {\n      this.currentUser = this.authService.getCurrentUser();\n      this.circleState = 'authenticated';\n      this.loadMessages();\n      this.loadContacts();\n      this.loadGroups();\n    } else {\n      this.circleState = 'guest';\n    }\n  }\n\n  private setupAuthListener() {\n    this.authService.authState$\n      .pipe(takeUntil(this.destroy$))\n      .subscribe(user => {\n        if (user) {\n          this.currentUser = user;\n          this.circleState = 'authenticated';\n          this.showLoginModal = false;\n          this.loadMessages();\n        } else {\n          this.currentUser = null;\n          this.circleState = 'guest';\n          this.messages = [];\n          this.unreadCount = 0;\n        }\n      });\n  }\n\n  private setupMessageListener() {\n    this.messageService.messages$\n      .pipe(takeUntil(this.destroy$))\n      .subscribe(messages => {\n        this.messages = messages;\n        this.updateUnreadCount();\n      });\n\n    this.messageService.newMessage$\n      .pipe(takeUntil(this.destroy$))\n      .subscribe(message => {\n        this.messages.unshift(message);\n        this.updateUnreadCount();\n        this.notificationService.showNotification('New message received');\n      });\n  }\n\n  private updateUnreadCount() {\n    this.unreadCount = this.messages.filter(m => !m.read).length;\n    if (this.unreadCount > 0 && this.circleState === 'authenticated') {\n      this.circleState = 'unread';\n    } else if (this.unreadCount === 0 && this.circleState === 'unread') {\n      this.circleState = 'authenticated';\n    }\n  }\n\n  private loadMessages() {\n    this.messageService.loadMessages()\n      .pipe(takeUntil(this.destroy$))\n      .subscribe({\n        next: (messages) => {\n          this.messages = messages;\n          this.updateUnreadCount();\n        },\n        error: (error) => {\n          console.error('Failed to load messages:', error);\n        }\n      });\n  }\n\n  private loadContacts() {\n    // TODO: Replace with actual API call\n    this.contacts = [\n      {\n        id: '1',\n        username: 'alice',\n        email: '<EMAIL>',\n        isOnline: true\n      },\n      {\n        id: '2',\n        username: 'bob',\n        email: '<EMAIL>',\n        isOnline: false,\n        lastSeen: new Date(Date.now() - 300000) // 5 minutes ago\n      }\n    ];\n    this.filteredContacts = [...this.contacts];\n  }\n\n  private loadGroups() {\n    // TODO: Replace with actual API call\n    this.groups = [\n      {\n        id: 'group1',\n        name: 'Work Team',\n        members: ['1', '2', 'current-user'],\n        isActive: true\n      },\n      {\n        id: 'group2',\n        name: 'Family',\n        members: ['3', '4', 'current-user'],\n        isActive: true\n      }\n    ];\n    this.filteredGroups = [...this.groups];\n  }\n\n  // Circle click handler - main interaction point\n  onCircleClick() {\n    // Don't handle click if context menu is showing\n    if (this.showContextMenu) {\n      this.closeContextMenu();\n      return;\n    }\n\n    switch (this.circleState) {\n      case 'guest':\n        this.openLoginModal();\n        break;\n      case 'authenticated':\n        this.openMessageComposer();\n        break;\n      case 'unread':\n        this.openMessagesViewer();\n        break;\n      case 'composing':\n        // Already composing, do nothing or close\n        break;\n    }\n  }\n\n  // Right click handler\n  onCircleRightClick(event: MouseEvent) {\n    event.preventDefault();\n\n    // Only show context menu for authenticated users\n    if (this.circleState === 'guest') return;\n\n    this.showContextMenu = true;\n    this.contextMenuPosition = {\n      x: event.clientX,\n      y: event.clientY\n    };\n  }\n\n  // Touch event handlers for long press\n  onCircleTouchStart(event: TouchEvent) {\n    // Only for authenticated users\n    if (this.circleState === 'guest') return;\n\n    this.longPressTimer = setTimeout(() => {\n      const touch = event.touches[0];\n      this.showContextMenu = true;\n      this.contextMenuPosition = {\n        x: touch.clientX,\n        y: touch.clientY\n      };\n\n      // Provide haptic feedback if available\n      if (navigator.vibrate) {\n        navigator.vibrate(50);\n      }\n    }, this.longPressDuration);\n  }\n\n  onCircleTouchEnd() {\n    if (this.longPressTimer) {\n      clearTimeout(this.longPressTimer);\n      this.longPressTimer = null;\n    }\n  }\n\n  onCircleTouchMove() {\n    // Cancel long press if user moves finger\n    if (this.longPressTimer) {\n      clearTimeout(this.longPressTimer);\n      this.longPressTimer = null;\n    }\n  }\n\n  // Authentication methods\n  openLoginModal() {\n    this.showLoginModal = true;\n    this.loginCredentials = { email: '', secretWord: '' };\n    this.loginError = '';\n  }\n\n  closeLoginModal() {\n    this.showLoginModal = false;\n    this.loginCredentials = { email: '', secretWord: '' };\n    this.loginError = '';\n  }\n\n  onLoginInputChange() {\n    // Auto-submit when both fields are valid\n    if (this.isValidCredentials()) {\n      this.performLogin();\n    }\n  }\n\n  private isValidCredentials(): boolean {\n    const emailValid = this.loginCredentials.email.includes('@') &&\n                      this.loginCredentials.email.includes('.');\n    const secretWordValid = this.loginCredentials.secretWord.length >= 4 &&\n                           /[A-Z]/.test(this.loginCredentials.secretWord) &&\n                           /[a-z]/.test(this.loginCredentials.secretWord) &&\n                           /[0-9]/.test(this.loginCredentials.secretWord) &&\n                           /[^A-Za-z0-9]/.test(this.loginCredentials.secretWord);\n\n    return emailValid && secretWordValid;\n  }\n\n  private performLogin() {\n    if (this.isLoading) return;\n\n    this.isLoading = true;\n    this.loginError = '';\n\n    this.authService.login(this.loginCredentials.email, this.loginCredentials.secretWord)\n      .pipe(takeUntil(this.destroy$))\n      .subscribe({\n        next: (response) => {\n          this.isLoading = false;\n          // Auth state will be updated via authState$ subscription\n        },\n        error: (error) => {\n          this.isLoading = false;\n          this.loginError = error.message || 'Authentication failed';\n        }\n      });\n  }\n\n  // Message composition methods\n  openMessageComposer() {\n    this.showMessageModal = true;\n    this.messageContent = '';\n    this.selectedRecipient = '';\n    this.selectedGroup = '';\n    this.messageType = 'direct';\n    this.recipientSearchQuery = '';\n    this.filteredContacts = [...this.contacts];\n    this.filteredGroups = [...this.groups];\n    this.circleState = 'composing';\n  }\n\n  closeMessageComposer() {\n    this.showMessageModal = false;\n    this.messageContent = '';\n    this.selectedRecipient = '';\n    this.selectedGroup = '';\n    this.recipientSearchQuery = '';\n    this.circleState = 'authenticated';\n  }\n\n  sendMessage() {\n    if (!this.messageContent.trim()) return;\n\n    // Validate recipient selection\n    if (this.messageType === 'direct' && !this.selectedRecipient) {\n      this.notificationService.showNotification('Please select a recipient', 'warning');\n      return;\n    }\n\n    if (this.messageType === 'group' && !this.selectedGroup) {\n      this.notificationService.showNotification('Please select a group', 'warning');\n      return;\n    }\n\n    const message: Partial<Message> = {\n      content: this.messageContent.trim(),\n      timestamp: new Date(),\n      sender: this.currentUser?.username || 'Unknown',\n      recipient: this.messageType === 'direct' ? this.selectedRecipient : undefined,\n      groupId: this.messageType === 'group' ? this.selectedGroup : undefined\n    };\n\n    this.messageService.sendMessage(message)\n      .pipe(takeUntil(this.destroy$))\n      .subscribe({\n        next: () => {\n          this.closeMessageComposer();\n          this.notificationService.showNotification('Message sent');\n        },\n        error: (error) => {\n          console.error('Failed to send message:', error);\n          this.notificationService.showNotification('Failed to send message', 'error');\n        }\n      });\n  }\n\n  // Recipient selection methods\n  onRecipientSearchChange() {\n    const query = this.recipientSearchQuery.toLowerCase();\n\n    if (this.messageType === 'direct') {\n      this.filteredContacts = this.contacts.filter(contact =>\n        contact.username.toLowerCase().includes(query) ||\n        contact.email.toLowerCase().includes(query)\n      );\n    } else {\n      this.filteredGroups = this.groups.filter(group =>\n        group.name.toLowerCase().includes(query)\n      );\n    }\n  }\n\n  selectContact(contact: Contact) {\n    this.selectedRecipient = contact.id;\n    this.recipientSearchQuery = contact.username;\n    this.filteredContacts = [];\n  }\n\n  selectGroup(group: Group) {\n    this.selectedGroup = group.id;\n    this.recipientSearchQuery = group.name;\n    this.filteredGroups = [];\n  }\n\n  switchMessageType(type: 'direct' | 'group') {\n    this.messageType = type;\n    this.selectedRecipient = '';\n    this.selectedGroup = '';\n    this.recipientSearchQuery = '';\n    this.onRecipientSearchChange();\n  }\n\n  getSelectedRecipientName(): string {\n    if (this.messageType === 'direct' && this.selectedRecipient) {\n      const contact = this.contacts.find(c => c.id === this.selectedRecipient);\n      return contact?.username || 'Unknown';\n    }\n\n    if (this.messageType === 'group' && this.selectedGroup) {\n      const group = this.groups.find(g => g.id === this.selectedGroup);\n      return group?.name || 'Unknown Group';\n    }\n\n    return '';\n  }\n\n  isMessageValid(): boolean {\n    const hasContent = this.messageContent.trim().length > 0;\n    const hasRecipient = this.messageType === 'direct' ? !!this.selectedRecipient : !!this.selectedGroup;\n    return hasContent && hasRecipient;\n  }\n\n  // Message viewing methods\n  openMessagesViewer() {\n    this.showMessagesModal = true;\n    this.markMessagesAsRead();\n  }\n\n  closeMessagesViewer() {\n    this.showMessagesModal = false;\n  }\n\n  private markMessagesAsRead() {\n    this.messageService.markAllAsRead()\n      .pipe(takeUntil(this.destroy$))\n      .subscribe(() => {\n        this.updateUnreadCount();\n      });\n  }\n\n  // Context menu methods\n  closeContextMenu() {\n    this.showContextMenu = false;\n  }\n\n  openAccountSettings() {\n    this.showAccountSettings = true;\n    this.showContextMenu = false;\n    this.loadUserProfile();\n  }\n\n  closeAccountSettings() {\n    this.showAccountSettings = false;\n  }\n\n  // Account settings methods\n  loadUserProfile() {\n    if (this.currentUser) {\n      this.userProfile = {\n        avatar: this.currentUser.avatar || '',\n        email: this.currentUser.email || '',\n        phoneNumber: this.currentUser.phoneNumber || ''\n      };\n    }\n  }\n\n  onAvatarChange(event: Event) {\n    const input = event.target as HTMLInputElement;\n    if (input.files && input.files[0]) {\n      const file = input.files[0];\n\n      // Validate file type\n      if (!file.type.startsWith('image/')) {\n        this.notificationService.showNotification('Please select an image file', 'error');\n        return;\n      }\n\n      // Validate file size (max 2MB)\n      if (file.size > 2 * 1024 * 1024) {\n        this.notificationService.showNotification('Image must be smaller than 2MB', 'error');\n        return;\n      }\n\n      const reader = new FileReader();\n      reader.onload = (e) => {\n        this.userProfile.avatar = e.target?.result as string;\n        this.saveAvatarChange();\n      };\n      reader.readAsDataURL(file);\n    }\n  }\n\n  saveAvatarChange() {\n    // TODO: Implement API call to save avatar\n    this.notificationService.showNotification('Avatar updated successfully', 'success');\n  }\n\n  // Logout\n  logout() {\n    this.closeContextMenu();\n    this.authService.logout();\n  }\n\n  // Keyboard shortcuts\n  @HostListener('document:keydown', ['$event'])\n  onKeyDown(event: KeyboardEvent) {\n    // Escape key closes modals\n    if (event.key === 'Escape') {\n      this.closeAllModals();\n    }\n\n    // Enter key in login modal\n    if (event.key === 'Enter' && this.showLoginModal) {\n      if (this.isValidCredentials()) {\n        this.performLogin();\n      }\n    }\n\n    // Enter key in message modal\n    if (event.key === 'Enter' && this.showMessageModal && !event.shiftKey) {\n      event.preventDefault();\n      this.sendMessage();\n    }\n  }\n\n  // Click outside handler to close context menu\n  @HostListener('document:click', ['$event'])\n  onDocumentClick(event: Event) {\n    // Close context menu when clicking outside\n    if (this.showContextMenu) {\n      this.closeContextMenu();\n    }\n  }\n\n  private closeAllModals() {\n    this.showLoginModal = false;\n    this.showMessageModal = false;\n    this.showMessagesModal = false;\n    this.showContextMenu = false;\n    this.showAccountSettings = false;\n    if (this.circleState === 'composing') {\n      this.circleState = 'authenticated';\n    }\n  }\n\n  // Utility methods\n  getCircleClass(): string {\n    return `circle-${this.circleState}`;\n  }\n\n  getCircleTitle(): string {\n    switch (this.circleState) {\n      case 'guest': return 'Click to sign in';\n      case 'authenticated': return 'Click to compose message';\n      case 'unread': return `Click to view ${this.unreadCount} unread message(s)`;\n      case 'composing': return 'Composing message...';\n      default: return '';\n    }\n  }\n\n  trackMessage(index: number, message: Message): string {\n    return message.id;\n  }\n}\n", "<!-- Main Container -->\n<div class=\"qsc-container\">\n  <!-- Central Circle -->\n  <div class=\"circle-container\">\n    <div\n      class=\"qsc-circle\"\n      [class]=\"getCircleClass()\"\n      [title]=\"getCircleTitle()\"\n      (click)=\"onCircleClick()\"\n      (contextmenu)=\"onCircleRightClick($event)\"\n      (touchstart)=\"onCircleTouchStart($event)\"\n      (touchend)=\"onCircleTouchEnd()\"\n      (touchmove)=\"onCircleTouchMove()\"\n    >\n      <div class=\"circle-inner\">\n        <div class=\"wind-effect\" *ngIf=\"circleState !== 'guest'\"></div>\n        <div class=\"unread-indicator\" *ngIf=\"circleState === 'unread'\">\n          {{ unreadCount }}\n        </div>\n      </div>\n    </div>\n  </div>\n\n  <!-- User Info (subtle, top-right) -->\n  <div class=\"user-info\" *ngIf=\"currentUser\">\n    <span>{{ currentUser.username }}</span>\n    <button class=\"logout-btn\" (click)=\"logout()\" title=\"Logout\">×</button>\n  </div>\n\n  <!-- Context Menu -->\n  <div\n    class=\"context-menu\"\n    *ngIf=\"showContextMenu\"\n    [style.left.px]=\"contextMenuPosition.x\"\n    [style.top.px]=\"contextMenuPosition.y\"\n    (click)=\"$event.stopPropagation()\"\n  >\n    <div class=\"context-menu-item\" (click)=\"openAccountSettings()\">\n      <span class=\"menu-icon\">👤</span>\n      <span class=\"menu-text\">Account Settings</span>\n    </div>\n    <div class=\"context-menu-divider\"></div>\n    <div class=\"context-menu-item logout-item\" (click)=\"logout()\">\n      <span class=\"menu-icon\">🚪</span>\n      <span class=\"menu-text\">Logout</span>\n    </div>\n  </div>\n</div>\n\n<!-- Login Modal -->\n<div class=\"modal-overlay\" *ngIf=\"showLoginModal\" (click)=\"closeLoginModal()\">\n  <div class=\"modal login-modal\" (click)=\"$event.stopPropagation()\">\n    <div class=\"modal-header\">\n      <h2>Sign In</h2>\n      <button class=\"close-btn\" (click)=\"closeLoginModal()\">×</button>\n    </div>\n\n    <div class=\"modal-content\">\n      <div class=\"form-group\">\n        <label for=\"email\">Email</label>\n        <input\n          type=\"email\"\n          id=\"email\"\n          [(ngModel)]=\"loginCredentials.email\"\n          (input)=\"onLoginInputChange()\"\n          placeholder=\"Enter your email\"\n          [disabled]=\"isLoading\"\n          autocomplete=\"email\"\n        />\n      </div>\n\n      <div class=\"form-group\">\n        <label for=\"secretWord\">Secret Word</label>\n        <input\n          type=\"password\"\n          id=\"secretWord\"\n          [(ngModel)]=\"loginCredentials.secretWord\"\n          (input)=\"onLoginInputChange()\"\n          placeholder=\"4+ chars: A-Z, a-z, 0-9, symbol\"\n          [disabled]=\"isLoading\"\n          autocomplete=\"current-password\"\n        />\n      </div>\n\n      <div class=\"error-message\" *ngIf=\"loginError\">\n        {{ loginError }}\n      </div>\n\n      <div class=\"loading-indicator\" *ngIf=\"isLoading\">\n        <div class=\"spinner\"></div>\n        <span>Authenticating...</span>\n      </div>\n\n      <div class=\"auth-info\" *ngIf=\"!isLoading\">\n        <p>🔒 Protected by post-quantum cryptography</p>\n        <p class=\"auto-submit-hint\">Form auto-submits when credentials are valid</p>\n      </div>\n    </div>\n  </div>\n</div>\n\n<!-- Message Composer Modal -->\n<div class=\"modal-overlay\" *ngIf=\"showMessageModal\" (click)=\"closeMessageComposer()\">\n  <div class=\"modal message-modal\" (click)=\"$event.stopPropagation()\">\n    <div class=\"modal-header\">\n      <h2>Compose Message</h2>\n      <button class=\"close-btn\" (click)=\"closeMessageComposer()\">×</button>\n    </div>\n\n    <div class=\"modal-content\">\n      <!-- Message Type Selector -->\n      <div class=\"message-type-selector\">\n        <button\n          class=\"type-btn\"\n          [class.active]=\"messageType === 'direct'\"\n          (click)=\"switchMessageType('direct')\"\n        >\n          👤 Direct\n        </button>\n        <button\n          class=\"type-btn\"\n          [class.active]=\"messageType === 'group'\"\n          (click)=\"switchMessageType('group')\"\n        >\n          👥 Group\n        </button>\n      </div>\n\n      <!-- Recipient Selection -->\n      <div class=\"form-group\">\n        <label for=\"recipientSearch\">\n          {{ messageType === 'direct' ? 'To (Contact)' : 'To (Group)' }}\n        </label>\n        <div class=\"recipient-selector\">\n          <input\n            type=\"text\"\n            id=\"recipientSearch\"\n            [(ngModel)]=\"recipientSearchQuery\"\n            (input)=\"onRecipientSearchChange()\"\n            [placeholder]=\"messageType === 'direct' ? 'Search contacts...' : 'Search groups...'\"\n            autocomplete=\"off\"\n          />\n\n          <!-- Selected Recipient Display -->\n          <div class=\"selected-recipient\" *ngIf=\"getSelectedRecipientName()\">\n            <span class=\"recipient-name\">{{ getSelectedRecipientName() }}</span>\n            <button\n              class=\"clear-recipient\"\n              (click)=\"switchMessageType(messageType)\"\n              title=\"Clear selection\"\n            >×</button>\n          </div>\n\n          <!-- Contact/Group Dropdown -->\n          <div class=\"recipient-dropdown\" *ngIf=\"recipientSearchQuery && !getSelectedRecipientName()\">\n            <!-- Direct Message Contacts -->\n            <div\n              class=\"recipient-item\"\n              *ngFor=\"let contact of filteredContacts\"\n              (click)=\"selectContact(contact)\"\n              [hidden]=\"messageType !== 'direct'\"\n            >\n              <div class=\"contact-info\">\n                <span class=\"contact-name\">{{ contact.username }}</span>\n                <span class=\"contact-email\">{{ contact.email }}</span>\n              </div>\n              <div class=\"contact-status\">\n                <span\n                  class=\"status-indicator\"\n                  [class.online]=\"contact.isOnline\"\n                  [class.offline]=\"!contact.isOnline\"\n                ></span>\n                <span class=\"status-text\">\n                  {{ contact.isOnline ? 'Online' : 'Offline' }}\n                </span>\n              </div>\n            </div>\n\n            <!-- Group Chats -->\n            <div\n              class=\"recipient-item\"\n              *ngFor=\"let group of filteredGroups\"\n              (click)=\"selectGroup(group)\"\n              [hidden]=\"messageType !== 'group'\"\n            >\n              <div class=\"group-info\">\n                <span class=\"group-name\">{{ group.name }}</span>\n                <span class=\"group-members\">{{ group.members.length }} members</span>\n              </div>\n              <div class=\"group-status\">\n                <span\n                  class=\"status-indicator\"\n                  [class.active]=\"group.isActive\"\n                ></span>\n              </div>\n            </div>\n\n            <!-- No Results -->\n            <div class=\"no-results\" *ngIf=\"messageType === 'direct' && filteredContacts.length === 0\">\n              No contacts found\n            </div>\n            <div class=\"no-results\" *ngIf=\"messageType === 'group' && filteredGroups.length === 0\">\n              No groups found\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- Message Content -->\n      <div class=\"form-group\">\n        <label for=\"messageContent\">Message</label>\n        <textarea\n          id=\"messageContent\"\n          [(ngModel)]=\"messageContent\"\n          placeholder=\"Type your message here...\"\n          rows=\"6\"\n          maxlength=\"1000\"\n        ></textarea>\n        <div class=\"char-count\">{{ messageContent.length }}/1000</div>\n      </div>\n\n      <div class=\"message-actions\">\n        <button\n          class=\"btn btn-primary\"\n          (click)=\"sendMessage()\"\n          [disabled]=\"!isMessageValid()\"\n        >\n          Send Message\n        </button>\n        <button class=\"btn btn-secondary\" (click)=\"closeMessageComposer()\">\n          Cancel\n        </button>\n      </div>\n\n      <div class=\"send-hint\">\n        <p>Press Enter to send • Shift+Enter for new line</p>\n      </div>\n    </div>\n  </div>\n</div>\n\n<!-- Messages Viewer Modal -->\n<div class=\"modal-overlay\" *ngIf=\"showMessagesModal\" (click)=\"closeMessagesViewer()\">\n  <div class=\"modal messages-modal\" (click)=\"$event.stopPropagation()\">\n    <div class=\"modal-header\">\n      <h2>Messages</h2>\n      <button class=\"close-btn\" (click)=\"closeMessagesViewer()\">×</button>\n    </div>\n\n    <div class=\"modal-content\">\n      <div class=\"messages-list\" *ngIf=\"messages.length > 0\">\n        <div\n          class=\"message-item\"\n          *ngFor=\"let message of messages; trackBy: trackMessage\"\n        >\n          <div class=\"message-header\">\n            <span class=\"sender\">{{ message.sender }}</span>\n            <span class=\"timestamp\">{{ message.timestamp | date:'short' }}</span>\n          </div>\n          <div class=\"message-content\">{{ message.content }}</div>\n        </div>\n      </div>\n\n      <div class=\"empty-state\" *ngIf=\"messages.length === 0\">\n        <p>No messages yet</p>\n        <p class=\"hint\">Click the circle to compose your first message</p>\n      </div>\n    </div>\n  </div>\n</div>\n\n<!-- Account Settings Modal -->\n<div class=\"modal-overlay\" *ngIf=\"showAccountSettings\" (click)=\"closeAccountSettings()\">\n  <div class=\"modal account-settings-modal\" (click)=\"$event.stopPropagation()\">\n    <div class=\"modal-header\">\n      <h2>Account Settings</h2>\n      <button class=\"close-btn\" (click)=\"closeAccountSettings()\">×</button>\n    </div>\n\n    <div class=\"modal-content\">\n      <!-- Avatar Section -->\n      <div class=\"avatar-section\">\n        <div class=\"avatar-container\">\n          <div class=\"avatar-display\">\n            <img\n              *ngIf=\"userProfile.avatar\"\n              [src]=\"userProfile.avatar\"\n              alt=\"Profile Avatar\"\n              class=\"avatar-image\"\n            />\n            <div *ngIf=\"!userProfile.avatar\" class=\"avatar-placeholder\">\n              <span class=\"avatar-initials\">\n                {{ currentUser?.username?.charAt(0)?.toUpperCase() || '?' }}\n              </span>\n            </div>\n          </div>\n          <div class=\"avatar-actions\">\n            <label for=\"avatarInput\" class=\"avatar-upload-btn\">\n              📷 Change Avatar\n            </label>\n            <input\n              type=\"file\"\n              id=\"avatarInput\"\n              accept=\"image/*\"\n              (change)=\"onAvatarChange($event)\"\n              style=\"display: none;\"\n            />\n          </div>\n        </div>\n      </div>\n\n      <!-- Profile Information -->\n      <div class=\"profile-info\">\n        <div class=\"form-group\">\n          <label>Username</label>\n          <div class=\"readonly-field\">\n            <span>{{ currentUser?.username || 'Not set' }}</span>\n          </div>\n        </div>\n\n        <div class=\"form-group\">\n          <label>Email Address</label>\n          <div class=\"readonly-field\">\n            <span>{{ userProfile.email || 'Not set' }}</span>\n            <span class=\"field-note\">Email cannot be changed for security reasons</span>\n          </div>\n        </div>\n\n        <div class=\"form-group\">\n          <label>Phone Number</label>\n          <div class=\"readonly-field\">\n            <span>{{ userProfile.phoneNumber || 'Not set' }}</span>\n            <span class=\"field-note\">Phone number cannot be changed for security reasons</span>\n          </div>\n        </div>\n      </div>\n\n      <!-- Security Information -->\n      <div class=\"security-info\">\n        <h3>Security Information</h3>\n        <div class=\"security-item\">\n          <span class=\"security-label\">🔐 Encryption:</span>\n          <span class=\"security-value\">Post-Quantum Cryptography (ML-DSA, ML-KEM)</span>\n        </div>\n        <div class=\"security-item\">\n          <span class=\"security-label\">🛡️ Security Level:</span>\n          <span class=\"security-value\">NIST Level 3 (AES-192 equivalent)</span>\n        </div>\n        <div class=\"security-item\">\n          <span class=\"security-label\">🔑 Key Rotation:</span>\n          <span class=\"security-value\">Every 30 days</span>\n        </div>\n      </div>\n\n      <div class=\"account-actions\">\n        <button class=\"btn btn-secondary\" (click)=\"closeAccountSettings()\">\n          Close\n        </button>\n      </div>\n    </div>\n  </div>\n</div>\n\n<!-- Keyboard Hints (bottom) -->\n<div class=\"keyboard-hints\" *ngIf=\"!showLoginModal && !showMessageModal && !showMessagesModal && !showAccountSettings\">\n  <span>ESC to close modals • Right-click circle for menu</span>\n</div>\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,OAAO,EAAEC,SAAS,QAAQ,MAAM;;;;;;;;;ICYjCC,EAAA,CAAAC,SAAA,cAA+D;;;;;IAC/DD,EAAA,CAAAE,cAAA,cAA+D;IAC7DF,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;IADJJ,EAAA,CAAAK,SAAA,EACF;IADEL,EAAA,CAAAM,kBAAA,MAAAC,MAAA,CAAAC,WAAA,MACF;;;;;;IAOJR,EADF,CAAAE,cAAA,cAA2C,WACnC;IAAAF,EAAA,CAAAG,MAAA,GAA0B;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACvCJ,EAAA,CAAAE,cAAA,iBAA6D;IAAlCF,EAAA,CAAAS,UAAA,mBAAAC,wDAAA;MAAAV,EAAA,CAAAW,aAAA,CAAAC,GAAA;MAAA,MAAAL,MAAA,GAAAP,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASP,MAAA,CAAAQ,MAAA,EAAQ;IAAA,EAAC;IAAgBf,EAAA,CAAAG,MAAA,aAAC;IAChEH,EADgE,CAAAI,YAAA,EAAS,EACnE;;;;IAFEJ,EAAA,CAAAK,SAAA,GAA0B;IAA1BL,EAAA,CAAAgB,iBAAA,CAAAT,MAAA,CAAAU,WAAA,CAAAC,QAAA,CAA0B;;;;;;IAKlClB,EAAA,CAAAE,cAAA,cAMC;IADCF,EAAA,CAAAS,UAAA,mBAAAU,qDAAAC,MAAA;MAAApB,EAAA,CAAAW,aAAA,CAAAU,GAAA;MAAA,OAAArB,EAAA,CAAAc,WAAA,CAASM,MAAA,CAAAE,eAAA,EAAwB;IAAA,EAAC;IAElCtB,EAAA,CAAAE,cAAA,cAA+D;IAAhCF,EAAA,CAAAS,UAAA,mBAAAc,qDAAA;MAAAvB,EAAA,CAAAW,aAAA,CAAAU,GAAA;MAAA,MAAAd,MAAA,GAAAP,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASP,MAAA,CAAAiB,mBAAA,EAAqB;IAAA,EAAC;IAC5DxB,EAAA,CAAAE,cAAA,eAAwB;IAAAF,EAAA,CAAAG,MAAA,mBAAE;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACjCJ,EAAA,CAAAE,cAAA,eAAwB;IAAAF,EAAA,CAAAG,MAAA,uBAAgB;IAC1CH,EAD0C,CAAAI,YAAA,EAAO,EAC3C;IACNJ,EAAA,CAAAC,SAAA,cAAwC;IACxCD,EAAA,CAAAE,cAAA,cAA8D;IAAnBF,EAAA,CAAAS,UAAA,mBAAAgB,qDAAA;MAAAzB,EAAA,CAAAW,aAAA,CAAAU,GAAA;MAAA,MAAAd,MAAA,GAAAP,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASP,MAAA,CAAAQ,MAAA,EAAQ;IAAA,EAAC;IAC3Df,EAAA,CAAAE,cAAA,eAAwB;IAAAF,EAAA,CAAAG,MAAA,mBAAE;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACjCJ,EAAA,CAAAE,cAAA,gBAAwB;IAAAF,EAAA,CAAAG,MAAA,cAAM;IAElCH,EAFkC,CAAAI,YAAA,EAAO,EACjC,EACF;;;;IAZJJ,EADA,CAAA0B,WAAA,SAAAnB,MAAA,CAAAoB,mBAAA,CAAAC,CAAA,OAAuC,QAAArB,MAAA,CAAAoB,mBAAA,CAAAE,CAAA,OACD;;;;;IAkDpC7B,EAAA,CAAAE,cAAA,cAA8C;IAC5CF,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;IADJJ,EAAA,CAAAK,SAAA,EACF;IADEL,EAAA,CAAAM,kBAAA,MAAAC,MAAA,CAAAuB,UAAA,MACF;;;;;IAEA9B,EAAA,CAAAE,cAAA,cAAiD;IAC/CF,EAAA,CAAAC,SAAA,cAA2B;IAC3BD,EAAA,CAAAE,cAAA,WAAM;IAAAF,EAAA,CAAAG,MAAA,wBAAiB;IACzBH,EADyB,CAAAI,YAAA,EAAO,EAC1B;;;;;IAGJJ,EADF,CAAAE,cAAA,cAA0C,QACrC;IAAAF,EAAA,CAAAG,MAAA,0DAAyC;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAChDJ,EAAA,CAAAE,cAAA,YAA4B;IAAAF,EAAA,CAAAG,MAAA,mDAA4C;IAC1EH,EAD0E,CAAAI,YAAA,EAAI,EACxE;;;;;;IA9CZJ,EAAA,CAAAE,cAAA,cAA8E;IAA5BF,EAAA,CAAAS,UAAA,mBAAAsB,qDAAA;MAAA/B,EAAA,CAAAW,aAAA,CAAAqB,GAAA;MAAA,MAAAzB,MAAA,GAAAP,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASP,MAAA,CAAA0B,eAAA,EAAiB;IAAA,EAAC;IAC3EjC,EAAA,CAAAE,cAAA,cAAkE;IAAnCF,EAAA,CAAAS,UAAA,mBAAAyB,qDAAAd,MAAA;MAAApB,EAAA,CAAAW,aAAA,CAAAqB,GAAA;MAAA,OAAAhC,EAAA,CAAAc,WAAA,CAASM,MAAA,CAAAE,eAAA,EAAwB;IAAA,EAAC;IAE7DtB,EADF,CAAAE,cAAA,cAA0B,SACpB;IAAAF,EAAA,CAAAG,MAAA,cAAO;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAChBJ,EAAA,CAAAE,cAAA,iBAAsD;IAA5BF,EAAA,CAAAS,UAAA,mBAAA0B,wDAAA;MAAAnC,EAAA,CAAAW,aAAA,CAAAqB,GAAA;MAAA,MAAAzB,MAAA,GAAAP,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASP,MAAA,CAAA0B,eAAA,EAAiB;IAAA,EAAC;IAACjC,EAAA,CAAAG,MAAA,aAAC;IACzDH,EADyD,CAAAI,YAAA,EAAS,EAC5D;IAIFJ,EAFJ,CAAAE,cAAA,cAA2B,cACD,gBACH;IAAAF,EAAA,CAAAG,MAAA,aAAK;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IAChCJ,EAAA,CAAAE,cAAA,iBAQE;IALAF,EAAA,CAAAoC,gBAAA,2BAAAC,gEAAAjB,MAAA;MAAApB,EAAA,CAAAW,aAAA,CAAAqB,GAAA;MAAA,MAAAzB,MAAA,GAAAP,EAAA,CAAAa,aAAA;MAAAb,EAAA,CAAAsC,kBAAA,CAAA/B,MAAA,CAAAgC,gBAAA,CAAAC,KAAA,EAAApB,MAAA,MAAAb,MAAA,CAAAgC,gBAAA,CAAAC,KAAA,GAAApB,MAAA;MAAA,OAAApB,EAAA,CAAAc,WAAA,CAAAM,MAAA;IAAA,EAAoC;IACpCpB,EAAA,CAAAS,UAAA,mBAAAgC,wDAAA;MAAAzC,EAAA,CAAAW,aAAA,CAAAqB,GAAA;MAAA,MAAAzB,MAAA,GAAAP,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASP,MAAA,CAAAmC,kBAAA,EAAoB;IAAA,EAAC;IAKlC1C,EATE,CAAAI,YAAA,EAQE,EACE;IAGJJ,EADF,CAAAE,cAAA,eAAwB,iBACE;IAAAF,EAAA,CAAAG,MAAA,mBAAW;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IAC3CJ,EAAA,CAAAE,cAAA,iBAQE;IALAF,EAAA,CAAAoC,gBAAA,2BAAAO,gEAAAvB,MAAA;MAAApB,EAAA,CAAAW,aAAA,CAAAqB,GAAA;MAAA,MAAAzB,MAAA,GAAAP,EAAA,CAAAa,aAAA;MAAAb,EAAA,CAAAsC,kBAAA,CAAA/B,MAAA,CAAAgC,gBAAA,CAAAK,UAAA,EAAAxB,MAAA,MAAAb,MAAA,CAAAgC,gBAAA,CAAAK,UAAA,GAAAxB,MAAA;MAAA,OAAApB,EAAA,CAAAc,WAAA,CAAAM,MAAA;IAAA,EAAyC;IACzCpB,EAAA,CAAAS,UAAA,mBAAAoC,wDAAA;MAAA7C,EAAA,CAAAW,aAAA,CAAAqB,GAAA;MAAA,MAAAzB,MAAA,GAAAP,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASP,MAAA,CAAAmC,kBAAA,EAAoB;IAAA,EAAC;IAKlC1C,EATE,CAAAI,YAAA,EAQE,EACE;IAWNJ,EATA,CAAA8C,UAAA,KAAAC,sCAAA,kBAA8C,KAAAC,sCAAA,kBAIG,KAAAC,sCAAA,kBAKP;IAMhDjD,EAFI,CAAAI,YAAA,EAAM,EACF,EACF;;;;IApCIJ,EAAA,CAAAK,SAAA,IAAoC;IAApCL,EAAA,CAAAkD,gBAAA,YAAA3C,MAAA,CAAAgC,gBAAA,CAAAC,KAAA,CAAoC;IAGpCxC,EAAA,CAAAmD,UAAA,aAAA5C,MAAA,CAAA6C,SAAA,CAAsB;IAUtBpD,EAAA,CAAAK,SAAA,GAAyC;IAAzCL,EAAA,CAAAkD,gBAAA,YAAA3C,MAAA,CAAAgC,gBAAA,CAAAK,UAAA,CAAyC;IAGzC5C,EAAA,CAAAmD,UAAA,aAAA5C,MAAA,CAAA6C,SAAA,CAAsB;IAKEpD,EAAA,CAAAK,SAAA,EAAgB;IAAhBL,EAAA,CAAAmD,UAAA,SAAA5C,MAAA,CAAAuB,UAAA,CAAgB;IAIZ9B,EAAA,CAAAK,SAAA,EAAe;IAAfL,EAAA,CAAAmD,UAAA,SAAA5C,MAAA,CAAA6C,SAAA,CAAe;IAKvBpD,EAAA,CAAAK,SAAA,EAAgB;IAAhBL,EAAA,CAAAmD,UAAA,UAAA5C,MAAA,CAAA6C,SAAA,CAAgB;;;;;;IAoDlCpD,EADF,CAAAE,cAAA,cAAmE,eACpC;IAAAF,EAAA,CAAAG,MAAA,GAAgC;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACpEJ,EAAA,CAAAE,cAAA,iBAIC;IAFCF,EAAA,CAAAS,UAAA,mBAAA4C,+DAAA;MAAArD,EAAA,CAAAW,aAAA,CAAA2C,GAAA;MAAA,MAAA/C,MAAA,GAAAP,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASP,MAAA,CAAAgD,iBAAA,CAAAhD,MAAA,CAAAiD,WAAA,CAA8B;IAAA,EAAC;IAEzCxD,EAAA,CAAAG,MAAA,aAAC;IACJH,EADI,CAAAI,YAAA,EAAS,EACP;;;;IANyBJ,EAAA,CAAAK,SAAA,GAAgC;IAAhCL,EAAA,CAAAgB,iBAAA,CAAAT,MAAA,CAAAkD,wBAAA,GAAgC;;;;;;IAW7DzD,EAAA,CAAAE,cAAA,cAKC;IAFCF,EAAA,CAAAS,UAAA,mBAAAiD,kEAAA;MAAA,MAAAC,UAAA,GAAA3D,EAAA,CAAAW,aAAA,CAAAiD,GAAA,EAAAC,SAAA;MAAA,MAAAtD,MAAA,GAAAP,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASP,MAAA,CAAAuD,aAAA,CAAAH,UAAA,CAAsB;IAAA,EAAC;IAI9B3D,EADF,CAAAE,cAAA,cAA0B,eACG;IAAAF,EAAA,CAAAG,MAAA,GAAsB;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACxDJ,EAAA,CAAAE,cAAA,eAA4B;IAAAF,EAAA,CAAAG,MAAA,GAAmB;IACjDH,EADiD,CAAAI,YAAA,EAAO,EAClD;IACNJ,EAAA,CAAAE,cAAA,cAA4B;IAC1BF,EAAA,CAAAC,SAAA,eAIQ;IACRD,EAAA,CAAAE,cAAA,eAA0B;IACxBF,EAAA,CAAAG,MAAA,GACF;IAEJH,EAFI,CAAAI,YAAA,EAAO,EACH,EACF;;;;;IAhBJJ,EAAA,CAAAmD,UAAA,WAAA5C,MAAA,CAAAiD,WAAA,cAAmC;IAGNxD,EAAA,CAAAK,SAAA,GAAsB;IAAtBL,EAAA,CAAAgB,iBAAA,CAAA2C,UAAA,CAAAzC,QAAA,CAAsB;IACrBlB,EAAA,CAAAK,SAAA,GAAmB;IAAnBL,EAAA,CAAAgB,iBAAA,CAAA2C,UAAA,CAAAnB,KAAA,CAAmB;IAK7CxC,EAAA,CAAAK,SAAA,GAAiC;IACjCL,EADA,CAAA+D,WAAA,WAAAJ,UAAA,CAAAK,QAAA,CAAiC,aAAAL,UAAA,CAAAK,QAAA,CACE;IAGnChE,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAqD,UAAA,CAAAK,QAAA,6BACF;;;;;;IAKJhE,EAAA,CAAAE,cAAA,cAKC;IAFCF,EAAA,CAAAS,UAAA,mBAAAwD,kEAAA;MAAA,MAAAC,SAAA,GAAAlE,EAAA,CAAAW,aAAA,CAAAwD,GAAA,EAAAN,SAAA;MAAA,MAAAtD,MAAA,GAAAP,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASP,MAAA,CAAA6D,WAAA,CAAAF,SAAA,CAAkB;IAAA,EAAC;IAI1BlE,EADF,CAAAE,cAAA,cAAwB,eACG;IAAAF,EAAA,CAAAG,MAAA,GAAgB;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAChDJ,EAAA,CAAAE,cAAA,eAA4B;IAAAF,EAAA,CAAAG,MAAA,GAAkC;IAChEH,EADgE,CAAAI,YAAA,EAAO,EACjE;IACNJ,EAAA,CAAAE,cAAA,cAA0B;IACxBF,EAAA,CAAAC,SAAA,eAGQ;IAEZD,EADE,CAAAI,YAAA,EAAM,EACF;;;;;IAZJJ,EAAA,CAAAmD,UAAA,WAAA5C,MAAA,CAAAiD,WAAA,aAAkC;IAGPxD,EAAA,CAAAK,SAAA,GAAgB;IAAhBL,EAAA,CAAAgB,iBAAA,CAAAkD,SAAA,CAAAG,IAAA,CAAgB;IACbrE,EAAA,CAAAK,SAAA,GAAkC;IAAlCL,EAAA,CAAAM,kBAAA,KAAA4D,SAAA,CAAAI,OAAA,CAAAC,MAAA,aAAkC;IAK5DvE,EAAA,CAAAK,SAAA,GAA+B;IAA/BL,EAAA,CAAA+D,WAAA,WAAAG,SAAA,CAAAM,QAAA,CAA+B;;;;;IAMrCxE,EAAA,CAAAE,cAAA,cAA0F;IACxFF,EAAA,CAAAG,MAAA,0BACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;;IACNJ,EAAA,CAAAE,cAAA,cAAuF;IACrFF,EAAA,CAAAG,MAAA,wBACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;;IAjDRJ,EAAA,CAAAE,cAAA,cAA4F;IA+C1FF,EA7CA,CAAA8C,UAAA,IAAA2B,4CAAA,mBAKC,IAAAC,4CAAA,kBAuBA,IAAAC,4CAAA,kBAcyF,IAAAC,4CAAA,kBAGH;IAGzF5E,EAAA,CAAAI,YAAA,EAAM;;;;IA9CkBJ,EAAA,CAAAK,SAAA,EAAmB;IAAnBL,EAAA,CAAAmD,UAAA,YAAA5C,MAAA,CAAAsE,gBAAA,CAAmB;IAuBrB7E,EAAA,CAAAK,SAAA,EAAiB;IAAjBL,EAAA,CAAAmD,UAAA,YAAA5C,MAAA,CAAAuE,cAAA,CAAiB;IAiBZ9E,EAAA,CAAAK,SAAA,EAA+D;IAA/DL,EAAA,CAAAmD,UAAA,SAAA5C,MAAA,CAAAiD,WAAA,iBAAAjD,MAAA,CAAAsE,gBAAA,CAAAN,MAAA,OAA+D;IAG/DvE,EAAA,CAAAK,SAAA,EAA4D;IAA5DL,EAAA,CAAAmD,UAAA,SAAA5C,MAAA,CAAAiD,WAAA,gBAAAjD,MAAA,CAAAuE,cAAA,CAAAP,MAAA,OAA4D;;;;;;IAnGjGvE,EAAA,CAAAE,cAAA,cAAqF;IAAjCF,EAAA,CAAAS,UAAA,mBAAAsE,qDAAA;MAAA/E,EAAA,CAAAW,aAAA,CAAAqE,GAAA;MAAA,MAAAzE,MAAA,GAAAP,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASP,MAAA,CAAA0E,oBAAA,EAAsB;IAAA,EAAC;IAClFjF,EAAA,CAAAE,cAAA,cAAoE;IAAnCF,EAAA,CAAAS,UAAA,mBAAAyE,qDAAA9D,MAAA;MAAApB,EAAA,CAAAW,aAAA,CAAAqE,GAAA;MAAA,OAAAhF,EAAA,CAAAc,WAAA,CAASM,MAAA,CAAAE,eAAA,EAAwB;IAAA,EAAC;IAE/DtB,EADF,CAAAE,cAAA,cAA0B,SACpB;IAAAF,EAAA,CAAAG,MAAA,sBAAe;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACxBJ,EAAA,CAAAE,cAAA,iBAA2D;IAAjCF,EAAA,CAAAS,UAAA,mBAAA0E,wDAAA;MAAAnF,EAAA,CAAAW,aAAA,CAAAqE,GAAA;MAAA,MAAAzE,MAAA,GAAAP,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASP,MAAA,CAAA0E,oBAAA,EAAsB;IAAA,EAAC;IAACjF,EAAA,CAAAG,MAAA,aAAC;IAC9DH,EAD8D,CAAAI,YAAA,EAAS,EACjE;IAKFJ,EAHJ,CAAAE,cAAA,cAA2B,cAEU,iBAKhC;IADCF,EAAA,CAAAS,UAAA,mBAAA2E,wDAAA;MAAApF,EAAA,CAAAW,aAAA,CAAAqE,GAAA;MAAA,MAAAzE,MAAA,GAAAP,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASP,MAAA,CAAAgD,iBAAA,CAAkB,QAAQ,CAAC;IAAA,EAAC;IAErCvD,EAAA,CAAAG,MAAA,6BACF;IAAAH,EAAA,CAAAI,YAAA,EAAS;IACTJ,EAAA,CAAAE,cAAA,kBAIC;IADCF,EAAA,CAAAS,UAAA,mBAAA4E,yDAAA;MAAArF,EAAA,CAAAW,aAAA,CAAAqE,GAAA;MAAA,MAAAzE,MAAA,GAAAP,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASP,MAAA,CAAAgD,iBAAA,CAAkB,OAAO,CAAC;IAAA,EAAC;IAEpCvD,EAAA,CAAAG,MAAA,4BACF;IACFH,EADE,CAAAI,YAAA,EAAS,EACL;IAIJJ,EADF,CAAAE,cAAA,eAAwB,iBACO;IAC3BF,EAAA,CAAAG,MAAA,IACF;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IAENJ,EADF,CAAAE,cAAA,eAAgC,iBAQ5B;IAJAF,EAAA,CAAAoC,gBAAA,2BAAAkD,gEAAAlE,MAAA;MAAApB,EAAA,CAAAW,aAAA,CAAAqE,GAAA;MAAA,MAAAzE,MAAA,GAAAP,EAAA,CAAAa,aAAA;MAAAb,EAAA,CAAAsC,kBAAA,CAAA/B,MAAA,CAAAgF,oBAAA,EAAAnE,MAAA,MAAAb,MAAA,CAAAgF,oBAAA,GAAAnE,MAAA;MAAA,OAAApB,EAAA,CAAAc,WAAA,CAAAM,MAAA;IAAA,EAAkC;IAClCpB,EAAA,CAAAS,UAAA,mBAAA+E,wDAAA;MAAAxF,EAAA,CAAAW,aAAA,CAAAqE,GAAA;MAAA,MAAAzE,MAAA,GAAAP,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASP,MAAA,CAAAkF,uBAAA,EAAyB;IAAA,EAAC;IAJrCzF,EAAA,CAAAI,YAAA,EAOE;IAaFJ,EAVA,CAAA8C,UAAA,KAAA4C,sCAAA,kBAAmE,KAAAC,sCAAA,kBAUyB;IAoDhG3F,EADE,CAAAI,YAAA,EAAM,EACF;IAIJJ,EADF,CAAAE,cAAA,eAAwB,iBACM;IAAAF,EAAA,CAAAG,MAAA,eAAO;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IAC3CJ,EAAA,CAAAE,cAAA,oBAMC;IAJCF,EAAA,CAAAoC,gBAAA,2BAAAwD,mEAAAxE,MAAA;MAAApB,EAAA,CAAAW,aAAA,CAAAqE,GAAA;MAAA,MAAAzE,MAAA,GAAAP,EAAA,CAAAa,aAAA;MAAAb,EAAA,CAAAsC,kBAAA,CAAA/B,MAAA,CAAAsF,cAAA,EAAAzE,MAAA,MAAAb,MAAA,CAAAsF,cAAA,GAAAzE,MAAA;MAAA,OAAApB,EAAA,CAAAc,WAAA,CAAAM,MAAA;IAAA,EAA4B;IAI7BpB,EAAA,CAAAI,YAAA,EAAW;IACZJ,EAAA,CAAAE,cAAA,eAAwB;IAAAF,EAAA,CAAAG,MAAA,IAAgC;IAC1DH,EAD0D,CAAAI,YAAA,EAAM,EAC1D;IAGJJ,EADF,CAAAE,cAAA,eAA6B,kBAK1B;IAFCF,EAAA,CAAAS,UAAA,mBAAAqF,yDAAA;MAAA9F,EAAA,CAAAW,aAAA,CAAAqE,GAAA;MAAA,MAAAzE,MAAA,GAAAP,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASP,MAAA,CAAAwF,WAAA,EAAa;IAAA,EAAC;IAGvB/F,EAAA,CAAAG,MAAA,sBACF;IAAAH,EAAA,CAAAI,YAAA,EAAS;IACTJ,EAAA,CAAAE,cAAA,kBAAmE;IAAjCF,EAAA,CAAAS,UAAA,mBAAAuF,yDAAA;MAAAhG,EAAA,CAAAW,aAAA,CAAAqE,GAAA;MAAA,MAAAzE,MAAA,GAAAP,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASP,MAAA,CAAA0E,oBAAA,EAAsB;IAAA,EAAC;IAChEjF,EAAA,CAAAG,MAAA,gBACF;IACFH,EADE,CAAAI,YAAA,EAAS,EACL;IAGJJ,EADF,CAAAE,cAAA,eAAuB,SAClB;IAAAF,EAAA,CAAAG,MAAA,2DAA8C;IAIzDH,EAJyD,CAAAI,YAAA,EAAI,EACjD,EACF,EACF,EACF;;;;IA7HIJ,EAAA,CAAAK,SAAA,GAAyC;IAAzCL,EAAA,CAAA+D,WAAA,WAAAxD,MAAA,CAAAiD,WAAA,cAAyC;IAOzCxD,EAAA,CAAAK,SAAA,GAAwC;IAAxCL,EAAA,CAAA+D,WAAA,WAAAxD,MAAA,CAAAiD,WAAA,aAAwC;IAUxCxD,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAC,MAAA,CAAAiD,WAAA,mDACF;IAKIxD,EAAA,CAAAK,SAAA,GAAkC;IAAlCL,EAAA,CAAAkD,gBAAA,YAAA3C,MAAA,CAAAgF,oBAAA,CAAkC;IAElCvF,EAAA,CAAAmD,UAAA,gBAAA5C,MAAA,CAAAiD,WAAA,0DAAoF;IAKrDxD,EAAA,CAAAK,SAAA,EAAgC;IAAhCL,EAAA,CAAAmD,UAAA,SAAA5C,MAAA,CAAAkD,wBAAA,GAAgC;IAUhCzD,EAAA,CAAAK,SAAA,EAAyD;IAAzDL,EAAA,CAAAmD,UAAA,SAAA5C,MAAA,CAAAgF,oBAAA,KAAAhF,MAAA,CAAAkD,wBAAA,GAAyD;IA2D1FzD,EAAA,CAAAK,SAAA,GAA4B;IAA5BL,EAAA,CAAAkD,gBAAA,YAAA3C,MAAA,CAAAsF,cAAA,CAA4B;IAKN7F,EAAA,CAAAK,SAAA,GAAgC;IAAhCL,EAAA,CAAAM,kBAAA,KAAAC,MAAA,CAAAsF,cAAA,CAAAtB,MAAA,UAAgC;IAOtDvE,EAAA,CAAAK,SAAA,GAA8B;IAA9BL,EAAA,CAAAmD,UAAA,cAAA5C,MAAA,CAAA0F,cAAA,GAA8B;;;;;IA+B5BjG,EALJ,CAAAE,cAAA,cAGC,cAC6B,eACL;IAAAF,EAAA,CAAAG,MAAA,GAAoB;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAChDJ,EAAA,CAAAE,cAAA,eAAwB;IAAAF,EAAA,CAAAG,MAAA,GAAsC;;IAChEH,EADgE,CAAAI,YAAA,EAAO,EACjE;IACNJ,EAAA,CAAAE,cAAA,cAA6B;IAAAF,EAAA,CAAAG,MAAA,GAAqB;IACpDH,EADoD,CAAAI,YAAA,EAAM,EACpD;;;;IAJmBJ,EAAA,CAAAK,SAAA,GAAoB;IAApBL,EAAA,CAAAgB,iBAAA,CAAAkF,WAAA,CAAAC,MAAA,CAAoB;IACjBnG,EAAA,CAAAK,SAAA,GAAsC;IAAtCL,EAAA,CAAAgB,iBAAA,CAAAhB,EAAA,CAAAoG,WAAA,OAAAF,WAAA,CAAAG,SAAA,WAAsC;IAEnCrG,EAAA,CAAAK,SAAA,GAAqB;IAArBL,EAAA,CAAAgB,iBAAA,CAAAkF,WAAA,CAAAI,OAAA,CAAqB;;;;;IATtDtG,EAAA,CAAAE,cAAA,cAAuD;IACrDF,EAAA,CAAA8C,UAAA,IAAAyD,4CAAA,kBAGC;IAOHvG,EAAA,CAAAI,YAAA,EAAM;;;;IARkBJ,EAAA,CAAAK,SAAA,EAAa;IAAAL,EAAb,CAAAmD,UAAA,YAAA5C,MAAA,CAAAiG,QAAA,CAAa,iBAAAjG,MAAA,CAAAkG,YAAA,CAAqB;;;;;IAWxDzG,EADF,CAAAE,cAAA,cAAuD,QAClD;IAAAF,EAAA,CAAAG,MAAA,sBAAe;IAAAH,EAAA,CAAAI,YAAA,EAAI;IACtBJ,EAAA,CAAAE,cAAA,YAAgB;IAAAF,EAAA,CAAAG,MAAA,qDAA8C;IAChEH,EADgE,CAAAI,YAAA,EAAI,EAC9D;;;;;;IAxBZJ,EAAA,CAAAE,cAAA,cAAqF;IAAhCF,EAAA,CAAAS,UAAA,mBAAAiG,sDAAA;MAAA1G,EAAA,CAAAW,aAAA,CAAAgG,IAAA;MAAA,MAAApG,MAAA,GAAAP,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASP,MAAA,CAAAqG,mBAAA,EAAqB;IAAA,EAAC;IAClF5G,EAAA,CAAAE,cAAA,cAAqE;IAAnCF,EAAA,CAAAS,UAAA,mBAAAoG,sDAAAzF,MAAA;MAAApB,EAAA,CAAAW,aAAA,CAAAgG,IAAA;MAAA,OAAA3G,EAAA,CAAAc,WAAA,CAASM,MAAA,CAAAE,eAAA,EAAwB;IAAA,EAAC;IAEhEtB,EADF,CAAAE,cAAA,cAA0B,SACpB;IAAAF,EAAA,CAAAG,MAAA,eAAQ;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACjBJ,EAAA,CAAAE,cAAA,iBAA0D;IAAhCF,EAAA,CAAAS,UAAA,mBAAAqG,yDAAA;MAAA9G,EAAA,CAAAW,aAAA,CAAAgG,IAAA;MAAA,MAAApG,MAAA,GAAAP,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASP,MAAA,CAAAqG,mBAAA,EAAqB;IAAA,EAAC;IAAC5G,EAAA,CAAAG,MAAA,aAAC;IAC7DH,EAD6D,CAAAI,YAAA,EAAS,EAChE;IAENJ,EAAA,CAAAE,cAAA,cAA2B;IAczBF,EAbA,CAAA8C,UAAA,IAAAiE,sCAAA,kBAAuD,IAAAC,sCAAA,kBAaA;IAM7DhH,EAFI,CAAAI,YAAA,EAAM,EACF,EACF;;;;IAnB4BJ,EAAA,CAAAK,SAAA,GAAyB;IAAzBL,EAAA,CAAAmD,UAAA,SAAA5C,MAAA,CAAAiG,QAAA,CAAAjC,MAAA,KAAyB;IAa3BvE,EAAA,CAAAK,SAAA,EAA2B;IAA3BL,EAAA,CAAAmD,UAAA,SAAA5C,MAAA,CAAAiG,QAAA,CAAAjC,MAAA,OAA2B;;;;;IAqB/CvE,EAAA,CAAAC,SAAA,eAKE;;;;IAHAD,EAAA,CAAAmD,UAAA,QAAA5C,MAAA,CAAA0G,WAAA,CAAAC,MAAA,EAAAlH,EAAA,CAAAmH,aAAA,CAA0B;;;;;IAK1BnH,EADF,CAAAE,cAAA,eAA4D,gBAC5B;IAC5BF,EAAA,CAAAG,MAAA,GACF;IACFH,EADE,CAAAI,YAAA,EAAO,EACH;;;;;IAFFJ,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,OAAAC,MAAA,CAAAU,WAAA,kBAAAV,MAAA,CAAAU,WAAA,CAAAC,QAAA,mBAAAkG,OAAA,GAAA7G,MAAA,CAAAU,WAAA,CAAAC,QAAA,CAAAmG,MAAA,sBAAAD,OAAA,CAAAE,WAAA,gBACF;;;;;;IArBdtH,EAAA,CAAAE,cAAA,cAAwF;IAAjCF,EAAA,CAAAS,UAAA,mBAAA8G,sDAAA;MAAAvH,EAAA,CAAAW,aAAA,CAAA6G,IAAA;MAAA,MAAAjH,MAAA,GAAAP,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASP,MAAA,CAAAkH,oBAAA,EAAsB;IAAA,EAAC;IACrFzH,EAAA,CAAAE,cAAA,cAA6E;IAAnCF,EAAA,CAAAS,UAAA,mBAAAiH,sDAAAtG,MAAA;MAAApB,EAAA,CAAAW,aAAA,CAAA6G,IAAA;MAAA,OAAAxH,EAAA,CAAAc,WAAA,CAASM,MAAA,CAAAE,eAAA,EAAwB;IAAA,EAAC;IAExEtB,EADF,CAAAE,cAAA,cAA0B,SACpB;IAAAF,EAAA,CAAAG,MAAA,uBAAgB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACzBJ,EAAA,CAAAE,cAAA,iBAA2D;IAAjCF,EAAA,CAAAS,UAAA,mBAAAkH,yDAAA;MAAA3H,EAAA,CAAAW,aAAA,CAAA6G,IAAA;MAAA,MAAAjH,MAAA,GAAAP,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASP,MAAA,CAAAkH,oBAAA,EAAsB;IAAA,EAAC;IAACzH,EAAA,CAAAG,MAAA,aAAC;IAC9DH,EAD8D,CAAAI,YAAA,EAAS,EACjE;IAMAJ,EAJN,CAAAE,cAAA,cAA2B,cAEG,cACI,eACA;IAO1BF,EANA,CAAA8C,UAAA,KAAA8E,uCAAA,kBAKE,KAAAC,uCAAA,kBAC0D;IAK9D7H,EAAA,CAAAI,YAAA,EAAM;IAEJJ,EADF,CAAAE,cAAA,eAA4B,iBACyB;IACjDF,EAAA,CAAAG,MAAA,oCACF;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IACRJ,EAAA,CAAAE,cAAA,iBAME;IAFAF,EAAA,CAAAS,UAAA,oBAAAqH,0DAAA1G,MAAA;MAAApB,EAAA,CAAAW,aAAA,CAAA6G,IAAA;MAAA,MAAAjH,MAAA,GAAAP,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAAUP,MAAA,CAAAwH,cAAA,CAAA3G,MAAA,CAAsB;IAAA,EAAC;IAKzCpB,EATM,CAAAI,YAAA,EAME,EACE,EACF,EACF;IAKFJ,EAFJ,CAAAE,cAAA,eAA0B,eACA,aACf;IAAAF,EAAA,CAAAG,MAAA,gBAAQ;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IAErBJ,EADF,CAAAE,cAAA,eAA4B,YACpB;IAAAF,EAAA,CAAAG,MAAA,IAAwC;IAElDH,EAFkD,CAAAI,YAAA,EAAO,EACjD,EACF;IAGJJ,EADF,CAAAE,cAAA,eAAwB,aACf;IAAAF,EAAA,CAAAG,MAAA,qBAAa;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IAE1BJ,EADF,CAAAE,cAAA,eAA4B,YACpB;IAAAF,EAAA,CAAAG,MAAA,IAAoC;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACjDJ,EAAA,CAAAE,cAAA,gBAAyB;IAAAF,EAAA,CAAAG,MAAA,oDAA4C;IAEzEH,EAFyE,CAAAI,YAAA,EAAO,EACxE,EACF;IAGJJ,EADF,CAAAE,cAAA,eAAwB,aACf;IAAAF,EAAA,CAAAG,MAAA,oBAAY;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IAEzBJ,EADF,CAAAE,cAAA,eAA4B,YACpB;IAAAF,EAAA,CAAAG,MAAA,IAA0C;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACvDJ,EAAA,CAAAE,cAAA,gBAAyB;IAAAF,EAAA,CAAAG,MAAA,2DAAmD;IAGlFH,EAHkF,CAAAI,YAAA,EAAO,EAC/E,EACF,EACF;IAIJJ,EADF,CAAAE,cAAA,eAA2B,UACrB;IAAAF,EAAA,CAAAG,MAAA,4BAAoB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAE3BJ,EADF,CAAAE,cAAA,eAA2B,gBACI;IAAAF,EAAA,CAAAG,MAAA,gCAAc;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAClDJ,EAAA,CAAAE,cAAA,gBAA6B;IAAAF,EAAA,CAAAG,MAAA,kDAA0C;IACzEH,EADyE,CAAAI,YAAA,EAAO,EAC1E;IAEJJ,EADF,CAAAE,cAAA,eAA2B,gBACI;IAAAF,EAAA,CAAAG,MAAA,0CAAmB;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACvDJ,EAAA,CAAAE,cAAA,gBAA6B;IAAAF,EAAA,CAAAG,MAAA,yCAAiC;IAChEH,EADgE,CAAAI,YAAA,EAAO,EACjE;IAEJJ,EADF,CAAAE,cAAA,eAA2B,gBACI;IAAAF,EAAA,CAAAG,MAAA,kCAAgB;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACpDJ,EAAA,CAAAE,cAAA,gBAA6B;IAAAF,EAAA,CAAAG,MAAA,qBAAa;IAE9CH,EAF8C,CAAAI,YAAA,EAAO,EAC7C,EACF;IAGJJ,EADF,CAAAE,cAAA,eAA6B,kBACwC;IAAjCF,EAAA,CAAAS,UAAA,mBAAAuH,0DAAA;MAAAhI,EAAA,CAAAW,aAAA,CAAA6G,IAAA;MAAA,MAAAjH,MAAA,GAAAP,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASP,MAAA,CAAAkH,oBAAA,EAAsB;IAAA,EAAC;IAChEzH,EAAA,CAAAG,MAAA,eACF;IAIRH,EAJQ,CAAAI,YAAA,EAAS,EACL,EACF,EACF,EACF;;;;IA5ESJ,EAAA,CAAAK,SAAA,IAAwB;IAAxBL,EAAA,CAAAmD,UAAA,SAAA5C,MAAA,CAAA0G,WAAA,CAAAC,MAAA,CAAwB;IAKrBlH,EAAA,CAAAK,SAAA,EAAyB;IAAzBL,EAAA,CAAAmD,UAAA,UAAA5C,MAAA,CAAA0G,WAAA,CAAAC,MAAA,CAAyB;IA0BzBlH,EAAA,CAAAK,SAAA,IAAwC;IAAxCL,EAAA,CAAAgB,iBAAA,EAAAT,MAAA,CAAAU,WAAA,kBAAAV,MAAA,CAAAU,WAAA,CAAAC,QAAA,eAAwC;IAOxClB,EAAA,CAAAK,SAAA,GAAoC;IAApCL,EAAA,CAAAgB,iBAAA,CAAAT,MAAA,CAAA0G,WAAA,CAAAzE,KAAA,cAAoC;IAQpCxC,EAAA,CAAAK,SAAA,GAA0C;IAA1CL,EAAA,CAAAgB,iBAAA,CAAAT,MAAA,CAAA0G,WAAA,CAAAgB,WAAA,cAA0C;;;;;IAkC1DjI,EADF,CAAAE,cAAA,eAAuH,WAC/G;IAAAF,EAAA,CAAAG,MAAA,6DAAiD;IACzDH,EADyD,CAAAI,YAAA,EAAO,EAC1D;;;AD7TN,OAAM,MAAO8H,gBAAgB;EAkD3BC,YACUC,WAAwB,EACxBC,cAA8B,EAC9BC,mBAAwC;IAFxC,KAAAF,WAAW,GAAXA,WAAW;IACX,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,mBAAmB,GAAnBA,mBAAmB;IApDrB,KAAAC,QAAQ,GAAG,IAAIzI,OAAO,EAAQ;IAEtC;IACA,KAAA0I,WAAW,GAAgB,OAAO;IAElC;IACA,KAAAC,cAAc,GAAG,KAAK;IACtB,KAAAC,gBAAgB,GAAG,KAAK;IACxB,KAAAC,iBAAiB,GAAG,KAAK;IACzB,KAAAC,eAAe,GAAG,KAAK;IACvB,KAAAC,mBAAmB,GAAG,KAAK;IAE3B;IACA,KAAAlH,mBAAmB,GAAG;MAAEC,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAC,CAAE;IAEpC;IACA,KAAAU,gBAAgB,GAAqB;MAAEC,KAAK,EAAE,EAAE;MAAEI,UAAU,EAAE;IAAE,CAAE;IAClE,KAAAd,UAAU,GAAG,EAAE;IACf,KAAAsB,SAAS,GAAG,KAAK;IAEjB;IACA,KAAAyC,cAAc,GAAG,EAAE;IACnB,KAAAiD,iBAAiB,GAAG,EAAE;IACtB,KAAAC,aAAa,GAAG,EAAE;IAClB,KAAAvF,WAAW,GAAuB,QAAQ;IAC1C,KAAAgD,QAAQ,GAAc,EAAE;IACxB,KAAAhG,WAAW,GAAG,CAAC;IAEf;IACA,KAAAwI,QAAQ,GAAc,EAAE;IACxB,KAAAC,MAAM,GAAY,EAAE;IACpB,KAAApE,gBAAgB,GAAc,EAAE;IAChC,KAAAC,cAAc,GAAY,EAAE;IAC5B,KAAAS,oBAAoB,GAAG,EAAE;IAEzB;IACA,KAAAtE,WAAW,GAAQ,IAAI;IAEvB;IACA,KAAAgG,WAAW,GAAG;MACZC,MAAM,EAAE,EAAE;MACV1E,KAAK,EAAE,EAAE;MACTyF,WAAW,EAAE;KACd;IAED;IACQ,KAAAiB,cAAc,GAAQ,IAAI;IAC1B,KAAAC,iBAAiB,GAAG,GAAG,CAAC,CAAC;EAM9B;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACC,aAAa,EAAE;IACpB,IAAI,CAACC,oBAAoB,EAAE;IAC3B,IAAI,CAACC,iBAAiB,EAAE;EAC1B;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACjB,QAAQ,CAACkB,IAAI,EAAE;IACpB,IAAI,CAAClB,QAAQ,CAACmB,QAAQ,EAAE;EAC1B;EAEQL,aAAaA,CAAA;IACnB;IACA,IAAI,IAAI,CAACjB,WAAW,CAACuB,eAAe,EAAE,EAAE;MACtC,IAAI,CAAC1I,WAAW,GAAG,IAAI,CAACmH,WAAW,CAACwB,cAAc,EAAE;MACpD,IAAI,CAACpB,WAAW,GAAG,eAAe;MAClC,IAAI,CAACqB,YAAY,EAAE;MACnB,IAAI,CAACC,YAAY,EAAE;MACnB,IAAI,CAACC,UAAU,EAAE;KAClB,MAAM;MACL,IAAI,CAACvB,WAAW,GAAG,OAAO;;EAE9B;EAEQe,iBAAiBA,CAAA;IACvB,IAAI,CAACnB,WAAW,CAAC4B,UAAU,CACxBC,IAAI,CAAClK,SAAS,CAAC,IAAI,CAACwI,QAAQ,CAAC,CAAC,CAC9B2B,SAAS,CAACC,IAAI,IAAG;MAChB,IAAIA,IAAI,EAAE;QACR,IAAI,CAAClJ,WAAW,GAAGkJ,IAAI;QACvB,IAAI,CAAC3B,WAAW,GAAG,eAAe;QAClC,IAAI,CAACC,cAAc,GAAG,KAAK;QAC3B,IAAI,CAACoB,YAAY,EAAE;OACpB,MAAM;QACL,IAAI,CAAC5I,WAAW,GAAG,IAAI;QACvB,IAAI,CAACuH,WAAW,GAAG,OAAO;QAC1B,IAAI,CAAChC,QAAQ,GAAG,EAAE;QAClB,IAAI,CAAChG,WAAW,GAAG,CAAC;;IAExB,CAAC,CAAC;EACN;EAEQ8I,oBAAoBA,CAAA;IAC1B,IAAI,CAACjB,cAAc,CAAC+B,SAAS,CAC1BH,IAAI,CAAClK,SAAS,CAAC,IAAI,CAACwI,QAAQ,CAAC,CAAC,CAC9B2B,SAAS,CAAC1D,QAAQ,IAAG;MACpB,IAAI,CAACA,QAAQ,GAAGA,QAAQ;MACxB,IAAI,CAAC6D,iBAAiB,EAAE;IAC1B,CAAC,CAAC;IAEJ,IAAI,CAAChC,cAAc,CAACiC,WAAW,CAC5BL,IAAI,CAAClK,SAAS,CAAC,IAAI,CAACwI,QAAQ,CAAC,CAAC,CAC9B2B,SAAS,CAACK,OAAO,IAAG;MACnB,IAAI,CAAC/D,QAAQ,CAACgE,OAAO,CAACD,OAAO,CAAC;MAC9B,IAAI,CAACF,iBAAiB,EAAE;MACxB,IAAI,CAAC/B,mBAAmB,CAACmC,gBAAgB,CAAC,sBAAsB,CAAC;IACnE,CAAC,CAAC;EACN;EAEQJ,iBAAiBA,CAAA;IACvB,IAAI,CAAC7J,WAAW,GAAG,IAAI,CAACgG,QAAQ,CAACkE,MAAM,CAACC,CAAC,IAAI,CAACA,CAAC,CAACC,IAAI,CAAC,CAACrG,MAAM;IAC5D,IAAI,IAAI,CAAC/D,WAAW,GAAG,CAAC,IAAI,IAAI,CAACgI,WAAW,KAAK,eAAe,EAAE;MAChE,IAAI,CAACA,WAAW,GAAG,QAAQ;KAC5B,MAAM,IAAI,IAAI,CAAChI,WAAW,KAAK,CAAC,IAAI,IAAI,CAACgI,WAAW,KAAK,QAAQ,EAAE;MAClE,IAAI,CAACA,WAAW,GAAG,eAAe;;EAEtC;EAEQqB,YAAYA,CAAA;IAClB,IAAI,CAACxB,cAAc,CAACwB,YAAY,EAAE,CAC/BI,IAAI,CAAClK,SAAS,CAAC,IAAI,CAACwI,QAAQ,CAAC,CAAC,CAC9B2B,SAAS,CAAC;MACTT,IAAI,EAAGjD,QAAQ,IAAI;QACjB,IAAI,CAACA,QAAQ,GAAGA,QAAQ;QACxB,IAAI,CAAC6D,iBAAiB,EAAE;MAC1B,CAAC;MACDQ,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAClD;KACD,CAAC;EACN;EAEQf,YAAYA,CAAA;IAClB;IACA,IAAI,CAACd,QAAQ,GAAG,CACd;MACE+B,EAAE,EAAE,GAAG;MACP7J,QAAQ,EAAE,OAAO;MACjBsB,KAAK,EAAE,mBAAmB;MAC1BwB,QAAQ,EAAE;KACX,EACD;MACE+G,EAAE,EAAE,GAAG;MACP7J,QAAQ,EAAE,KAAK;MACfsB,KAAK,EAAE,iBAAiB;MACxBwB,QAAQ,EAAE,KAAK;MACfgH,QAAQ,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,MAAM,CAAC,CAAC;KACzC,CACF;IACD,IAAI,CAACrG,gBAAgB,GAAG,CAAC,GAAG,IAAI,CAACmE,QAAQ,CAAC;EAC5C;EAEQe,UAAUA,CAAA;IAChB;IACA,IAAI,CAACd,MAAM,GAAG,CACZ;MACE8B,EAAE,EAAE,QAAQ;MACZ1G,IAAI,EAAE,WAAW;MACjBC,OAAO,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,cAAc,CAAC;MACnCE,QAAQ,EAAE;KACX,EACD;MACEuG,EAAE,EAAE,QAAQ;MACZ1G,IAAI,EAAE,QAAQ;MACdC,OAAO,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,cAAc,CAAC;MACnCE,QAAQ,EAAE;KACX,CACF;IACD,IAAI,CAACM,cAAc,GAAG,CAAC,GAAG,IAAI,CAACmE,MAAM,CAAC;EACxC;EAEA;EACAkC,aAAaA,CAAA;IACX;IACA,IAAI,IAAI,CAACvC,eAAe,EAAE;MACxB,IAAI,CAACwC,gBAAgB,EAAE;MACvB;;IAGF,QAAQ,IAAI,CAAC5C,WAAW;MACtB,KAAK,OAAO;QACV,IAAI,CAAC6C,cAAc,EAAE;QACrB;MACF,KAAK,eAAe;QAClB,IAAI,CAACC,mBAAmB,EAAE;QAC1B;MACF,KAAK,QAAQ;QACX,IAAI,CAACC,kBAAkB,EAAE;QACzB;MACF,KAAK,WAAW;QACd;QACA;;EAEN;EAEA;EACAC,kBAAkBA,CAACC,KAAiB;IAClCA,KAAK,CAACC,cAAc,EAAE;IAEtB;IACA,IAAI,IAAI,CAAClD,WAAW,KAAK,OAAO,EAAE;IAElC,IAAI,CAACI,eAAe,GAAG,IAAI;IAC3B,IAAI,CAACjH,mBAAmB,GAAG;MACzBC,CAAC,EAAE6J,KAAK,CAACE,OAAO;MAChB9J,CAAC,EAAE4J,KAAK,CAACG;KACV;EACH;EAEA;EACAC,kBAAkBA,CAACJ,KAAiB;IAClC;IACA,IAAI,IAAI,CAACjD,WAAW,KAAK,OAAO,EAAE;IAElC,IAAI,CAACU,cAAc,GAAG4C,UAAU,CAAC,MAAK;MACpC,MAAMC,KAAK,GAAGN,KAAK,CAACO,OAAO,CAAC,CAAC,CAAC;MAC9B,IAAI,CAACpD,eAAe,GAAG,IAAI;MAC3B,IAAI,CAACjH,mBAAmB,GAAG;QACzBC,CAAC,EAAEmK,KAAK,CAACJ,OAAO;QAChB9J,CAAC,EAAEkK,KAAK,CAACH;OACV;MAED;MACA,IAAIK,SAAS,CAACC,OAAO,EAAE;QACrBD,SAAS,CAACC,OAAO,CAAC,EAAE,CAAC;;IAEzB,CAAC,EAAE,IAAI,CAAC/C,iBAAiB,CAAC;EAC5B;EAEAgD,gBAAgBA,CAAA;IACd,IAAI,IAAI,CAACjD,cAAc,EAAE;MACvBkD,YAAY,CAAC,IAAI,CAAClD,cAAc,CAAC;MACjC,IAAI,CAACA,cAAc,GAAG,IAAI;;EAE9B;EAEAmD,iBAAiBA,CAAA;IACf;IACA,IAAI,IAAI,CAACnD,cAAc,EAAE;MACvBkD,YAAY,CAAC,IAAI,CAAClD,cAAc,CAAC;MACjC,IAAI,CAACA,cAAc,GAAG,IAAI;;EAE9B;EAEA;EACAmC,cAAcA,CAAA;IACZ,IAAI,CAAC5C,cAAc,GAAG,IAAI;IAC1B,IAAI,CAAClG,gBAAgB,GAAG;MAAEC,KAAK,EAAE,EAAE;MAAEI,UAAU,EAAE;IAAE,CAAE;IACrD,IAAI,CAACd,UAAU,GAAG,EAAE;EACtB;EAEAG,eAAeA,CAAA;IACb,IAAI,CAACwG,cAAc,GAAG,KAAK;IAC3B,IAAI,CAAClG,gBAAgB,GAAG;MAAEC,KAAK,EAAE,EAAE;MAAEI,UAAU,EAAE;IAAE,CAAE;IACrD,IAAI,CAACd,UAAU,GAAG,EAAE;EACtB;EAEAY,kBAAkBA,CAAA;IAChB;IACA,IAAI,IAAI,CAAC4J,kBAAkB,EAAE,EAAE;MAC7B,IAAI,CAACC,YAAY,EAAE;;EAEvB;EAEQD,kBAAkBA,CAAA;IACxB,MAAME,UAAU,GAAG,IAAI,CAACjK,gBAAgB,CAACC,KAAK,CAACiK,QAAQ,CAAC,GAAG,CAAC,IAC1C,IAAI,CAAClK,gBAAgB,CAACC,KAAK,CAACiK,QAAQ,CAAC,GAAG,CAAC;IAC3D,MAAMC,eAAe,GAAG,IAAI,CAACnK,gBAAgB,CAACK,UAAU,CAAC2B,MAAM,IAAI,CAAC,IAC7C,OAAO,CAACoI,IAAI,CAAC,IAAI,CAACpK,gBAAgB,CAACK,UAAU,CAAC,IAC9C,OAAO,CAAC+J,IAAI,CAAC,IAAI,CAACpK,gBAAgB,CAACK,UAAU,CAAC,IAC9C,OAAO,CAAC+J,IAAI,CAAC,IAAI,CAACpK,gBAAgB,CAACK,UAAU,CAAC,IAC9C,cAAc,CAAC+J,IAAI,CAAC,IAAI,CAACpK,gBAAgB,CAACK,UAAU,CAAC;IAE5E,OAAO4J,UAAU,IAAIE,eAAe;EACtC;EAEQH,YAAYA,CAAA;IAClB,IAAI,IAAI,CAACnJ,SAAS,EAAE;IAEpB,IAAI,CAACA,SAAS,GAAG,IAAI;IACrB,IAAI,CAACtB,UAAU,GAAG,EAAE;IAEpB,IAAI,CAACsG,WAAW,CAACwE,KAAK,CAAC,IAAI,CAACrK,gBAAgB,CAACC,KAAK,EAAE,IAAI,CAACD,gBAAgB,CAACK,UAAU,CAAC,CAClFqH,IAAI,CAAClK,SAAS,CAAC,IAAI,CAACwI,QAAQ,CAAC,CAAC,CAC9B2B,SAAS,CAAC;MACTT,IAAI,EAAGoD,QAAQ,IAAI;QACjB,IAAI,CAACzJ,SAAS,GAAG,KAAK;QACtB;MACF,CAAC;MACDyH,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACzH,SAAS,GAAG,KAAK;QACtB,IAAI,CAACtB,UAAU,GAAG+I,KAAK,CAACN,OAAO,IAAI,uBAAuB;MAC5D;KACD,CAAC;EACN;EAEA;EACAe,mBAAmBA,CAAA;IACjB,IAAI,CAAC5C,gBAAgB,GAAG,IAAI;IAC5B,IAAI,CAAC7C,cAAc,GAAG,EAAE;IACxB,IAAI,CAACiD,iBAAiB,GAAG,EAAE;IAC3B,IAAI,CAACC,aAAa,GAAG,EAAE;IACvB,IAAI,CAACvF,WAAW,GAAG,QAAQ;IAC3B,IAAI,CAAC+B,oBAAoB,GAAG,EAAE;IAC9B,IAAI,CAACV,gBAAgB,GAAG,CAAC,GAAG,IAAI,CAACmE,QAAQ,CAAC;IAC1C,IAAI,CAAClE,cAAc,GAAG,CAAC,GAAG,IAAI,CAACmE,MAAM,CAAC;IACtC,IAAI,CAACT,WAAW,GAAG,WAAW;EAChC;EAEAvD,oBAAoBA,CAAA;IAClB,IAAI,CAACyD,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAAC7C,cAAc,GAAG,EAAE;IACxB,IAAI,CAACiD,iBAAiB,GAAG,EAAE;IAC3B,IAAI,CAACC,aAAa,GAAG,EAAE;IACvB,IAAI,CAACxD,oBAAoB,GAAG,EAAE;IAC9B,IAAI,CAACiD,WAAW,GAAG,eAAe;EACpC;EAEAzC,WAAWA,CAAA;IACT,IAAI,CAAC,IAAI,CAACF,cAAc,CAACiH,IAAI,EAAE,EAAE;IAEjC;IACA,IAAI,IAAI,CAACtJ,WAAW,KAAK,QAAQ,IAAI,CAAC,IAAI,CAACsF,iBAAiB,EAAE;MAC5D,IAAI,CAACR,mBAAmB,CAACmC,gBAAgB,CAAC,2BAA2B,EAAE,SAAS,CAAC;MACjF;;IAGF,IAAI,IAAI,CAACjH,WAAW,KAAK,OAAO,IAAI,CAAC,IAAI,CAACuF,aAAa,EAAE;MACvD,IAAI,CAACT,mBAAmB,CAACmC,gBAAgB,CAAC,uBAAuB,EAAE,SAAS,CAAC;MAC7E;;IAGF,MAAMF,OAAO,GAAqB;MAChCjE,OAAO,EAAE,IAAI,CAACT,cAAc,CAACiH,IAAI,EAAE;MACnCzG,SAAS,EAAE,IAAI4E,IAAI,EAAE;MACrB9E,MAAM,EAAE,IAAI,CAAClF,WAAW,EAAEC,QAAQ,IAAI,SAAS;MAC/C6L,SAAS,EAAE,IAAI,CAACvJ,WAAW,KAAK,QAAQ,GAAG,IAAI,CAACsF,iBAAiB,GAAGkE,SAAS;MAC7EC,OAAO,EAAE,IAAI,CAACzJ,WAAW,KAAK,OAAO,GAAG,IAAI,CAACuF,aAAa,GAAGiE;KAC9D;IAED,IAAI,CAAC3E,cAAc,CAACtC,WAAW,CAACwE,OAAO,CAAC,CACrCN,IAAI,CAAClK,SAAS,CAAC,IAAI,CAACwI,QAAQ,CAAC,CAAC,CAC9B2B,SAAS,CAAC;MACTT,IAAI,EAAEA,CAAA,KAAK;QACT,IAAI,CAACxE,oBAAoB,EAAE;QAC3B,IAAI,CAACqD,mBAAmB,CAACmC,gBAAgB,CAAC,cAAc,CAAC;MAC3D,CAAC;MACDI,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;QAC/C,IAAI,CAACvC,mBAAmB,CAACmC,gBAAgB,CAAC,wBAAwB,EAAE,OAAO,CAAC;MAC9E;KACD,CAAC;EACN;EAEA;EACAhF,uBAAuBA,CAAA;IACrB,MAAMyH,KAAK,GAAG,IAAI,CAAC3H,oBAAoB,CAAC4H,WAAW,EAAE;IAErD,IAAI,IAAI,CAAC3J,WAAW,KAAK,QAAQ,EAAE;MACjC,IAAI,CAACqB,gBAAgB,GAAG,IAAI,CAACmE,QAAQ,CAAC0B,MAAM,CAAC0C,OAAO,IAClDA,OAAO,CAAClM,QAAQ,CAACiM,WAAW,EAAE,CAACV,QAAQ,CAACS,KAAK,CAAC,IAC9CE,OAAO,CAAC5K,KAAK,CAAC2K,WAAW,EAAE,CAACV,QAAQ,CAACS,KAAK,CAAC,CAC5C;KACF,MAAM;MACL,IAAI,CAACpI,cAAc,GAAG,IAAI,CAACmE,MAAM,CAACyB,MAAM,CAAC2C,KAAK,IAC5CA,KAAK,CAAChJ,IAAI,CAAC8I,WAAW,EAAE,CAACV,QAAQ,CAACS,KAAK,CAAC,CACzC;;EAEL;EAEApJ,aAAaA,CAACsJ,OAAgB;IAC5B,IAAI,CAACtE,iBAAiB,GAAGsE,OAAO,CAACrC,EAAE;IACnC,IAAI,CAACxF,oBAAoB,GAAG6H,OAAO,CAAClM,QAAQ;IAC5C,IAAI,CAAC2D,gBAAgB,GAAG,EAAE;EAC5B;EAEAT,WAAWA,CAACiJ,KAAY;IACtB,IAAI,CAACtE,aAAa,GAAGsE,KAAK,CAACtC,EAAE;IAC7B,IAAI,CAACxF,oBAAoB,GAAG8H,KAAK,CAAChJ,IAAI;IACtC,IAAI,CAACS,cAAc,GAAG,EAAE;EAC1B;EAEAvB,iBAAiBA,CAAC+J,IAAwB;IACxC,IAAI,CAAC9J,WAAW,GAAG8J,IAAI;IACvB,IAAI,CAACxE,iBAAiB,GAAG,EAAE;IAC3B,IAAI,CAACC,aAAa,GAAG,EAAE;IACvB,IAAI,CAACxD,oBAAoB,GAAG,EAAE;IAC9B,IAAI,CAACE,uBAAuB,EAAE;EAChC;EAEAhC,wBAAwBA,CAAA;IACtB,IAAI,IAAI,CAACD,WAAW,KAAK,QAAQ,IAAI,IAAI,CAACsF,iBAAiB,EAAE;MAC3D,MAAMsE,OAAO,GAAG,IAAI,CAACpE,QAAQ,CAACuE,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACzC,EAAE,KAAK,IAAI,CAACjC,iBAAiB,CAAC;MACxE,OAAOsE,OAAO,EAAElM,QAAQ,IAAI,SAAS;;IAGvC,IAAI,IAAI,CAACsC,WAAW,KAAK,OAAO,IAAI,IAAI,CAACuF,aAAa,EAAE;MACtD,MAAMsE,KAAK,GAAG,IAAI,CAACpE,MAAM,CAACsE,IAAI,CAACE,CAAC,IAAIA,CAAC,CAAC1C,EAAE,KAAK,IAAI,CAAChC,aAAa,CAAC;MAChE,OAAOsE,KAAK,EAAEhJ,IAAI,IAAI,eAAe;;IAGvC,OAAO,EAAE;EACX;EAEA4B,cAAcA,CAAA;IACZ,MAAMyH,UAAU,GAAG,IAAI,CAAC7H,cAAc,CAACiH,IAAI,EAAE,CAACvI,MAAM,GAAG,CAAC;IACxD,MAAMoJ,YAAY,GAAG,IAAI,CAACnK,WAAW,KAAK,QAAQ,GAAG,CAAC,CAAC,IAAI,CAACsF,iBAAiB,GAAG,CAAC,CAAC,IAAI,CAACC,aAAa;IACpG,OAAO2E,UAAU,IAAIC,YAAY;EACnC;EAEA;EACApC,kBAAkBA,CAAA;IAChB,IAAI,CAAC5C,iBAAiB,GAAG,IAAI;IAC7B,IAAI,CAACiF,kBAAkB,EAAE;EAC3B;EAEAhH,mBAAmBA,CAAA;IACjB,IAAI,CAAC+B,iBAAiB,GAAG,KAAK;EAChC;EAEQiF,kBAAkBA,CAAA;IACxB,IAAI,CAACvF,cAAc,CAACwF,aAAa,EAAE,CAChC5D,IAAI,CAAClK,SAAS,CAAC,IAAI,CAACwI,QAAQ,CAAC,CAAC,CAC9B2B,SAAS,CAAC,MAAK;MACd,IAAI,CAACG,iBAAiB,EAAE;IAC1B,CAAC,CAAC;EACN;EAEA;EACAe,gBAAgBA,CAAA;IACd,IAAI,CAACxC,eAAe,GAAG,KAAK;EAC9B;EAEApH,mBAAmBA,CAAA;IACjB,IAAI,CAACqH,mBAAmB,GAAG,IAAI;IAC/B,IAAI,CAACD,eAAe,GAAG,KAAK;IAC5B,IAAI,CAACkF,eAAe,EAAE;EACxB;EAEArG,oBAAoBA,CAAA;IAClB,IAAI,CAACoB,mBAAmB,GAAG,KAAK;EAClC;EAEA;EACAiF,eAAeA,CAAA;IACb,IAAI,IAAI,CAAC7M,WAAW,EAAE;MACpB,IAAI,CAACgG,WAAW,GAAG;QACjBC,MAAM,EAAE,IAAI,CAACjG,WAAW,CAACiG,MAAM,IAAI,EAAE;QACrC1E,KAAK,EAAE,IAAI,CAACvB,WAAW,CAACuB,KAAK,IAAI,EAAE;QACnCyF,WAAW,EAAE,IAAI,CAAChH,WAAW,CAACgH,WAAW,IAAI;OAC9C;;EAEL;EAEAF,cAAcA,CAAC0D,KAAY;IACzB,MAAMsC,KAAK,GAAGtC,KAAK,CAACuC,MAA0B;IAC9C,IAAID,KAAK,CAACE,KAAK,IAAIF,KAAK,CAACE,KAAK,CAAC,CAAC,CAAC,EAAE;MACjC,MAAMC,IAAI,GAAGH,KAAK,CAACE,KAAK,CAAC,CAAC,CAAC;MAE3B;MACA,IAAI,CAACC,IAAI,CAACZ,IAAI,CAACa,UAAU,CAAC,QAAQ,CAAC,EAAE;QACnC,IAAI,CAAC7F,mBAAmB,CAACmC,gBAAgB,CAAC,6BAA6B,EAAE,OAAO,CAAC;QACjF;;MAGF;MACA,IAAIyD,IAAI,CAACE,IAAI,GAAG,CAAC,GAAG,IAAI,GAAG,IAAI,EAAE;QAC/B,IAAI,CAAC9F,mBAAmB,CAACmC,gBAAgB,CAAC,gCAAgC,EAAE,OAAO,CAAC;QACpF;;MAGF,MAAM4D,MAAM,GAAG,IAAIC,UAAU,EAAE;MAC/BD,MAAM,CAACE,MAAM,GAAIC,CAAC,IAAI;QACpB,IAAI,CAACvH,WAAW,CAACC,MAAM,GAAGsH,CAAC,CAACR,MAAM,EAAES,MAAgB;QACpD,IAAI,CAACC,gBAAgB,EAAE;MACzB,CAAC;MACDL,MAAM,CAACM,aAAa,CAACT,IAAI,CAAC;;EAE9B;EAEAQ,gBAAgBA,CAAA;IACd;IACA,IAAI,CAACpG,mBAAmB,CAACmC,gBAAgB,CAAC,6BAA6B,EAAE,SAAS,CAAC;EACrF;EAEA;EACA1J,MAAMA,CAAA;IACJ,IAAI,CAACqK,gBAAgB,EAAE;IACvB,IAAI,CAAChD,WAAW,CAACrH,MAAM,EAAE;EAC3B;EAEA;EAEA6N,SAASA,CAACnD,KAAoB;IAC5B;IACA,IAAIA,KAAK,CAACoD,GAAG,KAAK,QAAQ,EAAE;MAC1B,IAAI,CAACC,cAAc,EAAE;;IAGvB;IACA,IAAIrD,KAAK,CAACoD,GAAG,KAAK,OAAO,IAAI,IAAI,CAACpG,cAAc,EAAE;MAChD,IAAI,IAAI,CAAC6D,kBAAkB,EAAE,EAAE;QAC7B,IAAI,CAACC,YAAY,EAAE;;;IAIvB;IACA,IAAId,KAAK,CAACoD,GAAG,KAAK,OAAO,IAAI,IAAI,CAACnG,gBAAgB,IAAI,CAAC+C,KAAK,CAACsD,QAAQ,EAAE;MACrEtD,KAAK,CAACC,cAAc,EAAE;MACtB,IAAI,CAAC3F,WAAW,EAAE;;EAEtB;EAEA;EAEAiJ,eAAeA,CAACvD,KAAY;IAC1B;IACA,IAAI,IAAI,CAAC7C,eAAe,EAAE;MACxB,IAAI,CAACwC,gBAAgB,EAAE;;EAE3B;EAEQ0D,cAAcA,CAAA;IACpB,IAAI,CAACrG,cAAc,GAAG,KAAK;IAC3B,IAAI,CAACC,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAACC,iBAAiB,GAAG,KAAK;IAC9B,IAAI,CAACC,eAAe,GAAG,KAAK;IAC5B,IAAI,CAACC,mBAAmB,GAAG,KAAK;IAChC,IAAI,IAAI,CAACL,WAAW,KAAK,WAAW,EAAE;MACpC,IAAI,CAACA,WAAW,GAAG,eAAe;;EAEtC;EAEA;EACAyG,cAAcA,CAAA;IACZ,OAAO,UAAU,IAAI,CAACzG,WAAW,EAAE;EACrC;EAEA0G,cAAcA,CAAA;IACZ,QAAQ,IAAI,CAAC1G,WAAW;MACtB,KAAK,OAAO;QAAE,OAAO,kBAAkB;MACvC,KAAK,eAAe;QAAE,OAAO,0BAA0B;MACvD,KAAK,QAAQ;QAAE,OAAO,iBAAiB,IAAI,CAAChI,WAAW,oBAAoB;MAC3E,KAAK,WAAW;QAAE,OAAO,sBAAsB;MAC/C;QAAS,OAAO,EAAE;;EAEtB;EAEAiG,YAAYA,CAAC0I,KAAa,EAAE5E,OAAgB;IAC1C,OAAOA,OAAO,CAACQ,EAAE;EACnB;;;uBA5iBW7C,gBAAgB,EAAAlI,EAAA,CAAAoP,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAtP,EAAA,CAAAoP,iBAAA,CAAAG,EAAA,CAAAC,cAAA,GAAAxP,EAAA,CAAAoP,iBAAA,CAAAK,EAAA,CAAAC,mBAAA;IAAA;EAAA;;;YAAhBxH,gBAAgB;MAAAyH,SAAA;MAAAC,YAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAhB9P,EAAA,CAAAS,UAAA,qBAAAuP,4CAAA5O,MAAA;YAAA,OAAA2O,GAAA,CAAAnB,SAAA,CAAAxN,MAAA,CAAiB;UAAA,UAAApB,EAAA,CAAAiQ,iBAAA,CAAD,mBAAAC,0CAAA9O,MAAA;YAAA,OAAhB2O,GAAA,CAAAf,eAAA,CAAA5N,MAAA,CAAuB;UAAA,UAAApB,EAAA,CAAAiQ,iBAAA,CAAP;;;;;;;;;;UC7CzBjQ,EAHJ,CAAAE,cAAA,aAA2B,aAEK,aAU3B;UADCF,EAJA,CAAAS,UAAA,mBAAA0P,+CAAA;YAAA,OAASJ,GAAA,CAAA5E,aAAA,EAAe;UAAA,EAAC,yBAAAiF,qDAAAhP,MAAA;YAAA,OACV2O,GAAA,CAAAvE,kBAAA,CAAApK,MAAA,CAA0B;UAAA,EAAC,wBAAAiP,oDAAAjP,MAAA;YAAA,OAC5B2O,GAAA,CAAAlE,kBAAA,CAAAzK,MAAA,CAA0B;UAAA,EAAC,sBAAAkP,kDAAA;YAAA,OAC7BP,GAAA,CAAA5D,gBAAA,EAAkB;UAAA,EAAC,uBAAAoE,mDAAA;YAAA,OAClBR,GAAA,CAAA1D,iBAAA,EAAmB;UAAA,EAAC;UAEjCrM,EAAA,CAAAE,cAAA,aAA0B;UAExBF,EADA,CAAA8C,UAAA,IAAA0N,+BAAA,iBAAyD,IAAAC,+BAAA,iBACM;UAKrEzQ,EAFI,CAAAI,YAAA,EAAM,EACF,EACF;UASNJ,EANA,CAAA8C,UAAA,IAAA4N,+BAAA,iBAA2C,IAAAC,+BAAA,kBAY1C;UAWH3Q,EAAA,CAAAI,YAAA,EAAM;UA6TNJ,EA1TA,CAAA8C,UAAA,IAAA8N,+BAAA,kBAA8E,IAAAC,+BAAA,mBAoDO,KAAAC,gCAAA,kBA4IA,KAAAC,gCAAA,kBA8BG,KAAAC,gCAAA,iBA4F+B;;;UAtWjHhR,EAAA,CAAAK,SAAA,GAA0B;UAA1BL,EAAA,CAAAiR,UAAA,CAAAlB,GAAA,CAAAd,cAAA,GAA0B;UAC1BjP,EAAA,CAAAmD,UAAA,UAAA4M,GAAA,CAAAb,cAAA,GAA0B;UAQElP,EAAA,CAAAK,SAAA,GAA6B;UAA7BL,EAAA,CAAAmD,UAAA,SAAA4M,GAAA,CAAAvH,WAAA,aAA6B;UACxBxI,EAAA,CAAAK,SAAA,EAA8B;UAA9BL,EAAA,CAAAmD,UAAA,SAAA4M,GAAA,CAAAvH,WAAA,cAA8B;UAQ3CxI,EAAA,CAAAK,SAAA,EAAiB;UAAjBL,EAAA,CAAAmD,UAAA,SAAA4M,GAAA,CAAA9O,WAAA,CAAiB;UAQtCjB,EAAA,CAAAK,SAAA,EAAqB;UAArBL,EAAA,CAAAmD,UAAA,SAAA4M,GAAA,CAAAnH,eAAA,CAAqB;UAkBE5I,EAAA,CAAAK,SAAA,EAAoB;UAApBL,EAAA,CAAAmD,UAAA,SAAA4M,GAAA,CAAAtH,cAAA,CAAoB;UAoDpBzI,EAAA,CAAAK,SAAA,EAAsB;UAAtBL,EAAA,CAAAmD,UAAA,SAAA4M,GAAA,CAAArH,gBAAA,CAAsB;UA4ItB1I,EAAA,CAAAK,SAAA,EAAuB;UAAvBL,EAAA,CAAAmD,UAAA,SAAA4M,GAAA,CAAApH,iBAAA,CAAuB;UA8BvB3I,EAAA,CAAAK,SAAA,EAAyB;UAAzBL,EAAA,CAAAmD,UAAA,SAAA4M,GAAA,CAAAlH,mBAAA,CAAyB;UA4FxB7I,EAAA,CAAAK,SAAA,EAAwF;UAAxFL,EAAA,CAAAmD,UAAA,UAAA4M,GAAA,CAAAtH,cAAA,KAAAsH,GAAA,CAAArH,gBAAA,KAAAqH,GAAA,CAAApH,iBAAA,KAAAoH,GAAA,CAAAlH,mBAAA,CAAwF;;;qBD/TzGjJ,YAAY,EAAAsR,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAAF,EAAA,CAAAG,QAAA,EAAExR,WAAW,EAAAyR,EAAA,CAAAC,oBAAA,EAAAD,EAAA,CAAAE,eAAA,EAAAF,EAAA,CAAAG,kBAAA,EAAAH,EAAA,CAAAI,OAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}