"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CRYPTO_CONSTANTS = void 0;
exports.generateSecureRandom = generateSecureRandom;
exports.generateSalt = generateSalt;
exports.hashSecretWord = hashSecretWord;
exports.verifySecretWord = verifySecretWord;
exports.generateMessageHash = generateMessageHash;
exports.generateDeviceHash = generateDeviceHash;
exports.generateInviteCode = generateInviteCode;
exports.deriveEncryptionKey = deriveEncryptionKey;
exports.constantTimeCompare = constantTimeCompare;
exports.sanitizeForLogging = sanitizeForLogging;
exports.generateSessionId = generateSessionId;
exports.generateRequestId = generateRequestId;
exports.validateSecretWordFormat = validateSecretWordFormat;
exports.clearSensitiveString = clearSensitiveString;
exports.clearSensitiveBuffer = clearSensitiveBuffer;
exports.generateNonce = generateNonce;
exports.encodeForTransmission = encodeForTransmission;
exports.decodeFromTransmission = decodeFromTransmission;
const crypto_1 = require("crypto");
const util_1 = require("util");
const scryptAsync = (0, util_1.promisify)(crypto_1.scrypt);
/**
 * Cryptographic utility functions for QSC
 * Following security protocols defined in RULES.md
 */
// Constants
exports.CRYPTO_CONSTANTS = {
    SALT_LENGTH: 32,
    KEY_LENGTH: 32,
    IV_LENGTH: 12,
    TAG_LENGTH: 16,
    SCRYPT_N: 32768, // CPU/memory cost parameter
    SCRYPT_R: 8, // Block size parameter
    SCRYPT_P: 1, // Parallelization parameter
    ARGON2_MEMORY: 65536, // 64 MB
    ARGON2_TIME: 3,
    ARGON2_PARALLELISM: 4,
};
/**
 * Generate cryptographically secure random bytes
 */
function generateSecureRandom(length) {
    return (0, crypto_1.randomBytes)(length);
}
/**
 * Generate a secure salt for password hashing
 */
function generateSalt() {
    return generateSecureRandom(exports.CRYPTO_CONSTANTS.SALT_LENGTH);
}
/**
 * Hash a secret word using scrypt (fallback for Argon2)
 * In production, this should use Argon2id
 */
async function hashSecretWord(secretWord, salt) {
    const saltBuffer = salt || generateSalt();
    const derivedKey = await scryptAsync(secretWord, saltBuffer, exports.CRYPTO_CONSTANTS.KEY_LENGTH);
    return {
        hash: derivedKey.toString('hex'),
        salt: saltBuffer.toString('hex'),
    };
}
/**
 * Verify a secret word against its hash
 */
async function verifySecretWord(secretWord, storedHash, storedSalt) {
    try {
        const saltBuffer = Buffer.from(storedSalt, 'hex');
        const { hash } = await hashSecretWord(secretWord, saltBuffer);
        const storedHashBuffer = Buffer.from(storedHash, 'hex');
        const computedHashBuffer = Buffer.from(hash, 'hex');
        return (0, crypto_1.timingSafeEqual)(storedHashBuffer, computedHashBuffer);
    }
    catch (error) {
        // Always return false on error to prevent timing attacks
        return false;
    }
}
/**
 * Generate a secure message hash for integrity verification
 */
function generateMessageHash(senderId, recipientId, content, timestamp) {
    const ts = timestamp || Date.now();
    const data = `${senderId}:${recipientId}:${content}:${ts}`;
    return (0, crypto_1.createHash)('sha3-512').update(data).digest('hex');
}
/**
 * Generate a device hash for device identification
 */
function generateDeviceHash(userId, deviceInfo, timestamp) {
    const ts = timestamp || Date.now();
    const data = `${userId}:${deviceInfo}:${ts}`;
    return (0, crypto_1.createHash)('sha3-256').update(data).digest('hex');
}
/**
 * Generate an invite code
 */
function generateInviteCode() {
    const randomData = generateSecureRandom(32);
    return (0, crypto_1.createHash)('sha256').update(randomData).digest('hex').substring(0, 16);
}
/**
 * Derive encryption key from password using PBKDF2
 */
async function deriveEncryptionKey(password, salt, iterations = 100000) {
    return scryptAsync(password, salt, exports.CRYPTO_CONSTANTS.KEY_LENGTH);
}
/**
 * Constant-time string comparison to prevent timing attacks
 */
function constantTimeCompare(a, b) {
    if (a.length !== b.length) {
        return false;
    }
    const bufferA = Buffer.from(a);
    const bufferB = Buffer.from(b);
    return (0, crypto_1.timingSafeEqual)(bufferA, bufferB);
}
/**
 * Sanitize sensitive data from objects for logging
 */
function sanitizeForLogging(obj) {
    const sensitiveFields = [
        'password',
        'secretWord',
        'privateKey',
        'encryptedContent',
        'symmetricKey',
        'authTag',
        'iv',
        'hash',
        'salt',
        'token',
        'signature',
    ];
    if (typeof obj !== 'object' || obj === null) {
        return obj;
    }
    if (Array.isArray(obj)) {
        return obj.map(sanitizeForLogging);
    }
    const sanitized = {};
    for (const [key, value] of Object.entries(obj)) {
        if (sensitiveFields.some(field => key.toLowerCase().includes(field.toLowerCase()))) {
            sanitized[key] = '[REDACTED]';
        }
        else if (typeof value === 'object') {
            sanitized[key] = sanitizeForLogging(value);
        }
        else {
            sanitized[key] = value;
        }
    }
    return sanitized;
}
/**
 * Generate a session ID
 */
function generateSessionId() {
    return generateSecureRandom(32).toString('hex');
}
/**
 * Generate a request ID for tracing
 */
function generateRequestId() {
    return generateSecureRandom(16).toString('hex');
}
/**
 * Validate secret word format according to RULES.md
 * 4-character minimum: 1 uppercase, 1 lowercase, 1 digit, 1 symbol
 */
function validateSecretWordFormat(secretWord) {
    const errors = [];
    if (secretWord.length < 4) {
        errors.push('Secret word must be at least 4 characters long');
    }
    if (!/[A-Z]/.test(secretWord)) {
        errors.push('Secret word must contain at least one uppercase letter');
    }
    if (!/[a-z]/.test(secretWord)) {
        errors.push('Secret word must contain at least one lowercase letter');
    }
    if (!/\d/.test(secretWord)) {
        errors.push('Secret word must contain at least one digit');
    }
    if (!/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(secretWord)) {
        errors.push('Secret word must contain at least one symbol');
    }
    return {
        isValid: errors.length === 0,
        errors,
    };
}
/**
 * Memory-safe string clearing (best effort)
 */
function clearSensitiveString(str) {
    // Note: This is best effort in JavaScript/Node.js
    // True memory clearing requires native code
    if (typeof str === 'string') {
        // Overwrite the string content (limited effectiveness in JS)
        for (let i = 0; i < str.length; i++) {
            str = str.substring(0, i) + '\0' + str.substring(i + 1);
        }
    }
}
/**
 * Memory-safe buffer clearing
 */
function clearSensitiveBuffer(buffer) {
    if (Buffer.isBuffer(buffer)) {
        buffer.fill(0);
    }
}
/**
 * Generate a nonce for cryptographic operations
 */
function generateNonce() {
    return generateSecureRandom(exports.CRYPTO_CONSTANTS.IV_LENGTH);
}
/**
 * Encode data for safe transmission
 */
function encodeForTransmission(data) {
    return data.toString('base64url');
}
/**
 * Decode data from transmission format
 */
function decodeFromTransmission(encoded) {
    return Buffer.from(encoded, 'base64url');
}
//# sourceMappingURL=crypto.js.map