import { Injectable } from '@angular/core';
import { StateService } from './state.service';

@Injectable({
  providedIn: 'root'
})
export class SecretWordService {
  private readonly MAX_ATTEMPTS = 3;
  private readonly MIN_LENGTH = 4;
  private readonly SALT_LENGTH = 16;
  private readonly HASH_LENGTH = 32;
  private readonly ITERATIONS = 3;
  private readonly MEMORY_COST = 65536; // 64MB
  private readonly PARALLELISM = 4;

  constructor(private stateService: StateService) {}

  public validateSecretWord(word: string): boolean {
    const hasUpperCase = /[A-Z]/.test(word);
    const hasLowerCase = /[a-z]/.test(word);
    const hasDigit = /[0-9]/.test(word);
    const hasSymbol = /[!@#$%^&*(),.?":{}|<>]/.test(word);
    const hasMinLength = word.length >= this.MIN_LENGTH;

    return hasUpperCase && hasLowerCase && hasDigit && hasSymbol && hasMinLength;
  }

  public async hashSecretWord(word: string): Promise<Uint8Array> {
    const encoder = new TextEncoder();
    const wordBytes = encoder.encode(word);

    // Generate random salt
    const salt = crypto.getRandomValues(new Uint8Array(this.SALT_LENGTH));

    // Import key for PBKDF2
    const keyMaterial = await crypto.subtle.importKey(
      'raw',
      wordBytes,
      { name: 'PBKDF2' },
      false,
      ['deriveBits']
    );

    // Derive key using PBKDF2 (as a fallback for argon2id)
    const hash = await crypto.subtle.deriveBits(
      {
        name: 'PBKDF2',
        salt,
        iterations: this.ITERATIONS * 1000, // Increased iterations to compensate for PBKDF2
        hash: 'SHA-256'
      },
      keyMaterial,
      this.HASH_LENGTH * 8
    );

    // Combine salt and hash
    const result = new Uint8Array(this.SALT_LENGTH + this.HASH_LENGTH);
    result.set(salt);
    result.set(new Uint8Array(hash), this.SALT_LENGTH);

    return result;
  }

  public async verifySecretWord(word: string, storedHash: Uint8Array): Promise<boolean> {
    const encoder = new TextEncoder();
    const wordBytes = encoder.encode(word);

    // Extract salt from stored hash
    const salt = storedHash.slice(0, this.SALT_LENGTH);

    // Import key for PBKDF2
    const keyMaterial = await crypto.subtle.importKey(
      'raw',
      wordBytes,
      { name: 'PBKDF2' },
      false,
      ['deriveBits']
    );

    // Derive key using PBKDF2
    const hash = await crypto.subtle.deriveBits(
      {
        name: 'PBKDF2',
        salt,
        iterations: this.ITERATIONS * 1000,
        hash: 'SHA-256'
      },
      keyMaterial,
      this.HASH_LENGTH * 8
    );

    // Compare hashes
    const newHash = new Uint8Array(hash);
    const storedHashPart = storedHash.slice(this.SALT_LENGTH);
    const isMatch = this.compareArrays(newHash, storedHashPart);

    if (!isMatch) {
      this.stateService.incrementAttempts();
      if (this.stateService.attempts() >= this.MAX_ATTEMPTS) {
        this.stateService.clearMessages();
        this.stateService.setAuthenticated(false);
      }
    } else {
      this.stateService.resetAttempts();
    }

    return isMatch;
  }

  private compareArrays(a: Uint8Array, b: Uint8Array): boolean {
    if (a.length !== b.length) return false;
    return a.every((val, index) => val === b[index]);
  }
}
