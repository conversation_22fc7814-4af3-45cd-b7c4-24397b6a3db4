import { IApiResponse, IQSCError } from '../types';
/**
 * Common helper utilities for QSC application
 */
/**
 * Create a standardized API response
 */
export declare function createApiResponse<T>(success: boolean, data?: T, error?: string, message?: string): IApiResponse<T>;
/**
 * Create a success response
 */
export declare function createSuccessResponse<T>(data: T, message?: string): IApiResponse<T>;
/**
 * Create an error response
 */
export declare function createErrorResponse(error: string, message?: string): IApiResponse;
/**
 * Create a QSC error object
 */
export declare function createQSCError(code: string, message: string, details?: any, userId?: string, requestId?: string): IQSCError;
/**
 * Sleep for a specified number of milliseconds
 */
export declare function sleep(ms: number): Promise<void>;
/**
 * Retry a function with exponential backoff
 */
export declare function retryWithBackoff<T>(fn: () => Promise<T>, maxRetries?: number, baseDelay?: number, maxDelay?: number): Promise<T>;
/**
 * Debounce a function
 */
export declare function debounce<T extends (...args: any[]) => any>(func: T, wait: number): (...args: Parameters<T>) => void;
/**
 * Throttle a function
 */
export declare function throttle<T extends (...args: any[]) => any>(func: T, limit: number): (...args: Parameters<T>) => void;
/**
 * Deep clone an object (JSON-safe)
 */
export declare function deepClone<T>(obj: T): T;
/**
 * Check if an object is empty
 */
export declare function isEmpty(obj: any): boolean;
/**
 * Pick specific properties from an object
 */
export declare function pick<T extends object, K extends keyof T>(obj: T, keys: K[]): Pick<T, K>;
/**
 * Omit specific properties from an object
 */
export declare function omit<T, K extends keyof T>(obj: T, keys: K[]): Omit<T, K>;
/**
 * Format a date for display
 */
export declare function formatDate(date: Date | string, format?: 'short' | 'long' | 'iso'): string;
/**
 * Format file size in human-readable format
 */
export declare function formatFileSize(bytes: number): string;
/**
 * Generate a random string of specified length
 */
export declare function generateRandomString(length: number, charset?: string): string;
/**
 * Truncate a string to a specified length
 */
export declare function truncateString(str: string, maxLength: number, suffix?: string): string;
/**
 * Convert camelCase to snake_case
 */
export declare function camelToSnake(str: string): string;
/**
 * Convert snake_case to camelCase
 */
export declare function snakeToCamel(str: string): string;
/**
 * Convert object keys from camelCase to snake_case
 */
export declare function objectKeysToSnake<T>(obj: T): any;
/**
 * Convert object keys from snake_case to camelCase
 */
export declare function objectKeysToCamel<T>(obj: T): any;
/**
 * Check if a value is a valid URL
 */
export declare function isValidUrl(url: string): boolean;
/**
 * Escape HTML characters
 */
export declare function escapeHtml(text: string): string;
/**
 * Calculate time difference in human-readable format
 */
export declare function timeAgo(date: Date | string): string;
/**
 * Check if code is running in development environment
 */
export declare function isDevelopment(): boolean;
/**
 * Check if code is running in production environment
 */
export declare function isProduction(): boolean;
/**
 * Get environment variable with default value
 */
export declare function getEnvVar(key: string, defaultValue?: string): string;
//# sourceMappingURL=helpers.d.ts.map