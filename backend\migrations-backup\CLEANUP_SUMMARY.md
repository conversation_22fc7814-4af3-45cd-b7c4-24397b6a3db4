# QSC Legacy Migration Cleanup Summary

## Date: 2025-06-03T22:06:21.738Z

## Reason for Cleanup
The QSC project has standardized on a single ORM solution (TypeORM) with username/secretWord authentication.
Legacy Knex migrations were creating conflicts with the new authentication system.

## Removed Migrations
- 20250601020005_init_users_table.js
- 20250601020006_create_messages_table.js
- 20250601020007_create_session_keys_table.js
- 20250601020008_add_secret_word_to_users.js
- 20250601020009_update_messages_for_security.js
- 20250601020010_add_account_status_fields.js
- 20250601020011_create_groups_table.js
- 20250601020012_create_group_members_table.js
- 20250601020013_create_invites_table.js
- 20250601020014_create_users_table.js
- 20250601020015_create_messages_table.js
- 20250601020016_create_session_keys_table.js
- 20250601020017_create_session_keys_table.js
- 20250601020018_create_session_keys_table.js

## Current Database Architecture
- **ORM**: TypeORM (standardized)
- **Database**: better-sqlite3 with SQLCipher encryption
- **Authentication**: username/secretWord (not email/password)
- **User Entity**: `backend/src/users/entities/user.entity.ts`
- **Database Config**: `backend/src/database/data-source.ts`

## TypeORM Migrations
TypeORM migrations are located in `backend/src/migrations/` and handle:
- User entity with secretWordHash field
- Post-quantum cryptography compatibility
- Security-first architecture
- Account status management

## Recovery
If you need to restore any legacy migration, they are backed up in this directory.
However, they are incompatible with the current TypeORM setup and should not be used.
