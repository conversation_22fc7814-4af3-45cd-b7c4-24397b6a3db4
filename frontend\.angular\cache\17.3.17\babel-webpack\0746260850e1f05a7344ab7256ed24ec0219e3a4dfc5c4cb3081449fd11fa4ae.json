{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Projects/QSC1/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport * as i0 from \"@angular/core\";\nexport let ErrorHandlingService = /*#__PURE__*/(() => {\n  class ErrorHandlingService {\n    constructor() {\n      this.MAX_RECOVERY_ATTEMPTS = 3;\n      this.MAX_NETWORK_RETRIES = 5;\n      this.INITIAL_RETRY_DELAY = 1000; // 1 second\n    }\n    handleError(error, type) {\n      var _this = this;\n      return _asyncToGenerator(function* () {\n        const errorLog = {\n          id: crypto.randomUUID(),\n          timestamp: new Date(),\n          type,\n          message: error.message,\n          stack: error.stack,\n          recoveryAttempted: false,\n          recovered: false\n        };\n        try {\n          switch (type) {\n            case 'SECURITY':\n              yield _this.handleSecurityError(errorLog);\n              break;\n            case 'STORAGE':\n              yield _this.handleStorageError(errorLog);\n              break;\n            case 'WASM':\n              yield _this.handleWasmError(errorLog);\n              break;\n            case 'NETWORK':\n              yield _this.handleNetworkError(errorLog);\n              break;\n          }\n        } catch (recoveryError) {\n          errorLog.recoveryAttempted = true;\n          errorLog.recovered = false;\n          yield _this.logError(errorLog);\n          throw recoveryError;\n        }\n      })();\n    }\n    handleSecurityError(errorLog) {\n      var _this2 = this;\n      return _asyncToGenerator(function* () {\n        // Force re-authentication and clear sensitive data\n        try {\n          sessionStorage.clear();\n          errorLog.recoveryAttempted = true;\n          errorLog.recovered = true;\n        } catch (error) {\n          errorLog.recoveryAttempted = true;\n          errorLog.recovered = false;\n          throw error;\n        } finally {\n          yield _this2.logError(errorLog);\n        }\n      })();\n    }\n    handleStorageError(errorLog) {\n      var _this3 = this;\n      return _asyncToGenerator(function* () {\n        let attempts = 0;\n        while (attempts < _this3.MAX_RECOVERY_ATTEMPTS) {\n          try {\n            // Storage rotation would be handled by storage service\n            errorLog.recoveryAttempted = true;\n            errorLog.recovered = true;\n            break;\n          } catch (error) {\n            attempts++;\n            if (attempts === _this3.MAX_RECOVERY_ATTEMPTS) {\n              errorLog.recoveryAttempted = true;\n              errorLog.recovered = false;\n              sessionStorage.clear();\n              throw error;\n            }\n            yield new Promise(resolve => setTimeout(resolve, _this3.INITIAL_RETRY_DELAY * attempts));\n          }\n        }\n        yield _this3.logError(errorLog);\n      })();\n    }\n    handleWasmError(errorLog) {\n      var _this4 = this;\n      return _asyncToGenerator(function* () {\n        let attempts = 0;\n        while (attempts < _this4.MAX_RECOVERY_ATTEMPTS) {\n          try {\n            // Attempt to reload WASM module\n            yield _this4.reloadWasmModule();\n            errorLog.recoveryAttempted = true;\n            errorLog.recovered = true;\n            break;\n          } catch (error) {\n            attempts++;\n            if (attempts === _this4.MAX_RECOVERY_ATTEMPTS) {\n              errorLog.recoveryAttempted = true;\n              errorLog.recovered = false;\n              throw error;\n            }\n            yield new Promise(resolve => setTimeout(resolve, _this4.INITIAL_RETRY_DELAY * attempts));\n          }\n        }\n        yield _this4.logError(errorLog);\n      })();\n    }\n    handleNetworkError(errorLog) {\n      var _this5 = this;\n      return _asyncToGenerator(function* () {\n        let attempts = 0;\n        while (attempts < _this5.MAX_NETWORK_RETRIES) {\n          try {\n            // Implement exponential backoff\n            const delay = _this5.INITIAL_RETRY_DELAY * Math.pow(2, attempts);\n            yield new Promise(resolve => setTimeout(resolve, delay));\n            // Check network connectivity\n            const isConnected = yield _this5.checkNetworkConnectivity();\n            if (isConnected) {\n              errorLog.recoveryAttempted = true;\n              errorLog.recovered = true;\n              break;\n            }\n            attempts++;\n            if (attempts === _this5.MAX_NETWORK_RETRIES) {\n              errorLog.recoveryAttempted = true;\n              errorLog.recovered = false;\n              throw new Error('Network connection failed after maximum retries');\n            }\n          } catch (error) {\n            attempts++;\n            if (attempts === _this5.MAX_NETWORK_RETRIES) {\n              errorLog.recoveryAttempted = true;\n              errorLog.recovered = false;\n              throw error;\n            }\n          }\n        }\n        yield _this5.logError(errorLog);\n      })();\n    }\n    reloadWasmModule() {\n      return _asyncToGenerator(function* () {\n        // Implementation will be provided by WASM service\n        throw new Error('Not implemented');\n      })();\n    }\n    checkNetworkConnectivity() {\n      return _asyncToGenerator(function* () {\n        try {\n          const response = yield fetch('/api/health', {\n            method: 'HEAD'\n          });\n          return response.ok;\n        } catch {\n          return false;\n        }\n      })();\n    }\n    logError(errorLog) {\n      return _asyncToGenerator(function* () {\n        try {\n          // Log error to console (in production, this could be sent to a logging service)\n          console.error('Error logged:', errorLog);\n        } catch (error) {\n          console.error('Failed to log error:', error);\n        }\n      })();\n    }\n    getErrorLogs() {\n      return _asyncToGenerator(function* () {\n        try {\n          // In a real implementation, this would retrieve from a logging service\n          return [];\n        } catch (error) {\n          console.error('Failed to retrieve error logs:', error);\n          return [];\n        }\n      })();\n    }\n    clearErrorLogs() {\n      return _asyncToGenerator(function* () {\n        try {\n          // In a real implementation, this would clear logs from a logging service\n          console.log('Error logs cleared');\n        } catch (error) {\n          console.error('Failed to clear error logs:', error);\n          throw error;\n        }\n      })();\n    }\n    static {\n      this.ɵfac = function ErrorHandlingService_Factory(t) {\n        return new (t || ErrorHandlingService)();\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: ErrorHandlingService,\n        factory: ErrorHandlingService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return ErrorHandlingService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}