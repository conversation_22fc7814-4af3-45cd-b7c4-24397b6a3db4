{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Projects/QSC1/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"./secure-storage.service\";\nimport * as i2 from \"./error-handling.service\";\nexport let NotificationService = /*#__PURE__*/(() => {\n  class NotificationService {\n    constructor(secureStorage, errorHandling) {\n      this.secureStorage = secureStorage;\n      this.errorHandling = errorHandling;\n    }\n    getNotifications() {\n      var _this = this;\n      return _asyncToGenerator(function* () {\n        try {\n          const notifications = yield _this.secureStorage.retrieveSecurely('notifications', 'notifications');\n          return notifications || [];\n        } catch (error) {\n          _this.errorHandling.handleError(error, 'STORAGE');\n          throw error;\n        }\n      })();\n    }\n    markAsRead(id) {\n      var _this2 = this;\n      return _asyncToGenerator(function* () {\n        try {\n          const notifications = yield _this2.getNotifications();\n          const notification = notifications.find(n => n.id === id);\n          if (notification) {\n            notification.read = true;\n            yield _this2.secureStorage.storeSecurely('notifications', notifications, 'notifications');\n          }\n        } catch (error) {\n          _this2.errorHandling.handleError(error, 'STORAGE');\n          throw error;\n        }\n      })();\n    }\n    createCompromiseNotification(messageIds) {\n      var _this3 = this;\n      return _asyncToGenerator(function* () {\n        try {\n          const notifications = yield _this3.getNotifications();\n          const newNotification = {\n            id: crypto.randomUUID(),\n            type: 'COMPROMISE',\n            message: `Messages ${messageIds.join(', ')} may have been compromised`,\n            timestamp: new Date(),\n            read: false\n          };\n          notifications.push(newNotification);\n          yield _this3.secureStorage.storeSecurely('notifications', notifications, 'notifications');\n        } catch (error) {\n          _this3.errorHandling.handleError(error, 'STORAGE');\n          throw error;\n        }\n      })();\n    }\n    static {\n      this.ɵfac = function NotificationService_Factory(t) {\n        return new (t || NotificationService)(i0.ɵɵinject(i1.SecureStorageService), i0.ɵɵinject(i2.ErrorHandlingService));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: NotificationService,\n        factory: NotificationService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return NotificationService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}