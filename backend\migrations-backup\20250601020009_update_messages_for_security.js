/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = function(knex) {
  return knex.schema.alterTable('messages', function(table) {
    table.boolean('is_read').defaultTo(false);
    table.timestamp('read_at');
    table.boolean('is_compromised').defaultTo(false);
    table.integer('compromise_attempts').defaultTo(0);
    // Add index for faster message cleanup
    table.index(['expires_at', 'is_read']);
  });
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function(knex) {
  return knex.schema.alterTable('messages', function(table) {
    table.dropColumn('is_read');
    table.dropColumn('read_at');
    table.dropColumn('is_compromised');
    table.dropColumn('compromise_attempts');
    table.dropIndex(['expires_at', 'is_read']);
  });
}; 