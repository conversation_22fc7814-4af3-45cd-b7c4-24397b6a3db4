<div class="group-manager-container">
  <div class="group-manager-card">
    <div class="manager-header">
      <h2>{{ mode === 'create' ? 'Create Group' : 'Join Group' }}</h2>
      <button class="close-btn" (click)="onCancel()" title="Cancel">×</button>
    </div>

    <!-- Mode Switcher -->
    <div class="mode-switcher">
      <button 
        class="mode-btn"
        [class.active]="mode === 'create'"
        (click)="switchMode('create')"
        [disabled]="isLoading"
      >
        Create Group
      </button>
      <button 
        class="mode-btn"
        [class.active]="mode === 'join'"
        (click)="switchMode('join')"
        [disabled]="isLoading"
      >
        Join Group
      </button>
    </div>

    <!-- Create Group Form -->
    <form *ngIf="mode === 'create'" class="group-form">
      <div class="form-group">
        <label for="onion-address">Onion Address</label>
        <input
          type="text"
          id="onion-address"
          name="onionAddress"
          [(ngModel)]="createForm.onionAddress"
          placeholder="Generated automatically"
          required
          [disabled]="isLoading"
          readonly
        />
        <small class="help-text">Unique .onion address for your group</small>
      </div>

      <div class="form-group">
        <label for="security-threshold">Security Threshold</label>
        <select
          id="security-threshold"
          name="securityThreshold"
          [(ngModel)]="createForm.securityThreshold"
          [disabled]="isLoading"
        >
          <option *ngFor="let option of securityThresholdOptions" [value]="option.value">
            {{ option.label }}
          </option>
        </select>
        <small class="help-text">Number of confirmations required for sensitive operations</small>
      </div>

      <div class="form-group">
        <label for="master-key">Master Key</label>
        <textarea
          id="master-key"
          name="masterKey"
          [(ngModel)]="createForm.masterKey"
          placeholder="Generated automatically"
          required
          [disabled]="isLoading"
          readonly
          rows="3"
        ></textarea>
        <small class="help-text">Cryptographic key for group encryption</small>
      </div>

      <div class="form-group">
        <label for="encrypted-config">Encrypted Configuration</label>
        <textarea
          id="encrypted-config"
          name="encryptedConfig"
          [(ngModel)]="createForm.encryptedConfig"
          placeholder="Generated automatically"
          required
          [disabled]="isLoading"
          readonly
          rows="4"
        ></textarea>
        <small class="help-text">Encrypted group settings and metadata</small>
      </div>
    </form>

    <!-- Join Group Form -->
    <form *ngIf="mode === 'join'" class="group-form">
      <div class="form-group">
        <label for="invite-token">Invite Token</label>
        <input
          type="text"
          id="invite-token"
          name="inviteToken"
          [(ngModel)]="joinForm.inviteToken"
          placeholder="Paste your invite token here"
          required
          [disabled]="isLoading"
        />
        <small class="help-text">Token provided by the group administrator</small>
      </div>

      <div class="form-group">
        <label for="secret-word">Secret Word</label>
        <input
          type="password"
          id="secret-word"
          name="secretWord"
          [(ngModel)]="joinForm.secretWord"
          placeholder="Enter the group secret word"
          required
          [disabled]="isLoading"
        />
        <small class="help-text">Secret word shared by the group administrator</small>
      </div>
    </form>

    <!-- Feedback Messages -->
    <div class="message-feedback">
      <div class="error-message" *ngIf="errorMessage">
        {{ errorMessage }}
      </div>
      
      <div class="success-message" *ngIf="successMessage">
        {{ successMessage }}
      </div>
    </div>

    <!-- Action Buttons -->
    <div class="manager-actions">
      <button 
        type="button" 
        class="cancel-btn"
        (click)="onCancel()"
        [disabled]="isLoading"
      >
        Cancel
      </button>
      
      <button 
        type="button" 
        class="action-btn"
        (click)="mode === 'create' ? onCreateGroup() : onJoinGroup()"
        [disabled]="isLoading"
      >
        <span *ngIf="!isLoading">
          {{ mode === 'create' ? 'Create Group' : 'Join Group' }}
        </span>
        <span *ngIf="isLoading" class="loading-spinner">
          {{ mode === 'create' ? 'Creating...' : 'Joining...' }}
        </span>
      </button>
    </div>

    <!-- Security Notice -->
    <div class="security-notice">
      <div class="security-badge">
        🔒 All group communications are protected by post-quantum cryptography
      </div>
      <div class="warning-text" *ngIf="mode === 'create'">
        ⚠️ Save your group credentials securely. They cannot be recovered if lost.
      </div>
    </div>
  </div>
</div>
