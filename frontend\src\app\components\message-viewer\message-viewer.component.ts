import { Component, inject, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { StateService } from '../../services/state.service';
import { DomSanitizer, SafeHtml } from '@angular/platform-browser';
import { MessageDeletionService } from '../../services/message-deletion.service';
import { EncryptionService } from '../../services/encryption.service';

@Component({
  selector: 'qs-message-viewer',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div class="message-container">
      <div *ngFor="let message of stateService.messages()" class="message">
        <div [innerHTML]="sanitizeContent(message.content)"></div>
        <button (click)="deleteMessage(message.id)">Delete</button>
      </div>
    </div>
  `,
  styles: [`
    .message-container {
      padding: 1rem;
    }
    .message {
      margin-bottom: 1rem;
      padding: 0.5rem;
      border: 1px solid #ccc;
    }
  `]
})
export class MessageViewerComponent implements OnDestroy {
  protected readonly stateService = inject(StateService);
  private readonly sanitizer = inject(DomSanitizer);
  private readonly messageDeletionService = inject(MessageDeletionService);
  private readonly encryptionService = inject(EncryptionService);

  protected sanitizeContent(content: string): SafeHtml {
    return this.sanitizer.bypassSecurityTrustHtml(content);
  }

  protected async deleteMessage(id: string): Promise<void> {
    await this.messageDeletionService.deleteMessage(id);
  }

  ngOnDestroy(): void {
    // Clear sensitive data from memory
    this.stateService.clearMessages();
    this.encryptionService.wipeMemory();
  }
}
