import { NestFactory } from '@nestjs/core';
import { AppModule } from '../app.module';
import { UserService } from '../user/user.service';
import { SecretWordService } from '../auth/services/secret-word.service';
import { ConfigService } from '@nestjs/config';
import { Logger } from '@nestjs/common';

function generateSecretWord(): string {
  const uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
  const lowercase = 'abcdefghijklmnopqrstuvwxyz';
  const digits = '0123456789';
  const symbols = '!@#$%^&*()_+-=[]{}|;:,.<>?';

  const getRandomChar = (str: string) => str[Math.floor(Math.random() * str.length)];

  return [
    getRandomChar(uppercase),
    getRandomChar(lowercase),
    getRandomChar(digits),
    getRandomChar(symbols)
  ].sort(() => Math.random() - 0.5).join('');
}

async function seed() {
  const logger = new Logger('DatabaseSeed');

  try {
    const app = await NestFactory.create(AppModule);
    const userService = app.get(UserService);
    const secretWordService = app.get(SecretWordService);
    const configService = app.get(ConfigService);

    // Check if any admin users exist
    const existingAdmins = await userService.findAdminUsers();

    if (existingAdmins.length > 0) {
      logger.log('Admin users already exist. Skipping initial admin creation.');
      await app.close();
      return;
    }

    // Get initial admin configuration from environment
    const adminUsername = configService.get<string>('INITIAL_ADMIN_USERNAME') || 'admin';
    const adminEmail = configService.get<string>('INITIAL_ADMIN_EMAIL') || '<EMAIL>';
    let adminSecretWord = configService.get<string>('INITIAL_ADMIN_SECRET_WORD');

    // Generate a secure 4-character secret word if not provided
    if (!adminSecretWord) {
      adminSecretWord = generateSecretWord();
    }

    // Check if user with this email already exists
    const existingUser = await userService.findByEmailSafe(adminEmail);
    if (existingUser) {
      logger.log(`User with email ${adminEmail} already exists. Skipping admin creation.`);
      await app.close();
      return;
    }

    // Create secret word hash
    const secretWordHash = await secretWordService.hashSecretWord(adminSecretWord);

    // Create the initial admin user
    const adminUser = await userService.createAdmin({
      username: adminUsername,
      email: adminEmail,
      secretWordHash,
    });

    logger.log(`✅ Initial admin user created successfully:`);
    logger.log(`   Username: ${adminUser.username}`);
    logger.log(`   Email: ${adminUser.email}`);
    logger.log(`   ID: ${adminUser.id}`);
    logger.log(`   Secret Word: ${adminSecretWord}`);
    logger.log('');
    logger.log('🔐 IMPORTANT: Save these credentials securely!');
    logger.log('🔐 The secret word will not be displayed again.');

    await app.close();
  } catch (error) {
    logger.error('Failed to seed database:', error);
    process.exit(1);
  }
}

// Run seeding if this file is executed directly
if (require.main === module) {
  seed();
}

export { seed };
