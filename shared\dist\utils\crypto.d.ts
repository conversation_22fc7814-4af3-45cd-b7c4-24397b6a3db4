/**
 * Cryptographic utility functions for QSC
 * Following security protocols defined in RULES.md
 */
export declare const CRYPTO_CONSTANTS: {
    readonly SALT_LENGTH: 32;
    readonly KEY_LENGTH: 32;
    readonly IV_LENGTH: 12;
    readonly TAG_LENGTH: 16;
    readonly SCRYPT_N: 32768;
    readonly SCRYPT_R: 8;
    readonly SCRYPT_P: 1;
    readonly ARGON2_MEMORY: 65536;
    readonly ARGON2_TIME: 3;
    readonly ARGON2_PARALLELISM: 4;
};
/**
 * Generate cryptographically secure random bytes
 */
export declare function generateSecureRandom(length: number): Buffer;
/**
 * Generate a secure salt for password hashing
 */
export declare function generateSalt(): Buffer;
/**
 * Hash a secret word using scrypt (fallback for Argon2)
 * In production, this should use Argon2id
 */
export declare function hashSecretWord(secretWord: string, salt?: Buffer): Promise<{
    hash: string;
    salt: string;
}>;
/**
 * Verify a secret word against its hash
 */
export declare function verifySecretWord(secretWord: string, storedHash: string, storedSalt: string): Promise<boolean>;
/**
 * Generate a secure message hash for integrity verification
 */
export declare function generateMessageHash(senderId: string, recipientId: string, content: string, timestamp?: number): string;
/**
 * Generate a device hash for device identification
 */
export declare function generateDeviceHash(userId: string, deviceInfo: string, timestamp?: number): string;
/**
 * Generate an invite code
 */
export declare function generateInviteCode(): string;
/**
 * Derive encryption key from password using PBKDF2
 */
export declare function deriveEncryptionKey(password: string, salt: Buffer, iterations?: number): Promise<Buffer>;
/**
 * Constant-time string comparison to prevent timing attacks
 */
export declare function constantTimeCompare(a: string, b: string): boolean;
/**
 * Sanitize sensitive data from objects for logging
 */
export declare function sanitizeForLogging(obj: any): any;
/**
 * Generate a session ID
 */
export declare function generateSessionId(): string;
/**
 * Generate a request ID for tracing
 */
export declare function generateRequestId(): string;
/**
 * Validate secret word format according to RULES.md
 * 4-character minimum: 1 uppercase, 1 lowercase, 1 digit, 1 symbol
 */
export declare function validateSecretWordFormat(secretWord: string): {
    isValid: boolean;
    errors: string[];
};
/**
 * Memory-safe string clearing (best effort)
 */
export declare function clearSensitiveString(str: string): void;
/**
 * Memory-safe buffer clearing
 */
export declare function clearSensitiveBuffer(buffer: Buffer): void;
/**
 * Generate a nonce for cryptographic operations
 */
export declare function generateNonce(): Buffer;
/**
 * Encode data for safe transmission
 */
export declare function encodeForTransmission(data: Buffer): string;
/**
 * Decode data from transmission format
 */
export declare function decodeFromTransmission(encoded: string): Buffer;
//# sourceMappingURL=crypto.d.ts.map