/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = function(knex) {
  return knex.schema.createTable('messages', function(table) {
    table.uuid('id').primary();
    table.uuid('sender_id').references('id').inTable('users').onDelete('CASCADE');
    table.uuid('recipient_id').references('id').inTable('users').onDelete('CASCADE');
    table.text('encrypted_content').notNullable();
    table.timestamp('created_at').defaultTo(knex.fn.now());
    table.timestamp('expires_at').notNullable();
    table.boolean('is_deleted').defaultTo(false);
    table.string('message_hash').notNullable();
    table.boolean('is_read').defaultTo(false);
    table.timestamp('read_at');
    table.boolean('is_compromised').defaultTo(false);
    table.integer('compromise_attempts').defaultTo(0);

    // Indexes for faster lookups and cleanup
    table.index(['sender_id', 'recipient_id', 'is_deleted']);
    table.index(['expires_at', 'is_read']);
    table.index('message_hash');
  });
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function(knex) {
  return knex.schema.dropTable('messages');
}; 