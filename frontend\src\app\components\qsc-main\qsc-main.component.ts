import { Component, OnInit, On<PERSON><PERSON>roy, HostListener } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Subject, takeUntil, take } from 'rxjs';
import { AuthService } from '../../services/auth.service';
import { MessageService } from '../../services/message.service';
import { NotificationService } from '../../services/notification.service';
import { AuthState, User } from '../../types/auth.types';

export type CircleState = 'guest' | 'authenticated' | 'unread' | 'composing';

interface LoginCredentials {
  username: string;
  secretWord: string;
}

interface Message {
  id: string;
  content: string;
  timestamp: Date;
  sender: string;
  recipient?: string;
  groupId?: string;
  read?: boolean;
}

interface Contact {
  id: string;
  username: string;
  email: string;
  publicKey?: string;
  isOnline?: boolean;
  lastSeen?: Date;
}

interface Group {
  id: string;
  name: string;
  members: string[];
  isActive: boolean;
}

@Component({
  selector: 'app-qsc-main',
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './qsc-main.component.html',
  styleUrls: ['./qsc-main.component.scss']
})
export class QscMainComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();

  // Circle state management
  circleState: CircleState = 'guest';

  // Modal states
  showLoginModal = false;
  showMessageModal = false;
  showMessagesModal = false;
  showContextMenu = false;
  showAccountSettings = false;

  // Context menu position
  contextMenuPosition = { x: 0, y: 0 };

  // Authentication
  loginCredentials: LoginCredentials = { username: '', secretWord: '' };
  loginError = '';
  isLoading = false;

  // Messaging
  messageContent = '';
  selectedRecipient = '';
  selectedGroup = '';
  messageType: 'direct' | 'group' = 'direct';
  messages: Message[] = [];
  unreadCount = 0;

  // Contacts and Groups
  contacts: Contact[] = [];
  groups: Group[] = [];
  filteredContacts: Contact[] = [];
  filteredGroups: Group[] = [];
  recipientSearchQuery = '';

  // Getter for template access
  get userGroups(): Group[] {
    return this.groups;
  }

  // User info
  currentUser: User | null = null;

  // Account settings
  userProfile = {
    avatar: '',
    email: '',
    phone: ''
  };

  // Long press handling
  private longPressTimer: any = null;
  private longPressDuration = 500; // 500ms for long press

  constructor(
    private authService: AuthService,
    private messageService: MessageService,
    private notificationService: NotificationService
  ) {}

  ngOnInit() {
    this.initializeApp();
    this.setupMessageListener();
    this.setupAuthListener();
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private initializeApp() {
    // Check authentication state and set initial circle state
    this.authService.authState$.pipe(take(1)).subscribe((authState: AuthState) => {
      if (authState.isAuthenticated && authState.user) {
        this.currentUser = authState.user;
        this.circleState = 'authenticated';
        this.loadMessages();
        this.loadContacts();
        this.loadGroups();
        this.loadUserProfile();
      } else {
        // Guest state - show red circle
        this.circleState = 'guest';
        this.currentUser = null;
      }
    });
  }

  private setupAuthListener() {
    this.authService.authState$
      .pipe(takeUntil(this.destroy$))
      .subscribe((authState: AuthState) => {
        if (authState.isAuthenticated && authState.user) {
          this.currentUser = authState.user;
          this.circleState = 'authenticated';
          this.showLoginModal = false;
          this.loadMessages();
        } else {
          this.currentUser = null;
          this.circleState = 'guest';
          this.messages = [];
          this.unreadCount = 0;
        }
      });
  }

  private setupMessageListener() {
    this.messageService.messages$
      .pipe(takeUntil(this.destroy$))
      .subscribe(messages => {
        this.messages = messages;
        this.updateUnreadCount();
      });

    this.messageService.newMessage$
      .pipe(takeUntil(this.destroy$))
      .subscribe(message => {
        this.messages.unshift(message);
        this.updateUnreadCount();
        this.notificationService.showNotification('New message received');
      });
  }

  private updateUnreadCount() {
    this.unreadCount = this.messages.filter(m => !m.read).length;
    if (this.unreadCount > 0 && this.circleState === 'authenticated') {
      this.circleState = 'unread';
    } else if (this.unreadCount === 0 && this.circleState === 'unread') {
      this.circleState = 'authenticated';
    }
  }

  private loadMessages() {
    this.messageService.loadMessages()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (messages) => {
          this.messages = messages;
          this.updateUnreadCount();
        },
        error: (error) => {
          console.error('Failed to load messages:', error);
        }
      });
  }

  private loadContacts() {
    // TODO: Replace with actual API call
    this.contacts = [
      {
        id: '1',
        username: 'alice',
        email: '<EMAIL>',
        isOnline: true
      },
      {
        id: '2',
        username: 'bob',
        email: '<EMAIL>',
        isOnline: false,
        lastSeen: new Date(Date.now() - 300000) // 5 minutes ago
      }
    ];
    this.filteredContacts = [...this.contacts];
  }

  private loadGroups() {
    // TODO: Replace with actual API call
    // Temporarily enable groups to test the interface
    this.groups = [
      {
        id: 'group1',
        name: 'Work Team',
        members: ['1', '2', 'current-user'],
        isActive: true
      },
      {
        id: 'group2',
        name: 'Family',
        members: ['3', '4', 'current-user'],
        isActive: true
      }
    ];

    // Set to empty array to hide group selector:
    // this.groups = [];

    this.filteredGroups = [...this.groups];
  }

  private loadUserProfile() {
    if (this.currentUser) {
      this.userProfile = {
        avatar: '',
        email: this.currentUser.email || '',
        phone: '' // TODO: Add phone to user profile
      };
    }
  }

  // Circle click handler - main interaction point
  onCircleClick() {
    // Don't handle click if context menu is showing
    if (this.showContextMenu) {
      this.closeContextMenu();
      return;
    }

    switch (this.circleState) {
      case 'guest':
        this.openLoginModal();
        break;
      case 'authenticated':
        this.openMessageComposer();
        break;
      case 'unread':
        this.openMessagesViewer();
        break;
      case 'composing':
        // Already composing, do nothing or close
        break;
    }
  }

  // Right click handler
  onCircleRightClick(event: MouseEvent) {
    event.preventDefault();

    // Only show context menu for authenticated users
    if (this.circleState === 'guest') return;

    this.showContextMenu = true;
    this.contextMenuPosition = {
      x: event.clientX,
      y: event.clientY
    };
  }

  // Touch event handlers for long press
  onCircleTouchStart(event: TouchEvent) {
    // Only for authenticated users
    if (this.circleState === 'guest') return;

    this.longPressTimer = setTimeout(() => {
      const touch = event.touches[0];
      this.showContextMenu = true;
      this.contextMenuPosition = {
        x: touch.clientX,
        y: touch.clientY
      };

      // Provide haptic feedback if available
      if (navigator.vibrate) {
        navigator.vibrate(50);
      }
    }, this.longPressDuration);
  }

  onCircleTouchEnd() {
    if (this.longPressTimer) {
      clearTimeout(this.longPressTimer);
      this.longPressTimer = null;
    }
  }

  onCircleTouchMove() {
    // Cancel long press if user moves finger
    if (this.longPressTimer) {
      clearTimeout(this.longPressTimer);
      this.longPressTimer = null;
    }
  }

  // Authentication methods
  openLoginModal() {
    this.showLoginModal = true;
    this.loginCredentials = { username: '', secretWord: '' };
    this.loginError = '';
  }

  closeLoginModal() {
    this.showLoginModal = false;
    this.loginCredentials = { username: '', secretWord: '' };
    this.loginError = '';
  }

  onLoginInputChange() {
    // Auto-submit when both fields are valid
    if (this.isValidCredentials()) {
      this.performLogin();
    }
  }

  private isValidCredentials(): boolean {
    const usernameValid = this.loginCredentials.username.length >= 3;
    // QSC secret word: exactly 4 characters with one from each character class
    const secretWordValid = this.loginCredentials.secretWord.length === 4 &&
                           /[A-Z]/.test(this.loginCredentials.secretWord) &&
                           /[a-z]/.test(this.loginCredentials.secretWord) &&
                           /[0-9]/.test(this.loginCredentials.secretWord) &&
                           /[^A-Za-z0-9]/.test(this.loginCredentials.secretWord);

    return usernameValid && secretWordValid;
  }

  private performLogin() {
    if (this.isLoading) return;

    this.isLoading = true;
    this.loginError = '';

    this.authService.login(this.loginCredentials.username, this.loginCredentials.secretWord)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response) => {
          this.isLoading = false;
          // Auth state will be updated via authState$ subscription
        },
        error: (error) => {
          this.isLoading = false;
          this.loginError = error.message || 'Authentication failed';
        }
      });
  }

  // Message composition methods
  openMessageComposer() {
    this.showMessageModal = true;
    this.messageContent = '';
    this.selectedRecipient = '';
    this.selectedGroup = '';
    this.messageType = 'direct';
    this.recipientSearchQuery = '';
    this.filteredContacts = [...this.contacts];
    this.filteredGroups = [...this.groups];
    this.circleState = 'composing';
  }

  closeMessageComposer() {
    this.showMessageModal = false;
    this.messageContent = '';
    this.selectedRecipient = '';
    this.selectedGroup = '';
    this.recipientSearchQuery = '';
    this.circleState = 'authenticated';
  }

  sendMessage() {
    if (!this.messageContent.trim()) return;

    // Validate recipient selection
    if (this.messageType === 'direct' && !this.selectedRecipient) {
      this.notificationService.showNotification('Please select a recipient', 'warning');
      return;
    }

    if (this.messageType === 'group' && !this.selectedGroup) {
      this.notificationService.showNotification('Please select a group', 'warning');
      return;
    }

    const message: Partial<Message> = {
      content: this.messageContent.trim(),
      timestamp: new Date(),
      sender: this.currentUser?.username || 'Unknown',
      recipient: this.messageType === 'direct' ? this.selectedRecipient : undefined,
      groupId: this.messageType === 'group' ? this.selectedGroup : undefined
    };

    this.messageService.sendMessage(message)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: () => {
          this.closeMessageComposer();
          this.notificationService.showNotification('Message sent');
        },
        error: (error) => {
          console.error('Failed to send message:', error);
          this.notificationService.showNotification('Failed to send message', 'error');
        }
      });
  }

  // Recipient selection methods
  onRecipientSearchChange() {
    const query = this.recipientSearchQuery.toLowerCase();

    if (this.messageType === 'direct') {
      this.filteredContacts = this.contacts.filter(contact =>
        contact.username.toLowerCase().includes(query) ||
        contact.email.toLowerCase().includes(query)
      );
    } else {
      this.filteredGroups = this.groups.filter(group =>
        group.name.toLowerCase().includes(query)
      );
    }
  }

  selectContact(contact: Contact) {
    this.selectedRecipient = contact.id;
    this.recipientSearchQuery = contact.username;
    this.filteredContacts = [];
  }

  selectGroup(group: Group) {
    this.selectedGroup = group.id;
    this.recipientSearchQuery = group.name;
    this.filteredGroups = [];
  }

  switchMessageType(type: 'direct' | 'group') {
    this.messageType = type;
    this.selectedRecipient = '';
    this.selectedGroup = '';
    this.recipientSearchQuery = '';
    this.onRecipientSearchChange();
  }

  getSelectedRecipientName(): string {
    if (this.messageType === 'direct' && this.selectedRecipient) {
      const contact = this.contacts.find(c => c.id === this.selectedRecipient);
      return contact?.username || 'Unknown';
    }

    if (this.messageType === 'group' && this.selectedGroup) {
      const group = this.groups.find(g => g.id === this.selectedGroup);
      return group?.name || 'Unknown Group';
    }

    return '';
  }

  isMessageValid(): boolean {
    const hasContent = this.messageContent.trim().length > 0;
    const hasRecipient = this.messageType === 'direct' ? !!this.selectedRecipient : !!this.selectedGroup;
    return hasContent && hasRecipient;
  }

  // Message viewing methods
  openMessagesViewer() {
    this.showMessagesModal = true;
    this.markMessagesAsRead();
  }

  closeMessagesViewer() {
    this.showMessagesModal = false;
  }

  private markMessagesAsRead() {
    this.messageService.markAllAsRead()
      .pipe(takeUntil(this.destroy$))
      .subscribe(() => {
        this.updateUnreadCount();
      });
  }

  // Context menu methods
  closeContextMenu() {
    this.showContextMenu = false;
  }

  openAccountSettings() {
    this.showAccountSettings = true;
    this.showContextMenu = false;
    this.loadUserProfile();
  }

  closeAccountSettings() {
    this.showAccountSettings = false;
  }

  // Account settings methods

  onAvatarChange(event: Event) {
    const input = event.target as HTMLInputElement;
    if (input.files && input.files[0]) {
      const file = input.files[0];

      // Validate file type
      if (!file.type.startsWith('image/')) {
        this.notificationService.showNotification('Please select an image file', 'error');
        return;
      }

      // Validate file size (max 2MB)
      if (file.size > 2 * 1024 * 1024) {
        this.notificationService.showNotification('Image must be smaller than 2MB', 'error');
        return;
      }

      const reader = new FileReader();
      reader.onload = (e) => {
        this.userProfile.avatar = e.target?.result as string;
        this.saveAvatarChange();
      };
      reader.readAsDataURL(file);
    }
  }

  saveAvatarChange() {
    // TODO: Implement API call to save avatar
    this.notificationService.showNotification('Avatar updated successfully', 'success');
  }

  // Logout
  logout() {
    this.closeContextMenu();
    this.authService.logout();
  }

  // Keyboard shortcuts
  @HostListener('document:keydown', ['$event'])
  onKeyDown(event: KeyboardEvent) {
    // Escape key closes modals
    if (event.key === 'Escape') {
      this.closeAllModals();
    }

    // Enter key in login modal
    if (event.key === 'Enter' && this.showLoginModal) {
      if (this.isValidCredentials()) {
        this.performLogin();
      }
    }

    // Enter key in message modal
    if (event.key === 'Enter' && this.showMessageModal && !event.shiftKey) {
      event.preventDefault();
      this.sendMessage();
    }
  }

  // Click outside handler to close context menu
  @HostListener('document:click', ['$event'])
  onDocumentClick(event: Event) {
    // Close context menu when clicking outside
    if (this.showContextMenu) {
      this.closeContextMenu();
    }
  }

  private closeAllModals() {
    this.showLoginModal = false;
    this.showMessageModal = false;
    this.showMessagesModal = false;
    this.showContextMenu = false;
    this.showAccountSettings = false;
    if (this.circleState === 'composing') {
      this.circleState = 'authenticated';
    }
  }

  // Utility methods
  getCircleClass(): string {
    return `circle-${this.circleState}`;
  }

  getCircleTitle(): string {
    switch (this.circleState) {
      case 'guest': return 'Click to sign in';
      case 'authenticated': return 'Click to compose message';
      case 'unread': return `Click to view ${this.unreadCount} unread message(s)`;
      case 'composing': return 'Composing message...';
      default: return '';
    }
  }

  trackMessage(index: number, message: Message): string {
    return message.id;
  }
}
