import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ChatService } from './chat.service';
// import { ChatController } from './chat.controller';
import { ChatGateway } from './chat.gateway';
import { Message } from './entities/message.entity';
import { ChatRoom } from './entities/chat-room.entity';
import { Group } from './entities/group.entity';
import { GroupMember } from './entities/group-member.entity';
import { Invite } from './entities/invite.entity';
import { SessionKey } from './entities/session-key.entity';
import { User } from '../users/entities/user.entity';
import { CryptoService } from './services/crypto.service';
// import { MessageService } from './services/message.service';
// import { RoomService } from './services/room.service';
// import { KyberService } from '../security/services/kyber.service';
// import { LibOQSService } from '../security/services/liboqs.service';
// import { UserService } from '../users/user.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      Message,
      ChatRoom,
      Group,
      GroupMember,
      Invite,
      SessionKey,
      User
    ]),
  ],
  providers: [
    ChatService,
    ChatGateway,
    CryptoService,
    // MessageService,
    // RoomService,
    // KyberService,
    // LibOQSService,
    // UserService,
  ],
  // controllers: [ChatController],
  exports: [ChatService],
})
export class ChatModule {} 