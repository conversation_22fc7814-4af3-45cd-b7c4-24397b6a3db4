{"ast": null, "code": "import { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nexport function filter(predicate, thisArg) {\n  return operate((source, subscriber) => {\n    let index = 0;\n    source.subscribe(createOperatorSubscriber(subscriber, value => predicate.call(thisArg, value, index++) && subscriber.next(value)));\n  });\n}", "map": {"version": 3, "names": ["operate", "createOperatorSubscriber", "filter", "predicate", "thisArg", "source", "subscriber", "index", "subscribe", "value", "call", "next"], "sources": ["D:/TCL1/Projects/Projects/QSC1/frontend/node_modules/rxjs/dist/esm/internal/operators/filter.js"], "sourcesContent": ["import { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nexport function filter(predicate, thisArg) {\n    return operate((source, subscriber) => {\n        let index = 0;\n        source.subscribe(createOperatorSubscriber(subscriber, (value) => predicate.call(thisArg, value, index++) && subscriber.next(value)));\n    });\n}\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,cAAc;AACtC,SAASC,wBAAwB,QAAQ,sBAAsB;AAC/D,OAAO,SAASC,MAAMA,CAACC,SAAS,EAAEC,OAAO,EAAE;EACvC,OAAOJ,OAAO,CAAC,CAACK,MAAM,EAAEC,UAAU,KAAK;IACnC,IAAIC,KAAK,GAAG,CAAC;IACbF,MAAM,CAACG,SAAS,CAACP,wBAAwB,CAACK,UAAU,EAAGG,KAAK,IAAKN,SAAS,CAACO,IAAI,CAACN,OAAO,EAAEK,KAAK,EAAEF,KAAK,EAAE,CAAC,IAAID,UAAU,CAACK,IAAI,CAACF,KAAK,CAAC,CAAC,CAAC;EACxI,CAAC,CAAC;AACN", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}