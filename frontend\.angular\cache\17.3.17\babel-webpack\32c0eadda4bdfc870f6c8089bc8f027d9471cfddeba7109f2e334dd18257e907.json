{"ast": null, "code": "import { authGuard, loginGuard } from './guards/auth.guard';\nexport const routes = [{\n  path: '',\n  redirectTo: 'login',\n  pathMatch: 'full'\n}, {\n  path: 'login',\n  loadComponent: () => import('./components/login/login.component').then(m => m.LoginComponent),\n  canActivate: [loginGuard]\n}, {\n  path: 'home',\n  loadComponent: () => import('./components/home/<USER>').then(m => m.HomeComponent),\n  canActivate: [authGuard]\n}, {\n  path: 'main',\n  loadComponent: () => import('./components/main-screen/main-screen.component').then(m => m.MainScreenComponent),\n  canActivate: [authGuard]\n}, {\n  path: 'key-management',\n  loadComponent: () => import('./components/key-management/key-management.component').then(m => m.KeyManagementComponent),\n  canActivate: [authGuard]\n}, {\n  path: '**',\n  redirectTo: 'login'\n}];", "map": {"version": 3, "names": ["<PERSON>th<PERSON><PERSON>", "loginGuard", "routes", "path", "redirectTo", "pathMatch", "loadComponent", "then", "m", "LoginComponent", "canActivate", "HomeComponent", "MainScreenComponent", "KeyManagementComponent"], "sources": ["C:\\Users\\<USER>\\Projects\\QSC1\\frontend\\src\\app\\app.routes.ts"], "sourcesContent": ["import { Routes } from '@angular/router';\r\nimport { authGuard, loginGuard } from './guards/auth.guard';\r\n\r\nexport const routes: Routes = [\r\n  {\r\n    path: '',\r\n    redirectTo: 'login',\r\n    pathMatch: 'full'\r\n  },\r\n  {\r\n    path: 'login',\r\n    loadComponent: () => import('./components/login/login.component').then(m => m.LoginComponent),\r\n    canActivate: [loginGuard]\r\n  },\r\n  {\r\n    path: 'home',\r\n    loadComponent: () => import('./components/home/<USER>').then(m => m.HomeComponent),\r\n    canActivate: [authGuard]\r\n  },\r\n  {\r\n    path: 'main',\r\n    loadComponent: () => import('./components/main-screen/main-screen.component').then(m => m.MainScreenComponent),\r\n    canActivate: [authGuard]\r\n  },\r\n  {\r\n    path: 'key-management',\r\n    loadComponent: () => import('./components/key-management/key-management.component').then(m => m.KeyManagementComponent),\r\n    canActivate: [authGuard]\r\n  },\r\n  {\r\n    path: '**',\r\n    redirectTo: 'login'\r\n  }\r\n];\r\n"], "mappings": "AACA,SAASA,SAAS,EAAEC,UAAU,QAAQ,qBAAqB;AAE3D,OAAO,MAAMC,MAAM,GAAW,CAC5B;EACEC,IAAI,EAAE,EAAE;EACRC,UAAU,EAAE,OAAO;EACnBC,SAAS,EAAE;CACZ,EACD;EACEF,IAAI,EAAE,OAAO;EACbG,aAAa,EAAEA,CAAA,KAAM,MAAM,CAAC,oCAAoC,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,cAAc,CAAC;EAC7FC,WAAW,EAAE,CAACT,UAAU;CACzB,EACD;EACEE,IAAI,EAAE,MAAM;EACZG,aAAa,EAAEA,CAAA,KAAM,MAAM,CAAC,kCAAkC,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACG,aAAa,CAAC;EAC1FD,WAAW,EAAE,CAACV,SAAS;CACxB,EACD;EACEG,IAAI,EAAE,MAAM;EACZG,aAAa,EAAEA,CAAA,KAAM,MAAM,CAAC,gDAAgD,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACI,mBAAmB,CAAC;EAC9GF,WAAW,EAAE,CAACV,SAAS;CACxB,EACD;EACEG,IAAI,EAAE,gBAAgB;EACtBG,aAAa,EAAEA,CAAA,KAAM,MAAM,CAAC,sDAAsD,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACK,sBAAsB,CAAC;EACvHH,WAAW,EAAE,CAACV,SAAS;CACxB,EACD;EACEG,IAAI,EAAE,IAAI;EACVC,UAAU,EAAE;CACb,CACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}