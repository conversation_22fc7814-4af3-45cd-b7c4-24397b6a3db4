<div class="login-container">
  <div class="login-card">
    <div class="logo-container">
      <div class="qsc-logo">
        <div class="circle">
          <div class="wind-effect"></div>
        </div>
      </div>
      <h1>QSC</h1>
      <p>Quantum Secure Communication</p>
    </div>

    <form (ngSubmit)="onSubmit()" class="login-form">
      <div class="form-group">
        <label for="username">Username</label>
        <input
          type="text"
          id="username"
          name="username"
          [(ngModel)]="credentials.username"
          (keypress)="onKeyPress($event)"
          placeholder="Enter your username"
          required
          [disabled]="isLoading"
        />
      </div>

      <div class="form-group">
        <label for="secretWord">Secret Word</label>
        <input
          type="password"
          id="secretWord"
          name="secretWord"
          [(ngModel)]="credentials.secretWord"
          (keypress)="onKeyPress($event)"
          placeholder="Enter your secret word"
          required
          [disabled]="isLoading"
        />
      </div>

      <div class="error-message" *ngIf="errorMessage">
        {{ errorMessage }}
      </div>

      <button
        type="submit"
        class="login-button"
        [disabled]="isLoading"
      >
        <span *ngIf="!isLoading">Sign In</span>
        <span *ngIf="isLoading" class="loading-spinner">●</span>
      </button>
    </form>

    <div class="security-notice">
      <p>🔒 Protected by post-quantum cryptography</p>
    </div>
  </div>
</div>
