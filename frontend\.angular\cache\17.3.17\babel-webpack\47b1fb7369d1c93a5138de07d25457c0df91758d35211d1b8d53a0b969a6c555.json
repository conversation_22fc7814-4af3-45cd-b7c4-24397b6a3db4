{"ast": null, "code": "import { HttpHeaders } from '@angular/common/http';\nimport { throwError } from 'rxjs';\nimport { catchError, map } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport let ApiService = /*#__PURE__*/(() => {\n  class ApiService {\n    constructor(http) {\n      this.http = http;\n      this.baseUrl = 'http://localhost:3000/api';\n    }\n    getHeaders() {\n      const token = localStorage.getItem('qsc_token');\n      let headers = new HttpHeaders({\n        'Content-Type': 'application/json'\n      });\n      if (token) {\n        headers = headers.set('Authorization', `Bearer ${token}`);\n      }\n      return headers;\n    }\n    handleError(error) {\n      let errorMessage = 'An unknown error occurred';\n      if (error.error instanceof ErrorEvent) {\n        // Client-side error\n        errorMessage = error.error.message;\n      } else {\n        // Server-side error\n        if (error.error && error.error.message) {\n          errorMessage = error.error.message;\n        } else if (error.message) {\n          errorMessage = error.message;\n        }\n      }\n      console.error('API Error:', error);\n      return throwError(() => new Error(errorMessage));\n    }\n    get(endpoint) {\n      return this.http.get(`${this.baseUrl}${endpoint}`, {\n        headers: this.getHeaders()\n      }).pipe(map(response => response.data || response), catchError(this.handleError));\n    }\n    post(endpoint, data) {\n      return this.http.post(`${this.baseUrl}${endpoint}`, data, {\n        headers: this.getHeaders()\n      }).pipe(map(response => response.data || response), catchError(this.handleError));\n    }\n    put(endpoint, data) {\n      return this.http.put(`${this.baseUrl}${endpoint}`, data, {\n        headers: this.getHeaders()\n      }).pipe(map(response => response.data || response), catchError(this.handleError));\n    }\n    delete(endpoint) {\n      return this.http.delete(`${this.baseUrl}${endpoint}`, {\n        headers: this.getHeaders()\n      }).pipe(map(response => response.data || response), catchError(this.handleError));\n    }\n    checkHealth() {\n      return this.http.get(`${this.baseUrl}/health`);\n    }\n    testDatabase() {\n      return this.http.get(`${this.baseUrl}/test-db`);\n    }\n    static {\n      this.ɵfac = function ApiService_Factory(t) {\n        return new (t || ApiService)(i0.ɵɵinject(i1.HttpClient));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: ApiService,\n        factory: ApiService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return ApiService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}