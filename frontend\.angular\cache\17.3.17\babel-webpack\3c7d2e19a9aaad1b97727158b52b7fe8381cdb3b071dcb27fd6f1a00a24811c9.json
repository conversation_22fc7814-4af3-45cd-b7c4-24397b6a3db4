{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Projects/QSC1/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { TestBed } from '@angular/core/testing';\nimport { NotificationListComponent } from './notification-list.component';\nimport { NotificationService } from '../../services/notification.service';\nimport { ErrorHandlingService } from '../../services/error-handling.service';\ndescribe('NotificationListComponent', () => {\n  let component;\n  let fixture;\n  let notificationService;\n  let errorHandlingService;\n  const mockNotifications = [{\n    id: '1',\n    type: 'DELETION',\n    message: 'Message deleted',\n    timestamp: new Date(),\n    read: false\n  }, {\n    id: '2',\n    type: 'COMPROMISE',\n    message: 'Message compromised',\n    timestamp: new Date(),\n    read: true\n  }];\n  beforeEach(/*#__PURE__*/_asyncToGenerator(function* () {\n    const notificationSpy = jasmine.createSpyObj('NotificationService', ['getNotifications', 'markAsRead']);\n    const errorHandlingSpy = jasmine.createSpyObj('ErrorHandlingService', ['handleError']);\n    yield TestBed.configureTestingModule({\n      imports: [NotificationListComponent],\n      providers: [{\n        provide: NotificationService,\n        useValue: notificationSpy\n      }, {\n        provide: ErrorHandlingService,\n        useValue: errorHandlingSpy\n      }]\n    }).compileComponents();\n    notificationService = TestBed.inject(NotificationService);\n    errorHandlingService = TestBed.inject(ErrorHandlingService);\n  }));\n  beforeEach(() => {\n    fixture = TestBed.createComponent(NotificationListComponent);\n    component = fixture.componentInstance;\n  });\n  it('should create', () => {\n    expect(component).toBeTruthy();\n  });\n  it('should load notifications on init', /*#__PURE__*/_asyncToGenerator(function* () {\n    notificationService.getNotifications.and.returnValue(Promise.resolve(mockNotifications));\n    yield component.ngOnInit();\n    expect(component.notifications).toEqual(mockNotifications);\n    expect(component.errorMessage).toBeNull();\n  }));\n  it('should handle notification loading error', /*#__PURE__*/_asyncToGenerator(function* () {\n    const error = new Error('Failed to load notifications');\n    notificationService.getNotifications.and.returnValue(Promise.reject(error));\n    yield component.ngOnInit();\n    expect(errorHandlingService.handleError).toHaveBeenCalledWith(error, 'STORAGE');\n    expect(component.errorMessage).toBe('Failed to load notifications');\n  }));\n  it('should mark notification as read', /*#__PURE__*/_asyncToGenerator(function* () {\n    notificationService.getNotifications.and.returnValue(Promise.resolve(mockNotifications));\n    notificationService.markAsRead.and.returnValue(Promise.resolve());\n    yield component.ngOnInit();\n    yield component.markAsRead(mockNotifications[0]);\n    expect(notificationService.markAsRead).toHaveBeenCalledWith('1');\n    expect(mockNotifications[0].read).toBeTrue();\n    expect(component.errorMessage).toBeNull();\n  }));\n  it('should not mark already read notification', /*#__PURE__*/_asyncToGenerator(function* () {\n    notificationService.getNotifications.and.returnValue(Promise.resolve(mockNotifications));\n    notificationService.markAsRead.and.returnValue(Promise.resolve());\n    yield component.ngOnInit();\n    yield component.markAsRead(mockNotifications[1]);\n    expect(notificationService.markAsRead).not.toHaveBeenCalled();\n  }));\n  it('should handle mark as read error', /*#__PURE__*/_asyncToGenerator(function* () {\n    notificationService.getNotifications.and.returnValue(Promise.resolve(mockNotifications));\n    const error = new Error('Failed to mark as read');\n    notificationService.markAsRead.and.returnValue(Promise.reject(error));\n    yield component.ngOnInit();\n    yield component.markAsRead(mockNotifications[0]);\n    expect(errorHandlingService.handleError).toHaveBeenCalledWith(error, 'STORAGE');\n    expect(component.errorMessage).toBe('Failed to mark notification as read');\n    expect(mockNotifications[0].read).toBeFalse();\n  }));\n});", "map": {"version": 3, "names": ["TestBed", "NotificationListComponent", "NotificationService", "ErrorHandlingService", "describe", "component", "fixture", "notificationService", "errorHandlingService", "mockNotifications", "id", "type", "message", "timestamp", "Date", "read", "beforeEach", "_asyncToGenerator", "notificationSpy", "jasmine", "createSpyObj", "errorHandlingSpy", "configureTestingModule", "imports", "providers", "provide", "useValue", "compileComponents", "inject", "createComponent", "componentInstance", "it", "expect", "toBeTruthy", "getNotifications", "and", "returnValue", "Promise", "resolve", "ngOnInit", "notifications", "toEqual", "errorMessage", "toBeNull", "error", "Error", "reject", "handleError", "toHaveBeenCalledWith", "toBe", "mark<PERSON><PERSON><PERSON>", "toBeTrue", "not", "toHaveBeenCalled", "toBeFalse"], "sources": ["C:\\Users\\<USER>\\Projects\\QSC1\\frontend\\src\\app\\components\\notification-list\\notification-list.component.spec.ts"], "sourcesContent": ["import { ComponentFixture, TestBed } from '@angular/core/testing';\r\nimport { NotificationListComponent } from './notification-list.component';\r\nimport { NotificationService } from '../../services/notification.service';\r\nimport { ErrorHandlingService } from '../../services/error-handling.service';\r\n\r\ndescribe('NotificationListComponent', () => {\r\n  let component: NotificationListComponent;\r\n  let fixture: ComponentFixture<NotificationListComponent>;\r\n  let notificationService: jasmine.SpyObj<NotificationService>;\r\n  let errorHandlingService: jasmine.SpyObj<ErrorHandlingService>;\r\n\r\n  const mockNotifications = [\r\n    {\r\n      id: '1',\r\n      type: 'DELETION' as const,\r\n      message: 'Message deleted',\r\n      timestamp: new Date(),\r\n      read: false\r\n    },\r\n    {\r\n      id: '2',\r\n      type: 'COMPROMISE' as const,\r\n      message: 'Message compromised',\r\n      timestamp: new Date(),\r\n      read: true\r\n    }\r\n  ];\r\n\r\n  beforeEach(async () => {\r\n    const notificationSpy = jasmine.createSpyObj('NotificationService', [\r\n      'getNotifications',\r\n      'markAsRead'\r\n    ]);\r\n    const errorHandlingSpy = jasmine.createSpyObj('ErrorHandlingService', [\r\n      'handleError'\r\n    ]);\r\n\r\n    await TestBed.configureTestingModule({\r\n      imports: [NotificationListComponent],\r\n      providers: [\r\n        { provide: NotificationService, useValue: notificationSpy },\r\n        { provide: ErrorHandlingService, useValue: errorHandlingSpy }\r\n      ]\r\n    }).compileComponents();\r\n\r\n    notificationService = TestBed.inject(NotificationService) as jasmine.SpyObj<NotificationService>;\r\n    errorHandlingService = TestBed.inject(ErrorHandlingService) as jasmine.SpyObj<ErrorHandlingService>;\r\n  });\r\n\r\n  beforeEach(() => {\r\n    fixture = TestBed.createComponent(NotificationListComponent);\r\n    component = fixture.componentInstance;\r\n  });\r\n\r\n  it('should create', () => {\r\n    expect(component).toBeTruthy();\r\n  });\r\n\r\n  it('should load notifications on init', async () => {\r\n    notificationService.getNotifications.and.returnValue(Promise.resolve(mockNotifications));\r\n\r\n    await component.ngOnInit();\r\n    expect(component.notifications).toEqual(mockNotifications);\r\n    expect(component.errorMessage).toBeNull();\r\n  });\r\n\r\n  it('should handle notification loading error', async () => {\r\n    const error = new Error('Failed to load notifications');\r\n    notificationService.getNotifications.and.returnValue(Promise.reject(error));\r\n\r\n    await component.ngOnInit();\r\n    expect(errorHandlingService.handleError).toHaveBeenCalledWith(error, 'STORAGE');\r\n    expect(component.errorMessage).toBe('Failed to load notifications');\r\n  });\r\n\r\n  it('should mark notification as read', async () => {\r\n    notificationService.getNotifications.and.returnValue(Promise.resolve(mockNotifications));\r\n    notificationService.markAsRead.and.returnValue(Promise.resolve());\r\n\r\n    await component.ngOnInit();\r\n    await component.markAsRead(mockNotifications[0]);\r\n\r\n    expect(notificationService.markAsRead).toHaveBeenCalledWith('1');\r\n    expect(mockNotifications[0].read).toBeTrue();\r\n    expect(component.errorMessage).toBeNull();\r\n  });\r\n\r\n  it('should not mark already read notification', async () => {\r\n    notificationService.getNotifications.and.returnValue(Promise.resolve(mockNotifications));\r\n    notificationService.markAsRead.and.returnValue(Promise.resolve());\r\n\r\n    await component.ngOnInit();\r\n    await component.markAsRead(mockNotifications[1]);\r\n\r\n    expect(notificationService.markAsRead).not.toHaveBeenCalled();\r\n  });\r\n\r\n  it('should handle mark as read error', async () => {\r\n    notificationService.getNotifications.and.returnValue(Promise.resolve(mockNotifications));\r\n    const error = new Error('Failed to mark as read');\r\n    notificationService.markAsRead.and.returnValue(Promise.reject(error));\r\n\r\n    await component.ngOnInit();\r\n    await component.markAsRead(mockNotifications[0]);\r\n\r\n    expect(errorHandlingService.handleError).toHaveBeenCalledWith(error, 'STORAGE');\r\n    expect(component.errorMessage).toBe('Failed to mark notification as read');\r\n    expect(mockNotifications[0].read).toBeFalse();\r\n  });\r\n});\r\n"], "mappings": ";AAAA,SAA2BA,OAAO,QAAQ,uBAAuB;AACjE,SAASC,yBAAyB,QAAQ,+BAA+B;AACzE,SAASC,mBAAmB,QAAQ,qCAAqC;AACzE,SAASC,oBAAoB,QAAQ,uCAAuC;AAE5EC,QAAQ,CAAC,2BAA2B,EAAE,MAAK;EACzC,IAAIC,SAAoC;EACxC,IAAIC,OAAoD;EACxD,IAAIC,mBAAwD;EAC5D,IAAIC,oBAA0D;EAE9D,MAAMC,iBAAiB,GAAG,CACxB;IACEC,EAAE,EAAE,GAAG;IACPC,IAAI,EAAE,UAAmB;IACzBC,OAAO,EAAE,iBAAiB;IAC1BC,SAAS,EAAE,IAAIC,IAAI,EAAE;IACrBC,IAAI,EAAE;GACP,EACD;IACEL,EAAE,EAAE,GAAG;IACPC,IAAI,EAAE,YAAqB;IAC3BC,OAAO,EAAE,qBAAqB;IAC9BC,SAAS,EAAE,IAAIC,IAAI,EAAE;IACrBC,IAAI,EAAE;GACP,CACF;EAEDC,UAAU,cAAAC,iBAAA,CAAC,aAAW;IACpB,MAAMC,eAAe,GAAGC,OAAO,CAACC,YAAY,CAAC,qBAAqB,EAAE,CAClE,kBAAkB,EAClB,YAAY,CACb,CAAC;IACF,MAAMC,gBAAgB,GAAGF,OAAO,CAACC,YAAY,CAAC,sBAAsB,EAAE,CACpE,aAAa,CACd,CAAC;IAEF,MAAMpB,OAAO,CAACsB,sBAAsB,CAAC;MACnCC,OAAO,EAAE,CAACtB,yBAAyB,CAAC;MACpCuB,SAAS,EAAE,CACT;QAAEC,OAAO,EAAEvB,mBAAmB;QAAEwB,QAAQ,EAAER;MAAe,CAAE,EAC3D;QAAEO,OAAO,EAAEtB,oBAAoB;QAAEuB,QAAQ,EAAEL;MAAgB,CAAE;KAEhE,CAAC,CAACM,iBAAiB,EAAE;IAEtBpB,mBAAmB,GAAGP,OAAO,CAAC4B,MAAM,CAAC1B,mBAAmB,CAAwC;IAChGM,oBAAoB,GAAGR,OAAO,CAAC4B,MAAM,CAACzB,oBAAoB,CAAyC;EACrG,CAAC,EAAC;EAEFa,UAAU,CAAC,MAAK;IACdV,OAAO,GAAGN,OAAO,CAAC6B,eAAe,CAAC5B,yBAAyB,CAAC;IAC5DI,SAAS,GAAGC,OAAO,CAACwB,iBAAiB;EACvC,CAAC,CAAC;EAEFC,EAAE,CAAC,eAAe,EAAE,MAAK;IACvBC,MAAM,CAAC3B,SAAS,CAAC,CAAC4B,UAAU,EAAE;EAChC,CAAC,CAAC;EAEFF,EAAE,CAAC,mCAAmC,eAAAd,iBAAA,CAAE,aAAW;IACjDV,mBAAmB,CAAC2B,gBAAgB,CAACC,GAAG,CAACC,WAAW,CAACC,OAAO,CAACC,OAAO,CAAC7B,iBAAiB,CAAC,CAAC;IAExF,MAAMJ,SAAS,CAACkC,QAAQ,EAAE;IAC1BP,MAAM,CAAC3B,SAAS,CAACmC,aAAa,CAAC,CAACC,OAAO,CAAChC,iBAAiB,CAAC;IAC1DuB,MAAM,CAAC3B,SAAS,CAACqC,YAAY,CAAC,CAACC,QAAQ,EAAE;EAC3C,CAAC,EAAC;EAEFZ,EAAE,CAAC,0CAA0C,eAAAd,iBAAA,CAAE,aAAW;IACxD,MAAM2B,KAAK,GAAG,IAAIC,KAAK,CAAC,8BAA8B,CAAC;IACvDtC,mBAAmB,CAAC2B,gBAAgB,CAACC,GAAG,CAACC,WAAW,CAACC,OAAO,CAACS,MAAM,CAACF,KAAK,CAAC,CAAC;IAE3E,MAAMvC,SAAS,CAACkC,QAAQ,EAAE;IAC1BP,MAAM,CAACxB,oBAAoB,CAACuC,WAAW,CAAC,CAACC,oBAAoB,CAACJ,KAAK,EAAE,SAAS,CAAC;IAC/EZ,MAAM,CAAC3B,SAAS,CAACqC,YAAY,CAAC,CAACO,IAAI,CAAC,8BAA8B,CAAC;EACrE,CAAC,EAAC;EAEFlB,EAAE,CAAC,kCAAkC,eAAAd,iBAAA,CAAE,aAAW;IAChDV,mBAAmB,CAAC2B,gBAAgB,CAACC,GAAG,CAACC,WAAW,CAACC,OAAO,CAACC,OAAO,CAAC7B,iBAAiB,CAAC,CAAC;IACxFF,mBAAmB,CAAC2C,UAAU,CAACf,GAAG,CAACC,WAAW,CAACC,OAAO,CAACC,OAAO,EAAE,CAAC;IAEjE,MAAMjC,SAAS,CAACkC,QAAQ,EAAE;IAC1B,MAAMlC,SAAS,CAAC6C,UAAU,CAACzC,iBAAiB,CAAC,CAAC,CAAC,CAAC;IAEhDuB,MAAM,CAACzB,mBAAmB,CAAC2C,UAAU,CAAC,CAACF,oBAAoB,CAAC,GAAG,CAAC;IAChEhB,MAAM,CAACvB,iBAAiB,CAAC,CAAC,CAAC,CAACM,IAAI,CAAC,CAACoC,QAAQ,EAAE;IAC5CnB,MAAM,CAAC3B,SAAS,CAACqC,YAAY,CAAC,CAACC,QAAQ,EAAE;EAC3C,CAAC,EAAC;EAEFZ,EAAE,CAAC,2CAA2C,eAAAd,iBAAA,CAAE,aAAW;IACzDV,mBAAmB,CAAC2B,gBAAgB,CAACC,GAAG,CAACC,WAAW,CAACC,OAAO,CAACC,OAAO,CAAC7B,iBAAiB,CAAC,CAAC;IACxFF,mBAAmB,CAAC2C,UAAU,CAACf,GAAG,CAACC,WAAW,CAACC,OAAO,CAACC,OAAO,EAAE,CAAC;IAEjE,MAAMjC,SAAS,CAACkC,QAAQ,EAAE;IAC1B,MAAMlC,SAAS,CAAC6C,UAAU,CAACzC,iBAAiB,CAAC,CAAC,CAAC,CAAC;IAEhDuB,MAAM,CAACzB,mBAAmB,CAAC2C,UAAU,CAAC,CAACE,GAAG,CAACC,gBAAgB,EAAE;EAC/D,CAAC,EAAC;EAEFtB,EAAE,CAAC,kCAAkC,eAAAd,iBAAA,CAAE,aAAW;IAChDV,mBAAmB,CAAC2B,gBAAgB,CAACC,GAAG,CAACC,WAAW,CAACC,OAAO,CAACC,OAAO,CAAC7B,iBAAiB,CAAC,CAAC;IACxF,MAAMmC,KAAK,GAAG,IAAIC,KAAK,CAAC,wBAAwB,CAAC;IACjDtC,mBAAmB,CAAC2C,UAAU,CAACf,GAAG,CAACC,WAAW,CAACC,OAAO,CAACS,MAAM,CAACF,KAAK,CAAC,CAAC;IAErE,MAAMvC,SAAS,CAACkC,QAAQ,EAAE;IAC1B,MAAMlC,SAAS,CAAC6C,UAAU,CAACzC,iBAAiB,CAAC,CAAC,CAAC,CAAC;IAEhDuB,MAAM,CAACxB,oBAAoB,CAACuC,WAAW,CAAC,CAACC,oBAAoB,CAACJ,KAAK,EAAE,SAAS,CAAC;IAC/EZ,MAAM,CAAC3B,SAAS,CAACqC,YAAY,CAAC,CAACO,IAAI,CAAC,qCAAqC,CAAC;IAC1EjB,MAAM,CAACvB,iBAAiB,CAAC,CAAC,CAAC,CAACM,IAAI,CAAC,CAACuC,SAAS,EAAE;EAC/C,CAAC,EAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}