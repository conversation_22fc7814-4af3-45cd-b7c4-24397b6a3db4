import { Injectable } from '@angular/core';
import { WasmService } from './wasm.service';
import { NotificationService } from './notification.service';
import { ErrorHandlingService } from './error-handling.service';
import { SecureStorageService } from './secure-storage.service';

export interface ChainNode {
  id: string;
  messageId: string;
  timestamp: Date;
  deleted: boolean;
  deletionTimestamp?: Date;
}

@Injectable({
  providedIn: 'root'
})
export class ChainDeletionService {
  constructor(
    private wasmService: WasmService,
    private notificationService: NotificationService,
    private errorHandlingService: ErrorHandlingService,
    private secureStorage: SecureStorageService
  ) {}

  async getChainNodes(): Promise<ChainNode[]> {
    try {
      const nodes = await this.secureStorage.retrieveSecurely('chain_nodes', 'chain');
      return nodes || [];
    } catch (error) {
      this.errorHandlingService.handleError(error as Error, 'STORAGE');
      throw error;
    }
  }

  public async deleteFromChain(messageId: string): Promise<void> {
    try {
      const nodes = await this.getChainNodes();
      const node = nodes.find(n => n.messageId === messageId);

      if (!node) {
        throw new Error('Message not found in chain');
      }

      node.deleted = true;
      node.deletionTimestamp = new Date();

      await this.secureStorage.storeSecurely('chain_nodes', nodes, 'chain');
      await this.wasmService.wipeMemory();
    } catch (error) {
      this.errorHandlingService.handleError(error as Error, 'SECURITY');
      throw error;
    }
  }

  public async deleteCompromisedChain(messageIds: string[]): Promise<void> {
    // 1. Get all chain nodes
    const nodes = await Promise.all(
      messageIds.map(id => this.getChainNode(id))
    );

    // 2. Mark all as deleted
    const deletionPromises = nodes.map(async node => {
      if (!node) return;

      node.deleted = true;
      node.deletionTimestamp = new Date();

      const deletionRecord = await this.wasmService.encryptMessage(
        JSON.stringify({
          messageId: node.messageId,
          deletionTimestamp: node.deletionTimestamp.toISOString(),
          reason: 'COMPROMISE'
        })
      );

      await this.storeDeletionRecord(node.messageId);
    });

    await Promise.all(deletionPromises);

    // 3. Notify about compromise
    await this.notificationService.createCompromiseNotification(messageIds);

    // 4. Wipe sensitive data
    this.wipeMemory();
  }

  private async getChainNode(messageId: string): Promise<ChainNode | null> {
    // TODO: Implement secure chain node retrieval
    // This should be replaced with actual chain storage implementation
    console.log(`Retrieving chain node for message: ${messageId}`);
    return null;
  }

  private async storeDeletionRecord(messageId: string): Promise<void> {
    try {
      const record = {
        messageId,
        timestamp: new Date(),
        signature: await this.wasmService.signMessage(messageId)
      };

      await this.secureStorage.storeSecurely('deletion_records', record, 'deletions');
      await this.wasmService.wipeMemory();
    } catch (error) {
      this.errorHandlingService.handleError(error as Error, 'SECURITY');
      throw error;
    }
  }

  async verifyDeletionRecord(messageId: string): Promise<boolean> {
    try {
      const records = await this.secureStorage.retrieveSecurely('deletion_records', 'deletions');
      const record = records?.find((r: any) => r.messageId === messageId);

      if (!record) {
        return false;
      }

      return await this.wasmService.verifySignature(messageId, record.signature);
    } catch (error) {
      this.errorHandlingService.handleError(error as Error, 'SECURITY');
      throw error;
    }
  }

  public wipeMemory(): void {
    this.wasmService.wipeMemory();
  }
}
