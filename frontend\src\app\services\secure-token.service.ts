import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { TokenData } from '../types/auth.types';

@Injectable({
  providedIn: 'root'
})
export class SecureTokenService {
  private readonly TOKEN_KEY = 'qsc_secure_token';
  private readonly REFRESH_TOKEN_KEY = 'qsc_refresh_token';
  private tokenSubject = new BehaviorSubject<string | null>(null);
  private refreshTokenSubject = new BehaviorSubject<string | null>(null);

  constructor() {
    this.initializeTokens();
  }

  /**
   * Initialize tokens from secure storage on service startup
   */
  private initializeTokens(): void {
    try {
      // Try to get tokens from sessionStorage first (more secure than localStorage)
      const token = sessionStorage.getItem(this.TOKEN_KEY);
      const refreshToken = sessionStorage.getItem(this.REFRESH_TOKEN_KEY);

      if (token && this.isTokenValid(token)) {
        this.tokenSubject.next(token);
      }

      if (refreshToken) {
        this.refreshTokenSubject.next(refreshToken);
      }
    } catch (error) {
      console.error('Failed to initialize tokens:', error);
      this.clearTokens();
    }
  }

  /**
   * Store tokens securely
   */
  setTokens(tokenData: TokenData): void {
    try {
      // Store in sessionStorage instead of localStorage for better security
      sessionStorage.setItem(this.TOKEN_KEY, tokenData.token);

      if (tokenData.refreshToken) {
        sessionStorage.setItem(this.REFRESH_TOKEN_KEY, tokenData.refreshToken);
        this.refreshTokenSubject.next(tokenData.refreshToken);
      }

      this.tokenSubject.next(tokenData.token);

      // Set up automatic token cleanup
      this.scheduleTokenCleanup(tokenData.expiresAt);
    } catch (error) {
      console.error('Failed to store tokens:', error);
      throw new Error('Token storage failed');
    }
  }

  /**
   * Get current access token
   */
  getToken(): string | null {
    return this.tokenSubject.value;
  }

  /**
   * Get current refresh token
   */
  getRefreshToken(): string | null {
    return this.refreshTokenSubject.value;
  }

  /**
   * Observable for token changes
   */
  get token$(): Observable<string | null> {
    return this.tokenSubject.asObservable();
  }

  /**
   * Observable for refresh token changes
   */
  get refreshToken$(): Observable<string | null> {
    return this.refreshTokenSubject.asObservable();
  }

  /**
   * Check if current token is valid
   */
  isTokenValid(token?: string): boolean {
    const currentToken = token || this.getToken();
    if (!currentToken) return false;

    try {
      const payload = this.parseJwtPayload(currentToken);
      const now = Math.floor(Date.now() / 1000);
      return payload.exp > now;
    } catch {
      return false;
    }
  }

  /**
   * Clear all tokens
   */
  clearTokens(): void {
    try {
      sessionStorage.removeItem(this.TOKEN_KEY);
      sessionStorage.removeItem(this.REFRESH_TOKEN_KEY);

      // Also clear from localStorage if they exist there (migration)
      localStorage.removeItem('qsc_token');
      localStorage.removeItem('qsc_user');

      this.tokenSubject.next(null);
      this.refreshTokenSubject.next(null);
    } catch (error) {
      console.error('Failed to clear tokens:', error);
    }
  }

  /**
   * Parse JWT payload without verification (for expiration check)
   */
  private parseJwtPayload(token: string): any {
    try {
      const base64Url = token.split('.')[1];
      const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
      const jsonPayload = decodeURIComponent(
        atob(base64)
          .split('')
          .map(c => '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2))
          .join('')
      );
      return JSON.parse(jsonPayload);
    } catch (error) {
      throw new Error('Invalid JWT token');
    }
  }

  /**
   * Schedule automatic token cleanup when it expires
   */
  private scheduleTokenCleanup(expiresAt: number): void {
    const now = Date.now();
    const expirationTime = expiresAt * 1000; // Convert to milliseconds
    const timeUntilExpiration = expirationTime - now;

    if (timeUntilExpiration > 0) {
      setTimeout(() => {
        if (!this.isTokenValid()) {
          this.clearTokens();
        }
      }, timeUntilExpiration);
    }
  }

  /**
   * Get token expiration time
   */
  getTokenExpiration(): number | null {
    const token = this.getToken();
    if (!token) return null;

    try {
      const payload = this.parseJwtPayload(token);
      return payload.exp * 1000; // Convert to milliseconds
    } catch {
      return null;
    }
  }

  /**
   * Check if token will expire soon (within 5 minutes)
   */
  isTokenExpiringSoon(): boolean {
    const expiration = this.getTokenExpiration();
    if (!expiration) return true;

    const fiveMinutesFromNow = Date.now() + (5 * 60 * 1000);
    return expiration < fiveMinutesFromNow;
  }
}
