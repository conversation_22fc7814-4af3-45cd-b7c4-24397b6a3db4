#!/usr/bin/env ts-node

/**
 * Test script to verify Post-Quantum Cryptography integration
 * This script tests the LibOQSService with noble-post-quantum library
 */

import { LibOQSService } from '../security/services/liboqs.service';
import { Logger } from '@nestjs/common';

class TestLogger extends Logger {
  constructor() {
    super('PQC-Test');
  }
}

async function testPQCIntegration() {
  console.log('🔐 Testing Post-Quantum Cryptography Integration\n');

  // Create LibOQS service instance
  const libOQSService = new LibOQSService();
  
  // Mock the logger
  (libOQSService as any).logger = new TestLogger();

  // Initialize the service
  await libOQSService.onModuleInit();

  console.log('✅ LibOQS Service initialized');
  console.log(`📊 PQC Available: ${libOQSService.isAvailable()}\n`);

  // Test algorithm listing
  console.log('📋 Available Algorithms:');
  const sigAlgorithms = libOQSService.getAvailableSignatureAlgorithms();
  const kemAlgorithms = libOQSService.getAvailableKEMAlgorithms();
  
  console.log(`   Signature: ${sigAlgorithms.join(', ')}`);
  console.log(`   KEM: ${kemAlgorithms.join(', ')}\n`);

  // Test Dilithium (ML-DSA) operations
  console.log('🔏 Testing Dilithium (ML-DSA) Operations:');
  try {
    const dilithiumKeyPair = await libOQSService.generateDilithiumKeyPair('Dilithium3');
    console.log(`   ✅ Key generation successful`);
    console.log(`   📏 Public key length: ${dilithiumKeyPair.publicKey.length} bytes`);
    console.log(`   📏 Private key length: ${dilithiumKeyPair.privateKey.length} bytes`);

    const testMessage = Buffer.from('Hello, Post-Quantum World!');
    const signature = await libOQSService.signWithDilithium(testMessage, dilithiumKeyPair.privateKey);
    console.log(`   ✅ Signing successful`);
    console.log(`   📏 Signature length: ${signature.signature.length} bytes`);

    const isValid = await libOQSService.verifyDilithiumSignature(signature, dilithiumKeyPair.publicKey);
    console.log(`   ✅ Verification result: ${isValid ? 'VALID' : 'INVALID'}`);
  } catch (error) {
    console.log(`   ❌ Dilithium test failed: ${error.message}`);
  }

  console.log('');

  // Test Kyber (ML-KEM) operations
  console.log('🔐 Testing Kyber (ML-KEM) Operations:');
  try {
    const kyberKeyPair = await libOQSService.generateKyberKeyPair('Kyber768');
    console.log(`   ✅ Key generation successful`);
    console.log(`   📏 Public key length: ${kyberKeyPair.publicKey.length} bytes`);
    console.log(`   📏 Private key length: ${kyberKeyPair.privateKey.length} bytes`);

    const encapsulation = await libOQSService.kyberEncapsulate(kyberKeyPair.publicKey);
    console.log(`   ✅ Encapsulation successful`);
    console.log(`   📏 Shared secret length: ${encapsulation.sharedSecret.length} bytes`);
    console.log(`   📏 Ciphertext length: ${encapsulation.ciphertext.length} bytes`);

    const decapsulatedSecret = await libOQSService.kyberDecapsulate(
      kyberKeyPair.privateKey, 
      encapsulation.ciphertext
    );
    console.log(`   ✅ Decapsulation successful`);
    
    const secretsMatch = encapsulation.sharedSecret.equals(decapsulatedSecret);
    console.log(`   ✅ Shared secrets match: ${secretsMatch ? 'YES' : 'NO'}`);
  } catch (error) {
    console.log(`   ❌ Kyber test failed: ${error.message}`);
  }

  console.log('');

  // Test algorithm details
  console.log('📊 Algorithm Details:');
  const dilithiumDetails = libOQSService.getAlgorithmDetails('Dilithium3', 'signature');
  const kyberDetails = libOQSService.getAlgorithmDetails('Kyber768', 'kem');
  
  console.log('   Dilithium3:', dilithiumDetails);
  console.log('   Kyber768:', kyberDetails);

  console.log('\n🎉 Post-Quantum Cryptography integration test completed!');
}

// Run the test
testPQCIntegration().catch(error => {
  console.error('❌ Test failed:', error);
  process.exit(1);
});
