/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = function(knex) {
  return knex.schema.createTable('session_keys', function(table) {
    table.uuid('id').primary();
    table.uuid('user1_id').references('id').inTable('users').onDelete('CASCADE');
    table.uuid('user2_id').references('id').inTable('users').onDelete('CASCADE');
    table.text('encrypted_shared_key').notNullable(); // Encrypted with each user's public key
    table.timestamp('created_at').defaultTo(knex.fn.now());
    table.timestamp('expires_at').notNullable(); // Keys must be rotated
    table.boolean('is_active').defaultTo(true);
    
    // Ensure unique sessions between users
    table.unique(['user1_id', 'user2_id']);
  });
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function(knex) {
  return knex.schema.dropTable('session_keys');
}; 