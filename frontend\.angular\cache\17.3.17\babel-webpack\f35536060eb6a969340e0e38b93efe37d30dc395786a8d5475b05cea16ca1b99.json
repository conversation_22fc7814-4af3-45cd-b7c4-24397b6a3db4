{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Projects/QSC1/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { __decorate } from \"tslib\";\nimport { Injectable } from '@angular/core';\nimport { WasmService } from './wasm.service';\nimport { NotificationService } from './notification.service';\nimport { ErrorHandlingService } from './error-handling.service';\nimport { SecureStorageService } from './secure-storage.service';\nlet ChainDeletionService = class ChainDeletionService {\n  constructor(wasmService, notificationService, errorHandlingService, secureStorage) {\n    this.wasmService = wasmService;\n    this.notificationService = notificationService;\n    this.errorHandlingService = errorHandlingService;\n    this.secureStorage = secureStorage;\n  }\n  getChainNodes() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      try {\n        const nodes = yield _this.secureStorage.retrieveSecurely('chain_nodes', 'chain');\n        return nodes || [];\n      } catch (error) {\n        _this.errorHandlingService.handleError(error, 'STORAGE');\n        throw error;\n      }\n    })();\n  }\n  deleteFromChain(messageId) {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        const nodes = yield _this2.getChainNodes();\n        const node = nodes.find(n => n.messageId === messageId);\n        if (!node) {\n          throw new Error('Message not found in chain');\n        }\n        node.deleted = true;\n        node.deletionTimestamp = new Date();\n        yield _this2.secureStorage.storeSecurely('chain_nodes', nodes, 'chain');\n        yield _this2.wasmService.wipeMemory();\n      } catch (error) {\n        _this2.errorHandlingService.handleError(error, 'SECURITY');\n        throw error;\n      }\n    })();\n  }\n  deleteCompromisedChain(messageIds) {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      // 1. Get all chain nodes\n      const nodes = yield Promise.all(messageIds.map(id => _this3.getChainNode(id)));\n      // 2. Mark all as deleted\n      const deletionPromises = nodes.map(/*#__PURE__*/function () {\n        var _ref = _asyncToGenerator(function* (node) {\n          if (!node) return;\n          node.deleted = true;\n          node.deletionTimestamp = new Date();\n          const deletionRecord = yield _this3.wasmService.encryptMessage(JSON.stringify({\n            messageId: node.messageId,\n            deletionTimestamp: node.deletionTimestamp.toISOString(),\n            reason: 'COMPROMISE'\n          }));\n          yield _this3.storeDeletionRecord(node.messageId);\n        });\n        return function (_x) {\n          return _ref.apply(this, arguments);\n        };\n      }());\n      yield Promise.all(deletionPromises);\n      // 3. Notify about compromise\n      yield _this3.notificationService.createCompromiseNotification(messageIds);\n      // 4. Wipe sensitive data\n      _this3.wipeMemory();\n    })();\n  }\n  getChainNode(messageId) {\n    return _asyncToGenerator(function* () {\n      // TODO: Implement secure chain node retrieval\n      // This should be replaced with actual chain storage implementation\n      console.log(`Retrieving chain node for message: ${messageId}`);\n      return null;\n    })();\n  }\n  storeDeletionRecord(messageId) {\n    var _this4 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        const record = {\n          messageId,\n          timestamp: new Date(),\n          signature: yield _this4.wasmService.signMessage(messageId)\n        };\n        yield _this4.secureStorage.storeSecurely('deletion_records', record, 'deletions');\n        yield _this4.wasmService.wipeMemory();\n      } catch (error) {\n        _this4.errorHandlingService.handleError(error, 'SECURITY');\n        throw error;\n      }\n    })();\n  }\n  verifyDeletionRecord(messageId) {\n    var _this5 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        const records = yield _this5.secureStorage.retrieveSecurely('deletion_records', 'deletions');\n        const record = records?.find(r => r.messageId === messageId);\n        if (!record) {\n          return false;\n        }\n        return yield _this5.wasmService.verifySignature(messageId, record.signature);\n      } catch (error) {\n        _this5.errorHandlingService.handleError(error, 'SECURITY');\n        throw error;\n      }\n    })();\n  }\n  wipeMemory() {\n    this.wasmService.wipeMemory();\n  }\n  static {\n    this.ctorParameters = () => [{\n      type: WasmService\n    }, {\n      type: NotificationService\n    }, {\n      type: ErrorHandlingService\n    }, {\n      type: SecureStorageService\n    }];\n  }\n};\nChainDeletionService = __decorate([Injectable({\n  providedIn: 'root'\n})], ChainDeletionService);\nexport { ChainDeletionService };", "map": {"version": 3, "names": ["Injectable", "WasmService", "NotificationService", "ErrorHandlingService", "SecureStorageService", "ChainDeletionService", "constructor", "wasmService", "notificationService", "errorHandlingService", "secureStorage", "getChainNodes", "_this", "_asyncToGenerator", "nodes", "retrieveS<PERSON>urely", "error", "handleError", "deleteFrom<PERSON><PERSON><PERSON>", "messageId", "_this2", "node", "find", "n", "Error", "deleted", "deletionTimestamp", "Date", "storeSecurely", "wipeMemory", "deleteCompromised<PERSON><PERSON>n", "messageIds", "_this3", "Promise", "all", "map", "id", "getChainNode", "deletionPromises", "_ref", "deletionRecord", "encryptMessage", "JSON", "stringify", "toISOString", "reason", "storeDeletionRecord", "_x", "apply", "arguments", "createCompromiseNotification", "console", "log", "_this4", "record", "timestamp", "signature", "signMessage", "verifyDeletionRecord", "_this5", "records", "r", "verifySignature", "__decorate", "providedIn"], "sources": ["C:\\Users\\<USER>\\Projects\\QSC1\\frontend\\src\\app\\services\\chain-deletion.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { WasmService } from './wasm.service';\r\nimport { NotificationService } from './notification.service';\r\nimport { ErrorHandlingService } from './error-handling.service';\r\nimport { SecureStorageService } from './secure-storage.service';\r\n\r\nexport interface ChainNode {\r\n  id: string;\r\n  messageId: string;\r\n  timestamp: Date;\r\n  deleted: boolean;\r\n  deletionTimestamp?: Date;\r\n}\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class ChainDeletionService {\r\n  constructor(\r\n    private wasmService: WasmService,\r\n    private notificationService: NotificationService,\r\n    private errorHandlingService: ErrorHandlingService,\r\n    private secureStorage: SecureStorageService\r\n  ) {}\r\n\r\n  async getChainNodes(): Promise<ChainNode[]> {\r\n    try {\r\n      const nodes = await this.secureStorage.retrieveSecurely('chain_nodes', 'chain');\r\n      return nodes || [];\r\n    } catch (error) {\r\n      this.errorHandlingService.handleError(error as Error, 'STORAGE');\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  public async deleteFromChain(messageId: string): Promise<void> {\r\n    try {\r\n      const nodes = await this.getChainNodes();\r\n      const node = nodes.find(n => n.messageId === messageId);\r\n\r\n      if (!node) {\r\n        throw new Error('Message not found in chain');\r\n      }\r\n\r\n      node.deleted = true;\r\n      node.deletionTimestamp = new Date();\r\n\r\n      await this.secureStorage.storeSecurely('chain_nodes', nodes, 'chain');\r\n      await this.wasmService.wipeMemory();\r\n    } catch (error) {\r\n      this.errorHandlingService.handleError(error as Error, 'SECURITY');\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  public async deleteCompromisedChain(messageIds: string[]): Promise<void> {\r\n    // 1. Get all chain nodes\r\n    const nodes = await Promise.all(\r\n      messageIds.map(id => this.getChainNode(id))\r\n    );\r\n\r\n    // 2. Mark all as deleted\r\n    const deletionPromises = nodes.map(async node => {\r\n      if (!node) return;\r\n\r\n      node.deleted = true;\r\n      node.deletionTimestamp = new Date();\r\n\r\n      const deletionRecord = await this.wasmService.encryptMessage(\r\n        JSON.stringify({\r\n          messageId: node.messageId,\r\n          deletionTimestamp: node.deletionTimestamp.toISOString(),\r\n          reason: 'COMPROMISE'\r\n        })\r\n      );\r\n\r\n      await this.storeDeletionRecord(node.messageId);\r\n    });\r\n\r\n    await Promise.all(deletionPromises);\r\n\r\n    // 3. Notify about compromise\r\n    await this.notificationService.createCompromiseNotification(messageIds);\r\n\r\n    // 4. Wipe sensitive data\r\n    this.wipeMemory();\r\n  }\r\n\r\n  private async getChainNode(messageId: string): Promise<ChainNode | null> {\r\n    // TODO: Implement secure chain node retrieval\r\n    // This should be replaced with actual chain storage implementation\r\n    console.log(`Retrieving chain node for message: ${messageId}`);\r\n    return null;\r\n  }\r\n\r\n  private async storeDeletionRecord(messageId: string): Promise<void> {\r\n    try {\r\n      const record = {\r\n        messageId,\r\n        timestamp: new Date(),\r\n        signature: await this.wasmService.signMessage(messageId)\r\n      };\r\n\r\n      await this.secureStorage.storeSecurely('deletion_records', record, 'deletions');\r\n      await this.wasmService.wipeMemory();\r\n    } catch (error) {\r\n      this.errorHandlingService.handleError(error as Error, 'SECURITY');\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  async verifyDeletionRecord(messageId: string): Promise<boolean> {\r\n    try {\r\n      const records = await this.secureStorage.retrieveSecurely('deletion_records', 'deletions');\r\n      const record = records?.find((r: any) => r.messageId === messageId);\r\n\r\n      if (!record) {\r\n        return false;\r\n      }\r\n\r\n      return await this.wasmService.verifySignature(messageId, record.signature);\r\n    } catch (error) {\r\n      this.errorHandlingService.handleError(error as Error, 'SECURITY');\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  public wipeMemory(): void {\r\n    this.wasmService.wipeMemory();\r\n  }\r\n}\r\n"], "mappings": ";;AAAA,SAASA,UAAU,QAAQ,eAAe;AAC1C,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,mBAAmB,QAAQ,wBAAwB;AAC5D,SAASC,oBAAoB,QAAQ,0BAA0B;AAC/D,SAASC,oBAAoB,QAAQ,0BAA0B;AAaxD,IAAMC,oBAAoB,GAA1B,MAAMA,oBAAoB;EAC/BC,YACUC,WAAwB,EACxBC,mBAAwC,EACxCC,oBAA0C,EAC1CC,aAAmC;IAHnC,KAAAH,WAAW,GAAXA,WAAW;IACX,KAAAC,mBAAmB,GAAnBA,mBAAmB;IACnB,KAAAC,oBAAoB,GAApBA,oBAAoB;IACpB,KAAAC,aAAa,GAAbA,aAAa;EACpB;EAEGC,aAAaA,CAAA;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MACjB,IAAI;QACF,MAAMC,KAAK,SAASF,KAAI,CAACF,aAAa,CAACK,gBAAgB,CAAC,aAAa,EAAE,OAAO,CAAC;QAC/E,OAAOD,KAAK,IAAI,EAAE;OACnB,CAAC,OAAOE,KAAK,EAAE;QACdJ,KAAI,CAACH,oBAAoB,CAACQ,WAAW,CAACD,KAAc,EAAE,SAAS,CAAC;QAChE,MAAMA,KAAK;;IACZ;EACH;EAEaE,eAAeA,CAACC,SAAiB;IAAA,IAAAC,MAAA;IAAA,OAAAP,iBAAA;MAC5C,IAAI;QACF,MAAMC,KAAK,SAASM,MAAI,CAACT,aAAa,EAAE;QACxC,MAAMU,IAAI,GAAGP,KAAK,CAACQ,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACJ,SAAS,KAAKA,SAAS,CAAC;QAEvD,IAAI,CAACE,IAAI,EAAE;UACT,MAAM,IAAIG,KAAK,CAAC,4BAA4B,CAAC;;QAG/CH,IAAI,CAACI,OAAO,GAAG,IAAI;QACnBJ,IAAI,CAACK,iBAAiB,GAAG,IAAIC,IAAI,EAAE;QAEnC,MAAMP,MAAI,CAACV,aAAa,CAACkB,aAAa,CAAC,aAAa,EAAEd,KAAK,EAAE,OAAO,CAAC;QACrE,MAAMM,MAAI,CAACb,WAAW,CAACsB,UAAU,EAAE;OACpC,CAAC,OAAOb,KAAK,EAAE;QACdI,MAAI,CAACX,oBAAoB,CAACQ,WAAW,CAACD,KAAc,EAAE,UAAU,CAAC;QACjE,MAAMA,KAAK;;IACZ;EACH;EAEac,sBAAsBA,CAACC,UAAoB;IAAA,IAAAC,MAAA;IAAA,OAAAnB,iBAAA;MACtD;MACA,MAAMC,KAAK,SAASmB,OAAO,CAACC,GAAG,CAC7BH,UAAU,CAACI,GAAG,CAACC,EAAE,IAAIJ,MAAI,CAACK,YAAY,CAACD,EAAE,CAAC,CAAC,CAC5C;MAED;MACA,MAAME,gBAAgB,GAAGxB,KAAK,CAACqB,GAAG;QAAA,IAAAI,IAAA,GAAA1B,iBAAA,CAAC,WAAMQ,IAAI,EAAG;UAC9C,IAAI,CAACA,IAAI,EAAE;UAEXA,IAAI,CAACI,OAAO,GAAG,IAAI;UACnBJ,IAAI,CAACK,iBAAiB,GAAG,IAAIC,IAAI,EAAE;UAEnC,MAAMa,cAAc,SAASR,MAAI,CAACzB,WAAW,CAACkC,cAAc,CAC1DC,IAAI,CAACC,SAAS,CAAC;YACbxB,SAAS,EAAEE,IAAI,CAACF,SAAS;YACzBO,iBAAiB,EAAEL,IAAI,CAACK,iBAAiB,CAACkB,WAAW,EAAE;YACvDC,MAAM,EAAE;WACT,CAAC,CACH;UAED,MAAMb,MAAI,CAACc,mBAAmB,CAACzB,IAAI,CAACF,SAAS,CAAC;QAChD,CAAC;QAAA,iBAAA4B,EAAA;UAAA,OAAAR,IAAA,CAAAS,KAAA,OAAAC,SAAA;QAAA;MAAA,IAAC;MAEF,MAAMhB,OAAO,CAACC,GAAG,CAACI,gBAAgB,CAAC;MAEnC;MACA,MAAMN,MAAI,CAACxB,mBAAmB,CAAC0C,4BAA4B,CAACnB,UAAU,CAAC;MAEvE;MACAC,MAAI,CAACH,UAAU,EAAE;IAAC;EACpB;EAEcQ,YAAYA,CAAClB,SAAiB;IAAA,OAAAN,iBAAA;MAC1C;MACA;MACAsC,OAAO,CAACC,GAAG,CAAC,sCAAsCjC,SAAS,EAAE,CAAC;MAC9D,OAAO,IAAI;IAAC;EACd;EAEc2B,mBAAmBA,CAAC3B,SAAiB;IAAA,IAAAkC,MAAA;IAAA,OAAAxC,iBAAA;MACjD,IAAI;QACF,MAAMyC,MAAM,GAAG;UACbnC,SAAS;UACToC,SAAS,EAAE,IAAI5B,IAAI,EAAE;UACrB6B,SAAS,QAAQH,MAAI,CAAC9C,WAAW,CAACkD,WAAW,CAACtC,SAAS;SACxD;QAED,MAAMkC,MAAI,CAAC3C,aAAa,CAACkB,aAAa,CAAC,kBAAkB,EAAE0B,MAAM,EAAE,WAAW,CAAC;QAC/E,MAAMD,MAAI,CAAC9C,WAAW,CAACsB,UAAU,EAAE;OACpC,CAAC,OAAOb,KAAK,EAAE;QACdqC,MAAI,CAAC5C,oBAAoB,CAACQ,WAAW,CAACD,KAAc,EAAE,UAAU,CAAC;QACjE,MAAMA,KAAK;;IACZ;EACH;EAEM0C,oBAAoBA,CAACvC,SAAiB;IAAA,IAAAwC,MAAA;IAAA,OAAA9C,iBAAA;MAC1C,IAAI;QACF,MAAM+C,OAAO,SAASD,MAAI,CAACjD,aAAa,CAACK,gBAAgB,CAAC,kBAAkB,EAAE,WAAW,CAAC;QAC1F,MAAMuC,MAAM,GAAGM,OAAO,EAAEtC,IAAI,CAAEuC,CAAM,IAAKA,CAAC,CAAC1C,SAAS,KAAKA,SAAS,CAAC;QAEnE,IAAI,CAACmC,MAAM,EAAE;UACX,OAAO,KAAK;;QAGd,aAAaK,MAAI,CAACpD,WAAW,CAACuD,eAAe,CAAC3C,SAAS,EAAEmC,MAAM,CAACE,SAAS,CAAC;OAC3E,CAAC,OAAOxC,KAAK,EAAE;QACd2C,MAAI,CAAClD,oBAAoB,CAACQ,WAAW,CAACD,KAAc,EAAE,UAAU,CAAC;QACjE,MAAMA,KAAK;;IACZ;EACH;EAEOa,UAAUA,CAAA;IACf,IAAI,CAACtB,WAAW,CAACsB,UAAU,EAAE;EAC/B;;;;;;;;;;;;;AAhHWxB,oBAAoB,GAAA0D,UAAA,EAHhC/D,UAAU,CAAC;EACVgE,UAAU,EAAE;CACb,CAAC,C,EACW3D,oBAAoB,CAiHhC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}