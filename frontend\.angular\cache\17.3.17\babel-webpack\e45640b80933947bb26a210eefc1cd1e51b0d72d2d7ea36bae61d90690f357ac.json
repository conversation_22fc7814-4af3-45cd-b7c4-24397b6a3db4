{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { Subject, takeUntil } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../services/auth.service\";\nimport * as i2 from \"../../services/message.service\";\nimport * as i3 from \"../../services/notification.service\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/forms\";\nfunction QscMainComponent_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 9);\n  }\n}\nfunction QscMainComponent_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 10);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.unreadCount, \" \");\n  }\n}\nfunction QscMainComponent_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 11)(1, \"span\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 12);\n    i0.ɵɵlistener(\"click\", function QscMainComponent_div_6_Template_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.logout());\n    });\n    i0.ɵɵtext(4, \"\\u00D7\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r0.currentUser.username);\n  }\n}\nfunction QscMainComponent_div_7_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 26);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.loginError, \" \");\n  }\n}\nfunction QscMainComponent_div_7_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 27);\n    i0.ɵɵelement(1, \"div\", 28);\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3, \"Authenticating...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction QscMainComponent_div_7_div_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 29)(1, \"p\");\n    i0.ɵɵtext(2, \"\\uD83D\\uDD12 Protected by post-quantum cryptography\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p\", 30);\n    i0.ɵɵtext(4, \"Form auto-submits when credentials are valid\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction QscMainComponent_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 13);\n    i0.ɵɵlistener(\"click\", function QscMainComponent_div_7_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.closeLoginModal());\n    });\n    i0.ɵɵelementStart(1, \"div\", 14);\n    i0.ɵɵlistener(\"click\", function QscMainComponent_div_7_Template_div_click_1_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelementStart(2, \"div\", 15)(3, \"h2\");\n    i0.ɵɵtext(4, \"Sign In\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 16);\n    i0.ɵɵlistener(\"click\", function QscMainComponent_div_7_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.closeLoginModal());\n    });\n    i0.ɵɵtext(6, \"\\u00D7\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 17)(8, \"div\", 18)(9, \"label\", 19);\n    i0.ɵɵtext(10, \"Email\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"input\", 20);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function QscMainComponent_div_7_Template_input_ngModelChange_11_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r0 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r0.loginCredentials.email, $event) || (ctx_r0.loginCredentials.email = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"input\", function QscMainComponent_div_7_Template_input_input_11_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.onLoginInputChange());\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"div\", 18)(13, \"label\", 21);\n    i0.ɵɵtext(14, \"Secret Word\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"input\", 22);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function QscMainComponent_div_7_Template_input_ngModelChange_15_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r0 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r0.loginCredentials.secretWord, $event) || (ctx_r0.loginCredentials.secretWord = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"input\", function QscMainComponent_div_7_Template_input_input_15_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.onLoginInputChange());\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(16, QscMainComponent_div_7_div_16_Template, 2, 1, \"div\", 23)(17, QscMainComponent_div_7_div_17_Template, 4, 0, \"div\", 24)(18, QscMainComponent_div_7_div_18_Template, 5, 0, \"div\", 25);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(11);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r0.loginCredentials.email);\n    i0.ɵɵproperty(\"disabled\", ctx_r0.isLoading);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r0.loginCredentials.secretWord);\n    i0.ɵɵproperty(\"disabled\", ctx_r0.isLoading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.loginError);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isLoading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.isLoading);\n  }\n}\nfunction QscMainComponent_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 13);\n    i0.ɵɵlistener(\"click\", function QscMainComponent_div_8_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.closeMessageComposer());\n    });\n    i0.ɵɵelementStart(1, \"div\", 31);\n    i0.ɵɵlistener(\"click\", function QscMainComponent_div_8_Template_div_click_1_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelementStart(2, \"div\", 15)(3, \"h2\");\n    i0.ɵɵtext(4, \"Compose Message\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 16);\n    i0.ɵɵlistener(\"click\", function QscMainComponent_div_8_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.closeMessageComposer());\n    });\n    i0.ɵɵtext(6, \"\\u00D7\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 17)(8, \"div\", 18)(9, \"label\", 32);\n    i0.ɵɵtext(10, \"Message\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"textarea\", 33);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function QscMainComponent_div_8_Template_textarea_ngModelChange_11_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r0 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r0.messageContent, $event) || (ctx_r0.messageContent = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"div\", 34);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"div\", 35)(15, \"button\", 36);\n    i0.ɵɵlistener(\"click\", function QscMainComponent_div_8_Template_button_click_15_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.sendMessage());\n    });\n    i0.ɵɵtext(16, \" Send Message \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"button\", 37);\n    i0.ɵɵlistener(\"click\", function QscMainComponent_div_8_Template_button_click_17_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.closeMessageComposer());\n    });\n    i0.ɵɵtext(18, \" Cancel \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(19, \"div\", 38)(20, \"p\");\n    i0.ɵɵtext(21, \"Press Enter to send \\u2022 Shift+Enter for new line\");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(11);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r0.messageContent);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r0.messageContent.length, \"/1000\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", !ctx_r0.messageContent.trim());\n  }\n}\nfunction QscMainComponent_div_9_div_8_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 44)(1, \"div\", 45)(2, \"span\", 46);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 47);\n    i0.ɵɵtext(5);\n    i0.ɵɵpipe(6, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 48);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const message_r6 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(message_r6.sender);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(6, 3, message_r6.timestamp, \"short\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(message_r6.content);\n  }\n}\nfunction QscMainComponent_div_9_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 42);\n    i0.ɵɵtemplate(1, QscMainComponent_div_9_div_8_div_1_Template, 9, 6, \"div\", 43);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.messages)(\"ngForTrackBy\", ctx_r0.trackMessage);\n  }\n}\nfunction QscMainComponent_div_9_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 49)(1, \"p\");\n    i0.ɵɵtext(2, \"No messages yet\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p\", 50);\n    i0.ɵɵtext(4, \"Click the circle to compose your first message\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction QscMainComponent_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 13);\n    i0.ɵɵlistener(\"click\", function QscMainComponent_div_9_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.closeMessagesViewer());\n    });\n    i0.ɵɵelementStart(1, \"div\", 39);\n    i0.ɵɵlistener(\"click\", function QscMainComponent_div_9_Template_div_click_1_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelementStart(2, \"div\", 15)(3, \"h2\");\n    i0.ɵɵtext(4, \"Messages\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 16);\n    i0.ɵɵlistener(\"click\", function QscMainComponent_div_9_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.closeMessagesViewer());\n    });\n    i0.ɵɵtext(6, \"\\u00D7\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 17);\n    i0.ɵɵtemplate(8, QscMainComponent_div_9_div_8_Template, 2, 2, \"div\", 40)(9, QscMainComponent_div_9_div_9_Template, 5, 0, \"div\", 41);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.messages.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.messages.length === 0);\n  }\n}\nfunction QscMainComponent_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 51)(1, \"span\");\n    i0.ɵɵtext(2, \"ESC to close modals\");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport let QscMainComponent = /*#__PURE__*/(() => {\n  class QscMainComponent {\n    constructor(authService, messageService, notificationService) {\n      this.authService = authService;\n      this.messageService = messageService;\n      this.notificationService = notificationService;\n      this.destroy$ = new Subject();\n      // Circle state management\n      this.circleState = 'guest';\n      // Modal states\n      this.showLoginModal = false;\n      this.showMessageModal = false;\n      this.showMessagesModal = false;\n      // Authentication\n      this.loginCredentials = {\n        email: '',\n        secretWord: ''\n      };\n      this.loginError = '';\n      this.isLoading = false;\n      // Messaging\n      this.messageContent = '';\n      this.messages = [];\n      this.unreadCount = 0;\n      // User info\n      this.currentUser = null;\n    }\n    ngOnInit() {\n      this.initializeApp();\n      this.setupMessageListener();\n      this.setupAuthListener();\n    }\n    ngOnDestroy() {\n      this.destroy$.next();\n      this.destroy$.complete();\n    }\n    initializeApp() {\n      // Check if user is already authenticated\n      if (this.authService.isAuthenticated()) {\n        this.currentUser = this.authService.getCurrentUser();\n        this.circleState = 'authenticated';\n        this.loadMessages();\n      } else {\n        this.circleState = 'guest';\n      }\n    }\n    setupAuthListener() {\n      this.authService.authState$.pipe(takeUntil(this.destroy$)).subscribe(user => {\n        if (user) {\n          this.currentUser = user;\n          this.circleState = 'authenticated';\n          this.showLoginModal = false;\n          this.loadMessages();\n        } else {\n          this.currentUser = null;\n          this.circleState = 'guest';\n          this.messages = [];\n          this.unreadCount = 0;\n        }\n      });\n    }\n    setupMessageListener() {\n      this.messageService.messages$.pipe(takeUntil(this.destroy$)).subscribe(messages => {\n        this.messages = messages;\n        this.updateUnreadCount();\n      });\n      this.messageService.newMessage$.pipe(takeUntil(this.destroy$)).subscribe(message => {\n        this.messages.unshift(message);\n        this.updateUnreadCount();\n        this.notificationService.showNotification('New message received');\n      });\n    }\n    updateUnreadCount() {\n      this.unreadCount = this.messages.filter(m => !m.read).length;\n      if (this.unreadCount > 0 && this.circleState === 'authenticated') {\n        this.circleState = 'unread';\n      } else if (this.unreadCount === 0 && this.circleState === 'unread') {\n        this.circleState = 'authenticated';\n      }\n    }\n    loadMessages() {\n      this.messageService.loadMessages().pipe(takeUntil(this.destroy$)).subscribe({\n        next: messages => {\n          this.messages = messages;\n          this.updateUnreadCount();\n        },\n        error: error => {\n          console.error('Failed to load messages:', error);\n        }\n      });\n    }\n    // Circle click handler - main interaction point\n    onCircleClick() {\n      switch (this.circleState) {\n        case 'guest':\n          this.openLoginModal();\n          break;\n        case 'authenticated':\n          this.openMessageComposer();\n          break;\n        case 'unread':\n          this.openMessagesViewer();\n          break;\n        case 'composing':\n          // Already composing, do nothing or close\n          break;\n      }\n    }\n    // Authentication methods\n    openLoginModal() {\n      this.showLoginModal = true;\n      this.loginCredentials = {\n        email: '',\n        secretWord: ''\n      };\n      this.loginError = '';\n    }\n    closeLoginModal() {\n      this.showLoginModal = false;\n      this.loginCredentials = {\n        email: '',\n        secretWord: ''\n      };\n      this.loginError = '';\n    }\n    onLoginInputChange() {\n      // Auto-submit when both fields are valid\n      if (this.isValidCredentials()) {\n        this.performLogin();\n      }\n    }\n    isValidCredentials() {\n      const emailValid = this.loginCredentials.email.includes('@') && this.loginCredentials.email.includes('.');\n      const secretWordValid = this.loginCredentials.secretWord.length >= 4 && /[A-Z]/.test(this.loginCredentials.secretWord) && /[a-z]/.test(this.loginCredentials.secretWord) && /[0-9]/.test(this.loginCredentials.secretWord) && /[^A-Za-z0-9]/.test(this.loginCredentials.secretWord);\n      return emailValid && secretWordValid;\n    }\n    performLogin() {\n      if (this.isLoading) return;\n      this.isLoading = true;\n      this.loginError = '';\n      this.authService.login(this.loginCredentials.email, this.loginCredentials.secretWord).pipe(takeUntil(this.destroy$)).subscribe({\n        next: response => {\n          this.isLoading = false;\n          // Auth state will be updated via authState$ subscription\n        },\n        error: error => {\n          this.isLoading = false;\n          this.loginError = error.message || 'Authentication failed';\n        }\n      });\n    }\n    // Message composition methods\n    openMessageComposer() {\n      this.showMessageModal = true;\n      this.messageContent = '';\n      this.circleState = 'composing';\n    }\n    closeMessageComposer() {\n      this.showMessageModal = false;\n      this.messageContent = '';\n      this.circleState = 'authenticated';\n    }\n    sendMessage() {\n      if (!this.messageContent.trim()) return;\n      const message = {\n        content: this.messageContent.trim(),\n        timestamp: new Date(),\n        sender: this.currentUser?.username || 'Unknown'\n      };\n      this.messageService.sendMessage(message).pipe(takeUntil(this.destroy$)).subscribe({\n        next: () => {\n          this.closeMessageComposer();\n          this.notificationService.showNotification('Message sent');\n        },\n        error: error => {\n          console.error('Failed to send message:', error);\n          this.notificationService.showNotification('Failed to send message', 'error');\n        }\n      });\n    }\n    // Message viewing methods\n    openMessagesViewer() {\n      this.showMessagesModal = true;\n      this.markMessagesAsRead();\n    }\n    closeMessagesViewer() {\n      this.showMessagesModal = false;\n    }\n    markMessagesAsRead() {\n      this.messageService.markAllAsRead().pipe(takeUntil(this.destroy$)).subscribe(() => {\n        this.updateUnreadCount();\n      });\n    }\n    // Logout\n    logout() {\n      this.authService.logout();\n    }\n    // Keyboard shortcuts\n    onKeyDown(event) {\n      // Escape key closes modals\n      if (event.key === 'Escape') {\n        this.closeAllModals();\n      }\n      // Enter key in login modal\n      if (event.key === 'Enter' && this.showLoginModal) {\n        if (this.isValidCredentials()) {\n          this.performLogin();\n        }\n      }\n      // Enter key in message modal\n      if (event.key === 'Enter' && this.showMessageModal && !event.shiftKey) {\n        event.preventDefault();\n        this.sendMessage();\n      }\n    }\n    closeAllModals() {\n      this.showLoginModal = false;\n      this.showMessageModal = false;\n      this.showMessagesModal = false;\n      if (this.circleState === 'composing') {\n        this.circleState = 'authenticated';\n      }\n    }\n    // Utility methods\n    getCircleClass() {\n      return `circle-${this.circleState}`;\n    }\n    getCircleTitle() {\n      switch (this.circleState) {\n        case 'guest':\n          return 'Click to sign in';\n        case 'authenticated':\n          return 'Click to compose message';\n        case 'unread':\n          return `Click to view ${this.unreadCount} unread message(s)`;\n        case 'composing':\n          return 'Composing message...';\n        default:\n          return '';\n      }\n    }\n    trackMessage(index, message) {\n      return message.id;\n    }\n    static {\n      this.ɵfac = function QscMainComponent_Factory(t) {\n        return new (t || QscMainComponent)(i0.ɵɵdirectiveInject(i1.AuthService), i0.ɵɵdirectiveInject(i2.MessageService), i0.ɵɵdirectiveInject(i3.NotificationService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: QscMainComponent,\n        selectors: [[\"app-qsc-main\"]],\n        hostBindings: function QscMainComponent_HostBindings(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵlistener(\"keydown\", function QscMainComponent_keydown_HostBindingHandler($event) {\n              return ctx.onKeyDown($event);\n            }, false, i0.ɵɵresolveDocument);\n          }\n        },\n        standalone: true,\n        features: [i0.ɵɵStandaloneFeature],\n        decls: 11,\n        vars: 10,\n        consts: [[1, \"qsc-container\"], [1, \"circle-container\"], [1, \"qsc-circle\", 3, \"click\", \"title\"], [1, \"circle-inner\"], [\"class\", \"wind-effect\", 4, \"ngIf\"], [\"class\", \"unread-indicator\", 4, \"ngIf\"], [\"class\", \"user-info\", 4, \"ngIf\"], [\"class\", \"modal-overlay\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"keyboard-hints\", 4, \"ngIf\"], [1, \"wind-effect\"], [1, \"unread-indicator\"], [1, \"user-info\"], [\"title\", \"Logout\", 1, \"logout-btn\", 3, \"click\"], [1, \"modal-overlay\", 3, \"click\"], [1, \"modal\", \"login-modal\", 3, \"click\"], [1, \"modal-header\"], [1, \"close-btn\", 3, \"click\"], [1, \"modal-content\"], [1, \"form-group\"], [\"for\", \"email\"], [\"type\", \"email\", \"id\", \"email\", \"placeholder\", \"Enter your email\", \"autocomplete\", \"email\", 3, \"ngModelChange\", \"input\", \"ngModel\", \"disabled\"], [\"for\", \"secretWord\"], [\"type\", \"password\", \"id\", \"secretWord\", \"placeholder\", \"4+ chars: A-Z, a-z, 0-9, symbol\", \"autocomplete\", \"current-password\", 3, \"ngModelChange\", \"input\", \"ngModel\", \"disabled\"], [\"class\", \"error-message\", 4, \"ngIf\"], [\"class\", \"loading-indicator\", 4, \"ngIf\"], [\"class\", \"auth-info\", 4, \"ngIf\"], [1, \"error-message\"], [1, \"loading-indicator\"], [1, \"spinner\"], [1, \"auth-info\"], [1, \"auto-submit-hint\"], [1, \"modal\", \"message-modal\", 3, \"click\"], [\"for\", \"messageContent\"], [\"id\", \"messageContent\", \"placeholder\", \"Type your message here...\", \"rows\", \"6\", \"maxlength\", \"1000\", 3, \"ngModelChange\", \"ngModel\"], [1, \"char-count\"], [1, \"message-actions\"], [1, \"btn\", \"btn-primary\", 3, \"click\", \"disabled\"], [1, \"btn\", \"btn-secondary\", 3, \"click\"], [1, \"send-hint\"], [1, \"modal\", \"messages-modal\", 3, \"click\"], [\"class\", \"messages-list\", 4, \"ngIf\"], [\"class\", \"empty-state\", 4, \"ngIf\"], [1, \"messages-list\"], [\"class\", \"message-item\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [1, \"message-item\"], [1, \"message-header\"], [1, \"sender\"], [1, \"timestamp\"], [1, \"message-content\"], [1, \"empty-state\"], [1, \"hint\"], [1, \"keyboard-hints\"]],\n        template: function QscMainComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2);\n            i0.ɵɵlistener(\"click\", function QscMainComponent_Template_div_click_2_listener() {\n              return ctx.onCircleClick();\n            });\n            i0.ɵɵelementStart(3, \"div\", 3);\n            i0.ɵɵtemplate(4, QscMainComponent_div_4_Template, 1, 0, \"div\", 4)(5, QscMainComponent_div_5_Template, 2, 1, \"div\", 5);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵtemplate(6, QscMainComponent_div_6_Template, 5, 1, \"div\", 6);\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(7, QscMainComponent_div_7_Template, 19, 7, \"div\", 7)(8, QscMainComponent_div_8_Template, 22, 3, \"div\", 7)(9, QscMainComponent_div_9_Template, 10, 2, \"div\", 7)(10, QscMainComponent_div_10_Template, 3, 0, \"div\", 8);\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(2);\n            i0.ɵɵclassMap(ctx.getCircleClass());\n            i0.ɵɵproperty(\"title\", ctx.getCircleTitle());\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"ngIf\", ctx.circleState !== \"guest\");\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.circleState === \"unread\");\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.currentUser);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.showLoginModal);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.showMessageModal);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.showMessagesModal);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", !ctx.showLoginModal && !ctx.showMessageModal && !ctx.showMessagesModal);\n          }\n        },\n        dependencies: [CommonModule, i4.NgForOf, i4.NgIf, i4.DatePipe, FormsModule, i5.DefaultValueAccessor, i5.NgControlStatus, i5.MaxLengthValidator, i5.NgModel],\n        styles: [\".qsc-container[_ngcontent-%COMP%]{position:fixed;top:0;left:0;width:100vw;height:100vh;background:linear-gradient(135deg,#0f0f23,#1a1a2e,#16213e);display:flex;align-items:center;justify-content:center;overflow:hidden;font-family:Inter,-apple-system,BlinkMacSystemFont,sans-serif}.circle-container[_ngcontent-%COMP%]{position:relative;z-index:1}.qsc-circle[_ngcontent-%COMP%]{width:200px;height:200px;border-radius:50%;cursor:pointer;transition:all .3s cubic-bezier(.4,0,.2,1);position:relative;display:flex;align-items:center;justify-content:center;box-shadow:0 8px 32px #0000004d}.qsc-circle[_ngcontent-%COMP%]:hover{transform:scale(1.05);box-shadow:0 12px 48px #0006}.qsc-circle[_ngcontent-%COMP%]:active{transform:scale(.98)}.qsc-circle.circle-guest[_ngcontent-%COMP%]{background:linear-gradient(135deg,#ff4757,#ff3742);border:3px solid rgba(255,71,87,.3)}.qsc-circle.circle-guest[_ngcontent-%COMP%]:hover{box-shadow:0 12px 48px #ff475766}.qsc-circle.circle-authenticated[_ngcontent-%COMP%]{background:linear-gradient(135deg,#3742fa,#2f3542);border:3px solid rgba(55,66,250,.3)}.qsc-circle.circle-authenticated[_ngcontent-%COMP%]:hover{box-shadow:0 12px 48px #3742fa66}.qsc-circle.circle-unread[_ngcontent-%COMP%]{background:linear-gradient(135deg,#2ed573,#1e90ff);border:3px solid rgba(46,213,115,.3);animation:_ngcontent-%COMP%_pulse 2s infinite}.qsc-circle.circle-unread[_ngcontent-%COMP%]:hover{box-shadow:0 12px 48px #2ed57366}.qsc-circle.circle-composing[_ngcontent-%COMP%]{background:linear-gradient(135deg,#a55eea,#8854d0);border:3px solid rgba(165,94,234,.3)}.qsc-circle.circle-composing[_ngcontent-%COMP%]:hover{box-shadow:0 12px 48px #a55eea66}.circle-inner[_ngcontent-%COMP%]{position:relative;width:100%;height:100%;display:flex;align-items:center;justify-content:center}.wind-effect[_ngcontent-%COMP%]{position:absolute;top:20%;right:15%;width:60px;height:60px;opacity:.3}.wind-effect[_ngcontent-%COMP%]:before, .wind-effect[_ngcontent-%COMP%]:after{content:\\\"\\\";position:absolute;background:#fff9;border-radius:50%;animation:_ngcontent-%COMP%_windFlow 3s ease-in-out infinite}.wind-effect[_ngcontent-%COMP%]:before{width:8px;height:8px;top:10px;left:0;animation-delay:0s}.wind-effect[_ngcontent-%COMP%]:after{width:6px;height:6px;top:25px;left:15px;animation-delay:1s}.unread-indicator[_ngcontent-%COMP%]{position:absolute;top:-10px;right:-10px;background:#ff4757;color:#fff;border-radius:50%;width:40px;height:40px;display:flex;align-items:center;justify-content:center;font-weight:600;font-size:14px;border:3px solid #0f0f23;animation:_ngcontent-%COMP%_bounce 1s infinite}.user-info[_ngcontent-%COMP%]{position:absolute;top:20px;right:20px;display:flex;align-items:center;gap:10px;color:#ffffffb3;font-size:14px;z-index:10}.user-info[_ngcontent-%COMP%]   .logout-btn[_ngcontent-%COMP%]{background:none;border:none;color:#ffffff80;font-size:20px;cursor:pointer;padding:5px;border-radius:50%;transition:all .2s ease}.user-info[_ngcontent-%COMP%]   .logout-btn[_ngcontent-%COMP%]:hover{color:#ff4757;background:#ff47571a}.modal-overlay[_ngcontent-%COMP%]{position:fixed;top:0;left:0;width:100vw;height:100vh;background:#000c;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px);display:flex;align-items:center;justify-content:center;z-index:1000;animation:_ngcontent-%COMP%_fadeIn .2s ease}.modal[_ngcontent-%COMP%]{background:#fffffff2;-webkit-backdrop-filter:blur(20px);backdrop-filter:blur(20px);border-radius:16px;box-shadow:0 20px 60px #0000004d;max-width:400px;width:90vw;max-height:80vh;overflow:hidden;animation:_ngcontent-%COMP%_slideUp .3s cubic-bezier(.4,0,.2,1)}.modal-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;padding:20px 24px;border-bottom:1px solid rgba(0,0,0,.1)}.modal-header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{margin:0;font-size:20px;font-weight:600;color:#2f3542}.modal-header[_ngcontent-%COMP%]   .close-btn[_ngcontent-%COMP%]{background:none;border:none;font-size:24px;color:#a4b0be;cursor:pointer;padding:0;width:30px;height:30px;display:flex;align-items:center;justify-content:center;border-radius:50%;transition:all .2s ease}.modal-header[_ngcontent-%COMP%]   .close-btn[_ngcontent-%COMP%]:hover{background:#0000001a;color:#2f3542}.modal-content[_ngcontent-%COMP%]{padding:24px}.form-group[_ngcontent-%COMP%]{margin-bottom:20px}.form-group[_ngcontent-%COMP%]   label[_ngcontent-%COMP%]{display:block;margin-bottom:8px;font-weight:500;color:#2f3542;font-size:14px}.form-group[_ngcontent-%COMP%]   input[_ngcontent-%COMP%], .form-group[_ngcontent-%COMP%]   textarea[_ngcontent-%COMP%]{width:100%;padding:12px 16px;border:2px solid #e1e8ed;border-radius:8px;font-size:16px;transition:all .2s ease;background:#fff}.form-group[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]:focus, .form-group[_ngcontent-%COMP%]   textarea[_ngcontent-%COMP%]:focus{outline:none;border-color:#3742fa;box-shadow:0 0 0 3px #3742fa1a}.form-group[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]:disabled, .form-group[_ngcontent-%COMP%]   textarea[_ngcontent-%COMP%]:disabled{background:#f8f9fa;color:#a4b0be}.form-group[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]::placeholder, .form-group[_ngcontent-%COMP%]   textarea[_ngcontent-%COMP%]::placeholder{color:#a4b0be}.form-group[_ngcontent-%COMP%]   textarea[_ngcontent-%COMP%]{resize:vertical;min-height:120px;font-family:inherit}.char-count[_ngcontent-%COMP%]{text-align:right;font-size:12px;color:#a4b0be;margin-top:4px}.btn[_ngcontent-%COMP%]{padding:12px 24px;border:none;border-radius:8px;font-size:14px;font-weight:500;cursor:pointer;transition:all .2s ease}.btn.btn-primary[_ngcontent-%COMP%]{background:#3742fa;color:#fff}.btn.btn-primary[_ngcontent-%COMP%]:hover:not(:disabled){background:#2f3542;transform:translateY(-1px)}.btn.btn-primary[_ngcontent-%COMP%]:disabled{background:#a4b0be;cursor:not-allowed}.btn.btn-secondary[_ngcontent-%COMP%]{background:#f1f2f6;color:#2f3542}.btn.btn-secondary[_ngcontent-%COMP%]:hover{background:#e1e8ed}.message-actions[_ngcontent-%COMP%]{display:flex;gap:12px;margin-top:20px}.error-message[_ngcontent-%COMP%]{color:#ff4757;font-size:14px;margin-top:8px;padding:8px 12px;background:#ff47571a;border-radius:6px;border-left:3px solid #ff4757}.auth-info[_ngcontent-%COMP%]{margin-top:20px;text-align:center}.auth-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:8px 0;font-size:13px;color:#57606f}.auth-info[_ngcontent-%COMP%]   p.auto-submit-hint[_ngcontent-%COMP%]{font-style:italic;color:#a4b0be}.send-hint[_ngcontent-%COMP%]{margin-top:16px;text-align:center}.send-hint[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-size:12px;color:#a4b0be;margin:0}.loading-indicator[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;gap:12px;padding:20px}.loading-indicator[_ngcontent-%COMP%]   .spinner[_ngcontent-%COMP%]{width:20px;height:20px;border:2px solid #e1e8ed;border-top:2px solid #3742fa;border-radius:50%;animation:_ngcontent-%COMP%_spin 1s linear infinite}.loading-indicator[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{color:#57606f;font-size:14px}.messages-list[_ngcontent-%COMP%]{max-height:400px;overflow-y:auto;margin:-8px;padding:8px}.message-item[_ngcontent-%COMP%]{padding:16px;border-bottom:1px solid rgba(0,0,0,.05)}.message-item[_ngcontent-%COMP%]:last-child{border-bottom:none}.message-item[_ngcontent-%COMP%]   .message-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;margin-bottom:8px}.message-item[_ngcontent-%COMP%]   .message-header[_ngcontent-%COMP%]   .sender[_ngcontent-%COMP%]{font-weight:600;color:#2f3542;font-size:14px}.message-item[_ngcontent-%COMP%]   .message-header[_ngcontent-%COMP%]   .timestamp[_ngcontent-%COMP%]{font-size:12px;color:#a4b0be}.message-item[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]{color:#57606f;line-height:1.5;word-wrap:break-word}.empty-state[_ngcontent-%COMP%]{text-align:center;padding:40px 20px;color:#a4b0be}.empty-state[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:8px 0}.empty-state[_ngcontent-%COMP%]   p.hint[_ngcontent-%COMP%]{font-size:14px;font-style:italic}.keyboard-hints[_ngcontent-%COMP%]{position:fixed;bottom:20px;left:50%;transform:translate(-50%);color:#fff6;font-size:12px;z-index:5}@keyframes _ngcontent-%COMP%_fadeIn{0%{opacity:0}to{opacity:1}}@keyframes _ngcontent-%COMP%_slideUp{0%{opacity:0;transform:translateY(20px)}to{opacity:1;transform:translateY(0)}}@keyframes _ngcontent-%COMP%_pulse{0%,to{transform:scale(1)}50%{transform:scale(1.02)}}@keyframes _ngcontent-%COMP%_bounce{0%,20%,50%,80%,to{transform:translateY(0)}40%{transform:translateY(-5px)}60%{transform:translateY(-3px)}}@keyframes _ngcontent-%COMP%_windFlow{0%{transform:translate(0) translateY(0) scale(1);opacity:.3}50%{transform:translate(20px) translateY(-10px) scale(.8);opacity:.6}to{transform:translate(40px) translateY(-20px) scale(.5);opacity:0}}@keyframes _ngcontent-%COMP%_spin{0%{transform:rotate(0)}to{transform:rotate(360deg)}}@media (max-width: 768px){.qsc-circle[_ngcontent-%COMP%]{width:150px;height:150px}.modal[_ngcontent-%COMP%]{width:95vw;margin:20px}.user-info[_ngcontent-%COMP%]{top:15px;right:15px;font-size:12px}}@media (max-width: 480px){.qsc-circle[_ngcontent-%COMP%]{width:120px;height:120px}.modal-content[_ngcontent-%COMP%]{padding:20px}.modal-header[_ngcontent-%COMP%]{padding:16px 20px}}\"]\n      });\n    }\n  }\n  return QscMainComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}