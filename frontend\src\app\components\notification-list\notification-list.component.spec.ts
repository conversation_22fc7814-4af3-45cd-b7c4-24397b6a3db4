import { ComponentFixture, TestBed } from '@angular/core/testing';
import { NotificationListComponent } from './notification-list.component';
import { NotificationService } from '../../services/notification.service';
import { ErrorHandlingService } from '../../services/error-handling.service';

describe('NotificationListComponent', () => {
  let component: NotificationListComponent;
  let fixture: ComponentFixture<NotificationListComponent>;
  let notificationService: jasmine.SpyObj<NotificationService>;
  let errorHandlingService: jasmine.SpyObj<ErrorHandlingService>;

  const mockNotifications = [
    {
      id: '1',
      type: 'DELETION' as const,
      message: 'Message deleted',
      timestamp: new Date(),
      read: false
    },
    {
      id: '2',
      type: 'COMPROMISE' as const,
      message: 'Message compromised',
      timestamp: new Date(),
      read: true
    }
  ];

  beforeEach(async () => {
    const notificationSpy = jasmine.createSpyObj('NotificationService', [
      'getNotifications',
      'markAsRead'
    ]);
    const errorHandlingSpy = jasmine.createSpyObj('ErrorHandlingService', [
      'handleError'
    ]);

    await TestBed.configureTestingModule({
      imports: [NotificationListComponent],
      providers: [
        { provide: NotificationService, useValue: notificationSpy },
        { provide: ErrorHandlingService, useValue: errorHandlingSpy }
      ]
    }).compileComponents();

    notificationService = TestBed.inject(NotificationService) as jasmine.SpyObj<NotificationService>;
    errorHandlingService = TestBed.inject(ErrorHandlingService) as jasmine.SpyObj<ErrorHandlingService>;
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(NotificationListComponent);
    component = fixture.componentInstance;
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should load notifications on init', async () => {
    notificationService.getNotifications.and.returnValue(Promise.resolve(mockNotifications));

    await component.ngOnInit();
    expect(component.notifications).toEqual(mockNotifications);
    expect(component.errorMessage).toBeNull();
  });

  it('should handle notification loading error', async () => {
    const error = new Error('Failed to load notifications');
    notificationService.getNotifications.and.returnValue(Promise.reject(error));

    await component.ngOnInit();
    expect(errorHandlingService.handleError).toHaveBeenCalledWith(error, 'STORAGE');
    expect(component.errorMessage).toBe('Failed to load notifications');
  });

  it('should mark notification as read', async () => {
    notificationService.getNotifications.and.returnValue(Promise.resolve(mockNotifications));
    notificationService.markAsRead.and.returnValue(Promise.resolve());

    await component.ngOnInit();
    await component.markAsRead(mockNotifications[0]);

    expect(notificationService.markAsRead).toHaveBeenCalledWith('1');
    expect(mockNotifications[0].read).toBeTrue();
    expect(component.errorMessage).toBeNull();
  });

  it('should not mark already read notification', async () => {
    notificationService.getNotifications.and.returnValue(Promise.resolve(mockNotifications));
    notificationService.markAsRead.and.returnValue(Promise.resolve());

    await component.ngOnInit();
    await component.markAsRead(mockNotifications[1]);

    expect(notificationService.markAsRead).not.toHaveBeenCalled();
  });

  it('should handle mark as read error', async () => {
    notificationService.getNotifications.and.returnValue(Promise.resolve(mockNotifications));
    const error = new Error('Failed to mark as read');
    notificationService.markAsRead.and.returnValue(Promise.reject(error));

    await component.ngOnInit();
    await component.markAsRead(mockNotifications[0]);

    expect(errorHandlingService.handleError).toHaveBeenCalledWith(error, 'STORAGE');
    expect(component.errorMessage).toBe('Failed to mark notification as read');
    expect(mockNotifications[0].read).toBeFalse();
  });
});
