<div class="main-container">
  <!-- Main Logo Area -->
  <div class="logo-area">
    <div 
      class="qsc-logo clickable"
      (click)="onLogoClick()"
      (contextmenu)="onLogoRightClick($event)"
      title="Click to compose new message"
    >
      <div class="circle">
        <div class="wind-effect"></div>
      </div>
    </div>
    
    <div class="logo-text">
      <h1>QSC</h1>
      <p>Click logo to compose message</p>
    </div>
  </div>

  <!-- Context Menu -->
  <div 
    class="context-menu"
    *ngIf="showContextMenu"
    [style.left.px]="contextMenuPosition.x"
    [style.top.px]="contextMenuPosition.y"
  >
    <div class="menu-item" (click)="toggleGroupPanel()">
      <span>Groups</span>
      <span class="shortcut">Ctrl+G</span>
    </div>
    <div class="menu-divider"></div>
    <div class="menu-item" (click)="onLogout()">
      <span>Logout</span>
    </div>
  </div>

  <!-- Group Panel -->
  <div class="group-panel" [class.visible]="showGroupPanel">
    <div class="panel-header">
      <h3>Groups</h3>
      <button class="close-btn" (click)="showGroupPanel = false">×</button>
    </div>
    
    <div class="panel-content">
      <div class="group-actions">
        <button class="action-btn primary" (click)="onCreateGroup()">
          Create Group
        </button>
        <button class="action-btn secondary" (click)="onJoinGroup()">
          Join Group
        </button>
      </div>

      <div class="groups-list" *ngIf="groups.length > 0">
        <h4>Your Groups</h4>
        <div 
          class="group-item"
          *ngFor="let group of groups"
          (click)="onGroupSelect(group)"
        >
          <div class="group-info">
            <span class="group-name">{{ group.name }}</span>
            <span class="group-meta">{{ group.memberCount }} members</span>
          </div>
          <div class="group-role">
            <span class="role-badge" [class]="group.permission">
              {{ group.permission }}
            </span>
          </div>
        </div>
      </div>

      <div class="empty-state" *ngIf="groups.length === 0">
        <p>No groups yet</p>
        <p class="hint">Create or join a group to start secure communications</p>
      </div>
    </div>
  </div>

  <!-- Overlay for group panel -->
  <div 
    class="overlay"
    *ngIf="showGroupPanel"
    (click)="showGroupPanel = false"
  ></div>

  <!-- User Info (subtle) -->
  <div class="user-info" *ngIf="user">
    <span>{{ user.username }}</span>
  </div>

  <!-- Keyboard Hints -->
  <div class="keyboard-hints">
    <span>Ctrl+G for Groups</span>
  </div>
</div>
