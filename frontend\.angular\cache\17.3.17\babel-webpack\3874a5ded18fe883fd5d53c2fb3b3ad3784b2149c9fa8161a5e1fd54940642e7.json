{"ast": null, "code": "import { BehaviorSubject, tap, switchMap, combineLatest } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"./api.service\";\nimport * as i2 from \"./secure-token.service\";\nexport class AuthService {\n  constructor(apiService, secureTokenService) {\n    this.apiService = apiService;\n    this.secureTokenService = secureTokenService;\n    this.userSubject = new BehaviorSubject(null);\n    // Combine token and user observables to create auth state\n    this.authState$ = combineLatest([this.secureTokenService.token$, this.userSubject.asObservable()]).pipe(tap(([token, user]) => {\n      // Auto-refresh token if it's expiring soon\n      if (token && this.secureTokenService.isTokenExpiringSoon()) {\n        this.refreshTokenIfNeeded();\n      }\n    }), switchMap(([token, user]) => {\n      const isAuthenticated = !!token && this.secureTokenService.isTokenValid();\n      return [{\n        isAuthenticated,\n        user\n      }];\n    }));\n    this.initializeAuthState();\n  }\n  initializeAuthState() {\n    // Check for existing tokens and migrate from localStorage if needed\n    const legacyToken = localStorage.getItem('qsc_token');\n    const legacyUserStr = localStorage.getItem('qsc_user');\n    if (legacyToken && legacyUserStr) {\n      try {\n        const user = JSON.parse(legacyUserStr);\n        // Migrate to secure storage\n        this.secureTokenService.setTokens({\n          token: legacyToken,\n          expiresAt: this.getTokenExpiration(legacyToken) || Date.now() + 3600000 // 1 hour default\n        });\n        this.userSubject.next(user);\n        // Clean up legacy storage\n        localStorage.removeItem('qsc_token');\n        localStorage.removeItem('qsc_user');\n      } catch (error) {\n        console.error('Error migrating legacy auth data:', error);\n        this.logout();\n      }\n    } else {\n      // Check if we have a valid token in secure storage\n      const currentToken = this.secureTokenService.getToken();\n      if (currentToken && this.secureTokenService.isTokenValid()) {\n        // Fetch current user data\n        this.getCurrentUser().subscribe({\n          next: user => this.userSubject.next(user),\n          error: () => this.logout()\n        });\n      }\n    }\n  }\n  login(credentials) {\n    return this.apiService.post('/auth/login', credentials).pipe(tap(response => {\n      // Store tokens securely\n      this.secureTokenService.setTokens({\n        token: response.access_token,\n        refreshToken: response.refresh_token,\n        expiresAt: this.getTokenExpiration(response.access_token) || Date.now() + 3600000\n      });\n      // Update user state\n      this.userSubject.next(response.user);\n    }));\n  }\n  logout() {\n    // Clear all tokens\n    this.secureTokenService.clearTokens();\n    this.userSubject.next(null);\n    // Optionally call logout endpoint\n    this.apiService.post('/auth/logout', {}).subscribe({\n      error: error => console.warn('Logout endpoint failed:', error)\n    });\n  }\n  getCurrentUser() {\n    return this.apiService.get('/users/profile');\n  }\n  isAuthenticated() {\n    const token = this.secureTokenService.getToken();\n    return !!token && this.secureTokenService.isTokenValid();\n  }\n  getToken() {\n    return this.secureTokenService.getToken();\n  }\n  getUser() {\n    return this.userSubject.value;\n  }\n  /**\n   * Refresh token if needed\n   */\n  refreshTokenIfNeeded() {\n    const refreshToken = this.secureTokenService.getRefreshToken();\n    if (refreshToken) {\n      this.apiService.post('/auth/refresh', {\n        refresh_token: refreshToken\n      }).subscribe({\n        next: response => {\n          this.secureTokenService.setTokens({\n            token: response.access_token,\n            refreshToken: response.refresh_token,\n            expiresAt: this.getTokenExpiration(response.access_token) || Date.now() + 3600000\n          });\n        },\n        error: () => {\n          // Refresh failed, logout user\n          this.logout();\n        }\n      });\n    }\n  }\n  /**\n   * Extract expiration time from JWT token\n   */\n  getTokenExpiration(token) {\n    try {\n      const payload = JSON.parse(atob(token.split('.')[1]));\n      return payload.exp;\n    } catch {\n      return null;\n    }\n  }\n  static {\n    this.ɵfac = function AuthService_Factory(t) {\n      return new (t || AuthService)(i0.ɵɵinject(i1.ApiService), i0.ɵɵinject(i2.SecureTokenService));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: AuthService,\n      factory: AuthService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["BehaviorSubject", "tap", "switchMap", "combineLatest", "AuthService", "constructor", "apiService", "secureTokenService", "userSubject", "authState$", "token$", "asObservable", "pipe", "token", "user", "isTokenExpiringSoon", "refreshTokenIfNeeded", "isAuthenticated", "isTokenValid", "initializeAuthState", "legacyToken", "localStorage", "getItem", "legacyUserStr", "JSON", "parse", "setTokens", "expiresAt", "getTokenExpiration", "Date", "now", "next", "removeItem", "error", "console", "logout", "currentToken", "getToken", "getCurrentUser", "subscribe", "login", "credentials", "post", "response", "access_token", "refreshToken", "refresh_token", "clearTokens", "warn", "get", "getUser", "value", "getRefreshToken", "payload", "atob", "split", "exp", "i0", "ɵɵinject", "i1", "ApiService", "i2", "SecureTokenService", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Projects\\QSC1\\frontend\\src\\app\\services\\auth.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { BehaviorSubject, Observable, tap, switchMap, combineLatest, catchError, throwError } from 'rxjs';\nimport { ApiService } from './api.service';\nimport { SecureTokenService } from './secure-token.service';\nimport { LoginRequest, LoginResponse, RegisterRequest, User, AuthState, TokenData } from '../types/auth.types';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class AuthService {\n  private userSubject = new BehaviorSubject<User | null>(null);\n  public authState$: Observable<AuthState>;\n\n  constructor(\n    private apiService: ApiService,\n    private secureTokenService: SecureTokenService\n  ) {\n    // Combine token and user observables to create auth state\n    this.authState$ = combineLatest([\n      this.secureTokenService.token$,\n      this.userSubject.asObservable()\n    ]).pipe(\n      tap(([token, user]) => {\n        // Auto-refresh token if it's expiring soon\n        if (token && this.secureTokenService.isTokenExpiringSoon()) {\n          this.refreshTokenIfNeeded();\n        }\n      }),\n      switchMap(([token, user]) => {\n        const isAuthenticated = !!token && this.secureTokenService.isTokenValid();\n        return [{ isAuthenticated, user }];\n      })\n    );\n\n    this.initializeAuthState();\n  }\n\n  private initializeAuthState(): void {\n    // Check for existing tokens and migrate from localStorage if needed\n    const legacyToken = localStorage.getItem('qsc_token');\n    const legacyUserStr = localStorage.getItem('qsc_user');\n\n    if (legacyToken && legacyUserStr) {\n      try {\n        const user = JSON.parse(legacyUserStr);\n        // Migrate to secure storage\n        this.secureTokenService.setTokens({\n          token: legacyToken,\n          expiresAt: this.getTokenExpiration(legacyToken) || Date.now() + 3600000 // 1 hour default\n        });\n        this.userSubject.next(user);\n\n        // Clean up legacy storage\n        localStorage.removeItem('qsc_token');\n        localStorage.removeItem('qsc_user');\n      } catch (error) {\n        console.error('Error migrating legacy auth data:', error);\n        this.logout();\n      }\n    } else {\n      // Check if we have a valid token in secure storage\n      const currentToken = this.secureTokenService.getToken();\n      if (currentToken && this.secureTokenService.isTokenValid()) {\n        // Fetch current user data\n        this.getCurrentUser().subscribe({\n          next: (user) => this.userSubject.next(user),\n          error: () => this.logout()\n        });\n      }\n    }\n  }\n\n  login(credentials: LoginRequest): Observable<LoginResponse> {\n    return this.apiService.post<LoginResponse>('/auth/login', credentials).pipe(\n      tap(response => {\n        // Store tokens securely\n        this.secureTokenService.setTokens({\n          token: response.access_token,\n          refreshToken: response.refresh_token,\n          expiresAt: this.getTokenExpiration(response.access_token) || Date.now() + 3600000\n        });\n\n        // Update user state\n        this.userSubject.next(response.user);\n      })\n    );\n  }\n\n  logout(): void {\n    // Clear all tokens\n    this.secureTokenService.clearTokens();\n    this.userSubject.next(null);\n\n    // Optionally call logout endpoint\n    this.apiService.post('/auth/logout', {}).subscribe({\n      error: (error) => console.warn('Logout endpoint failed:', error)\n    });\n  }\n\n  getCurrentUser(): Observable<User> {\n    return this.apiService.get<User>('/users/profile');\n  }\n\n  isAuthenticated(): boolean {\n    const token = this.secureTokenService.getToken();\n    return !!token && this.secureTokenService.isTokenValid();\n  }\n\n  getToken(): string | null {\n    return this.secureTokenService.getToken();\n  }\n\n  getUser(): User | null {\n    return this.userSubject.value;\n  }\n\n  /**\n   * Refresh token if needed\n   */\n  private refreshTokenIfNeeded(): void {\n    const refreshToken = this.secureTokenService.getRefreshToken();\n    if (refreshToken) {\n      this.apiService.post<LoginResponse>('/auth/refresh', { refresh_token: refreshToken })\n        .subscribe({\n          next: (response) => {\n            this.secureTokenService.setTokens({\n              token: response.access_token,\n              refreshToken: response.refresh_token,\n              expiresAt: this.getTokenExpiration(response.access_token) || Date.now() + 3600000\n            });\n          },\n          error: () => {\n            // Refresh failed, logout user\n            this.logout();\n          }\n        });\n    }\n  }\n\n  /**\n   * Extract expiration time from JWT token\n   */\n  private getTokenExpiration(token: string): number | null {\n    try {\n      const payload = JSON.parse(atob(token.split('.')[1]));\n      return payload.exp;\n    } catch {\n      return null;\n    }\n  }\n}\n"], "mappings": "AACA,SAASA,eAAe,EAAcC,GAAG,EAAEC,SAAS,EAAEC,aAAa,QAAgC,MAAM;;;;AAQzG,OAAM,MAAOC,WAAW;EAItBC,YACUC,UAAsB,EACtBC,kBAAsC;IADtC,KAAAD,UAAU,GAAVA,UAAU;IACV,KAAAC,kBAAkB,GAAlBA,kBAAkB;IALpB,KAAAC,WAAW,GAAG,IAAIR,eAAe,CAAc,IAAI,CAAC;IAO1D;IACA,IAAI,CAACS,UAAU,GAAGN,aAAa,CAAC,CAC9B,IAAI,CAACI,kBAAkB,CAACG,MAAM,EAC9B,IAAI,CAACF,WAAW,CAACG,YAAY,EAAE,CAChC,CAAC,CAACC,IAAI,CACLX,GAAG,CAAC,CAAC,CAACY,KAAK,EAAEC,IAAI,CAAC,KAAI;MACpB;MACA,IAAID,KAAK,IAAI,IAAI,CAACN,kBAAkB,CAACQ,mBAAmB,EAAE,EAAE;QAC1D,IAAI,CAACC,oBAAoB,EAAE;;IAE/B,CAAC,CAAC,EACFd,SAAS,CAAC,CAAC,CAACW,KAAK,EAAEC,IAAI,CAAC,KAAI;MAC1B,MAAMG,eAAe,GAAG,CAAC,CAACJ,KAAK,IAAI,IAAI,CAACN,kBAAkB,CAACW,YAAY,EAAE;MACzE,OAAO,CAAC;QAAED,eAAe;QAAEH;MAAI,CAAE,CAAC;IACpC,CAAC,CAAC,CACH;IAED,IAAI,CAACK,mBAAmB,EAAE;EAC5B;EAEQA,mBAAmBA,CAAA;IACzB;IACA,MAAMC,WAAW,GAAGC,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC;IACrD,MAAMC,aAAa,GAAGF,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC;IAEtD,IAAIF,WAAW,IAAIG,aAAa,EAAE;MAChC,IAAI;QACF,MAAMT,IAAI,GAAGU,IAAI,CAACC,KAAK,CAACF,aAAa,CAAC;QACtC;QACA,IAAI,CAAChB,kBAAkB,CAACmB,SAAS,CAAC;UAChCb,KAAK,EAAEO,WAAW;UAClBO,SAAS,EAAE,IAAI,CAACC,kBAAkB,CAACR,WAAW,CAAC,IAAIS,IAAI,CAACC,GAAG,EAAE,GAAG,OAAO,CAAC;SACzE,CAAC;QACF,IAAI,CAACtB,WAAW,CAACuB,IAAI,CAACjB,IAAI,CAAC;QAE3B;QACAO,YAAY,CAACW,UAAU,CAAC,WAAW,CAAC;QACpCX,YAAY,CAACW,UAAU,CAAC,UAAU,CAAC;OACpC,CAAC,OAAOC,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;QACzD,IAAI,CAACE,MAAM,EAAE;;KAEhB,MAAM;MACL;MACA,MAAMC,YAAY,GAAG,IAAI,CAAC7B,kBAAkB,CAAC8B,QAAQ,EAAE;MACvD,IAAID,YAAY,IAAI,IAAI,CAAC7B,kBAAkB,CAACW,YAAY,EAAE,EAAE;QAC1D;QACA,IAAI,CAACoB,cAAc,EAAE,CAACC,SAAS,CAAC;UAC9BR,IAAI,EAAGjB,IAAI,IAAK,IAAI,CAACN,WAAW,CAACuB,IAAI,CAACjB,IAAI,CAAC;UAC3CmB,KAAK,EAAEA,CAAA,KAAM,IAAI,CAACE,MAAM;SACzB,CAAC;;;EAGR;EAEAK,KAAKA,CAACC,WAAyB;IAC7B,OAAO,IAAI,CAACnC,UAAU,CAACoC,IAAI,CAAgB,aAAa,EAAED,WAAW,CAAC,CAAC7B,IAAI,CACzEX,GAAG,CAAC0C,QAAQ,IAAG;MACb;MACA,IAAI,CAACpC,kBAAkB,CAACmB,SAAS,CAAC;QAChCb,KAAK,EAAE8B,QAAQ,CAACC,YAAY;QAC5BC,YAAY,EAAEF,QAAQ,CAACG,aAAa;QACpCnB,SAAS,EAAE,IAAI,CAACC,kBAAkB,CAACe,QAAQ,CAACC,YAAY,CAAC,IAAIf,IAAI,CAACC,GAAG,EAAE,GAAG;OAC3E,CAAC;MAEF;MACA,IAAI,CAACtB,WAAW,CAACuB,IAAI,CAACY,QAAQ,CAAC7B,IAAI,CAAC;IACtC,CAAC,CAAC,CACH;EACH;EAEAqB,MAAMA,CAAA;IACJ;IACA,IAAI,CAAC5B,kBAAkB,CAACwC,WAAW,EAAE;IACrC,IAAI,CAACvC,WAAW,CAACuB,IAAI,CAAC,IAAI,CAAC;IAE3B;IACA,IAAI,CAACzB,UAAU,CAACoC,IAAI,CAAC,cAAc,EAAE,EAAE,CAAC,CAACH,SAAS,CAAC;MACjDN,KAAK,EAAGA,KAAK,IAAKC,OAAO,CAACc,IAAI,CAAC,yBAAyB,EAAEf,KAAK;KAChE,CAAC;EACJ;EAEAK,cAAcA,CAAA;IACZ,OAAO,IAAI,CAAChC,UAAU,CAAC2C,GAAG,CAAO,gBAAgB,CAAC;EACpD;EAEAhC,eAAeA,CAAA;IACb,MAAMJ,KAAK,GAAG,IAAI,CAACN,kBAAkB,CAAC8B,QAAQ,EAAE;IAChD,OAAO,CAAC,CAACxB,KAAK,IAAI,IAAI,CAACN,kBAAkB,CAACW,YAAY,EAAE;EAC1D;EAEAmB,QAAQA,CAAA;IACN,OAAO,IAAI,CAAC9B,kBAAkB,CAAC8B,QAAQ,EAAE;EAC3C;EAEAa,OAAOA,CAAA;IACL,OAAO,IAAI,CAAC1C,WAAW,CAAC2C,KAAK;EAC/B;EAEA;;;EAGQnC,oBAAoBA,CAAA;IAC1B,MAAM6B,YAAY,GAAG,IAAI,CAACtC,kBAAkB,CAAC6C,eAAe,EAAE;IAC9D,IAAIP,YAAY,EAAE;MAChB,IAAI,CAACvC,UAAU,CAACoC,IAAI,CAAgB,eAAe,EAAE;QAAEI,aAAa,EAAED;MAAY,CAAE,CAAC,CAClFN,SAAS,CAAC;QACTR,IAAI,EAAGY,QAAQ,IAAI;UACjB,IAAI,CAACpC,kBAAkB,CAACmB,SAAS,CAAC;YAChCb,KAAK,EAAE8B,QAAQ,CAACC,YAAY;YAC5BC,YAAY,EAAEF,QAAQ,CAACG,aAAa;YACpCnB,SAAS,EAAE,IAAI,CAACC,kBAAkB,CAACe,QAAQ,CAACC,YAAY,CAAC,IAAIf,IAAI,CAACC,GAAG,EAAE,GAAG;WAC3E,CAAC;QACJ,CAAC;QACDG,KAAK,EAAEA,CAAA,KAAK;UACV;UACA,IAAI,CAACE,MAAM,EAAE;QACf;OACD,CAAC;;EAER;EAEA;;;EAGQP,kBAAkBA,CAACf,KAAa;IACtC,IAAI;MACF,MAAMwC,OAAO,GAAG7B,IAAI,CAACC,KAAK,CAAC6B,IAAI,CAACzC,KAAK,CAAC0C,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACrD,OAAOF,OAAO,CAACG,GAAG;KACnB,CAAC,MAAM;MACN,OAAO,IAAI;;EAEf;;;uBA5IWpD,WAAW,EAAAqD,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,kBAAA;IAAA;EAAA;;;aAAX1D,WAAW;MAAA2D,OAAA,EAAX3D,WAAW,CAAA4D,IAAA;MAAAC,UAAA,EAFV;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}