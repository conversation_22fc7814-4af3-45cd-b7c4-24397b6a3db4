"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.VALIDATION_CONSTANTS = void 0;
exports.validateUsername = validateUsername;
exports.validateEmail = validateEmail;
exports.validateSecretWord = validateSecretWord;
exports.validateMessageContent = validateMessageContent;
exports.validateRoomName = validateRoomName;
exports.validateUUID = validateUUID;
exports.validateInviteCode = validateInviteCode;
exports.validateHexString = validateHexString;
exports.validateBase64 = validateBase64;
exports.validateBase64URL = validateBase64URL;
exports.validateTimestamp = validateTimestamp;
exports.validateExpirationTime = validateExpirationTime;
exports.sanitizeInput = sanitizeInput;
exports.validateAndSanitizeInput = validateAndSanitizeInput;
exports.validatePagination = validatePagination;
const crypto_1 = require("./crypto");
/**
 * Validation utilities for QSC application
 * Ensures data integrity and security compliance
 */
// Validation constants
exports.VALIDATION_CONSTANTS = {
    USERNAME_MIN_LENGTH: 3,
    USERNAME_MAX_LENGTH: 30,
    EMAIL_MAX_LENGTH: 254,
    MESSAGE_MAX_LENGTH: 4096,
    ROOM_NAME_MAX_LENGTH: 100,
    INVITE_CODE_LENGTH: 16,
    UUID_REGEX: /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i,
    USERNAME_REGEX: /^[a-zA-Z0-9_-]+$/,
    EMAIL_REGEX: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
    HEX_REGEX: /^[0-9a-f]+$/i,
    BASE64_REGEX: /^[A-Za-z0-9+/]*={0,2}$/,
    BASE64URL_REGEX: /^[A-Za-z0-9_-]*$/,
};
/**
 * Validate username format and constraints
 */
function validateUsername(username) {
    const errors = [];
    if (!username || typeof username !== 'string') {
        errors.push('Username is required');
        return { isValid: false, errors };
    }
    if (username.length < exports.VALIDATION_CONSTANTS.USERNAME_MIN_LENGTH) {
        errors.push(`Username must be at least ${exports.VALIDATION_CONSTANTS.USERNAME_MIN_LENGTH} characters long`);
    }
    if (username.length > exports.VALIDATION_CONSTANTS.USERNAME_MAX_LENGTH) {
        errors.push(`Username must not exceed ${exports.VALIDATION_CONSTANTS.USERNAME_MAX_LENGTH} characters`);
    }
    if (!exports.VALIDATION_CONSTANTS.USERNAME_REGEX.test(username)) {
        errors.push('Username can only contain letters, numbers, underscores, and hyphens');
    }
    // Check for reserved usernames
    const reservedUsernames = ['admin', 'root', 'system', 'api', 'support', 'help'];
    if (reservedUsernames.includes(username.toLowerCase())) {
        errors.push('Username is reserved and cannot be used');
    }
    return {
        isValid: errors.length === 0,
        errors,
    };
}
/**
 * Validate email format
 */
function validateEmail(email) {
    const errors = [];
    if (!email || typeof email !== 'string') {
        errors.push('Email is required');
        return { isValid: false, errors };
    }
    if (email.length > exports.VALIDATION_CONSTANTS.EMAIL_MAX_LENGTH) {
        errors.push(`Email must not exceed ${exports.VALIDATION_CONSTANTS.EMAIL_MAX_LENGTH} characters`);
    }
    if (!exports.VALIDATION_CONSTANTS.EMAIL_REGEX.test(email)) {
        errors.push('Invalid email format');
    }
    return {
        isValid: errors.length === 0,
        errors,
    };
}
/**
 * Validate secret word using crypto utility
 */
function validateSecretWord(secretWord) {
    if (!secretWord || typeof secretWord !== 'string') {
        return {
            isValid: false,
            errors: ['Secret word is required'],
        };
    }
    return (0, crypto_1.validateSecretWordFormat)(secretWord);
}
/**
 * Validate message content
 */
function validateMessageContent(content) {
    const errors = [];
    if (!content || typeof content !== 'string') {
        errors.push('Message content is required');
        return { isValid: false, errors };
    }
    if (content.trim().length === 0) {
        errors.push('Message content cannot be empty');
    }
    if (content.length > exports.VALIDATION_CONSTANTS.MESSAGE_MAX_LENGTH) {
        errors.push(`Message content must not exceed ${exports.VALIDATION_CONSTANTS.MESSAGE_MAX_LENGTH} characters`);
    }
    return {
        isValid: errors.length === 0,
        errors,
    };
}
/**
 * Validate room name
 */
function validateRoomName(name) {
    const errors = [];
    if (!name || typeof name !== 'string') {
        errors.push('Room name is required');
        return { isValid: false, errors };
    }
    if (name.trim().length === 0) {
        errors.push('Room name cannot be empty');
    }
    if (name.length > exports.VALIDATION_CONSTANTS.ROOM_NAME_MAX_LENGTH) {
        errors.push(`Room name must not exceed ${exports.VALIDATION_CONSTANTS.ROOM_NAME_MAX_LENGTH} characters`);
    }
    return {
        isValid: errors.length === 0,
        errors,
    };
}
/**
 * Validate UUID format
 */
function validateUUID(uuid) {
    const errors = [];
    if (!uuid || typeof uuid !== 'string') {
        errors.push('UUID is required');
        return { isValid: false, errors };
    }
    if (!exports.VALIDATION_CONSTANTS.UUID_REGEX.test(uuid)) {
        errors.push('Invalid UUID format');
    }
    return {
        isValid: errors.length === 0,
        errors,
    };
}
/**
 * Validate invite code format
 */
function validateInviteCode(code) {
    const errors = [];
    if (!code || typeof code !== 'string') {
        errors.push('Invite code is required');
        return { isValid: false, errors };
    }
    if (code.length !== exports.VALIDATION_CONSTANTS.INVITE_CODE_LENGTH) {
        errors.push(`Invite code must be exactly ${exports.VALIDATION_CONSTANTS.INVITE_CODE_LENGTH} characters long`);
    }
    if (!exports.VALIDATION_CONSTANTS.HEX_REGEX.test(code)) {
        errors.push('Invite code must contain only hexadecimal characters');
    }
    return {
        isValid: errors.length === 0,
        errors,
    };
}
/**
 * Validate hexadecimal string
 */
function validateHexString(hex, expectedLength) {
    const errors = [];
    if (!hex || typeof hex !== 'string') {
        errors.push('Hexadecimal string is required');
        return { isValid: false, errors };
    }
    if (!exports.VALIDATION_CONSTANTS.HEX_REGEX.test(hex)) {
        errors.push('Invalid hexadecimal format');
    }
    if (expectedLength && hex.length !== expectedLength) {
        errors.push(`Hexadecimal string must be exactly ${expectedLength} characters long`);
    }
    return {
        isValid: errors.length === 0,
        errors,
    };
}
/**
 * Validate Base64 string
 */
function validateBase64(base64) {
    const errors = [];
    if (!base64 || typeof base64 !== 'string') {
        errors.push('Base64 string is required');
        return { isValid: false, errors };
    }
    if (!exports.VALIDATION_CONSTANTS.BASE64_REGEX.test(base64)) {
        errors.push('Invalid Base64 format');
    }
    return {
        isValid: errors.length === 0,
        errors,
    };
}
/**
 * Validate Base64URL string
 */
function validateBase64URL(base64url) {
    const errors = [];
    if (!base64url || typeof base64url !== 'string') {
        errors.push('Base64URL string is required');
        return { isValid: false, errors };
    }
    if (!exports.VALIDATION_CONSTANTS.BASE64URL_REGEX.test(base64url)) {
        errors.push('Invalid Base64URL format');
    }
    return {
        isValid: errors.length === 0,
        errors,
    };
}
/**
 * Validate timestamp
 */
function validateTimestamp(timestamp) {
    const errors = [];
    if (timestamp === null || timestamp === undefined) {
        errors.push('Timestamp is required');
        return { isValid: false, errors };
    }
    const date = new Date(timestamp);
    if (isNaN(date.getTime())) {
        errors.push('Invalid timestamp format');
    }
    return {
        isValid: errors.length === 0,
        errors,
    };
}
/**
 * Validate expiration time (must be in the future)
 */
function validateExpirationTime(expiresAt) {
    const errors = [];
    const timestampValidation = validateTimestamp(expiresAt);
    if (!timestampValidation.isValid) {
        return timestampValidation;
    }
    const expirationDate = new Date(expiresAt);
    const now = new Date();
    if (expirationDate <= now) {
        errors.push('Expiration time must be in the future');
    }
    return {
        isValid: errors.length === 0,
        errors,
    };
}
/**
 * Sanitize input string to prevent XSS and injection attacks
 */
function sanitizeInput(input) {
    if (typeof input !== 'string') {
        return '';
    }
    return input
        .replace(/[<>]/g, '') // Remove potential HTML tags
        .replace(/['"]/g, '') // Remove quotes
        .replace(/[\\]/g, '') // Remove backslashes
        .trim();
}
/**
 * Validate and sanitize user input
 */
function validateAndSanitizeInput(input, validator) {
    const sanitized = sanitizeInput(input);
    const validation = validator(sanitized);
    return {
        isValid: validation.isValid,
        sanitized,
        errors: validation.errors,
    };
}
/**
 * Validate pagination parameters
 */
function validatePagination(page, limit) {
    const errors = [];
    if (page !== undefined) {
        if (!Number.isInteger(page) || page < 1) {
            errors.push('Page must be a positive integer');
        }
    }
    if (limit !== undefined) {
        if (!Number.isInteger(limit) || limit < 1 || limit > 100) {
            errors.push('Limit must be a positive integer between 1 and 100');
        }
    }
    return {
        isValid: errors.length === 0,
        errors,
    };
}
//# sourceMappingURL=validation.js.map