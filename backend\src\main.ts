import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { ValidationPipe } from '@nestjs/common';
import helmet from 'helmet';
import rateLimit from 'express-rate-limit';
import * as winston from 'winston';
import { GlobalExceptionFilter } from './common/filters/global-exception.filter';
import { CryptoExceptionFilter } from './common/filters/crypto-exception.filter';
import { LoggingInterceptor } from './common/interceptors/logging.interceptor';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);
  
  // Security: Helmet (disabled for development)
  // app.use(helmet());

  // Security: Rate Limiting (disabled for development)
  // app.use(
  //   rateLimit({
  //     windowMs: 15 * 60 * 1000, // 15 minutes
  //     max: 100, // limit each IP to 100 requests per windowMs
  //     standardHeaders: true,
  //     legacyHeaders: false,
  //   })
  // );
  
  // Enable CORS for development
  app.enableCors({
    origin: [
      'http://localhost:4200',
      'http://localhost:51786',
      process.env.FRONTEND_URL
    ].filter(Boolean),
    credentials: true,
  });
  
  // Enable validation
  app.useGlobalPipes(new ValidationPipe({
    whitelist: true,
    transform: true,
  }));
  
  // Set global prefix
  app.setGlobalPrefix('api');
  
  // Enhanced Winston logger setup with structured logging
  const logger = winston.createLogger({
    level: process.env.LOG_LEVEL || 'info',
    format: winston.format.combine(
      winston.format.timestamp(),
      winston.format.errors({ stack: true }),
      winston.format.json(),
      winston.format.printf(({ timestamp, level, message, stack, ...meta }) => {
        const logEntry: any = {
          timestamp,
          level,
          message,
          ...meta,
        };

        if (stack) {
          logEntry.stack = stack;
        }
        return JSON.stringify(logEntry);
      })
    ),
    defaultMeta: {
      service: 'qsc-backend',
      version: process.env.npm_package_version || '1.0.0',
    },
    transports: [
      new winston.transports.Console({
        format: winston.format.combine(
          winston.format.colorize(),
          winston.format.simple()
        ),
      }),
      // File transport for production
      ...(process.env.NODE_ENV === 'production' ? [
        new winston.transports.File({
          filename: 'logs/error.log',
          level: 'error',
          maxsize: 5242880, // 5MB
          maxFiles: 5,
        }),
        new winston.transports.File({
          filename: 'logs/combined.log',
          maxsize: 5242880, // 5MB
          maxFiles: 5,
        }),
      ] : []),
    ],
    exceptionHandlers: [
      new winston.transports.File({ filename: 'logs/exceptions.log' }),
    ],
    rejectionHandlers: [
      new winston.transports.File({ filename: 'logs/rejections.log' }),
    ],
  });

  app.useLogger(logger);

  // Global exception filters
  app.useGlobalFilters(
    new GlobalExceptionFilter(),
    new CryptoExceptionFilter(),
  );

  // Global interceptors
  app.useGlobalInterceptors(new LoggingInterceptor());

  const port = process.env.PORT || 3001;
  await app.listen(port, '0.0.0.0');
  logger.info(`Server running on http://localhost:${port}`);
}

// Zero Uncaught Exceptions
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
  process.exit(1);
});

bootstrap(); 