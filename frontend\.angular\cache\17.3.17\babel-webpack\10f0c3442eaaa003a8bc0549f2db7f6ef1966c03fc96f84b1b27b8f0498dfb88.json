{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { Subject, takeUntil, take } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../services/auth.service\";\nimport * as i2 from \"../../services/message.service\";\nimport * as i3 from \"../../services/notification.service\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/forms\";\nfunction QscMainComponent_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 10);\n  }\n}\nfunction QscMainComponent_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 11);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.unreadCount, \" \");\n  }\n}\nfunction QscMainComponent_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 12)(1, \"span\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 13);\n    i0.ɵɵlistener(\"click\", function QscMainComponent_div_6_Template_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.logout());\n    });\n    i0.ɵɵtext(4, \"\\u00D7\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r0.currentUser.username);\n  }\n}\nfunction QscMainComponent_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 14);\n    i0.ɵɵlistener(\"click\", function QscMainComponent_div_7_Template_div_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelementStart(1, \"div\", 15);\n    i0.ɵɵlistener(\"click\", function QscMainComponent_div_7_Template_div_click_1_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.openAccountSettings());\n    });\n    i0.ɵɵelementStart(2, \"span\", 16);\n    i0.ɵɵtext(3, \"\\uD83D\\uDC64\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 17);\n    i0.ɵɵtext(5, \"Account Settings\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(6, \"div\", 18);\n    i0.ɵɵelementStart(7, \"div\", 19);\n    i0.ɵɵlistener(\"click\", function QscMainComponent_div_7_Template_div_click_7_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.logout());\n    });\n    i0.ɵɵelementStart(8, \"span\", 16);\n    i0.ɵɵtext(9, \"\\uD83D\\uDEAA\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"span\", 17);\n    i0.ɵɵtext(11, \"Logout\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵstyleProp(\"left\", ctx_r0.contextMenuPosition.x, \"px\")(\"top\", ctx_r0.contextMenuPosition.y, \"px\");\n  }\n}\nfunction QscMainComponent_div_8_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 30);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.loginError, \" \");\n  }\n}\nfunction QscMainComponent_div_8_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 31);\n    i0.ɵɵelement(1, \"div\", 32);\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3, \"Authenticating...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction QscMainComponent_div_8_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 33)(1, \"p\");\n    i0.ɵɵtext(2, \"\\uD83D\\uDD12 Protected by post-quantum cryptography\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p\", 34);\n    i0.ɵɵtext(4, \"Form auto-submits when credentials are valid\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction QscMainComponent_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 20);\n    i0.ɵɵlistener(\"click\", function QscMainComponent_div_8_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.closeLoginModal());\n    });\n    i0.ɵɵelementStart(1, \"div\", 21);\n    i0.ɵɵlistener(\"click\", function QscMainComponent_div_8_Template_div_click_1_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelementStart(2, \"button\", 22);\n    i0.ɵɵlistener(\"click\", function QscMainComponent_div_8_Template_button_click_2_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.closeLoginModal());\n    });\n    i0.ɵɵtext(3, \"\\u00D7\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 23)(5, \"div\", 24)(6, \"input\", 25);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function QscMainComponent_div_8_Template_input_ngModelChange_6_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r0 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r0.loginCredentials.username, $event) || (ctx_r0.loginCredentials.username = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"input\", function QscMainComponent_div_8_Template_input_input_6_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.onLoginInputChange());\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 24)(8, \"input\", 26);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function QscMainComponent_div_8_Template_input_ngModelChange_8_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r0 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r0.loginCredentials.secretWord, $event) || (ctx_r0.loginCredentials.secretWord = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"input\", function QscMainComponent_div_8_Template_input_input_8_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.onLoginInputChange());\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(9, QscMainComponent_div_8_div_9_Template, 2, 1, \"div\", 27)(10, QscMainComponent_div_8_div_10_Template, 4, 0, \"div\", 28)(11, QscMainComponent_div_8_div_11_Template, 5, 0, \"div\", 29);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r0.loginCredentials.username);\n    i0.ɵɵproperty(\"disabled\", ctx_r0.isLoading);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r0.loginCredentials.secretWord);\n    i0.ɵɵproperty(\"disabled\", ctx_r0.isLoading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.loginError);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isLoading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.isLoading);\n  }\n}\nfunction QscMainComponent_div_9_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 53)(1, \"button\", 54);\n    i0.ɵɵlistener(\"click\", function QscMainComponent_div_9_div_5_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.switchMessageType(\"direct\"));\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(2, \"svg\", 46);\n    i0.ɵɵelement(3, \"path\", 55)(4, \"circle\", 56);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(5, \" Direct \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(6, \"button\", 57);\n    i0.ɵɵlistener(\"click\", function QscMainComponent_div_9_div_5_Template_button_click_6_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.switchMessageType(\"group\"));\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(7, \"svg\", 46);\n    i0.ɵɵelement(8, \"path\", 58)(9, \"circle\", 59)(10, \"path\", 60)(11, \"path\", 61);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(12, \" Group \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"active\", ctx_r0.messageType === \"direct\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵclassProp(\"active\", ctx_r0.messageType === \"group\");\n  }\n}\nfunction QscMainComponent_div_9_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 62)(1, \"span\", 63);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 64);\n    i0.ɵɵlistener(\"click\", function QscMainComponent_div_9_div_9_Template_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.switchMessageType(ctx_r0.messageType));\n    });\n    i0.ɵɵtext(4, \"\\u00D7\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r0.getSelectedRecipientName());\n  }\n}\nfunction QscMainComponent_div_9_div_10_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 68);\n    i0.ɵɵlistener(\"click\", function QscMainComponent_div_9_div_10_div_1_Template_div_click_0_listener() {\n      const contact_r9 = i0.ɵɵrestoreView(_r8).$implicit;\n      const ctx_r0 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r0.selectContact(contact_r9));\n    });\n    i0.ɵɵelementStart(1, \"div\", 69)(2, \"span\", 70);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 71);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 72);\n    i0.ɵɵelement(7, \"span\", 73);\n    i0.ɵɵelementStart(8, \"span\", 74);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const contact_r9 = ctx.$implicit;\n    const ctx_r0 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"hidden\", ctx_r0.messageType !== \"direct\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(contact_r9.username);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(contact_r9.email);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"online\", contact_r9.isOnline)(\"offline\", !contact_r9.isOnline);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", contact_r9.isOnline ? \"Online\" : \"Offline\", \" \");\n  }\n}\nfunction QscMainComponent_div_9_div_10_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 68);\n    i0.ɵɵlistener(\"click\", function QscMainComponent_div_9_div_10_div_2_Template_div_click_0_listener() {\n      const group_r11 = i0.ɵɵrestoreView(_r10).$implicit;\n      const ctx_r0 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r0.selectGroup(group_r11));\n    });\n    i0.ɵɵelementStart(1, \"div\", 75)(2, \"span\", 76);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 77);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 78);\n    i0.ɵɵelement(7, \"span\", 73);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const group_r11 = ctx.$implicit;\n    const ctx_r0 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"hidden\", ctx_r0.messageType !== \"group\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(group_r11.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", group_r11.members.length, \" members\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"active\", group_r11.isActive);\n  }\n}\nfunction QscMainComponent_div_9_div_10_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 79);\n    i0.ɵɵtext(1, \" No contacts found \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction QscMainComponent_div_9_div_10_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 79);\n    i0.ɵɵtext(1, \" No groups found \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction QscMainComponent_div_9_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 65);\n    i0.ɵɵtemplate(1, QscMainComponent_div_9_div_10_div_1_Template, 10, 8, \"div\", 66)(2, QscMainComponent_div_9_div_10_div_2_Template, 8, 5, \"div\", 66)(3, QscMainComponent_div_9_div_10_div_3_Template, 2, 0, \"div\", 67)(4, QscMainComponent_div_9_div_10_div_4_Template, 2, 0, \"div\", 67);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.filteredContacts);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.filteredGroups);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.messageType === \"direct\" && ctx_r0.filteredContacts.length === 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.messageType === \"group\" && ctx_r0.filteredGroups.length === 0);\n  }\n}\nfunction QscMainComponent_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 20);\n    i0.ɵɵlistener(\"click\", function QscMainComponent_div_9_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.closeMessageComposer());\n    });\n    i0.ɵɵelementStart(1, \"div\", 35);\n    i0.ɵɵlistener(\"click\", function QscMainComponent_div_9_Template_div_click_1_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelementStart(2, \"button\", 36);\n    i0.ɵɵlistener(\"click\", function QscMainComponent_div_9_Template_button_click_2_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.closeMessageComposer());\n    });\n    i0.ɵɵtext(3, \"\\u00D7\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 23);\n    i0.ɵɵtemplate(5, QscMainComponent_div_9_div_5_Template, 13, 4, \"div\", 37);\n    i0.ɵɵelementStart(6, \"div\", 24)(7, \"div\", 38)(8, \"input\", 39);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function QscMainComponent_div_9_Template_input_ngModelChange_8_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r0 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r0.recipientSearchQuery, $event) || (ctx_r0.recipientSearchQuery = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"input\", function QscMainComponent_div_9_Template_input_input_8_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.onRecipientSearchChange());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(9, QscMainComponent_div_9_div_9_Template, 5, 1, \"div\", 40)(10, QscMainComponent_div_9_div_10_Template, 5, 4, \"div\", 41);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 24)(12, \"textarea\", 42);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function QscMainComponent_div_9_Template_textarea_ngModelChange_12_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r0 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r0.messageContent, $event) || (ctx_r0.messageContent = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"div\", 43);\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"div\", 44)(16, \"button\", 45);\n    i0.ɵɵlistener(\"click\", function QscMainComponent_div_9_Template_button_click_16_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.sendMessage());\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(17, \"svg\", 46);\n    i0.ɵɵelement(18, \"line\", 47)(19, \"polygon\", 48);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(20, \" Send \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(21, \"button\", 49);\n    i0.ɵɵlistener(\"click\", function QscMainComponent_div_9_Template_button_click_21_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.closeMessageComposer());\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(22, \"svg\", 46);\n    i0.ɵɵelement(23, \"line\", 50)(24, \"line\", 51);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(25, \" Cancel \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(26, \"div\", 52)(27, \"p\");\n    i0.ɵɵtext(28, \"Press Enter to send \\u2022 Shift+Enter for new line\");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.userGroups.length > 0);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r0.recipientSearchQuery);\n    i0.ɵɵproperty(\"placeholder\", ctx_r0.messageType === \"direct\" ? \"Search contacts...\" : \"Search groups...\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.getSelectedRecipientName());\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.recipientSearchQuery && !ctx_r0.getSelectedRecipientName());\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r0.messageContent);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r0.messageContent.length, \"/1000\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", !ctx_r0.isMessageValid());\n  }\n}\nfunction QscMainComponent_div_10_div_5_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 85)(1, \"div\", 86)(2, \"span\", 87);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 88);\n    i0.ɵɵtext(5);\n    i0.ɵɵpipe(6, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 89);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const message_r13 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(message_r13.sender);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(6, 3, message_r13.timestamp, \"short\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(message_r13.content);\n  }\n}\nfunction QscMainComponent_div_10_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 83);\n    i0.ɵɵtemplate(1, QscMainComponent_div_10_div_5_div_1_Template, 9, 6, \"div\", 84);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.messages)(\"ngForTrackBy\", ctx_r0.trackMessage);\n  }\n}\nfunction QscMainComponent_div_10_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 90)(1, \"p\");\n    i0.ɵɵtext(2, \"No messages yet\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p\", 91);\n    i0.ɵɵtext(4, \"Click the circle to compose your first message\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction QscMainComponent_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 20);\n    i0.ɵɵlistener(\"click\", function QscMainComponent_div_10_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.closeMessagesViewer());\n    });\n    i0.ɵɵelementStart(1, \"div\", 80);\n    i0.ɵɵlistener(\"click\", function QscMainComponent_div_10_Template_div_click_1_listener($event) {\n      i0.ɵɵrestoreView(_r12);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelementStart(2, \"button\", 22);\n    i0.ɵɵlistener(\"click\", function QscMainComponent_div_10_Template_button_click_2_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.closeMessagesViewer());\n    });\n    i0.ɵɵtext(3, \"\\u00D7\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 23);\n    i0.ɵɵtemplate(5, QscMainComponent_div_10_div_5_Template, 2, 2, \"div\", 81)(6, QscMainComponent_div_10_div_6_Template, 5, 0, \"div\", 82);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.messages.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.messages.length === 0);\n  }\n}\nfunction QscMainComponent_div_11_img_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 112);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"src\", ctx_r0.userProfile.avatar, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction QscMainComponent_div_11_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 113)(1, \"span\", 114);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    let tmp_2_0;\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", (ctx_r0.currentUser == null ? null : ctx_r0.currentUser.username == null ? null : (tmp_2_0 = ctx_r0.currentUser.username.charAt(0)) == null ? null : tmp_2_0.toUpperCase()) || \"?\", \" \");\n  }\n}\nfunction QscMainComponent_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 20);\n    i0.ɵɵlistener(\"click\", function QscMainComponent_div_11_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.closeAccountSettings());\n    });\n    i0.ɵɵelementStart(1, \"div\", 92);\n    i0.ɵɵlistener(\"click\", function QscMainComponent_div_11_Template_div_click_1_listener($event) {\n      i0.ɵɵrestoreView(_r14);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelementStart(2, \"button\", 36);\n    i0.ɵɵlistener(\"click\", function QscMainComponent_div_11_Template_button_click_2_listener() {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.closeAccountSettings());\n    });\n    i0.ɵɵtext(3, \"\\u00D7\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 23)(5, \"div\", 93)(6, \"div\", 94)(7, \"div\", 95);\n    i0.ɵɵtemplate(8, QscMainComponent_div_11_img_8_Template, 1, 1, \"img\", 96)(9, QscMainComponent_div_11_div_9_Template, 3, 1, \"div\", 97);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 98)(11, \"label\", 99);\n    i0.ɵɵtext(12, \" \\uD83D\\uDCF7 Change Avatar \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"input\", 100);\n    i0.ɵɵlistener(\"change\", function QscMainComponent_div_11_Template_input_change_13_listener($event) {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.onAvatarChange($event));\n    });\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(14, \"div\", 101)(15, \"div\", 24)(16, \"div\", 102)(17, \"span\");\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(19, \"div\", 24)(20, \"div\", 102)(21, \"span\");\n    i0.ɵɵtext(22);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"span\", 103);\n    i0.ɵɵtext(24, \"Email cannot be changed for security reasons\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(25, \"div\", 24)(26, \"div\", 102)(27, \"span\");\n    i0.ɵɵtext(28);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"span\", 103);\n    i0.ɵɵtext(30, \"Phone number cannot be changed for security reasons\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(31, \"div\", 104)(32, \"h3\");\n    i0.ɵɵtext(33, \"Security Information\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(34, \"div\", 105)(35, \"span\", 106);\n    i0.ɵɵtext(36, \"\\uD83D\\uDD10 Encryption:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(37, \"span\", 107);\n    i0.ɵɵtext(38, \"Post-Quantum Cryptography (ML-DSA, ML-KEM)\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(39, \"div\", 105)(40, \"span\", 106);\n    i0.ɵɵtext(41, \"\\uD83D\\uDEE1\\uFE0F Security Level:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(42, \"span\", 107);\n    i0.ɵɵtext(43, \"NIST Level 3 (AES-192 equivalent)\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(44, \"div\", 105)(45, \"span\", 106);\n    i0.ɵɵtext(46, \"\\uD83D\\uDD11 Key Rotation:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(47, \"span\", 107);\n    i0.ɵɵtext(48, \"Every 30 days\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(49, \"div\", 108)(50, \"button\", 49);\n    i0.ɵɵlistener(\"click\", function QscMainComponent_div_11_Template_button_click_50_listener() {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.closeAccountSettings());\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(51, \"svg\", 46);\n    i0.ɵɵelement(52, \"path\", 109)(53, \"polyline\", 110)(54, \"line\", 111);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(55, \" Close \");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.userProfile.avatar);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.userProfile.avatar);\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate((ctx_r0.currentUser == null ? null : ctx_r0.currentUser.username) || \"Not set\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r0.userProfile.email || \"Not set\");\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r0.userProfile.phone || \"Not set\");\n  }\n}\nfunction QscMainComponent_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 115)(1, \"span\");\n    i0.ɵɵtext(2, \"ESC to close modals \\u2022 Right-click circle for menu\");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport class QscMainComponent {\n  // Getter for template access\n  get userGroups() {\n    return this.groups;\n  }\n  constructor(authService, messageService, notificationService) {\n    this.authService = authService;\n    this.messageService = messageService;\n    this.notificationService = notificationService;\n    this.destroy$ = new Subject();\n    // Circle state management\n    this.circleState = 'guest';\n    // Modal states\n    this.showLoginModal = false;\n    this.showMessageModal = false;\n    this.showMessagesModal = false;\n    this.showContextMenu = false;\n    this.showAccountSettings = false;\n    // Context menu position\n    this.contextMenuPosition = {\n      x: 0,\n      y: 0\n    };\n    // Authentication\n    this.loginCredentials = {\n      username: '',\n      secretWord: ''\n    };\n    this.loginError = '';\n    this.isLoading = false;\n    // Messaging\n    this.messageContent = '';\n    this.selectedRecipient = '';\n    this.selectedGroup = '';\n    this.messageType = 'direct';\n    this.messages = [];\n    this.unreadCount = 0;\n    // Contacts and Groups\n    this.contacts = [];\n    this.groups = [];\n    this.filteredContacts = [];\n    this.filteredGroups = [];\n    this.recipientSearchQuery = '';\n    // User info\n    this.currentUser = null;\n    // Account settings\n    this.userProfile = {\n      avatar: '',\n      email: '',\n      phone: ''\n    };\n    // Long press handling\n    this.longPressTimer = null;\n    this.longPressDuration = 500; // 500ms for long press\n  }\n  ngOnInit() {\n    this.initializeApp();\n    this.setupMessageListener();\n    this.setupAuthListener();\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  initializeApp() {\n    // Check authentication state and set initial circle state\n    this.authService.authState$.pipe(take(1)).subscribe(authState => {\n      if (authState.isAuthenticated && authState.user) {\n        this.currentUser = authState.user;\n        this.circleState = 'authenticated';\n        this.loadMessages();\n        this.loadContacts();\n        this.loadGroups();\n        this.loadUserProfile();\n      } else {\n        // Guest state - show red circle\n        this.circleState = 'guest';\n        this.currentUser = null;\n      }\n    });\n  }\n  setupAuthListener() {\n    this.authService.authState$.pipe(takeUntil(this.destroy$)).subscribe(authState => {\n      if (authState.isAuthenticated && authState.user) {\n        this.currentUser = authState.user;\n        this.circleState = 'authenticated';\n        this.showLoginModal = false;\n        this.loadMessages();\n      } else {\n        this.currentUser = null;\n        this.circleState = 'guest';\n        this.messages = [];\n        this.unreadCount = 0;\n      }\n    });\n  }\n  setupMessageListener() {\n    this.messageService.messages$.pipe(takeUntil(this.destroy$)).subscribe(messages => {\n      this.messages = messages;\n      this.updateUnreadCount();\n    });\n    this.messageService.newMessage$.pipe(takeUntil(this.destroy$)).subscribe(message => {\n      this.messages.unshift(message);\n      this.updateUnreadCount();\n      this.notificationService.showNotification('New message received');\n    });\n  }\n  updateUnreadCount() {\n    this.unreadCount = this.messages.filter(m => !m.read).length;\n    if (this.unreadCount > 0 && this.circleState === 'authenticated') {\n      this.circleState = 'unread';\n    } else if (this.unreadCount === 0 && this.circleState === 'unread') {\n      this.circleState = 'authenticated';\n    }\n  }\n  loadMessages() {\n    this.messageService.loadMessages().pipe(takeUntil(this.destroy$)).subscribe({\n      next: messages => {\n        this.messages = messages;\n        this.updateUnreadCount();\n      },\n      error: error => {\n        console.error('Failed to load messages:', error);\n      }\n    });\n  }\n  loadContacts() {\n    // TODO: Replace with actual API call\n    this.contacts = [{\n      id: '1',\n      username: 'alice',\n      email: '<EMAIL>',\n      isOnline: true\n    }, {\n      id: '2',\n      username: 'bob',\n      email: '<EMAIL>',\n      isOnline: false,\n      lastSeen: new Date(Date.now() - 300000) // 5 minutes ago\n    }];\n    this.filteredContacts = [...this.contacts];\n  }\n  loadGroups() {\n    // TODO: Replace with actual API call\n    // Temporarily enable groups to test the interface\n    this.groups = [{\n      id: 'group1',\n      name: 'Work Team',\n      members: ['1', '2', 'current-user'],\n      isActive: true\n    }, {\n      id: 'group2',\n      name: 'Family',\n      members: ['3', '4', 'current-user'],\n      isActive: true\n    }];\n    // Set to empty array to hide group selector:\n    // this.groups = [];\n    this.filteredGroups = [...this.groups];\n  }\n  loadUserProfile() {\n    if (this.currentUser) {\n      this.userProfile = {\n        avatar: '',\n        email: this.currentUser.email || '',\n        phone: '' // TODO: Add phone to user profile\n      };\n    }\n  }\n  // Circle click handler - main interaction point\n  onCircleClick() {\n    // Don't handle click if context menu is showing\n    if (this.showContextMenu) {\n      this.closeContextMenu();\n      return;\n    }\n    switch (this.circleState) {\n      case 'guest':\n        this.openLoginModal();\n        break;\n      case 'authenticated':\n        this.openMessageComposer();\n        break;\n      case 'unread':\n        this.openMessagesViewer();\n        break;\n      case 'composing':\n        // Already composing, do nothing or close\n        break;\n    }\n  }\n  // Right click handler\n  onCircleRightClick(event) {\n    event.preventDefault();\n    // Only show context menu for authenticated users\n    if (this.circleState === 'guest') return;\n    this.showContextMenu = true;\n    this.contextMenuPosition = {\n      x: event.clientX,\n      y: event.clientY\n    };\n  }\n  // Touch event handlers for long press\n  onCircleTouchStart(event) {\n    // Only for authenticated users\n    if (this.circleState === 'guest') return;\n    this.longPressTimer = setTimeout(() => {\n      const touch = event.touches[0];\n      this.showContextMenu = true;\n      this.contextMenuPosition = {\n        x: touch.clientX,\n        y: touch.clientY\n      };\n      // Provide haptic feedback if available\n      if (navigator.vibrate) {\n        navigator.vibrate(50);\n      }\n    }, this.longPressDuration);\n  }\n  onCircleTouchEnd() {\n    if (this.longPressTimer) {\n      clearTimeout(this.longPressTimer);\n      this.longPressTimer = null;\n    }\n  }\n  onCircleTouchMove() {\n    // Cancel long press if user moves finger\n    if (this.longPressTimer) {\n      clearTimeout(this.longPressTimer);\n      this.longPressTimer = null;\n    }\n  }\n  // Authentication methods\n  openLoginModal() {\n    this.showLoginModal = true;\n    this.loginCredentials = {\n      username: '',\n      secretWord: ''\n    };\n    this.loginError = '';\n  }\n  closeLoginModal() {\n    this.showLoginModal = false;\n    this.loginCredentials = {\n      username: '',\n      secretWord: ''\n    };\n    this.loginError = '';\n  }\n  onLoginInputChange() {\n    // Auto-submit when both fields are valid\n    if (this.isValidCredentials()) {\n      this.performLogin();\n    }\n  }\n  isValidCredentials() {\n    const usernameValid = this.loginCredentials.username.length >= 3;\n    // QSC secret word: exactly 4 characters with one from each character class\n    const secretWordValid = this.loginCredentials.secretWord.length === 4 && /[A-Z]/.test(this.loginCredentials.secretWord) && /[a-z]/.test(this.loginCredentials.secretWord) && /[0-9]/.test(this.loginCredentials.secretWord) && /[^A-Za-z0-9]/.test(this.loginCredentials.secretWord);\n    return usernameValid && secretWordValid;\n  }\n  performLogin() {\n    if (this.isLoading) return;\n    this.isLoading = true;\n    this.loginError = '';\n    this.authService.login(this.loginCredentials.username, this.loginCredentials.secretWord).pipe(takeUntil(this.destroy$)).subscribe({\n      next: response => {\n        this.isLoading = false;\n        // Auth state will be updated via authState$ subscription\n      },\n      error: error => {\n        this.isLoading = false;\n        this.loginError = error.message || 'Authentication failed';\n      }\n    });\n  }\n  // Message composition methods\n  openMessageComposer() {\n    this.showMessageModal = true;\n    this.messageContent = '';\n    this.selectedRecipient = '';\n    this.selectedGroup = '';\n    this.messageType = 'direct';\n    this.recipientSearchQuery = '';\n    this.filteredContacts = [...this.contacts];\n    this.filteredGroups = [...this.groups];\n    this.circleState = 'composing';\n  }\n  closeMessageComposer() {\n    this.showMessageModal = false;\n    this.messageContent = '';\n    this.selectedRecipient = '';\n    this.selectedGroup = '';\n    this.recipientSearchQuery = '';\n    this.circleState = 'authenticated';\n  }\n  sendMessage() {\n    if (!this.messageContent.trim()) return;\n    // Validate recipient selection\n    if (this.messageType === 'direct' && !this.selectedRecipient) {\n      this.notificationService.showNotification('Please select a recipient', 'warning');\n      return;\n    }\n    if (this.messageType === 'group' && !this.selectedGroup) {\n      this.notificationService.showNotification('Please select a group', 'warning');\n      return;\n    }\n    const message = {\n      content: this.messageContent.trim(),\n      timestamp: new Date(),\n      sender: this.currentUser?.username || 'Unknown',\n      recipient: this.messageType === 'direct' ? this.selectedRecipient : undefined,\n      groupId: this.messageType === 'group' ? this.selectedGroup : undefined\n    };\n    this.messageService.sendMessage(message).pipe(takeUntil(this.destroy$)).subscribe({\n      next: () => {\n        this.closeMessageComposer();\n        this.notificationService.showNotification('Message sent');\n      },\n      error: error => {\n        console.error('Failed to send message:', error);\n        this.notificationService.showNotification('Failed to send message', 'error');\n      }\n    });\n  }\n  // Recipient selection methods\n  onRecipientSearchChange() {\n    const query = this.recipientSearchQuery.toLowerCase();\n    if (this.messageType === 'direct') {\n      this.filteredContacts = this.contacts.filter(contact => contact.username.toLowerCase().includes(query) || contact.email.toLowerCase().includes(query));\n    } else {\n      this.filteredGroups = this.groups.filter(group => group.name.toLowerCase().includes(query));\n    }\n  }\n  selectContact(contact) {\n    this.selectedRecipient = contact.id;\n    this.recipientSearchQuery = contact.username;\n    this.filteredContacts = [];\n  }\n  selectGroup(group) {\n    this.selectedGroup = group.id;\n    this.recipientSearchQuery = group.name;\n    this.filteredGroups = [];\n  }\n  switchMessageType(type) {\n    this.messageType = type;\n    this.selectedRecipient = '';\n    this.selectedGroup = '';\n    this.recipientSearchQuery = '';\n    this.onRecipientSearchChange();\n  }\n  getSelectedRecipientName() {\n    if (this.messageType === 'direct' && this.selectedRecipient) {\n      const contact = this.contacts.find(c => c.id === this.selectedRecipient);\n      return contact?.username || 'Unknown';\n    }\n    if (this.messageType === 'group' && this.selectedGroup) {\n      const group = this.groups.find(g => g.id === this.selectedGroup);\n      return group?.name || 'Unknown Group';\n    }\n    return '';\n  }\n  isMessageValid() {\n    const hasContent = this.messageContent.trim().length > 0;\n    const hasRecipient = this.messageType === 'direct' ? !!this.selectedRecipient : !!this.selectedGroup;\n    return hasContent && hasRecipient;\n  }\n  // Message viewing methods\n  openMessagesViewer() {\n    this.showMessagesModal = true;\n    this.markMessagesAsRead();\n  }\n  closeMessagesViewer() {\n    this.showMessagesModal = false;\n  }\n  markMessagesAsRead() {\n    this.messageService.markAllAsRead().pipe(takeUntil(this.destroy$)).subscribe(() => {\n      this.updateUnreadCount();\n    });\n  }\n  // Context menu methods\n  closeContextMenu() {\n    this.showContextMenu = false;\n  }\n  openAccountSettings() {\n    this.showAccountSettings = true;\n    this.showContextMenu = false;\n    this.loadUserProfile();\n  }\n  closeAccountSettings() {\n    this.showAccountSettings = false;\n  }\n  // Account settings methods\n  onAvatarChange(event) {\n    const input = event.target;\n    if (input.files && input.files[0]) {\n      const file = input.files[0];\n      // Validate file type\n      if (!file.type.startsWith('image/')) {\n        this.notificationService.showNotification('Please select an image file', 'error');\n        return;\n      }\n      // Validate file size (max 2MB)\n      if (file.size > 2 * 1024 * 1024) {\n        this.notificationService.showNotification('Image must be smaller than 2MB', 'error');\n        return;\n      }\n      const reader = new FileReader();\n      reader.onload = e => {\n        this.userProfile.avatar = e.target?.result;\n        this.saveAvatarChange();\n      };\n      reader.readAsDataURL(file);\n    }\n  }\n  saveAvatarChange() {\n    // TODO: Implement API call to save avatar\n    this.notificationService.showNotification('Avatar updated successfully', 'success');\n  }\n  // Logout\n  logout() {\n    this.closeContextMenu();\n    this.authService.logout();\n  }\n  // Keyboard shortcuts\n  onKeyDown(event) {\n    // Escape key closes modals\n    if (event.key === 'Escape') {\n      this.closeAllModals();\n    }\n    // Enter key in login modal\n    if (event.key === 'Enter' && this.showLoginModal) {\n      if (this.isValidCredentials()) {\n        this.performLogin();\n      }\n    }\n    // Enter key in message modal\n    if (event.key === 'Enter' && this.showMessageModal && !event.shiftKey) {\n      event.preventDefault();\n      this.sendMessage();\n    }\n  }\n  // Click outside handler to close context menu\n  onDocumentClick(event) {\n    // Close context menu when clicking outside\n    if (this.showContextMenu) {\n      this.closeContextMenu();\n    }\n  }\n  closeAllModals() {\n    this.showLoginModal = false;\n    this.showMessageModal = false;\n    this.showMessagesModal = false;\n    this.showContextMenu = false;\n    this.showAccountSettings = false;\n    if (this.circleState === 'composing') {\n      this.circleState = 'authenticated';\n    }\n  }\n  // Utility methods\n  getCircleClass() {\n    return `circle-${this.circleState}`;\n  }\n  getCircleTitle() {\n    switch (this.circleState) {\n      case 'guest':\n        return 'Click to sign in';\n      case 'authenticated':\n        return 'Click to compose message';\n      case 'unread':\n        return `Click to view ${this.unreadCount} unread message(s)`;\n      case 'composing':\n        return 'Composing message...';\n      default:\n        return '';\n    }\n  }\n  trackMessage(index, message) {\n    return message.id;\n  }\n  static {\n    this.ɵfac = function QscMainComponent_Factory(t) {\n      return new (t || QscMainComponent)(i0.ɵɵdirectiveInject(i1.AuthService), i0.ɵɵdirectiveInject(i2.MessageService), i0.ɵɵdirectiveInject(i3.NotificationService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: QscMainComponent,\n      selectors: [[\"app-qsc-main\"]],\n      hostBindings: function QscMainComponent_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"keydown\", function QscMainComponent_keydown_HostBindingHandler($event) {\n            return ctx.onKeyDown($event);\n          }, false, i0.ɵɵresolveDocument)(\"click\", function QscMainComponent_click_HostBindingHandler($event) {\n            return ctx.onDocumentClick($event);\n          }, false, i0.ɵɵresolveDocument);\n        }\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 13,\n      vars: 12,\n      consts: [[1, \"qsc-container\"], [1, \"circle-container\"], [1, \"qsc-circle\", 3, \"click\", \"contextmenu\", \"touchstart\", \"touchend\", \"touchmove\", \"title\"], [1, \"circle-inner\"], [\"class\", \"wind-effect\", 4, \"ngIf\"], [\"class\", \"unread-indicator\", 4, \"ngIf\"], [\"class\", \"user-info\", 4, \"ngIf\"], [\"class\", \"context-menu\", 3, \"left\", \"top\", \"click\", 4, \"ngIf\"], [\"class\", \"modal-overlay\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"keyboard-hints\", 4, \"ngIf\"], [1, \"wind-effect\"], [1, \"unread-indicator\"], [1, \"user-info\"], [\"title\", \"Logout\", 1, \"logout-btn\", 3, \"click\"], [1, \"context-menu\", 3, \"click\"], [1, \"context-menu-item\", 3, \"click\"], [1, \"menu-icon\"], [1, \"menu-text\"], [1, \"context-menu-divider\"], [1, \"context-menu-item\", \"logout-item\", 3, \"click\"], [1, \"modal-overlay\", 3, \"click\"], [1, \"modal\", \"login-modal\", 3, \"click\"], [1, \"close-btn\", 3, \"click\"], [1, \"modal-content\"], [1, \"form-group\"], [\"type\", \"text\", \"id\", \"username\", \"placeholder\", \"Username\", \"autocomplete\", \"username\", 3, \"ngModelChange\", \"input\", \"ngModel\", \"disabled\"], [\"type\", \"password\", \"id\", \"secretWord\", \"placeholder\", \"Secret Word (4 chars: A-Z, a-z, 0-9, symbol)\", \"autocomplete\", \"current-password\", 3, \"ngModelChange\", \"input\", \"ngModel\", \"disabled\"], [\"class\", \"error-message\", 4, \"ngIf\"], [\"class\", \"loading-indicator\", 4, \"ngIf\"], [\"class\", \"auth-info\", 4, \"ngIf\"], [1, \"error-message\"], [1, \"loading-indicator\"], [1, \"spinner\"], [1, \"auth-info\"], [1, \"auto-submit-hint\"], [1, \"modal\", \"message-modal\", 3, \"click\"], [\"title\", \"Close\", 1, \"close-btn\", 3, \"click\"], [\"class\", \"message-type-selector\", 4, \"ngIf\"], [1, \"recipient-selector\"], [\"type\", \"text\", \"id\", \"recipientSearch\", \"autocomplete\", \"off\", 3, \"ngModelChange\", \"input\", \"ngModel\", \"placeholder\"], [\"class\", \"selected-recipient\", 4, \"ngIf\"], [\"class\", \"recipient-dropdown\", 4, \"ngIf\"], [\"id\", \"messageContent\", \"placeholder\", \"Type your message here...\", \"rows\", \"6\", \"maxlength\", \"1000\", 3, \"ngModelChange\", \"ngModel\"], [1, \"char-count\"], [1, \"message-actions\"], [1, \"btn\", \"btn-primary\", 3, \"click\", \"disabled\"], [\"viewBox\", \"0 0 24 24\", 1, \"icon\"], [\"x1\", \"22\", \"y1\", \"2\", \"x2\", \"11\", \"y2\", \"13\"], [\"points\", \"22,2 15,22 11,13 2,9\"], [1, \"btn\", \"btn-secondary\", 3, \"click\"], [\"x1\", \"18\", \"y1\", \"6\", \"x2\", \"6\", \"y2\", \"18\"], [\"x1\", \"6\", \"y1\", \"6\", \"x2\", \"18\", \"y2\", \"18\"], [1, \"send-hint\"], [1, \"message-type-selector\"], [1, \"type-btn\", \"direct-btn\", 3, \"click\"], [\"d\", \"M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2\"], [\"cx\", \"12\", \"cy\", \"7\", \"r\", \"4\"], [1, \"type-btn\", \"group-btn\", 3, \"click\"], [\"d\", \"M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2\"], [\"cx\", \"9\", \"cy\", \"7\", \"r\", \"4\"], [\"d\", \"M23 21v-2a4 4 0 0 0-3-3.87\"], [\"d\", \"M16 3.13a4 4 0 0 1 0 7.75\"], [1, \"selected-recipient\"], [1, \"recipient-name\"], [\"title\", \"Clear selection\", 1, \"clear-recipient\", 3, \"click\"], [1, \"recipient-dropdown\"], [\"class\", \"recipient-item\", 3, \"hidden\", \"click\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"no-results\", 4, \"ngIf\"], [1, \"recipient-item\", 3, \"click\", \"hidden\"], [1, \"contact-info\"], [1, \"contact-name\"], [1, \"contact-email\"], [1, \"contact-status\"], [1, \"status-indicator\"], [1, \"status-text\"], [1, \"group-info\"], [1, \"group-name\"], [1, \"group-members\"], [1, \"group-status\"], [1, \"no-results\"], [1, \"modal\", \"messages-modal\", 3, \"click\"], [\"class\", \"messages-list\", 4, \"ngIf\"], [\"class\", \"empty-state\", 4, \"ngIf\"], [1, \"messages-list\"], [\"class\", \"message-item\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [1, \"message-item\"], [1, \"message-header\"], [1, \"sender\"], [1, \"timestamp\"], [1, \"message-content\"], [1, \"empty-state\"], [1, \"hint\"], [1, \"modal\", \"account-settings-modal\", 3, \"click\"], [1, \"avatar-section\"], [1, \"avatar-container\"], [1, \"avatar-display\"], [\"alt\", \"Profile Avatar\", \"class\", \"avatar-image\", 3, \"src\", 4, \"ngIf\"], [\"class\", \"avatar-placeholder\", 4, \"ngIf\"], [1, \"avatar-actions\"], [\"for\", \"avatarInput\", 1, \"avatar-upload-btn\"], [\"type\", \"file\", \"id\", \"avatarInput\", \"accept\", \"image/*\", 2, \"display\", \"none\", 3, \"change\"], [1, \"profile-info\"], [1, \"readonly-field\"], [1, \"field-note\"], [1, \"security-info\"], [1, \"security-item\"], [1, \"security-label\"], [1, \"security-value\"], [1, \"account-actions\"], [\"d\", \"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4\"], [\"points\", \"16,17 21,12 16,7\"], [\"x1\", \"21\", \"y1\", \"12\", \"x2\", \"9\", \"y2\", \"12\"], [\"alt\", \"Profile Avatar\", 1, \"avatar-image\", 3, \"src\"], [1, \"avatar-placeholder\"], [1, \"avatar-initials\"], [1, \"keyboard-hints\"]],\n      template: function QscMainComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2);\n          i0.ɵɵlistener(\"click\", function QscMainComponent_Template_div_click_2_listener() {\n            return ctx.onCircleClick();\n          })(\"contextmenu\", function QscMainComponent_Template_div_contextmenu_2_listener($event) {\n            return ctx.onCircleRightClick($event);\n          })(\"touchstart\", function QscMainComponent_Template_div_touchstart_2_listener($event) {\n            return ctx.onCircleTouchStart($event);\n          })(\"touchend\", function QscMainComponent_Template_div_touchend_2_listener() {\n            return ctx.onCircleTouchEnd();\n          })(\"touchmove\", function QscMainComponent_Template_div_touchmove_2_listener() {\n            return ctx.onCircleTouchMove();\n          });\n          i0.ɵɵelementStart(3, \"div\", 3);\n          i0.ɵɵtemplate(4, QscMainComponent_div_4_Template, 1, 0, \"div\", 4)(5, QscMainComponent_div_5_Template, 2, 1, \"div\", 5);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(6, QscMainComponent_div_6_Template, 5, 1, \"div\", 6)(7, QscMainComponent_div_7_Template, 12, 4, \"div\", 7);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(8, QscMainComponent_div_8_Template, 12, 7, \"div\", 8)(9, QscMainComponent_div_9_Template, 29, 8, \"div\", 8)(10, QscMainComponent_div_10_Template, 7, 2, \"div\", 8)(11, QscMainComponent_div_11_Template, 56, 5, \"div\", 8)(12, QscMainComponent_div_12_Template, 3, 0, \"div\", 9);\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(2);\n          i0.ɵɵclassMap(ctx.getCircleClass());\n          i0.ɵɵproperty(\"title\", ctx.getCircleTitle());\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.circleState !== \"guest\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.circleState === \"unread\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.currentUser);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.showContextMenu);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.showLoginModal);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.showMessageModal);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.showMessagesModal);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.showAccountSettings);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.showLoginModal && !ctx.showMessageModal && !ctx.showMessagesModal && !ctx.showAccountSettings);\n        }\n      },\n      dependencies: [CommonModule, i4.NgForOf, i4.NgIf, i4.DatePipe, FormsModule, i5.DefaultValueAccessor, i5.NgControlStatus, i5.MaxLengthValidator, i5.NgModel],\n      styles: [\".qsc-container[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  width: 100vw;\\n  height: 100vh;\\n  background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  overflow: hidden;\\n  font-family: \\\"Inter\\\", -apple-system, BlinkMacSystemFont, sans-serif;\\n}\\n\\n.circle-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  z-index: 1;\\n}\\n\\n.qsc-circle[_ngcontent-%COMP%] {\\n  width: 200px;\\n  height: 200px;\\n  border-radius: 50%;\\n  cursor: pointer;\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n  position: relative;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);\\n}\\n.qsc-circle[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.05);\\n  box-shadow: 0 12px 48px rgba(0, 0, 0, 0.4);\\n}\\n.qsc-circle[_ngcontent-%COMP%]:active {\\n  transform: scale(0.98);\\n}\\n.qsc-circle.circle-guest[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #ff4757 0%, #ff3742 100%);\\n  border: 3px solid rgba(255, 71, 87, 0.3);\\n}\\n.qsc-circle.circle-guest[_ngcontent-%COMP%]:hover {\\n  box-shadow: 0 12px 48px rgba(255, 71, 87, 0.4);\\n}\\n.qsc-circle.circle-authenticated[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #3742fa 0%, #2f3542 100%);\\n  border: 3px solid rgba(55, 66, 250, 0.3);\\n}\\n.qsc-circle.circle-authenticated[_ngcontent-%COMP%]:hover {\\n  box-shadow: 0 12px 48px rgba(55, 66, 250, 0.4);\\n}\\n.qsc-circle.circle-unread[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #2ed573 0%, #1e90ff 100%);\\n  border: 3px solid rgba(46, 213, 115, 0.3);\\n  animation: _ngcontent-%COMP%_pulse 2s infinite;\\n}\\n.qsc-circle.circle-unread[_ngcontent-%COMP%]:hover {\\n  box-shadow: 0 12px 48px rgba(46, 213, 115, 0.4);\\n}\\n.qsc-circle.circle-composing[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #a55eea 0%, #8854d0 100%);\\n  border: 3px solid rgba(165, 94, 234, 0.3);\\n}\\n.qsc-circle.circle-composing[_ngcontent-%COMP%]:hover {\\n  box-shadow: 0 12px 48px rgba(165, 94, 234, 0.4);\\n}\\n\\n.circle-inner[_ngcontent-%COMP%] {\\n  position: relative;\\n  width: 100%;\\n  height: 100%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n\\n.wind-effect[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 20%;\\n  right: 15%;\\n  width: 60px;\\n  height: 60px;\\n  opacity: 0.3;\\n}\\n.wind-effect[_ngcontent-%COMP%]::before, .wind-effect[_ngcontent-%COMP%]::after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  background: rgba(255, 255, 255, 0.6);\\n  border-radius: 50%;\\n  animation: _ngcontent-%COMP%_windFlow 3s ease-in-out infinite;\\n}\\n.wind-effect[_ngcontent-%COMP%]::before {\\n  width: 8px;\\n  height: 8px;\\n  top: 10px;\\n  left: 0;\\n  animation-delay: 0s;\\n}\\n.wind-effect[_ngcontent-%COMP%]::after {\\n  width: 6px;\\n  height: 6px;\\n  top: 25px;\\n  left: 15px;\\n  animation-delay: 1s;\\n}\\n\\n.unread-indicator[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: -10px;\\n  right: -10px;\\n  background: #ff4757;\\n  color: white;\\n  border-radius: 50%;\\n  width: 40px;\\n  height: 40px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  font-weight: 600;\\n  font-size: 14px;\\n  border: 3px solid #0f0f23;\\n  animation: _ngcontent-%COMP%_bounce 1s infinite;\\n}\\n\\n.user-info[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 20px;\\n  right: 20px;\\n  display: flex;\\n  align-items: center;\\n  gap: 10px;\\n  color: rgba(255, 255, 255, 0.7);\\n  font-size: 14px;\\n  z-index: 10;\\n}\\n.user-info[_ngcontent-%COMP%]   .logout-btn[_ngcontent-%COMP%] {\\n  background: none;\\n  border: none;\\n  color: rgba(255, 255, 255, 0.5);\\n  font-size: 20px;\\n  cursor: pointer;\\n  padding: 5px;\\n  border-radius: 50%;\\n  transition: all 0.2s ease;\\n}\\n.user-info[_ngcontent-%COMP%]   .logout-btn[_ngcontent-%COMP%]:hover {\\n  color: #ff4757;\\n  background: rgba(255, 71, 87, 0.1);\\n}\\n\\n.modal-overlay[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  width: 100vw;\\n  height: 100vh;\\n  background: rgba(0, 0, 0, 0.3);\\n  -webkit-backdrop-filter: blur(8px);\\n          backdrop-filter: blur(8px);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  z-index: 1000;\\n  animation: _ngcontent-%COMP%_fadeIn 0.2s ease;\\n}\\n\\n.modal[_ngcontent-%COMP%] {\\n  background: #fafafa;\\n  border-radius: 16px;\\n  box-shadow: 0 0 40px rgba(255, 255, 255, 0.1), 0 8px 32px rgba(0, 0, 0, 0.12), inset 0 1px 0 rgba(255, 255, 255, 0.2);\\n  max-width: 400px;\\n  width: 90vw;\\n  max-height: 80vh;\\n  border: 1px solid rgba(255, 255, 255, 0.1);\\n  position: relative;\\n  animation: _ngcontent-%COMP%_slideUp 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n  display: flex;\\n  flex-direction: column;\\n}\\n.modal.login-modal[_ngcontent-%COMP%] {\\n  max-width: 400px;\\n}\\n.modal.login-modal[_ngcontent-%COMP%]   .close-btn[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 1rem;\\n  right: 1rem;\\n  background: none;\\n  border: none;\\n  font-size: 24px;\\n  color: #a4b0be;\\n  cursor: pointer;\\n  padding: 0;\\n  width: 30px;\\n  height: 30px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  border-radius: 50%;\\n  transition: all 0.2s ease;\\n  z-index: 10;\\n}\\n.modal.login-modal[_ngcontent-%COMP%]   .close-btn[_ngcontent-%COMP%]:hover {\\n  background: rgba(0, 0, 0, 0.1);\\n  color: #2f3542;\\n}\\n.modal.message-modal[_ngcontent-%COMP%] {\\n  max-width: 480px;\\n}\\n.modal.message-modal[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%] {\\n  padding: 1.5rem;\\n  margin-right: 0;\\n}\\n.modal.message-modal[_ngcontent-%COMP%]   .close-btn[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 1rem;\\n  right: 1rem;\\n  background: none;\\n  border: none;\\n  font-size: 24px;\\n  color: #a4b0be;\\n  cursor: pointer;\\n  padding: 0;\\n  width: 30px;\\n  height: 30px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  border-radius: 50%;\\n  transition: all 0.2s ease;\\n  z-index: 10;\\n}\\n.modal.message-modal[_ngcontent-%COMP%]   .close-btn[_ngcontent-%COMP%]:hover {\\n  background: rgba(0, 0, 0, 0.1);\\n  color: #2f3542;\\n}\\n.modal.account-settings-modal[_ngcontent-%COMP%] {\\n  max-width: 500px;\\n}\\n.modal.account-settings-modal[_ngcontent-%COMP%]   .close-btn[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 1rem;\\n  right: 1rem;\\n  background: none;\\n  border: none;\\n  font-size: 24px;\\n  color: #a4b0be;\\n  cursor: pointer;\\n  padding: 0;\\n  width: 30px;\\n  height: 30px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  border-radius: 50%;\\n  transition: all 0.2s ease;\\n  z-index: 10;\\n}\\n.modal.account-settings-modal[_ngcontent-%COMP%]   .close-btn[_ngcontent-%COMP%]:hover {\\n  background: rgba(0, 0, 0, 0.1);\\n  color: #2f3542;\\n}\\n\\n.modal[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: -2px;\\n  left: -2px;\\n  right: -2px;\\n  bottom: -2px;\\n  background: linear-gradient(45deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 50%, rgba(255, 255, 255, 0.1) 100%);\\n  border-radius: 18px;\\n  z-index: -1;\\n}\\n\\n.modal-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 20px 24px;\\n  border-bottom: 1px solid rgba(0, 0, 0, 0.1);\\n}\\n.modal-header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 20px;\\n  font-weight: 600;\\n  color: #2f3542;\\n}\\n.modal-header[_ngcontent-%COMP%]   .close-btn[_ngcontent-%COMP%] {\\n  background: none;\\n  border: none;\\n  font-size: 24px;\\n  color: #a4b0be;\\n  cursor: pointer;\\n  padding: 0;\\n  width: 30px;\\n  height: 30px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  border-radius: 50%;\\n  transition: all 0.2s ease;\\n}\\n.modal-header[_ngcontent-%COMP%]   .close-btn[_ngcontent-%COMP%]:hover {\\n  background: rgba(0, 0, 0, 0.1);\\n  color: #2f3542;\\n}\\n\\n.modal-content[_ngcontent-%COMP%] {\\n  padding: 2rem;\\n  flex: 1;\\n  overflow-y: auto;\\n}\\n.modal-content[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  width: 4px;\\n}\\n.modal-content[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: transparent;\\n}\\n.modal-content[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background: rgba(0, 0, 0, 0.1);\\n  border-radius: 2px;\\n}\\n.modal-content[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover {\\n  background: rgba(0, 0, 0, 0.2);\\n}\\n\\n.form-group[_ngcontent-%COMP%] {\\n  margin-bottom: 2rem;\\n  position: relative;\\n}\\n.form-group[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  display: block;\\n  margin-bottom: 0.5rem;\\n  font-size: 0.875rem;\\n  font-weight: 500;\\n  color: #6b7280;\\n  letter-spacing: 0.025em;\\n  text-transform: uppercase;\\n}\\n.form-group[_ngcontent-%COMP%]   input[_ngcontent-%COMP%], .form-group[_ngcontent-%COMP%]   textarea[_ngcontent-%COMP%] {\\n  width: 100%;\\n  padding: 0.75rem 0;\\n  background: transparent;\\n  border: none;\\n  border-bottom: 1px solid #e5e7eb;\\n  color: #1f2937;\\n  font-size: 1rem;\\n  font-weight: 400;\\n  transition: all 0.3s ease;\\n}\\n.form-group[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]:focus, .form-group[_ngcontent-%COMP%]   textarea[_ngcontent-%COMP%]:focus {\\n  outline: none;\\n  border-bottom-color: #1e40af;\\n}\\n.form-group[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]:disabled, .form-group[_ngcontent-%COMP%]   textarea[_ngcontent-%COMP%]:disabled {\\n  background: transparent;\\n  color: #9ca3af;\\n  border-bottom-color: #f3f4f6;\\n}\\n.form-group[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]::placeholder, .form-group[_ngcontent-%COMP%]   textarea[_ngcontent-%COMP%]::placeholder {\\n  color: #9ca3af;\\n  font-weight: 300;\\n}\\n.form-group[_ngcontent-%COMP%]   textarea[_ngcontent-%COMP%] {\\n  resize: vertical;\\n  min-height: 120px;\\n  font-family: inherit;\\n  border: 1px solid #e5e7eb;\\n  border-radius: 8px;\\n  padding: 0.75rem;\\n}\\n.form-group[_ngcontent-%COMP%]   textarea[_ngcontent-%COMP%]:focus {\\n  border-color: #1e40af;\\n}\\n\\n.char-count[_ngcontent-%COMP%] {\\n  text-align: right;\\n  font-size: 12px;\\n  color: #a4b0be;\\n  margin-top: 4px;\\n}\\n\\n.type-btn[_ngcontent-%COMP%] {\\n  display: inline-flex;\\n  align-items: center;\\n  justify-content: center;\\n  padding: 0.875rem 1.5rem;\\n  border: none;\\n  border-radius: 50px;\\n  font-size: 0.875rem;\\n  font-weight: 500;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  text-decoration: none;\\n  min-width: 120px;\\n  gap: 0.5rem;\\n  background: #e5e7eb;\\n  color: #6b7280;\\n}\\n.type-btn[_ngcontent-%COMP%]:hover {\\n  background: #d1d5db;\\n  transform: translateY(-1px);\\n}\\n.type-btn.active[_ngcontent-%COMP%] {\\n  background: #1e40af;\\n  color: white;\\n}\\n.type-btn.active[_ngcontent-%COMP%]:hover {\\n  background: #1d4ed8;\\n  box-shadow: 0 4px 12px rgba(30, 64, 175, 0.3);\\n}\\n.type-btn[_ngcontent-%COMP%]:active {\\n  transform: translateY(0);\\n}\\n.type-btn[_ngcontent-%COMP%]   .icon[_ngcontent-%COMP%] {\\n  width: 16px;\\n  height: 16px;\\n  stroke: currentColor;\\n  fill: none;\\n  stroke-width: 1.5;\\n}\\n\\n.btn[_ngcontent-%COMP%] {\\n  display: inline-flex;\\n  align-items: center;\\n  justify-content: center;\\n  padding: 0.75rem 0;\\n  border: none;\\n  background: transparent;\\n  font-size: 0.875rem;\\n  font-weight: 500;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  text-decoration: none;\\n  gap: 0.5rem;\\n  position: relative;\\n  color: #374151;\\n}\\n.btn[_ngcontent-%COMP%]::after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  bottom: 0;\\n  left: 0;\\n  width: 0;\\n  height: 1px;\\n  background: currentColor;\\n  transition: width 0.3s ease;\\n}\\n.btn[_ngcontent-%COMP%]:hover::after {\\n  width: 100%;\\n}\\n.btn.btn-primary[_ngcontent-%COMP%] {\\n  color: #1e40af;\\n}\\n.btn.btn-primary[_ngcontent-%COMP%]:disabled {\\n  color: #9ca3af;\\n  cursor: not-allowed;\\n}\\n.btn.btn-primary[_ngcontent-%COMP%]:disabled::after {\\n  display: none;\\n}\\n.btn.btn-secondary[_ngcontent-%COMP%] {\\n  color: #6b7280;\\n}\\n.btn[_ngcontent-%COMP%]   .icon[_ngcontent-%COMP%] {\\n  width: 16px;\\n  height: 16px;\\n  stroke: currentColor;\\n  fill: none;\\n  stroke-width: 1.5;\\n}\\n\\n.message-type-selector[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 1rem;\\n  margin-bottom: 2rem;\\n  justify-content: center;\\n}\\n\\n.recipient-selector[_ngcontent-%COMP%] {\\n  position: relative;\\n}\\n\\n.selected-recipient[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  padding: 8px 12px;\\n  background: rgba(55, 66, 250, 0.1);\\n  border: 1px solid #3742fa;\\n  border-radius: 6px;\\n  margin-top: 8px;\\n}\\n.selected-recipient[_ngcontent-%COMP%]   .recipient-name[_ngcontent-%COMP%] {\\n  color: #3742fa;\\n  font-weight: 500;\\n  font-size: 14px;\\n}\\n.selected-recipient[_ngcontent-%COMP%]   .clear-recipient[_ngcontent-%COMP%] {\\n  background: none;\\n  border: none;\\n  color: #3742fa;\\n  font-size: 16px;\\n  cursor: pointer;\\n  padding: 0;\\n  width: 20px;\\n  height: 20px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  border-radius: 50%;\\n  transition: all 0.2s ease;\\n}\\n.selected-recipient[_ngcontent-%COMP%]   .clear-recipient[_ngcontent-%COMP%]:hover {\\n  background: rgba(55, 66, 250, 0.2);\\n}\\n\\n.recipient-dropdown[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 100%;\\n  left: 0;\\n  right: 0;\\n  background: white;\\n  border: 1px solid #e1e8ed;\\n  border-radius: 8px;\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\\n  max-height: 200px;\\n  overflow-y: auto;\\n  z-index: 1000;\\n  margin-top: 4px;\\n}\\n\\n.recipient-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  padding: 12px 16px;\\n  cursor: pointer;\\n  border-bottom: 1px solid rgba(0, 0, 0, 0.05);\\n  transition: background 0.2s ease;\\n}\\n.recipient-item[_ngcontent-%COMP%]:hover {\\n  background: rgba(55, 66, 250, 0.05);\\n}\\n.recipient-item[_ngcontent-%COMP%]:last-child {\\n  border-bottom: none;\\n}\\n\\n.contact-info[_ngcontent-%COMP%], .group-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  flex: 1;\\n}\\n.contact-info[_ngcontent-%COMP%]   .contact-name[_ngcontent-%COMP%], .contact-info[_ngcontent-%COMP%]   .group-name[_ngcontent-%COMP%], .group-info[_ngcontent-%COMP%]   .contact-name[_ngcontent-%COMP%], .group-info[_ngcontent-%COMP%]   .group-name[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: #2f3542;\\n  font-size: 14px;\\n  margin-bottom: 2px;\\n}\\n.contact-info[_ngcontent-%COMP%]   .contact-email[_ngcontent-%COMP%], .contact-info[_ngcontent-%COMP%]   .group-members[_ngcontent-%COMP%], .group-info[_ngcontent-%COMP%]   .contact-email[_ngcontent-%COMP%], .group-info[_ngcontent-%COMP%]   .group-members[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: #a4b0be;\\n}\\n\\n.contact-status[_ngcontent-%COMP%], .group-status[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 6px;\\n}\\n.contact-status[_ngcontent-%COMP%]   .status-indicator[_ngcontent-%COMP%], .group-status[_ngcontent-%COMP%]   .status-indicator[_ngcontent-%COMP%] {\\n  width: 8px;\\n  height: 8px;\\n  border-radius: 50%;\\n}\\n.contact-status[_ngcontent-%COMP%]   .status-indicator.online[_ngcontent-%COMP%], .group-status[_ngcontent-%COMP%]   .status-indicator.online[_ngcontent-%COMP%] {\\n  background: #2ed573;\\n}\\n.contact-status[_ngcontent-%COMP%]   .status-indicator.offline[_ngcontent-%COMP%], .group-status[_ngcontent-%COMP%]   .status-indicator.offline[_ngcontent-%COMP%] {\\n  background: #a4b0be;\\n}\\n.contact-status[_ngcontent-%COMP%]   .status-indicator.active[_ngcontent-%COMP%], .group-status[_ngcontent-%COMP%]   .status-indicator.active[_ngcontent-%COMP%] {\\n  background: #3742fa;\\n}\\n.contact-status[_ngcontent-%COMP%]   .status-text[_ngcontent-%COMP%], .group-status[_ngcontent-%COMP%]   .status-text[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: #a4b0be;\\n}\\n\\n.no-results[_ngcontent-%COMP%] {\\n  padding: 16px;\\n  text-align: center;\\n  color: #a4b0be;\\n  font-size: 14px;\\n  font-style: italic;\\n}\\n\\n.message-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 2rem;\\n  margin-top: 2rem;\\n  justify-content: center;\\n}\\n\\n.error-message[_ngcontent-%COMP%] {\\n  color: #ff4757;\\n  font-size: 14px;\\n  margin-top: 8px;\\n  padding: 8px 12px;\\n  background: rgba(255, 71, 87, 0.1);\\n  border-radius: 6px;\\n  border-left: 3px solid #ff4757;\\n}\\n\\n.auth-info[_ngcontent-%COMP%] {\\n  margin-top: 20px;\\n  text-align: center;\\n}\\n.auth-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 8px 0;\\n  font-size: 13px;\\n  color: #57606f;\\n}\\n.auth-info[_ngcontent-%COMP%]   p.auto-submit-hint[_ngcontent-%COMP%] {\\n  font-style: italic;\\n  color: #a4b0be;\\n}\\n\\n.send-hint[_ngcontent-%COMP%] {\\n  margin-top: 16px;\\n  text-align: center;\\n}\\n.send-hint[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: #a4b0be;\\n  margin: 0;\\n}\\n\\n.loading-indicator[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  gap: 12px;\\n  padding: 20px;\\n}\\n.loading-indicator[_ngcontent-%COMP%]   .spinner[_ngcontent-%COMP%] {\\n  width: 20px;\\n  height: 20px;\\n  border: 2px solid #e1e8ed;\\n  border-top: 2px solid #3742fa;\\n  border-radius: 50%;\\n  animation: _ngcontent-%COMP%_spin 1s linear infinite;\\n}\\n.loading-indicator[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  color: #57606f;\\n  font-size: 14px;\\n}\\n\\n.messages-list[_ngcontent-%COMP%] {\\n  max-height: 400px;\\n  overflow-y: auto;\\n  margin: -8px;\\n  padding: 8px;\\n}\\n\\n.message-item[_ngcontent-%COMP%] {\\n  padding: 16px;\\n  border-bottom: 1px solid rgba(0, 0, 0, 0.05);\\n}\\n.message-item[_ngcontent-%COMP%]:last-child {\\n  border-bottom: none;\\n}\\n.message-item[_ngcontent-%COMP%]   .message-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 8px;\\n}\\n.message-item[_ngcontent-%COMP%]   .message-header[_ngcontent-%COMP%]   .sender[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #2f3542;\\n  font-size: 14px;\\n}\\n.message-item[_ngcontent-%COMP%]   .message-header[_ngcontent-%COMP%]   .timestamp[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: #a4b0be;\\n}\\n.message-item[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%] {\\n  color: #57606f;\\n  line-height: 1.5;\\n  word-wrap: break-word;\\n}\\n\\n.empty-state[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 40px 20px;\\n  color: #a4b0be;\\n}\\n.empty-state[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 8px 0;\\n}\\n.empty-state[_ngcontent-%COMP%]   p.hint[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-style: italic;\\n}\\n\\n.context-menu[_ngcontent-%COMP%] {\\n  position: fixed;\\n  background: rgba(255, 255, 255, 0.95);\\n  -webkit-backdrop-filter: blur(20px);\\n          backdrop-filter: blur(20px);\\n  border-radius: 12px;\\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);\\n  padding: 8px 0;\\n  min-width: 180px;\\n  z-index: 2000;\\n  animation: _ngcontent-%COMP%_contextMenuSlide 0.2s cubic-bezier(0.4, 0, 0.2, 1);\\n}\\n.context-menu[_ngcontent-%COMP%]   .context-menu-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n  padding: 12px 16px;\\n  cursor: pointer;\\n  transition: background 0.2s ease;\\n  color: #2f3542;\\n}\\n.context-menu[_ngcontent-%COMP%]   .context-menu-item[_ngcontent-%COMP%]:hover {\\n  background: rgba(55, 66, 250, 0.1);\\n}\\n.context-menu[_ngcontent-%COMP%]   .context-menu-item.logout-item[_ngcontent-%COMP%] {\\n  color: #ff4757;\\n}\\n.context-menu[_ngcontent-%COMP%]   .context-menu-item.logout-item[_ngcontent-%COMP%]:hover {\\n  background: rgba(255, 71, 87, 0.1);\\n}\\n.context-menu[_ngcontent-%COMP%]   .context-menu-item[_ngcontent-%COMP%]   .menu-icon[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  width: 20px;\\n  text-align: center;\\n}\\n.context-menu[_ngcontent-%COMP%]   .context-menu-item[_ngcontent-%COMP%]   .menu-text[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 500;\\n}\\n.context-menu[_ngcontent-%COMP%]   .context-menu-divider[_ngcontent-%COMP%] {\\n  height: 1px;\\n  background: rgba(0, 0, 0, 0.1);\\n  margin: 4px 0;\\n}\\n\\n.account-settings-modal[_ngcontent-%COMP%] {\\n  max-width: 500px;\\n  width: 90vw;\\n}\\n\\n.avatar-section[_ngcontent-%COMP%] {\\n  text-align: center;\\n  margin-bottom: 32px;\\n}\\n.avatar-section[_ngcontent-%COMP%]   .avatar-container[_ngcontent-%COMP%] {\\n  display: inline-block;\\n}\\n.avatar-section[_ngcontent-%COMP%]   .avatar-display[_ngcontent-%COMP%] {\\n  width: 120px;\\n  height: 120px;\\n  border-radius: 50%;\\n  margin: 0 auto 16px;\\n  position: relative;\\n  overflow: hidden;\\n  border: 4px solid #e1e8ed;\\n}\\n.avatar-section[_ngcontent-%COMP%]   .avatar-display[_ngcontent-%COMP%]   .avatar-image[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  object-fit: cover;\\n}\\n.avatar-section[_ngcontent-%COMP%]   .avatar-display[_ngcontent-%COMP%]   .avatar-placeholder[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  background: linear-gradient(135deg, #3742fa 0%, #2f3542 100%);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n.avatar-section[_ngcontent-%COMP%]   .avatar-display[_ngcontent-%COMP%]   .avatar-placeholder[_ngcontent-%COMP%]   .avatar-initials[_ngcontent-%COMP%] {\\n  font-size: 48px;\\n  font-weight: 600;\\n  color: white;\\n}\\n.avatar-section[_ngcontent-%COMP%]   .avatar-actions[_ngcontent-%COMP%]   .avatar-upload-btn[_ngcontent-%COMP%] {\\n  display: inline-block;\\n  padding: 8px 16px;\\n  background: #3742fa;\\n  color: white;\\n  border-radius: 6px;\\n  font-size: 14px;\\n  font-weight: 500;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n}\\n.avatar-section[_ngcontent-%COMP%]   .avatar-actions[_ngcontent-%COMP%]   .avatar-upload-btn[_ngcontent-%COMP%]:hover {\\n  background: #2f3542;\\n  transform: translateY(-1px);\\n}\\n\\n.profile-info[_ngcontent-%COMP%] {\\n  margin-bottom: 32px;\\n}\\n.profile-info[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%] {\\n  margin-bottom: 20px;\\n}\\n.profile-info[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  display: block;\\n  margin-bottom: 8px;\\n  font-weight: 500;\\n  color: #2f3542;\\n  font-size: 14px;\\n}\\n.profile-info[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .readonly-field[_ngcontent-%COMP%] {\\n  padding: 12px 16px;\\n  background: #f8f9fa;\\n  border: 2px solid #e1e8ed;\\n  border-radius: 8px;\\n  color: #57606f;\\n}\\n.profile-info[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .readonly-field[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  display: block;\\n}\\n.profile-info[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .readonly-field[_ngcontent-%COMP%]   span.field-note[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: #a4b0be;\\n  margin-top: 4px;\\n  font-style: italic;\\n}\\n\\n.security-info[_ngcontent-%COMP%] {\\n  background: rgba(55, 66, 250, 0.05);\\n  border: 1px solid rgba(55, 66, 250, 0.2);\\n  border-radius: 8px;\\n  padding: 20px;\\n  margin-bottom: 24px;\\n}\\n.security-info[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0 0 16px 0;\\n  font-size: 16px;\\n  color: #2f3542;\\n  font-weight: 600;\\n}\\n.security-info[_ngcontent-%COMP%]   .security-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 12px;\\n}\\n.security-info[_ngcontent-%COMP%]   .security-item[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0;\\n}\\n.security-info[_ngcontent-%COMP%]   .security-item[_ngcontent-%COMP%]   .security-label[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: #57606f;\\n  font-weight: 500;\\n}\\n.security-info[_ngcontent-%COMP%]   .security-item[_ngcontent-%COMP%]   .security-value[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: #3742fa;\\n  font-weight: 500;\\n  text-align: right;\\n  flex: 1;\\n  margin-left: 16px;\\n}\\n\\n.account-actions[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding-top: 16px;\\n  border-top: 1px solid rgba(0, 0, 0, 0.1);\\n}\\n\\n.keyboard-hints[_ngcontent-%COMP%] {\\n  position: fixed;\\n  bottom: 20px;\\n  left: 50%;\\n  transform: translateX(-50%);\\n  color: rgba(255, 255, 255, 0.4);\\n  font-size: 12px;\\n  z-index: 5;\\n}\\n\\n@keyframes _ngcontent-%COMP%_fadeIn {\\n  from {\\n    opacity: 0;\\n  }\\n  to {\\n    opacity: 1;\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_slideUp {\\n  from {\\n    opacity: 0;\\n    transform: translateY(20px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_pulse {\\n  0%, 100% {\\n    transform: scale(1);\\n  }\\n  50% {\\n    transform: scale(1.02);\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_bounce {\\n  0%, 20%, 50%, 80%, 100% {\\n    transform: translateY(0);\\n  }\\n  40% {\\n    transform: translateY(-5px);\\n  }\\n  60% {\\n    transform: translateY(-3px);\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_windFlow {\\n  0% {\\n    transform: translateX(0) translateY(0) scale(1);\\n    opacity: 0.3;\\n  }\\n  50% {\\n    transform: translateX(20px) translateY(-10px) scale(0.8);\\n    opacity: 0.6;\\n  }\\n  100% {\\n    transform: translateX(40px) translateY(-20px) scale(0.5);\\n    opacity: 0;\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_spin {\\n  0% {\\n    transform: rotate(0deg);\\n  }\\n  100% {\\n    transform: rotate(360deg);\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_contextMenuSlide {\\n  from {\\n    opacity: 0;\\n    transform: scale(0.95) translateY(-10px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: scale(1) translateY(0);\\n  }\\n}\\n@media (max-width: 768px) {\\n  .qsc-circle[_ngcontent-%COMP%] {\\n    width: 150px;\\n    height: 150px;\\n  }\\n  .modal[_ngcontent-%COMP%] {\\n    width: 95vw;\\n    margin: 20px;\\n  }\\n  .user-info[_ngcontent-%COMP%] {\\n    top: 15px;\\n    right: 15px;\\n    font-size: 12px;\\n  }\\n}\\n@media (max-width: 480px) {\\n  .qsc-circle[_ngcontent-%COMP%] {\\n    width: 120px;\\n    height: 120px;\\n  }\\n  .modal-content[_ngcontent-%COMP%] {\\n    padding: 20px;\\n  }\\n  .modal-header[_ngcontent-%COMP%] {\\n    padding: 16px 20px;\\n  }\\n  .context-menu[_ngcontent-%COMP%] {\\n    min-width: 160px;\\n  }\\n  .context-menu[_ngcontent-%COMP%]   .context-menu-item[_ngcontent-%COMP%] {\\n    padding: 14px 16px;\\n  }\\n  .context-menu[_ngcontent-%COMP%]   .context-menu-item[_ngcontent-%COMP%]   .menu-text[_ngcontent-%COMP%] {\\n    font-size: 16px;\\n  }\\n  .avatar-section[_ngcontent-%COMP%]   .avatar-display[_ngcontent-%COMP%] {\\n    width: 100px;\\n    height: 100px;\\n  }\\n  .avatar-section[_ngcontent-%COMP%]   .avatar-display[_ngcontent-%COMP%]   .avatar-initials[_ngcontent-%COMP%] {\\n    font-size: 40px;\\n  }\\n  .security-info[_ngcontent-%COMP%]   .security-item[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: flex-start;\\n    gap: 4px;\\n  }\\n  .security-info[_ngcontent-%COMP%]   .security-item[_ngcontent-%COMP%]   .security-value[_ngcontent-%COMP%] {\\n    text-align: left;\\n    margin-left: 0;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "FormsModule", "Subject", "takeUntil", "take", "i0", "ɵɵelement", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r0", "unreadCount", "ɵɵlistener", "QscMainComponent_div_6_Template_button_click_3_listener", "ɵɵrestoreView", "_r2", "ɵɵnextContext", "ɵɵresetView", "logout", "ɵɵtextInterpolate", "currentUser", "username", "QscMainComponent_div_7_Template_div_click_0_listener", "$event", "_r3", "stopPropagation", "QscMainComponent_div_7_Template_div_click_1_listener", "openAccountSettings", "QscMainComponent_div_7_Template_div_click_7_listener", "ɵɵstyleProp", "contextMenuPosition", "x", "y", "loginError", "QscMainComponent_div_8_Template_div_click_0_listener", "_r4", "closeLoginModal", "QscMainComponent_div_8_Template_div_click_1_listener", "QscMainComponent_div_8_Template_button_click_2_listener", "ɵɵtwoWayListener", "QscMainComponent_div_8_Template_input_ngModelChange_6_listener", "ɵɵtwoWayBindingSet", "loginCredentials", "QscMainComponent_div_8_Template_input_input_6_listener", "onLoginInputChange", "QscMainComponent_div_8_Template_input_ngModelChange_8_listener", "secretWord", "QscMainComponent_div_8_Template_input_input_8_listener", "ɵɵtemplate", "QscMainComponent_div_8_div_9_Template", "QscMainComponent_div_8_div_10_Template", "QscMainComponent_div_8_div_11_Template", "ɵɵtwoWayProperty", "ɵɵproperty", "isLoading", "QscMainComponent_div_9_div_5_Template_button_click_1_listener", "_r6", "switchMessageType", "QscMainComponent_div_9_div_5_Template_button_click_6_listener", "ɵɵclassProp", "messageType", "QscMainComponent_div_9_div_9_Template_button_click_3_listener", "_r7", "getSelectedRecipientName", "QscMainComponent_div_9_div_10_div_1_Template_div_click_0_listener", "contact_r9", "_r8", "$implicit", "selectContact", "email", "isOnline", "QscMainComponent_div_9_div_10_div_2_Template_div_click_0_listener", "group_r11", "_r10", "selectGroup", "name", "members", "length", "isActive", "QscMainComponent_div_9_div_10_div_1_Template", "QscMainComponent_div_9_div_10_div_2_Template", "QscMainComponent_div_9_div_10_div_3_Template", "QscMainComponent_div_9_div_10_div_4_Template", "filteredContacts", "filteredGroups", "QscMainComponent_div_9_Template_div_click_0_listener", "_r5", "closeMessageComposer", "QscMainComponent_div_9_Template_div_click_1_listener", "QscMainComponent_div_9_Template_button_click_2_listener", "QscMainComponent_div_9_div_5_Template", "QscMainComponent_div_9_Template_input_ngModelChange_8_listener", "recipient<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "QscMainComponent_div_9_Template_input_input_8_listener", "onRecipientSearchChange", "QscMainComponent_div_9_div_9_Template", "QscMainComponent_div_9_div_10_Template", "QscMainComponent_div_9_Template_textarea_ngModelChange_12_listener", "messageContent", "QscMainComponent_div_9_Template_button_click_16_listener", "sendMessage", "QscMainComponent_div_9_Template_button_click_21_listener", "userGroups", "isMessageValid", "message_r13", "sender", "ɵɵpipeBind2", "timestamp", "content", "QscMainComponent_div_10_div_5_div_1_Template", "messages", "trackMessage", "QscMainComponent_div_10_Template_div_click_0_listener", "_r12", "closeMessagesViewer", "QscMainComponent_div_10_Template_div_click_1_listener", "QscMainComponent_div_10_Template_button_click_2_listener", "QscMainComponent_div_10_div_5_Template", "QscMainComponent_div_10_div_6_Template", "userProfile", "avatar", "ɵɵsanitizeUrl", "tmp_2_0", "char<PERSON>t", "toUpperCase", "QscMainComponent_div_11_Template_div_click_0_listener", "_r14", "closeAccountSettings", "QscMainComponent_div_11_Template_div_click_1_listener", "QscMainComponent_div_11_Template_button_click_2_listener", "QscMainComponent_div_11_img_8_Template", "QscMainComponent_div_11_div_9_Template", "QscMainComponent_div_11_Template_input_change_13_listener", "onAvatarChange", "QscMainComponent_div_11_Template_button_click_50_listener", "phone", "QscMainComponent", "groups", "constructor", "authService", "messageService", "notificationService", "destroy$", "circleState", "showLoginModal", "showMessageModal", "showMessagesModal", "showContextMenu", "showAccountSettings", "selected<PERSON><PERSON><PERSON><PERSON>", "selectedGroup", "contacts", "longPressTimer", "longPressDuration", "ngOnInit", "initializeApp", "setupMessageListener", "setupAuthListener", "ngOnDestroy", "next", "complete", "authState$", "pipe", "subscribe", "authState", "isAuthenticated", "user", "loadMessages", "loadContacts", "loadGroups", "loadUserProfile", "messages$", "updateUnreadCount", "newMessage$", "message", "unshift", "showNotification", "filter", "m", "read", "error", "console", "id", "lastSeen", "Date", "now", "onCircleClick", "closeContextMenu", "openLoginModal", "openMessageComposer", "openMessagesViewer", "onCircleRightClick", "event", "preventDefault", "clientX", "clientY", "onCircleTouchStart", "setTimeout", "touch", "touches", "navigator", "vibrate", "onCircleTouchEnd", "clearTimeout", "onCircleTouchMove", "isValidCredentials", "performLogin", "usernameValid", "secretWordValid", "test", "login", "response", "trim", "recipient", "undefined", "groupId", "query", "toLowerCase", "contact", "includes", "group", "type", "find", "c", "g", "<PERSON><PERSON><PERSON><PERSON>", "hasRecipient", "markMessagesAsRead", "markAllAsRead", "input", "target", "files", "file", "startsWith", "size", "reader", "FileReader", "onload", "e", "result", "saveAvatarChange", "readAsDataURL", "onKeyDown", "key", "closeAllModals", "shift<PERSON>ey", "onDocumentClick", "getCircleClass", "getCircleTitle", "index", "ɵɵdirectiveInject", "i1", "AuthService", "i2", "MessageService", "i3", "NotificationService", "selectors", "hostBindings", "QscMainComponent_HostBindings", "rf", "ctx", "QscMainComponent_keydown_HostBindingHandler", "ɵɵresolveDocument", "QscMainComponent_click_HostBindingHandler", "QscMainComponent_Template_div_click_2_listener", "QscMainComponent_Template_div_contextmenu_2_listener", "QscMainComponent_Template_div_touchstart_2_listener", "QscMainComponent_Template_div_touchend_2_listener", "QscMainComponent_Template_div_touchmove_2_listener", "QscMainComponent_div_4_Template", "QscMainComponent_div_5_Template", "QscMainComponent_div_6_Template", "QscMainComponent_div_7_Template", "QscMainComponent_div_8_Template", "QscMainComponent_div_9_Template", "QscMainComponent_div_10_Template", "QscMainComponent_div_11_Template", "QscMainComponent_div_12_Template", "ɵɵclassMap", "i4", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "DatePipe", "i5", "DefaultValueAccessor", "NgControlStatus", "MaxLengthValidator", "NgModel", "styles"], "sources": ["C:\\Users\\<USER>\\Projects\\QSC1\\frontend\\src\\app\\components\\qsc-main\\qsc-main.component.ts", "C:\\Users\\<USER>\\Projects\\QSC1\\frontend\\src\\app\\components\\qsc-main\\qsc-main.component.html"], "sourcesContent": ["import { Component, OnInit, On<PERSON><PERSON>roy, HostListener } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { Subject, takeUntil, take } from 'rxjs';\nimport { AuthService } from '../../services/auth.service';\nimport { MessageService } from '../../services/message.service';\nimport { NotificationService } from '../../services/notification.service';\nimport { AuthState, User } from '../../types/auth.types';\n\nexport type CircleState = 'guest' | 'authenticated' | 'unread' | 'composing';\n\ninterface LoginCredentials {\n  username: string;\n  secretWord: string;\n}\n\ninterface Message {\n  id: string;\n  content: string;\n  timestamp: Date;\n  sender: string;\n  recipient?: string;\n  groupId?: string;\n  read?: boolean;\n}\n\ninterface Contact {\n  id: string;\n  username: string;\n  email: string;\n  publicKey?: string;\n  isOnline?: boolean;\n  lastSeen?: Date;\n}\n\ninterface Group {\n  id: string;\n  name: string;\n  members: string[];\n  isActive: boolean;\n}\n\n@Component({\n  selector: 'app-qsc-main',\n  standalone: true,\n  imports: [CommonModule, FormsModule],\n  templateUrl: './qsc-main.component.html',\n  styleUrls: ['./qsc-main.component.scss']\n})\nexport class QscMainComponent implements OnInit, OnDestroy {\n  private destroy$ = new Subject<void>();\n\n  // Circle state management\n  circleState: CircleState = 'guest';\n\n  // Modal states\n  showLoginModal = false;\n  showMessageModal = false;\n  showMessagesModal = false;\n  showContextMenu = false;\n  showAccountSettings = false;\n\n  // Context menu position\n  contextMenuPosition = { x: 0, y: 0 };\n\n  // Authentication\n  loginCredentials: LoginCredentials = { username: '', secretWord: '' };\n  loginError = '';\n  isLoading = false;\n\n  // Messaging\n  messageContent = '';\n  selectedRecipient = '';\n  selectedGroup = '';\n  messageType: 'direct' | 'group' = 'direct';\n  messages: Message[] = [];\n  unreadCount = 0;\n\n  // Contacts and Groups\n  contacts: Contact[] = [];\n  groups: Group[] = [];\n  filteredContacts: Contact[] = [];\n  filteredGroups: Group[] = [];\n  recipientSearchQuery = '';\n\n  // Getter for template access\n  get userGroups(): Group[] {\n    return this.groups;\n  }\n\n  // User info\n  currentUser: User | null = null;\n\n  // Account settings\n  userProfile = {\n    avatar: '',\n    email: '',\n    phone: ''\n  };\n\n  // Long press handling\n  private longPressTimer: any = null;\n  private longPressDuration = 500; // 500ms for long press\n\n  constructor(\n    private authService: AuthService,\n    private messageService: MessageService,\n    private notificationService: NotificationService\n  ) {}\n\n  ngOnInit() {\n    this.initializeApp();\n    this.setupMessageListener();\n    this.setupAuthListener();\n  }\n\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n\n  private initializeApp() {\n    // Check authentication state and set initial circle state\n    this.authService.authState$.pipe(take(1)).subscribe((authState: AuthState) => {\n      if (authState.isAuthenticated && authState.user) {\n        this.currentUser = authState.user;\n        this.circleState = 'authenticated';\n        this.loadMessages();\n        this.loadContacts();\n        this.loadGroups();\n        this.loadUserProfile();\n      } else {\n        // Guest state - show red circle\n        this.circleState = 'guest';\n        this.currentUser = null;\n      }\n    });\n  }\n\n  private setupAuthListener() {\n    this.authService.authState$\n      .pipe(takeUntil(this.destroy$))\n      .subscribe((authState: AuthState) => {\n        if (authState.isAuthenticated && authState.user) {\n          this.currentUser = authState.user;\n          this.circleState = 'authenticated';\n          this.showLoginModal = false;\n          this.loadMessages();\n        } else {\n          this.currentUser = null;\n          this.circleState = 'guest';\n          this.messages = [];\n          this.unreadCount = 0;\n        }\n      });\n  }\n\n  private setupMessageListener() {\n    this.messageService.messages$\n      .pipe(takeUntil(this.destroy$))\n      .subscribe(messages => {\n        this.messages = messages;\n        this.updateUnreadCount();\n      });\n\n    this.messageService.newMessage$\n      .pipe(takeUntil(this.destroy$))\n      .subscribe(message => {\n        this.messages.unshift(message);\n        this.updateUnreadCount();\n        this.notificationService.showNotification('New message received');\n      });\n  }\n\n  private updateUnreadCount() {\n    this.unreadCount = this.messages.filter(m => !m.read).length;\n    if (this.unreadCount > 0 && this.circleState === 'authenticated') {\n      this.circleState = 'unread';\n    } else if (this.unreadCount === 0 && this.circleState === 'unread') {\n      this.circleState = 'authenticated';\n    }\n  }\n\n  private loadMessages() {\n    this.messageService.loadMessages()\n      .pipe(takeUntil(this.destroy$))\n      .subscribe({\n        next: (messages) => {\n          this.messages = messages;\n          this.updateUnreadCount();\n        },\n        error: (error) => {\n          console.error('Failed to load messages:', error);\n        }\n      });\n  }\n\n  private loadContacts() {\n    // TODO: Replace with actual API call\n    this.contacts = [\n      {\n        id: '1',\n        username: 'alice',\n        email: '<EMAIL>',\n        isOnline: true\n      },\n      {\n        id: '2',\n        username: 'bob',\n        email: '<EMAIL>',\n        isOnline: false,\n        lastSeen: new Date(Date.now() - 300000) // 5 minutes ago\n      }\n    ];\n    this.filteredContacts = [...this.contacts];\n  }\n\n  private loadGroups() {\n    // TODO: Replace with actual API call\n    // Temporarily enable groups to test the interface\n    this.groups = [\n      {\n        id: 'group1',\n        name: 'Work Team',\n        members: ['1', '2', 'current-user'],\n        isActive: true\n      },\n      {\n        id: 'group2',\n        name: 'Family',\n        members: ['3', '4', 'current-user'],\n        isActive: true\n      }\n    ];\n\n    // Set to empty array to hide group selector:\n    // this.groups = [];\n\n    this.filteredGroups = [...this.groups];\n  }\n\n  private loadUserProfile() {\n    if (this.currentUser) {\n      this.userProfile = {\n        avatar: '',\n        email: this.currentUser.email || '',\n        phone: '' // TODO: Add phone to user profile\n      };\n    }\n  }\n\n  // Circle click handler - main interaction point\n  onCircleClick() {\n    // Don't handle click if context menu is showing\n    if (this.showContextMenu) {\n      this.closeContextMenu();\n      return;\n    }\n\n    switch (this.circleState) {\n      case 'guest':\n        this.openLoginModal();\n        break;\n      case 'authenticated':\n        this.openMessageComposer();\n        break;\n      case 'unread':\n        this.openMessagesViewer();\n        break;\n      case 'composing':\n        // Already composing, do nothing or close\n        break;\n    }\n  }\n\n  // Right click handler\n  onCircleRightClick(event: MouseEvent) {\n    event.preventDefault();\n\n    // Only show context menu for authenticated users\n    if (this.circleState === 'guest') return;\n\n    this.showContextMenu = true;\n    this.contextMenuPosition = {\n      x: event.clientX,\n      y: event.clientY\n    };\n  }\n\n  // Touch event handlers for long press\n  onCircleTouchStart(event: TouchEvent) {\n    // Only for authenticated users\n    if (this.circleState === 'guest') return;\n\n    this.longPressTimer = setTimeout(() => {\n      const touch = event.touches[0];\n      this.showContextMenu = true;\n      this.contextMenuPosition = {\n        x: touch.clientX,\n        y: touch.clientY\n      };\n\n      // Provide haptic feedback if available\n      if (navigator.vibrate) {\n        navigator.vibrate(50);\n      }\n    }, this.longPressDuration);\n  }\n\n  onCircleTouchEnd() {\n    if (this.longPressTimer) {\n      clearTimeout(this.longPressTimer);\n      this.longPressTimer = null;\n    }\n  }\n\n  onCircleTouchMove() {\n    // Cancel long press if user moves finger\n    if (this.longPressTimer) {\n      clearTimeout(this.longPressTimer);\n      this.longPressTimer = null;\n    }\n  }\n\n  // Authentication methods\n  openLoginModal() {\n    this.showLoginModal = true;\n    this.loginCredentials = { username: '', secretWord: '' };\n    this.loginError = '';\n  }\n\n  closeLoginModal() {\n    this.showLoginModal = false;\n    this.loginCredentials = { username: '', secretWord: '' };\n    this.loginError = '';\n  }\n\n  onLoginInputChange() {\n    // Auto-submit when both fields are valid\n    if (this.isValidCredentials()) {\n      this.performLogin();\n    }\n  }\n\n  private isValidCredentials(): boolean {\n    const usernameValid = this.loginCredentials.username.length >= 3;\n    // QSC secret word: exactly 4 characters with one from each character class\n    const secretWordValid = this.loginCredentials.secretWord.length === 4 &&\n                           /[A-Z]/.test(this.loginCredentials.secretWord) &&\n                           /[a-z]/.test(this.loginCredentials.secretWord) &&\n                           /[0-9]/.test(this.loginCredentials.secretWord) &&\n                           /[^A-Za-z0-9]/.test(this.loginCredentials.secretWord);\n\n    return usernameValid && secretWordValid;\n  }\n\n  private performLogin() {\n    if (this.isLoading) return;\n\n    this.isLoading = true;\n    this.loginError = '';\n\n    this.authService.login(this.loginCredentials.username, this.loginCredentials.secretWord)\n      .pipe(takeUntil(this.destroy$))\n      .subscribe({\n        next: (response) => {\n          this.isLoading = false;\n          // Auth state will be updated via authState$ subscription\n        },\n        error: (error) => {\n          this.isLoading = false;\n          this.loginError = error.message || 'Authentication failed';\n        }\n      });\n  }\n\n  // Message composition methods\n  openMessageComposer() {\n    this.showMessageModal = true;\n    this.messageContent = '';\n    this.selectedRecipient = '';\n    this.selectedGroup = '';\n    this.messageType = 'direct';\n    this.recipientSearchQuery = '';\n    this.filteredContacts = [...this.contacts];\n    this.filteredGroups = [...this.groups];\n    this.circleState = 'composing';\n  }\n\n  closeMessageComposer() {\n    this.showMessageModal = false;\n    this.messageContent = '';\n    this.selectedRecipient = '';\n    this.selectedGroup = '';\n    this.recipientSearchQuery = '';\n    this.circleState = 'authenticated';\n  }\n\n  sendMessage() {\n    if (!this.messageContent.trim()) return;\n\n    // Validate recipient selection\n    if (this.messageType === 'direct' && !this.selectedRecipient) {\n      this.notificationService.showNotification('Please select a recipient', 'warning');\n      return;\n    }\n\n    if (this.messageType === 'group' && !this.selectedGroup) {\n      this.notificationService.showNotification('Please select a group', 'warning');\n      return;\n    }\n\n    const message: Partial<Message> = {\n      content: this.messageContent.trim(),\n      timestamp: new Date(),\n      sender: this.currentUser?.username || 'Unknown',\n      recipient: this.messageType === 'direct' ? this.selectedRecipient : undefined,\n      groupId: this.messageType === 'group' ? this.selectedGroup : undefined\n    };\n\n    this.messageService.sendMessage(message)\n      .pipe(takeUntil(this.destroy$))\n      .subscribe({\n        next: () => {\n          this.closeMessageComposer();\n          this.notificationService.showNotification('Message sent');\n        },\n        error: (error) => {\n          console.error('Failed to send message:', error);\n          this.notificationService.showNotification('Failed to send message', 'error');\n        }\n      });\n  }\n\n  // Recipient selection methods\n  onRecipientSearchChange() {\n    const query = this.recipientSearchQuery.toLowerCase();\n\n    if (this.messageType === 'direct') {\n      this.filteredContacts = this.contacts.filter(contact =>\n        contact.username.toLowerCase().includes(query) ||\n        contact.email.toLowerCase().includes(query)\n      );\n    } else {\n      this.filteredGroups = this.groups.filter(group =>\n        group.name.toLowerCase().includes(query)\n      );\n    }\n  }\n\n  selectContact(contact: Contact) {\n    this.selectedRecipient = contact.id;\n    this.recipientSearchQuery = contact.username;\n    this.filteredContacts = [];\n  }\n\n  selectGroup(group: Group) {\n    this.selectedGroup = group.id;\n    this.recipientSearchQuery = group.name;\n    this.filteredGroups = [];\n  }\n\n  switchMessageType(type: 'direct' | 'group') {\n    this.messageType = type;\n    this.selectedRecipient = '';\n    this.selectedGroup = '';\n    this.recipientSearchQuery = '';\n    this.onRecipientSearchChange();\n  }\n\n  getSelectedRecipientName(): string {\n    if (this.messageType === 'direct' && this.selectedRecipient) {\n      const contact = this.contacts.find(c => c.id === this.selectedRecipient);\n      return contact?.username || 'Unknown';\n    }\n\n    if (this.messageType === 'group' && this.selectedGroup) {\n      const group = this.groups.find(g => g.id === this.selectedGroup);\n      return group?.name || 'Unknown Group';\n    }\n\n    return '';\n  }\n\n  isMessageValid(): boolean {\n    const hasContent = this.messageContent.trim().length > 0;\n    const hasRecipient = this.messageType === 'direct' ? !!this.selectedRecipient : !!this.selectedGroup;\n    return hasContent && hasRecipient;\n  }\n\n  // Message viewing methods\n  openMessagesViewer() {\n    this.showMessagesModal = true;\n    this.markMessagesAsRead();\n  }\n\n  closeMessagesViewer() {\n    this.showMessagesModal = false;\n  }\n\n  private markMessagesAsRead() {\n    this.messageService.markAllAsRead()\n      .pipe(takeUntil(this.destroy$))\n      .subscribe(() => {\n        this.updateUnreadCount();\n      });\n  }\n\n  // Context menu methods\n  closeContextMenu() {\n    this.showContextMenu = false;\n  }\n\n  openAccountSettings() {\n    this.showAccountSettings = true;\n    this.showContextMenu = false;\n    this.loadUserProfile();\n  }\n\n  closeAccountSettings() {\n    this.showAccountSettings = false;\n  }\n\n  // Account settings methods\n\n  onAvatarChange(event: Event) {\n    const input = event.target as HTMLInputElement;\n    if (input.files && input.files[0]) {\n      const file = input.files[0];\n\n      // Validate file type\n      if (!file.type.startsWith('image/')) {\n        this.notificationService.showNotification('Please select an image file', 'error');\n        return;\n      }\n\n      // Validate file size (max 2MB)\n      if (file.size > 2 * 1024 * 1024) {\n        this.notificationService.showNotification('Image must be smaller than 2MB', 'error');\n        return;\n      }\n\n      const reader = new FileReader();\n      reader.onload = (e) => {\n        this.userProfile.avatar = e.target?.result as string;\n        this.saveAvatarChange();\n      };\n      reader.readAsDataURL(file);\n    }\n  }\n\n  saveAvatarChange() {\n    // TODO: Implement API call to save avatar\n    this.notificationService.showNotification('Avatar updated successfully', 'success');\n  }\n\n  // Logout\n  logout() {\n    this.closeContextMenu();\n    this.authService.logout();\n  }\n\n  // Keyboard shortcuts\n  @HostListener('document:keydown', ['$event'])\n  onKeyDown(event: KeyboardEvent) {\n    // Escape key closes modals\n    if (event.key === 'Escape') {\n      this.closeAllModals();\n    }\n\n    // Enter key in login modal\n    if (event.key === 'Enter' && this.showLoginModal) {\n      if (this.isValidCredentials()) {\n        this.performLogin();\n      }\n    }\n\n    // Enter key in message modal\n    if (event.key === 'Enter' && this.showMessageModal && !event.shiftKey) {\n      event.preventDefault();\n      this.sendMessage();\n    }\n  }\n\n  // Click outside handler to close context menu\n  @HostListener('document:click', ['$event'])\n  onDocumentClick(event: Event) {\n    // Close context menu when clicking outside\n    if (this.showContextMenu) {\n      this.closeContextMenu();\n    }\n  }\n\n  private closeAllModals() {\n    this.showLoginModal = false;\n    this.showMessageModal = false;\n    this.showMessagesModal = false;\n    this.showContextMenu = false;\n    this.showAccountSettings = false;\n    if (this.circleState === 'composing') {\n      this.circleState = 'authenticated';\n    }\n  }\n\n  // Utility methods\n  getCircleClass(): string {\n    return `circle-${this.circleState}`;\n  }\n\n  getCircleTitle(): string {\n    switch (this.circleState) {\n      case 'guest': return 'Click to sign in';\n      case 'authenticated': return 'Click to compose message';\n      case 'unread': return `Click to view ${this.unreadCount} unread message(s)`;\n      case 'composing': return 'Composing message...';\n      default: return '';\n    }\n  }\n\n  trackMessage(index: number, message: Message): string {\n    return message.id;\n  }\n}\n", "<!-- Main Container -->\n<div class=\"qsc-container\">\n  <!-- Central Circle -->\n  <div class=\"circle-container\">\n    <div\n      class=\"qsc-circle\"\n      [class]=\"getCircleClass()\"\n      [title]=\"getCircleTitle()\"\n      (click)=\"onCircleClick()\"\n      (contextmenu)=\"onCircleRightClick($event)\"\n      (touchstart)=\"onCircleTouchStart($event)\"\n      (touchend)=\"onCircleTouchEnd()\"\n      (touchmove)=\"onCircleTouchMove()\"\n    >\n      <div class=\"circle-inner\">\n        <div class=\"wind-effect\" *ngIf=\"circleState !== 'guest'\"></div>\n        <div class=\"unread-indicator\" *ngIf=\"circleState === 'unread'\">\n          {{ unreadCount }}\n        </div>\n      </div>\n    </div>\n  </div>\n\n  <!-- User Info (subtle, top-right) -->\n  <div class=\"user-info\" *ngIf=\"currentUser\">\n    <span>{{ currentUser.username }}</span>\n    <button class=\"logout-btn\" (click)=\"logout()\" title=\"Logout\">×</button>\n  </div>\n\n  <!-- Context Menu -->\n  <div\n    class=\"context-menu\"\n    *ngIf=\"showContextMenu\"\n    [style.left.px]=\"contextMenuPosition.x\"\n    [style.top.px]=\"contextMenuPosition.y\"\n    (click)=\"$event.stopPropagation()\"\n  >\n    <div class=\"context-menu-item\" (click)=\"openAccountSettings()\">\n      <span class=\"menu-icon\">👤</span>\n      <span class=\"menu-text\">Account Settings</span>\n    </div>\n    <div class=\"context-menu-divider\"></div>\n    <div class=\"context-menu-item logout-item\" (click)=\"logout()\">\n      <span class=\"menu-icon\">🚪</span>\n      <span class=\"menu-text\">Logout</span>\n    </div>\n  </div>\n</div>\n\n<!-- Login Modal -->\n<div class=\"modal-overlay\" *ngIf=\"showLoginModal\" (click)=\"closeLoginModal()\">\n  <div class=\"modal login-modal\" (click)=\"$event.stopPropagation()\">\n    <button class=\"close-btn\" (click)=\"closeLoginModal()\">×</button>\n\n    <div class=\"modal-content\">\n      <div class=\"form-group\">\n        <input\n          type=\"text\"\n          id=\"username\"\n          [(ngModel)]=\"loginCredentials.username\"\n          (input)=\"onLoginInputChange()\"\n          placeholder=\"Username\"\n          [disabled]=\"isLoading\"\n          autocomplete=\"username\"\n        />\n      </div>\n\n      <div class=\"form-group\">\n        <input\n          type=\"password\"\n          id=\"secretWord\"\n          [(ngModel)]=\"loginCredentials.secretWord\"\n          (input)=\"onLoginInputChange()\"\n          placeholder=\"Secret Word (4 chars: A-Z, a-z, 0-9, symbol)\"\n          [disabled]=\"isLoading\"\n          autocomplete=\"current-password\"\n        />\n      </div>\n\n      <div class=\"error-message\" *ngIf=\"loginError\">\n        {{ loginError }}\n      </div>\n\n      <div class=\"loading-indicator\" *ngIf=\"isLoading\">\n        <div class=\"spinner\"></div>\n        <span>Authenticating...</span>\n      </div>\n\n      <div class=\"auth-info\" *ngIf=\"!isLoading\">\n        <p>🔒 Protected by post-quantum cryptography</p>\n        <p class=\"auto-submit-hint\">Form auto-submits when credentials are valid</p>\n      </div>\n    </div>\n  </div>\n</div>\n\n<!-- Message Composer Modal -->\n<div class=\"modal-overlay\" *ngIf=\"showMessageModal\" (click)=\"closeMessageComposer()\">\n  <div class=\"modal message-modal\" (click)=\"$event.stopPropagation()\">\n    <button class=\"close-btn\" (click)=\"closeMessageComposer()\" title=\"Close\">×</button>\n    <div class=\"modal-content\">\n      <!-- Message Type Selector - Only show when user has groups -->\n      <div class=\"message-type-selector\" *ngIf=\"userGroups.length > 0\">\n        <button\n          class=\"type-btn direct-btn\"\n          (click)=\"switchMessageType('direct')\"\n          [class.active]=\"messageType === 'direct'\"\n        >\n          <svg class=\"icon\" viewBox=\"0 0 24 24\">\n            <path d=\"M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2\"/>\n            <circle cx=\"12\" cy=\"7\" r=\"4\"/>\n          </svg>\n          Direct\n        </button>\n        <button\n          class=\"type-btn group-btn\"\n          (click)=\"switchMessageType('group')\"\n          [class.active]=\"messageType === 'group'\"\n        >\n          <svg class=\"icon\" viewBox=\"0 0 24 24\">\n            <path d=\"M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2\"/>\n            <circle cx=\"9\" cy=\"7\" r=\"4\"/>\n            <path d=\"M23 21v-2a4 4 0 0 0-3-3.87\"/>\n            <path d=\"M16 3.13a4 4 0 0 1 0 7.75\"/>\n          </svg>\n          Group\n        </button>\n      </div>\n\n      <!-- Recipient Selection -->\n      <div class=\"form-group\">\n        <div class=\"recipient-selector\">\n          <input\n            type=\"text\"\n            id=\"recipientSearch\"\n            [(ngModel)]=\"recipientSearchQuery\"\n            (input)=\"onRecipientSearchChange()\"\n            [placeholder]=\"messageType === 'direct' ? 'Search contacts...' : 'Search groups...'\"\n            autocomplete=\"off\"\n          />\n\n          <!-- Selected Recipient Display -->\n          <div class=\"selected-recipient\" *ngIf=\"getSelectedRecipientName()\">\n            <span class=\"recipient-name\">{{ getSelectedRecipientName() }}</span>\n            <button\n              class=\"clear-recipient\"\n              (click)=\"switchMessageType(messageType)\"\n              title=\"Clear selection\"\n            >×</button>\n          </div>\n\n          <!-- Contact/Group Dropdown -->\n          <div class=\"recipient-dropdown\" *ngIf=\"recipientSearchQuery && !getSelectedRecipientName()\">\n            <!-- Direct Message Contacts -->\n            <div\n              class=\"recipient-item\"\n              *ngFor=\"let contact of filteredContacts\"\n              (click)=\"selectContact(contact)\"\n              [hidden]=\"messageType !== 'direct'\"\n            >\n              <div class=\"contact-info\">\n                <span class=\"contact-name\">{{ contact.username }}</span>\n                <span class=\"contact-email\">{{ contact.email }}</span>\n              </div>\n              <div class=\"contact-status\">\n                <span\n                  class=\"status-indicator\"\n                  [class.online]=\"contact.isOnline\"\n                  [class.offline]=\"!contact.isOnline\"\n                ></span>\n                <span class=\"status-text\">\n                  {{ contact.isOnline ? 'Online' : 'Offline' }}\n                </span>\n              </div>\n            </div>\n\n            <!-- Group Chats -->\n            <div\n              class=\"recipient-item\"\n              *ngFor=\"let group of filteredGroups\"\n              (click)=\"selectGroup(group)\"\n              [hidden]=\"messageType !== 'group'\"\n            >\n              <div class=\"group-info\">\n                <span class=\"group-name\">{{ group.name }}</span>\n                <span class=\"group-members\">{{ group.members.length }} members</span>\n              </div>\n              <div class=\"group-status\">\n                <span\n                  class=\"status-indicator\"\n                  [class.active]=\"group.isActive\"\n                ></span>\n              </div>\n            </div>\n\n            <!-- No Results -->\n            <div class=\"no-results\" *ngIf=\"messageType === 'direct' && filteredContacts.length === 0\">\n              No contacts found\n            </div>\n            <div class=\"no-results\" *ngIf=\"messageType === 'group' && filteredGroups.length === 0\">\n              No groups found\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- Message Content -->\n      <div class=\"form-group\">\n        <textarea\n          id=\"messageContent\"\n          [(ngModel)]=\"messageContent\"\n          placeholder=\"Type your message here...\"\n          rows=\"6\"\n          maxlength=\"1000\"\n        ></textarea>\n        <div class=\"char-count\">{{ messageContent.length }}/1000</div>\n      </div>\n\n      <div class=\"message-actions\">\n        <button\n          class=\"btn btn-primary\"\n          (click)=\"sendMessage()\"\n          [disabled]=\"!isMessageValid()\"\n        >\n          <svg class=\"icon\" viewBox=\"0 0 24 24\">\n            <line x1=\"22\" y1=\"2\" x2=\"11\" y2=\"13\"/>\n            <polygon points=\"22,2 15,22 11,13 2,9\"/>\n          </svg>\n          Send\n        </button>\n        <button class=\"btn btn-secondary\" (click)=\"closeMessageComposer()\">\n          <svg class=\"icon\" viewBox=\"0 0 24 24\">\n            <line x1=\"18\" y1=\"6\" x2=\"6\" y2=\"18\"/>\n            <line x1=\"6\" y1=\"6\" x2=\"18\" y2=\"18\"/>\n          </svg>\n          Cancel\n        </button>\n      </div>\n\n      <div class=\"send-hint\">\n        <p>Press Enter to send • Shift+Enter for new line</p>\n      </div>\n    </div>\n  </div>\n</div>\n\n<!-- Messages Viewer Modal -->\n<div class=\"modal-overlay\" *ngIf=\"showMessagesModal\" (click)=\"closeMessagesViewer()\">\n  <div class=\"modal messages-modal\" (click)=\"$event.stopPropagation()\">\n    <button class=\"close-btn\" (click)=\"closeMessagesViewer()\">×</button>\n\n    <div class=\"modal-content\">\n      <div class=\"messages-list\" *ngIf=\"messages.length > 0\">\n        <div\n          class=\"message-item\"\n          *ngFor=\"let message of messages; trackBy: trackMessage\"\n        >\n          <div class=\"message-header\">\n            <span class=\"sender\">{{ message.sender }}</span>\n            <span class=\"timestamp\">{{ message.timestamp | date:'short' }}</span>\n          </div>\n          <div class=\"message-content\">{{ message.content }}</div>\n        </div>\n      </div>\n\n      <div class=\"empty-state\" *ngIf=\"messages.length === 0\">\n        <p>No messages yet</p>\n        <p class=\"hint\">Click the circle to compose your first message</p>\n      </div>\n    </div>\n  </div>\n</div>\n\n<!-- Account Settings Modal -->\n<div class=\"modal-overlay\" *ngIf=\"showAccountSettings\" (click)=\"closeAccountSettings()\">\n  <div class=\"modal account-settings-modal\" (click)=\"$event.stopPropagation()\">\n    <button class=\"close-btn\" (click)=\"closeAccountSettings()\" title=\"Close\">×</button>\n\n    <div class=\"modal-content\">\n      <!-- Avatar Section -->\n      <div class=\"avatar-section\">\n        <div class=\"avatar-container\">\n          <div class=\"avatar-display\">\n            <img\n              *ngIf=\"userProfile.avatar\"\n              [src]=\"userProfile.avatar\"\n              alt=\"Profile Avatar\"\n              class=\"avatar-image\"\n            />\n            <div *ngIf=\"!userProfile.avatar\" class=\"avatar-placeholder\">\n              <span class=\"avatar-initials\">\n                {{ currentUser?.username?.charAt(0)?.toUpperCase() || '?' }}\n              </span>\n            </div>\n          </div>\n          <div class=\"avatar-actions\">\n            <label for=\"avatarInput\" class=\"avatar-upload-btn\">\n              📷 Change Avatar\n            </label>\n            <input\n              type=\"file\"\n              id=\"avatarInput\"\n              accept=\"image/*\"\n              (change)=\"onAvatarChange($event)\"\n              style=\"display: none;\"\n            />\n          </div>\n        </div>\n      </div>\n\n      <!-- Profile Information -->\n      <div class=\"profile-info\">\n        <div class=\"form-group\">\n          <div class=\"readonly-field\">\n            <span>{{ currentUser?.username || 'Not set' }}</span>\n          </div>\n        </div>\n\n        <div class=\"form-group\">\n          <div class=\"readonly-field\">\n            <span>{{ userProfile.email || 'Not set' }}</span>\n            <span class=\"field-note\">Email cannot be changed for security reasons</span>\n          </div>\n        </div>\n\n        <div class=\"form-group\">\n          <div class=\"readonly-field\">\n            <span>{{ userProfile.phone || 'Not set' }}</span>\n            <span class=\"field-note\">Phone number cannot be changed for security reasons</span>\n          </div>\n        </div>\n      </div>\n\n      <!-- Security Information -->\n      <div class=\"security-info\">\n        <h3>Security Information</h3>\n        <div class=\"security-item\">\n          <span class=\"security-label\">🔐 Encryption:</span>\n          <span class=\"security-value\">Post-Quantum Cryptography (ML-DSA, ML-KEM)</span>\n        </div>\n        <div class=\"security-item\">\n          <span class=\"security-label\">🛡️ Security Level:</span>\n          <span class=\"security-value\">NIST Level 3 (AES-192 equivalent)</span>\n        </div>\n        <div class=\"security-item\">\n          <span class=\"security-label\">🔑 Key Rotation:</span>\n          <span class=\"security-value\">Every 30 days</span>\n        </div>\n      </div>\n\n      <div class=\"account-actions\">\n        <button class=\"btn btn-secondary\" (click)=\"closeAccountSettings()\">\n          <svg class=\"icon\" viewBox=\"0 0 24 24\">\n            <path d=\"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4\"/>\n            <polyline points=\"16,17 21,12 16,7\"/>\n            <line x1=\"21\" y1=\"12\" x2=\"9\" y2=\"12\"/>\n          </svg>\n          Close\n        </button>\n      </div>\n    </div>\n  </div>\n</div>\n\n<!-- Keyboard Hints (bottom) -->\n<div class=\"keyboard-hints\" *ngIf=\"!showLoginModal && !showMessageModal && !showMessagesModal && !showAccountSettings\">\n  <span>ESC to close modals • Right-click circle for menu</span>\n</div>\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,OAAO,EAAEC,SAAS,EAAEC,IAAI,QAAQ,MAAM;;;;;;;;;ICYvCC,EAAA,CAAAC,SAAA,cAA+D;;;;;IAC/DD,EAAA,CAAAE,cAAA,cAA+D;IAC7DF,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;IADJJ,EAAA,CAAAK,SAAA,EACF;IADEL,EAAA,CAAAM,kBAAA,MAAAC,MAAA,CAAAC,WAAA,MACF;;;;;;IAOJR,EADF,CAAAE,cAAA,cAA2C,WACnC;IAAAF,EAAA,CAAAG,MAAA,GAA0B;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACvCJ,EAAA,CAAAE,cAAA,iBAA6D;IAAlCF,EAAA,CAAAS,UAAA,mBAAAC,wDAAA;MAAAV,EAAA,CAAAW,aAAA,CAAAC,GAAA;MAAA,MAAAL,MAAA,GAAAP,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASP,MAAA,CAAAQ,MAAA,EAAQ;IAAA,EAAC;IAAgBf,EAAA,CAAAG,MAAA,aAAC;IAChEH,EADgE,CAAAI,YAAA,EAAS,EACnE;;;;IAFEJ,EAAA,CAAAK,SAAA,GAA0B;IAA1BL,EAAA,CAAAgB,iBAAA,CAAAT,MAAA,CAAAU,WAAA,CAAAC,QAAA,CAA0B;;;;;;IAKlClB,EAAA,CAAAE,cAAA,cAMC;IADCF,EAAA,CAAAS,UAAA,mBAAAU,qDAAAC,MAAA;MAAApB,EAAA,CAAAW,aAAA,CAAAU,GAAA;MAAA,OAAArB,EAAA,CAAAc,WAAA,CAASM,MAAA,CAAAE,eAAA,EAAwB;IAAA,EAAC;IAElCtB,EAAA,CAAAE,cAAA,cAA+D;IAAhCF,EAAA,CAAAS,UAAA,mBAAAc,qDAAA;MAAAvB,EAAA,CAAAW,aAAA,CAAAU,GAAA;MAAA,MAAAd,MAAA,GAAAP,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASP,MAAA,CAAAiB,mBAAA,EAAqB;IAAA,EAAC;IAC5DxB,EAAA,CAAAE,cAAA,eAAwB;IAAAF,EAAA,CAAAG,MAAA,mBAAE;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACjCJ,EAAA,CAAAE,cAAA,eAAwB;IAAAF,EAAA,CAAAG,MAAA,uBAAgB;IAC1CH,EAD0C,CAAAI,YAAA,EAAO,EAC3C;IACNJ,EAAA,CAAAC,SAAA,cAAwC;IACxCD,EAAA,CAAAE,cAAA,cAA8D;IAAnBF,EAAA,CAAAS,UAAA,mBAAAgB,qDAAA;MAAAzB,EAAA,CAAAW,aAAA,CAAAU,GAAA;MAAA,MAAAd,MAAA,GAAAP,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASP,MAAA,CAAAQ,MAAA,EAAQ;IAAA,EAAC;IAC3Df,EAAA,CAAAE,cAAA,eAAwB;IAAAF,EAAA,CAAAG,MAAA,mBAAE;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACjCJ,EAAA,CAAAE,cAAA,gBAAwB;IAAAF,EAAA,CAAAG,MAAA,cAAM;IAElCH,EAFkC,CAAAI,YAAA,EAAO,EACjC,EACF;;;;IAZJJ,EADA,CAAA0B,WAAA,SAAAnB,MAAA,CAAAoB,mBAAA,CAAAC,CAAA,OAAuC,QAAArB,MAAA,CAAAoB,mBAAA,CAAAE,CAAA,OACD;;;;;IA6CpC7B,EAAA,CAAAE,cAAA,cAA8C;IAC5CF,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;IADJJ,EAAA,CAAAK,SAAA,EACF;IADEL,EAAA,CAAAM,kBAAA,MAAAC,MAAA,CAAAuB,UAAA,MACF;;;;;IAEA9B,EAAA,CAAAE,cAAA,cAAiD;IAC/CF,EAAA,CAAAC,SAAA,cAA2B;IAC3BD,EAAA,CAAAE,cAAA,WAAM;IAAAF,EAAA,CAAAG,MAAA,wBAAiB;IACzBH,EADyB,CAAAI,YAAA,EAAO,EAC1B;;;;;IAGJJ,EADF,CAAAE,cAAA,cAA0C,QACrC;IAAAF,EAAA,CAAAG,MAAA,0DAAyC;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAChDJ,EAAA,CAAAE,cAAA,YAA4B;IAAAF,EAAA,CAAAG,MAAA,mDAA4C;IAC1EH,EAD0E,CAAAI,YAAA,EAAI,EACxE;;;;;;IAzCZJ,EAAA,CAAAE,cAAA,cAA8E;IAA5BF,EAAA,CAAAS,UAAA,mBAAAsB,qDAAA;MAAA/B,EAAA,CAAAW,aAAA,CAAAqB,GAAA;MAAA,MAAAzB,MAAA,GAAAP,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASP,MAAA,CAAA0B,eAAA,EAAiB;IAAA,EAAC;IAC3EjC,EAAA,CAAAE,cAAA,cAAkE;IAAnCF,EAAA,CAAAS,UAAA,mBAAAyB,qDAAAd,MAAA;MAAApB,EAAA,CAAAW,aAAA,CAAAqB,GAAA;MAAA,OAAAhC,EAAA,CAAAc,WAAA,CAASM,MAAA,CAAAE,eAAA,EAAwB;IAAA,EAAC;IAC/DtB,EAAA,CAAAE,cAAA,iBAAsD;IAA5BF,EAAA,CAAAS,UAAA,mBAAA0B,wDAAA;MAAAnC,EAAA,CAAAW,aAAA,CAAAqB,GAAA;MAAA,MAAAzB,MAAA,GAAAP,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASP,MAAA,CAAA0B,eAAA,EAAiB;IAAA,EAAC;IAACjC,EAAA,CAAAG,MAAA,aAAC;IAAAH,EAAA,CAAAI,YAAA,EAAS;IAI5DJ,EAFJ,CAAAE,cAAA,cAA2B,cACD,gBASpB;IALAF,EAAA,CAAAoC,gBAAA,2BAAAC,+DAAAjB,MAAA;MAAApB,EAAA,CAAAW,aAAA,CAAAqB,GAAA;MAAA,MAAAzB,MAAA,GAAAP,EAAA,CAAAa,aAAA;MAAAb,EAAA,CAAAsC,kBAAA,CAAA/B,MAAA,CAAAgC,gBAAA,CAAArB,QAAA,EAAAE,MAAA,MAAAb,MAAA,CAAAgC,gBAAA,CAAArB,QAAA,GAAAE,MAAA;MAAA,OAAApB,EAAA,CAAAc,WAAA,CAAAM,MAAA;IAAA,EAAuC;IACvCpB,EAAA,CAAAS,UAAA,mBAAA+B,uDAAA;MAAAxC,EAAA,CAAAW,aAAA,CAAAqB,GAAA;MAAA,MAAAzB,MAAA,GAAAP,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASP,MAAA,CAAAkC,kBAAA,EAAoB;IAAA,EAAC;IAKlCzC,EATE,CAAAI,YAAA,EAQE,EACE;IAGJJ,EADF,CAAAE,cAAA,cAAwB,gBASpB;IALAF,EAAA,CAAAoC,gBAAA,2BAAAM,+DAAAtB,MAAA;MAAApB,EAAA,CAAAW,aAAA,CAAAqB,GAAA;MAAA,MAAAzB,MAAA,GAAAP,EAAA,CAAAa,aAAA;MAAAb,EAAA,CAAAsC,kBAAA,CAAA/B,MAAA,CAAAgC,gBAAA,CAAAI,UAAA,EAAAvB,MAAA,MAAAb,MAAA,CAAAgC,gBAAA,CAAAI,UAAA,GAAAvB,MAAA;MAAA,OAAApB,EAAA,CAAAc,WAAA,CAAAM,MAAA;IAAA,EAAyC;IACzCpB,EAAA,CAAAS,UAAA,mBAAAmC,uDAAA;MAAA5C,EAAA,CAAAW,aAAA,CAAAqB,GAAA;MAAA,MAAAzB,MAAA,GAAAP,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASP,MAAA,CAAAkC,kBAAA,EAAoB;IAAA,EAAC;IAKlCzC,EATE,CAAAI,YAAA,EAQE,EACE;IAWNJ,EATA,CAAA6C,UAAA,IAAAC,qCAAA,kBAA8C,KAAAC,sCAAA,kBAIG,KAAAC,sCAAA,kBAKP;IAMhDhD,EAFI,CAAAI,YAAA,EAAM,EACF,EACF;;;;IAnCIJ,EAAA,CAAAK,SAAA,GAAuC;IAAvCL,EAAA,CAAAiD,gBAAA,YAAA1C,MAAA,CAAAgC,gBAAA,CAAArB,QAAA,CAAuC;IAGvClB,EAAA,CAAAkD,UAAA,aAAA3C,MAAA,CAAA4C,SAAA,CAAsB;IAStBnD,EAAA,CAAAK,SAAA,GAAyC;IAAzCL,EAAA,CAAAiD,gBAAA,YAAA1C,MAAA,CAAAgC,gBAAA,CAAAI,UAAA,CAAyC;IAGzC3C,EAAA,CAAAkD,UAAA,aAAA3C,MAAA,CAAA4C,SAAA,CAAsB;IAKEnD,EAAA,CAAAK,SAAA,EAAgB;IAAhBL,EAAA,CAAAkD,UAAA,SAAA3C,MAAA,CAAAuB,UAAA,CAAgB;IAIZ9B,EAAA,CAAAK,SAAA,EAAe;IAAfL,EAAA,CAAAkD,UAAA,SAAA3C,MAAA,CAAA4C,SAAA,CAAe;IAKvBnD,EAAA,CAAAK,SAAA,EAAgB;IAAhBL,EAAA,CAAAkD,UAAA,UAAA3C,MAAA,CAAA4C,SAAA,CAAgB;;;;;;IAetCnD,EADF,CAAAE,cAAA,cAAiE,iBAK9D;IAFCF,EAAA,CAAAS,UAAA,mBAAA2C,8DAAA;MAAApD,EAAA,CAAAW,aAAA,CAAA0C,GAAA;MAAA,MAAA9C,MAAA,GAAAP,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASP,MAAA,CAAA+C,iBAAA,CAAkB,QAAQ,CAAC;IAAA,EAAC;;IAGrCtD,EAAA,CAAAE,cAAA,cAAsC;IAEpCF,EADA,CAAAC,SAAA,eAAqD,iBACvB;IAChCD,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAG,MAAA,eACF;IAAAH,EAAA,CAAAI,YAAA,EAAS;;IACTJ,EAAA,CAAAE,cAAA,iBAIC;IAFCF,EAAA,CAAAS,UAAA,mBAAA8C,8DAAA;MAAAvD,EAAA,CAAAW,aAAA,CAAA0C,GAAA;MAAA,MAAA9C,MAAA,GAAAP,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASP,MAAA,CAAA+C,iBAAA,CAAkB,OAAO,CAAC;IAAA,EAAC;;IAGpCtD,EAAA,CAAAE,cAAA,cAAsC;IAIpCF,EAHA,CAAAC,SAAA,eAAqD,iBACxB,gBACS,gBACD;IACvCD,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAG,MAAA,eACF;IACFH,EADE,CAAAI,YAAA,EAAS,EACL;;;;IArBFJ,EAAA,CAAAK,SAAA,EAAyC;IAAzCL,EAAA,CAAAwD,WAAA,WAAAjD,MAAA,CAAAkD,WAAA,cAAyC;IAWzCzD,EAAA,CAAAK,SAAA,GAAwC;IAAxCL,EAAA,CAAAwD,WAAA,WAAAjD,MAAA,CAAAkD,WAAA,aAAwC;;;;;;IA0BtCzD,EADF,CAAAE,cAAA,cAAmE,eACpC;IAAAF,EAAA,CAAAG,MAAA,GAAgC;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACpEJ,EAAA,CAAAE,cAAA,iBAIC;IAFCF,EAAA,CAAAS,UAAA,mBAAAiD,8DAAA;MAAA1D,EAAA,CAAAW,aAAA,CAAAgD,GAAA;MAAA,MAAApD,MAAA,GAAAP,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASP,MAAA,CAAA+C,iBAAA,CAAA/C,MAAA,CAAAkD,WAAA,CAA8B;IAAA,EAAC;IAEzCzD,EAAA,CAAAG,MAAA,aAAC;IACJH,EADI,CAAAI,YAAA,EAAS,EACP;;;;IANyBJ,EAAA,CAAAK,SAAA,GAAgC;IAAhCL,EAAA,CAAAgB,iBAAA,CAAAT,MAAA,CAAAqD,wBAAA,GAAgC;;;;;;IAW7D5D,EAAA,CAAAE,cAAA,cAKC;IAFCF,EAAA,CAAAS,UAAA,mBAAAoD,kEAAA;MAAA,MAAAC,UAAA,GAAA9D,EAAA,CAAAW,aAAA,CAAAoD,GAAA,EAAAC,SAAA;MAAA,MAAAzD,MAAA,GAAAP,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASP,MAAA,CAAA0D,aAAA,CAAAH,UAAA,CAAsB;IAAA,EAAC;IAI9B9D,EADF,CAAAE,cAAA,cAA0B,eACG;IAAAF,EAAA,CAAAG,MAAA,GAAsB;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACxDJ,EAAA,CAAAE,cAAA,eAA4B;IAAAF,EAAA,CAAAG,MAAA,GAAmB;IACjDH,EADiD,CAAAI,YAAA,EAAO,EAClD;IACNJ,EAAA,CAAAE,cAAA,cAA4B;IAC1BF,EAAA,CAAAC,SAAA,eAIQ;IACRD,EAAA,CAAAE,cAAA,eAA0B;IACxBF,EAAA,CAAAG,MAAA,GACF;IAEJH,EAFI,CAAAI,YAAA,EAAO,EACH,EACF;;;;;IAhBJJ,EAAA,CAAAkD,UAAA,WAAA3C,MAAA,CAAAkD,WAAA,cAAmC;IAGNzD,EAAA,CAAAK,SAAA,GAAsB;IAAtBL,EAAA,CAAAgB,iBAAA,CAAA8C,UAAA,CAAA5C,QAAA,CAAsB;IACrBlB,EAAA,CAAAK,SAAA,GAAmB;IAAnBL,EAAA,CAAAgB,iBAAA,CAAA8C,UAAA,CAAAI,KAAA,CAAmB;IAK7ClE,EAAA,CAAAK,SAAA,GAAiC;IACjCL,EADA,CAAAwD,WAAA,WAAAM,UAAA,CAAAK,QAAA,CAAiC,aAAAL,UAAA,CAAAK,QAAA,CACE;IAGnCnE,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAwD,UAAA,CAAAK,QAAA,6BACF;;;;;;IAKJnE,EAAA,CAAAE,cAAA,cAKC;IAFCF,EAAA,CAAAS,UAAA,mBAAA2D,kEAAA;MAAA,MAAAC,SAAA,GAAArE,EAAA,CAAAW,aAAA,CAAA2D,IAAA,EAAAN,SAAA;MAAA,MAAAzD,MAAA,GAAAP,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASP,MAAA,CAAAgE,WAAA,CAAAF,SAAA,CAAkB;IAAA,EAAC;IAI1BrE,EADF,CAAAE,cAAA,cAAwB,eACG;IAAAF,EAAA,CAAAG,MAAA,GAAgB;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAChDJ,EAAA,CAAAE,cAAA,eAA4B;IAAAF,EAAA,CAAAG,MAAA,GAAkC;IAChEH,EADgE,CAAAI,YAAA,EAAO,EACjE;IACNJ,EAAA,CAAAE,cAAA,cAA0B;IACxBF,EAAA,CAAAC,SAAA,eAGQ;IAEZD,EADE,CAAAI,YAAA,EAAM,EACF;;;;;IAZJJ,EAAA,CAAAkD,UAAA,WAAA3C,MAAA,CAAAkD,WAAA,aAAkC;IAGPzD,EAAA,CAAAK,SAAA,GAAgB;IAAhBL,EAAA,CAAAgB,iBAAA,CAAAqD,SAAA,CAAAG,IAAA,CAAgB;IACbxE,EAAA,CAAAK,SAAA,GAAkC;IAAlCL,EAAA,CAAAM,kBAAA,KAAA+D,SAAA,CAAAI,OAAA,CAAAC,MAAA,aAAkC;IAK5D1E,EAAA,CAAAK,SAAA,GAA+B;IAA/BL,EAAA,CAAAwD,WAAA,WAAAa,SAAA,CAAAM,QAAA,CAA+B;;;;;IAMrC3E,EAAA,CAAAE,cAAA,cAA0F;IACxFF,EAAA,CAAAG,MAAA,0BACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;;IACNJ,EAAA,CAAAE,cAAA,cAAuF;IACrFF,EAAA,CAAAG,MAAA,wBACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;;IAjDRJ,EAAA,CAAAE,cAAA,cAA4F;IA+C1FF,EA7CA,CAAA6C,UAAA,IAAA+B,4CAAA,mBAKC,IAAAC,4CAAA,kBAuBA,IAAAC,4CAAA,kBAcyF,IAAAC,4CAAA,kBAGH;IAGzF/E,EAAA,CAAAI,YAAA,EAAM;;;;IA9CkBJ,EAAA,CAAAK,SAAA,EAAmB;IAAnBL,EAAA,CAAAkD,UAAA,YAAA3C,MAAA,CAAAyE,gBAAA,CAAmB;IAuBrBhF,EAAA,CAAAK,SAAA,EAAiB;IAAjBL,EAAA,CAAAkD,UAAA,YAAA3C,MAAA,CAAA0E,cAAA,CAAiB;IAiBZjF,EAAA,CAAAK,SAAA,EAA+D;IAA/DL,EAAA,CAAAkD,UAAA,SAAA3C,MAAA,CAAAkD,WAAA,iBAAAlD,MAAA,CAAAyE,gBAAA,CAAAN,MAAA,OAA+D;IAG/D1E,EAAA,CAAAK,SAAA,EAA4D;IAA5DL,EAAA,CAAAkD,UAAA,SAAA3C,MAAA,CAAAkD,WAAA,gBAAAlD,MAAA,CAAA0E,cAAA,CAAAP,MAAA,OAA4D;;;;;;IAtGjG1E,EAAA,CAAAE,cAAA,cAAqF;IAAjCF,EAAA,CAAAS,UAAA,mBAAAyE,qDAAA;MAAAlF,EAAA,CAAAW,aAAA,CAAAwE,GAAA;MAAA,MAAA5E,MAAA,GAAAP,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASP,MAAA,CAAA6E,oBAAA,EAAsB;IAAA,EAAC;IAClFpF,EAAA,CAAAE,cAAA,cAAoE;IAAnCF,EAAA,CAAAS,UAAA,mBAAA4E,qDAAAjE,MAAA;MAAApB,EAAA,CAAAW,aAAA,CAAAwE,GAAA;MAAA,OAAAnF,EAAA,CAAAc,WAAA,CAASM,MAAA,CAAAE,eAAA,EAAwB;IAAA,EAAC;IACjEtB,EAAA,CAAAE,cAAA,iBAAyE;IAA/CF,EAAA,CAAAS,UAAA,mBAAA6E,wDAAA;MAAAtF,EAAA,CAAAW,aAAA,CAAAwE,GAAA;MAAA,MAAA5E,MAAA,GAAAP,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASP,MAAA,CAAA6E,oBAAA,EAAsB;IAAA,EAAC;IAAepF,EAAA,CAAAG,MAAA,aAAC;IAAAH,EAAA,CAAAI,YAAA,EAAS;IACnFJ,EAAA,CAAAE,cAAA,cAA2B;IAEzBF,EAAA,CAAA6C,UAAA,IAAA0C,qCAAA,mBAAiE;IA8B7DvF,EAFJ,CAAAE,cAAA,cAAwB,cACU,gBAQ5B;IAJAF,EAAA,CAAAoC,gBAAA,2BAAAoD,+DAAApE,MAAA;MAAApB,EAAA,CAAAW,aAAA,CAAAwE,GAAA;MAAA,MAAA5E,MAAA,GAAAP,EAAA,CAAAa,aAAA;MAAAb,EAAA,CAAAsC,kBAAA,CAAA/B,MAAA,CAAAkF,oBAAA,EAAArE,MAAA,MAAAb,MAAA,CAAAkF,oBAAA,GAAArE,MAAA;MAAA,OAAApB,EAAA,CAAAc,WAAA,CAAAM,MAAA;IAAA,EAAkC;IAClCpB,EAAA,CAAAS,UAAA,mBAAAiF,uDAAA;MAAA1F,EAAA,CAAAW,aAAA,CAAAwE,GAAA;MAAA,MAAA5E,MAAA,GAAAP,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASP,MAAA,CAAAoF,uBAAA,EAAyB;IAAA,EAAC;IAJrC3F,EAAA,CAAAI,YAAA,EAOE;IAaFJ,EAVA,CAAA6C,UAAA,IAAA+C,qCAAA,kBAAmE,KAAAC,sCAAA,kBAUyB;IAoDhG7F,EADE,CAAAI,YAAA,EAAM,EACF;IAIJJ,EADF,CAAAE,cAAA,eAAwB,oBAOrB;IAJCF,EAAA,CAAAoC,gBAAA,2BAAA0D,mEAAA1E,MAAA;MAAApB,EAAA,CAAAW,aAAA,CAAAwE,GAAA;MAAA,MAAA5E,MAAA,GAAAP,EAAA,CAAAa,aAAA;MAAAb,EAAA,CAAAsC,kBAAA,CAAA/B,MAAA,CAAAwF,cAAA,EAAA3E,MAAA,MAAAb,MAAA,CAAAwF,cAAA,GAAA3E,MAAA;MAAA,OAAApB,EAAA,CAAAc,WAAA,CAAAM,MAAA;IAAA,EAA4B;IAI7BpB,EAAA,CAAAI,YAAA,EAAW;IACZJ,EAAA,CAAAE,cAAA,eAAwB;IAAAF,EAAA,CAAAG,MAAA,IAAgC;IAC1DH,EAD0D,CAAAI,YAAA,EAAM,EAC1D;IAGJJ,EADF,CAAAE,cAAA,eAA6B,kBAK1B;IAFCF,EAAA,CAAAS,UAAA,mBAAAuF,yDAAA;MAAAhG,EAAA,CAAAW,aAAA,CAAAwE,GAAA;MAAA,MAAA5E,MAAA,GAAAP,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASP,MAAA,CAAA0F,WAAA,EAAa;IAAA,EAAC;;IAGvBjG,EAAA,CAAAE,cAAA,eAAsC;IAEpCF,EADA,CAAAC,SAAA,gBAAsC,mBACE;IAC1CD,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAG,MAAA,cACF;IAAAH,EAAA,CAAAI,YAAA,EAAS;;IACTJ,EAAA,CAAAE,cAAA,kBAAmE;IAAjCF,EAAA,CAAAS,UAAA,mBAAAyF,yDAAA;MAAAlG,EAAA,CAAAW,aAAA,CAAAwE,GAAA;MAAA,MAAA5E,MAAA,GAAAP,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASP,MAAA,CAAA6E,oBAAA,EAAsB;IAAA,EAAC;;IAChEpF,EAAA,CAAAE,cAAA,eAAsC;IAEpCF,EADA,CAAAC,SAAA,gBAAqC,gBACA;IACvCD,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAG,MAAA,gBACF;IACFH,EADE,CAAAI,YAAA,EAAS,EACL;;IAGJJ,EADF,CAAAE,cAAA,eAAuB,SAClB;IAAAF,EAAA,CAAAG,MAAA,2DAA8C;IAIzDH,EAJyD,CAAAI,YAAA,EAAI,EACjD,EACF,EACF,EACF;;;;IA9IoCJ,EAAA,CAAAK,SAAA,GAA2B;IAA3BL,EAAA,CAAAkD,UAAA,SAAA3C,MAAA,CAAA4F,UAAA,CAAAzB,MAAA,KAA2B;IAiCzD1E,EAAA,CAAAK,SAAA,GAAkC;IAAlCL,EAAA,CAAAiD,gBAAA,YAAA1C,MAAA,CAAAkF,oBAAA,CAAkC;IAElCzF,EAAA,CAAAkD,UAAA,gBAAA3C,MAAA,CAAAkD,WAAA,0DAAoF;IAKrDzD,EAAA,CAAAK,SAAA,EAAgC;IAAhCL,EAAA,CAAAkD,UAAA,SAAA3C,MAAA,CAAAqD,wBAAA,GAAgC;IAUhC5D,EAAA,CAAAK,SAAA,EAAyD;IAAzDL,EAAA,CAAAkD,UAAA,SAAA3C,MAAA,CAAAkF,oBAAA,KAAAlF,MAAA,CAAAqD,wBAAA,GAAyD;IA0D1F5D,EAAA,CAAAK,SAAA,GAA4B;IAA5BL,EAAA,CAAAiD,gBAAA,YAAA1C,MAAA,CAAAwF,cAAA,CAA4B;IAKN/F,EAAA,CAAAK,SAAA,GAAgC;IAAhCL,EAAA,CAAAM,kBAAA,KAAAC,MAAA,CAAAwF,cAAA,CAAArB,MAAA,UAAgC;IAOtD1E,EAAA,CAAAK,SAAA,GAA8B;IAA9BL,EAAA,CAAAkD,UAAA,cAAA3C,MAAA,CAAA6F,cAAA,GAA8B;;;;;IAoC5BpG,EALJ,CAAAE,cAAA,cAGC,cAC6B,eACL;IAAAF,EAAA,CAAAG,MAAA,GAAoB;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAChDJ,EAAA,CAAAE,cAAA,eAAwB;IAAAF,EAAA,CAAAG,MAAA,GAAsC;;IAChEH,EADgE,CAAAI,YAAA,EAAO,EACjE;IACNJ,EAAA,CAAAE,cAAA,cAA6B;IAAAF,EAAA,CAAAG,MAAA,GAAqB;IACpDH,EADoD,CAAAI,YAAA,EAAM,EACpD;;;;IAJmBJ,EAAA,CAAAK,SAAA,GAAoB;IAApBL,EAAA,CAAAgB,iBAAA,CAAAqF,WAAA,CAAAC,MAAA,CAAoB;IACjBtG,EAAA,CAAAK,SAAA,GAAsC;IAAtCL,EAAA,CAAAgB,iBAAA,CAAAhB,EAAA,CAAAuG,WAAA,OAAAF,WAAA,CAAAG,SAAA,WAAsC;IAEnCxG,EAAA,CAAAK,SAAA,GAAqB;IAArBL,EAAA,CAAAgB,iBAAA,CAAAqF,WAAA,CAAAI,OAAA,CAAqB;;;;;IATtDzG,EAAA,CAAAE,cAAA,cAAuD;IACrDF,EAAA,CAAA6C,UAAA,IAAA6D,4CAAA,kBAGC;IAOH1G,EAAA,CAAAI,YAAA,EAAM;;;;IARkBJ,EAAA,CAAAK,SAAA,EAAa;IAAAL,EAAb,CAAAkD,UAAA,YAAA3C,MAAA,CAAAoG,QAAA,CAAa,iBAAApG,MAAA,CAAAqG,YAAA,CAAqB;;;;;IAWxD5G,EADF,CAAAE,cAAA,cAAuD,QAClD;IAAAF,EAAA,CAAAG,MAAA,sBAAe;IAAAH,EAAA,CAAAI,YAAA,EAAI;IACtBJ,EAAA,CAAAE,cAAA,YAAgB;IAAAF,EAAA,CAAAG,MAAA,qDAA8C;IAChEH,EADgE,CAAAI,YAAA,EAAI,EAC9D;;;;;;IArBZJ,EAAA,CAAAE,cAAA,cAAqF;IAAhCF,EAAA,CAAAS,UAAA,mBAAAoG,sDAAA;MAAA7G,EAAA,CAAAW,aAAA,CAAAmG,IAAA;MAAA,MAAAvG,MAAA,GAAAP,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASP,MAAA,CAAAwG,mBAAA,EAAqB;IAAA,EAAC;IAClF/G,EAAA,CAAAE,cAAA,cAAqE;IAAnCF,EAAA,CAAAS,UAAA,mBAAAuG,sDAAA5F,MAAA;MAAApB,EAAA,CAAAW,aAAA,CAAAmG,IAAA;MAAA,OAAA9G,EAAA,CAAAc,WAAA,CAASM,MAAA,CAAAE,eAAA,EAAwB;IAAA,EAAC;IAClEtB,EAAA,CAAAE,cAAA,iBAA0D;IAAhCF,EAAA,CAAAS,UAAA,mBAAAwG,yDAAA;MAAAjH,EAAA,CAAAW,aAAA,CAAAmG,IAAA;MAAA,MAAAvG,MAAA,GAAAP,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASP,MAAA,CAAAwG,mBAAA,EAAqB;IAAA,EAAC;IAAC/G,EAAA,CAAAG,MAAA,aAAC;IAAAH,EAAA,CAAAI,YAAA,EAAS;IAEpEJ,EAAA,CAAAE,cAAA,cAA2B;IAczBF,EAbA,CAAA6C,UAAA,IAAAqE,sCAAA,kBAAuD,IAAAC,sCAAA,kBAaA;IAM7DnH,EAFI,CAAAI,YAAA,EAAM,EACF,EACF;;;;IAnB4BJ,EAAA,CAAAK,SAAA,GAAyB;IAAzBL,EAAA,CAAAkD,UAAA,SAAA3C,MAAA,CAAAoG,QAAA,CAAAjC,MAAA,KAAyB;IAa3B1E,EAAA,CAAAK,SAAA,EAA2B;IAA3BL,EAAA,CAAAkD,UAAA,SAAA3C,MAAA,CAAAoG,QAAA,CAAAjC,MAAA,OAA2B;;;;;IAkB/C1E,EAAA,CAAAC,SAAA,eAKE;;;;IAHAD,EAAA,CAAAkD,UAAA,QAAA3C,MAAA,CAAA6G,WAAA,CAAAC,MAAA,EAAArH,EAAA,CAAAsH,aAAA,CAA0B;;;;;IAK1BtH,EADF,CAAAE,cAAA,eAA4D,gBAC5B;IAC5BF,EAAA,CAAAG,MAAA,GACF;IACFH,EADE,CAAAI,YAAA,EAAO,EACH;;;;;IAFFJ,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,OAAAC,MAAA,CAAAU,WAAA,kBAAAV,MAAA,CAAAU,WAAA,CAAAC,QAAA,mBAAAqG,OAAA,GAAAhH,MAAA,CAAAU,WAAA,CAAAC,QAAA,CAAAsG,MAAA,sBAAAD,OAAA,CAAAE,WAAA,gBACF;;;;;;IAlBdzH,EAAA,CAAAE,cAAA,cAAwF;IAAjCF,EAAA,CAAAS,UAAA,mBAAAiH,sDAAA;MAAA1H,EAAA,CAAAW,aAAA,CAAAgH,IAAA;MAAA,MAAApH,MAAA,GAAAP,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASP,MAAA,CAAAqH,oBAAA,EAAsB;IAAA,EAAC;IACrF5H,EAAA,CAAAE,cAAA,cAA6E;IAAnCF,EAAA,CAAAS,UAAA,mBAAAoH,sDAAAzG,MAAA;MAAApB,EAAA,CAAAW,aAAA,CAAAgH,IAAA;MAAA,OAAA3H,EAAA,CAAAc,WAAA,CAASM,MAAA,CAAAE,eAAA,EAAwB;IAAA,EAAC;IAC1EtB,EAAA,CAAAE,cAAA,iBAAyE;IAA/CF,EAAA,CAAAS,UAAA,mBAAAqH,yDAAA;MAAA9H,EAAA,CAAAW,aAAA,CAAAgH,IAAA;MAAA,MAAApH,MAAA,GAAAP,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASP,MAAA,CAAAqH,oBAAA,EAAsB;IAAA,EAAC;IAAe5H,EAAA,CAAAG,MAAA,aAAC;IAAAH,EAAA,CAAAI,YAAA,EAAS;IAM7EJ,EAJN,CAAAE,cAAA,cAA2B,cAEG,cACI,cACA;IAO1BF,EANA,CAAA6C,UAAA,IAAAkF,sCAAA,kBAKE,IAAAC,sCAAA,kBAC0D;IAK9DhI,EAAA,CAAAI,YAAA,EAAM;IAEJJ,EADF,CAAAE,cAAA,eAA4B,iBACyB;IACjDF,EAAA,CAAAG,MAAA,oCACF;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IACRJ,EAAA,CAAAE,cAAA,kBAME;IAFAF,EAAA,CAAAS,UAAA,oBAAAwH,0DAAA7G,MAAA;MAAApB,EAAA,CAAAW,aAAA,CAAAgH,IAAA;MAAA,MAAApH,MAAA,GAAAP,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAAUP,MAAA,CAAA2H,cAAA,CAAA9G,MAAA,CAAsB;IAAA,EAAC;IAKzCpB,EATM,CAAAI,YAAA,EAME,EACE,EACF,EACF;IAMAJ,EAHN,CAAAE,cAAA,gBAA0B,eACA,gBACM,YACpB;IAAAF,EAAA,CAAAG,MAAA,IAAwC;IAElDH,EAFkD,CAAAI,YAAA,EAAO,EACjD,EACF;IAIFJ,EAFJ,CAAAE,cAAA,eAAwB,gBACM,YACpB;IAAAF,EAAA,CAAAG,MAAA,IAAoC;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACjDJ,EAAA,CAAAE,cAAA,iBAAyB;IAAAF,EAAA,CAAAG,MAAA,oDAA4C;IAEzEH,EAFyE,CAAAI,YAAA,EAAO,EACxE,EACF;IAIFJ,EAFJ,CAAAE,cAAA,eAAwB,gBACM,YACpB;IAAAF,EAAA,CAAAG,MAAA,IAAoC;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACjDJ,EAAA,CAAAE,cAAA,iBAAyB;IAAAF,EAAA,CAAAG,MAAA,2DAAmD;IAGlFH,EAHkF,CAAAI,YAAA,EAAO,EAC/E,EACF,EACF;IAIJJ,EADF,CAAAE,cAAA,gBAA2B,UACrB;IAAAF,EAAA,CAAAG,MAAA,4BAAoB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAE3BJ,EADF,CAAAE,cAAA,gBAA2B,iBACI;IAAAF,EAAA,CAAAG,MAAA,gCAAc;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAClDJ,EAAA,CAAAE,cAAA,iBAA6B;IAAAF,EAAA,CAAAG,MAAA,kDAA0C;IACzEH,EADyE,CAAAI,YAAA,EAAO,EAC1E;IAEJJ,EADF,CAAAE,cAAA,gBAA2B,iBACI;IAAAF,EAAA,CAAAG,MAAA,0CAAmB;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACvDJ,EAAA,CAAAE,cAAA,iBAA6B;IAAAF,EAAA,CAAAG,MAAA,yCAAiC;IAChEH,EADgE,CAAAI,YAAA,EAAO,EACjE;IAEJJ,EADF,CAAAE,cAAA,gBAA2B,iBACI;IAAAF,EAAA,CAAAG,MAAA,kCAAgB;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACpDJ,EAAA,CAAAE,cAAA,iBAA6B;IAAAF,EAAA,CAAAG,MAAA,qBAAa;IAE9CH,EAF8C,CAAAI,YAAA,EAAO,EAC7C,EACF;IAGJJ,EADF,CAAAE,cAAA,gBAA6B,kBACwC;IAAjCF,EAAA,CAAAS,UAAA,mBAAA0H,0DAAA;MAAAnI,EAAA,CAAAW,aAAA,CAAAgH,IAAA;MAAA,MAAApH,MAAA,GAAAP,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASP,MAAA,CAAAqH,oBAAA,EAAsB;IAAA,EAAC;;IAChE5H,EAAA,CAAAE,cAAA,eAAsC;IAGpCF,EAFA,CAAAC,SAAA,iBAAmD,qBACd,iBACC;IACxCD,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAG,MAAA,eACF;IAIRH,EAJQ,CAAAI,YAAA,EAAS,EACL,EACF,EACF,EACF;;;;IA9ESJ,EAAA,CAAAK,SAAA,GAAwB;IAAxBL,EAAA,CAAAkD,UAAA,SAAA3C,MAAA,CAAA6G,WAAA,CAAAC,MAAA,CAAwB;IAKrBrH,EAAA,CAAAK,SAAA,EAAyB;IAAzBL,EAAA,CAAAkD,UAAA,UAAA3C,MAAA,CAAA6G,WAAA,CAAAC,MAAA,CAAyB;IAyBzBrH,EAAA,CAAAK,SAAA,GAAwC;IAAxCL,EAAA,CAAAgB,iBAAA,EAAAT,MAAA,CAAAU,WAAA,kBAAAV,MAAA,CAAAU,WAAA,CAAAC,QAAA,eAAwC;IAMxClB,EAAA,CAAAK,SAAA,GAAoC;IAApCL,EAAA,CAAAgB,iBAAA,CAAAT,MAAA,CAAA6G,WAAA,CAAAlD,KAAA,cAAoC;IAOpClE,EAAA,CAAAK,SAAA,GAAoC;IAApCL,EAAA,CAAAgB,iBAAA,CAAAT,MAAA,CAAA6G,WAAA,CAAAgB,KAAA,cAAoC;;;;;IAuCpDpI,EADF,CAAAE,cAAA,eAAuH,WAC/G;IAAAF,EAAA,CAAAG,MAAA,6DAAiD;IACzDH,EADyD,CAAAI,YAAA,EAAO,EAC1D;;;AD9TN,OAAM,MAAOiI,gBAAgB;EAoC3B;EACA,IAAIlC,UAAUA,CAAA;IACZ,OAAO,IAAI,CAACmC,MAAM;EACpB;EAgBAC,YACUC,WAAwB,EACxBC,cAA8B,EAC9BC,mBAAwC;IAFxC,KAAAF,WAAW,GAAXA,WAAW;IACX,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,mBAAmB,GAAnBA,mBAAmB;IAzDrB,KAAAC,QAAQ,GAAG,IAAI9I,OAAO,EAAQ;IAEtC;IACA,KAAA+I,WAAW,GAAgB,OAAO;IAElC;IACA,KAAAC,cAAc,GAAG,KAAK;IACtB,KAAAC,gBAAgB,GAAG,KAAK;IACxB,KAAAC,iBAAiB,GAAG,KAAK;IACzB,KAAAC,eAAe,GAAG,KAAK;IACvB,KAAAC,mBAAmB,GAAG,KAAK;IAE3B;IACA,KAAAtH,mBAAmB,GAAG;MAAEC,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAC,CAAE;IAEpC;IACA,KAAAU,gBAAgB,GAAqB;MAAErB,QAAQ,EAAE,EAAE;MAAEyB,UAAU,EAAE;IAAE,CAAE;IACrE,KAAAb,UAAU,GAAG,EAAE;IACf,KAAAqB,SAAS,GAAG,KAAK;IAEjB;IACA,KAAA4C,cAAc,GAAG,EAAE;IACnB,KAAAmD,iBAAiB,GAAG,EAAE;IACtB,KAAAC,aAAa,GAAG,EAAE;IAClB,KAAA1F,WAAW,GAAuB,QAAQ;IAC1C,KAAAkD,QAAQ,GAAc,EAAE;IACxB,KAAAnG,WAAW,GAAG,CAAC;IAEf;IACA,KAAA4I,QAAQ,GAAc,EAAE;IACxB,KAAAd,MAAM,GAAY,EAAE;IACpB,KAAAtD,gBAAgB,GAAc,EAAE;IAChC,KAAAC,cAAc,GAAY,EAAE;IAC5B,KAAAQ,oBAAoB,GAAG,EAAE;IAOzB;IACA,KAAAxE,WAAW,GAAgB,IAAI;IAE/B;IACA,KAAAmG,WAAW,GAAG;MACZC,MAAM,EAAE,EAAE;MACVnD,KAAK,EAAE,EAAE;MACTkE,KAAK,EAAE;KACR;IAED;IACQ,KAAAiB,cAAc,GAAQ,IAAI;IAC1B,KAAAC,iBAAiB,GAAG,GAAG,CAAC,CAAC;EAM9B;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACC,aAAa,EAAE;IACpB,IAAI,CAACC,oBAAoB,EAAE;IAC3B,IAAI,CAACC,iBAAiB,EAAE;EAC1B;EAEAC,WAAWA,CAAA;IACT,IAAI,CAAChB,QAAQ,CAACiB,IAAI,EAAE;IACpB,IAAI,CAACjB,QAAQ,CAACkB,QAAQ,EAAE;EAC1B;EAEQL,aAAaA,CAAA;IACnB;IACA,IAAI,CAAChB,WAAW,CAACsB,UAAU,CAACC,IAAI,CAAChK,IAAI,CAAC,CAAC,CAAC,CAAC,CAACiK,SAAS,CAAEC,SAAoB,IAAI;MAC3E,IAAIA,SAAS,CAACC,eAAe,IAAID,SAAS,CAACE,IAAI,EAAE;QAC/C,IAAI,CAAClJ,WAAW,GAAGgJ,SAAS,CAACE,IAAI;QACjC,IAAI,CAACvB,WAAW,GAAG,eAAe;QAClC,IAAI,CAACwB,YAAY,EAAE;QACnB,IAAI,CAACC,YAAY,EAAE;QACnB,IAAI,CAACC,UAAU,EAAE;QACjB,IAAI,CAACC,eAAe,EAAE;OACvB,MAAM;QACL;QACA,IAAI,CAAC3B,WAAW,GAAG,OAAO;QAC1B,IAAI,CAAC3H,WAAW,GAAG,IAAI;;IAE3B,CAAC,CAAC;EACJ;EAEQyI,iBAAiBA,CAAA;IACvB,IAAI,CAAClB,WAAW,CAACsB,UAAU,CACxBC,IAAI,CAACjK,SAAS,CAAC,IAAI,CAAC6I,QAAQ,CAAC,CAAC,CAC9BqB,SAAS,CAAEC,SAAoB,IAAI;MAClC,IAAIA,SAAS,CAACC,eAAe,IAAID,SAAS,CAACE,IAAI,EAAE;QAC/C,IAAI,CAAClJ,WAAW,GAAGgJ,SAAS,CAACE,IAAI;QACjC,IAAI,CAACvB,WAAW,GAAG,eAAe;QAClC,IAAI,CAACC,cAAc,GAAG,KAAK;QAC3B,IAAI,CAACuB,YAAY,EAAE;OACpB,MAAM;QACL,IAAI,CAACnJ,WAAW,GAAG,IAAI;QACvB,IAAI,CAAC2H,WAAW,GAAG,OAAO;QAC1B,IAAI,CAACjC,QAAQ,GAAG,EAAE;QAClB,IAAI,CAACnG,WAAW,GAAG,CAAC;;IAExB,CAAC,CAAC;EACN;EAEQiJ,oBAAoBA,CAAA;IAC1B,IAAI,CAAChB,cAAc,CAAC+B,SAAS,CAC1BT,IAAI,CAACjK,SAAS,CAAC,IAAI,CAAC6I,QAAQ,CAAC,CAAC,CAC9BqB,SAAS,CAACrD,QAAQ,IAAG;MACpB,IAAI,CAACA,QAAQ,GAAGA,QAAQ;MACxB,IAAI,CAAC8D,iBAAiB,EAAE;IAC1B,CAAC,CAAC;IAEJ,IAAI,CAAChC,cAAc,CAACiC,WAAW,CAC5BX,IAAI,CAACjK,SAAS,CAAC,IAAI,CAAC6I,QAAQ,CAAC,CAAC,CAC9BqB,SAAS,CAACW,OAAO,IAAG;MACnB,IAAI,CAAChE,QAAQ,CAACiE,OAAO,CAACD,OAAO,CAAC;MAC9B,IAAI,CAACF,iBAAiB,EAAE;MACxB,IAAI,CAAC/B,mBAAmB,CAACmC,gBAAgB,CAAC,sBAAsB,CAAC;IACnE,CAAC,CAAC;EACN;EAEQJ,iBAAiBA,CAAA;IACvB,IAAI,CAACjK,WAAW,GAAG,IAAI,CAACmG,QAAQ,CAACmE,MAAM,CAACC,CAAC,IAAI,CAACA,CAAC,CAACC,IAAI,CAAC,CAACtG,MAAM;IAC5D,IAAI,IAAI,CAAClE,WAAW,GAAG,CAAC,IAAI,IAAI,CAACoI,WAAW,KAAK,eAAe,EAAE;MAChE,IAAI,CAACA,WAAW,GAAG,QAAQ;KAC5B,MAAM,IAAI,IAAI,CAACpI,WAAW,KAAK,CAAC,IAAI,IAAI,CAACoI,WAAW,KAAK,QAAQ,EAAE;MAClE,IAAI,CAACA,WAAW,GAAG,eAAe;;EAEtC;EAEQwB,YAAYA,CAAA;IAClB,IAAI,CAAC3B,cAAc,CAAC2B,YAAY,EAAE,CAC/BL,IAAI,CAACjK,SAAS,CAAC,IAAI,CAAC6I,QAAQ,CAAC,CAAC,CAC9BqB,SAAS,CAAC;MACTJ,IAAI,EAAGjD,QAAQ,IAAI;QACjB,IAAI,CAACA,QAAQ,GAAGA,QAAQ;QACxB,IAAI,CAAC8D,iBAAiB,EAAE;MAC1B,CAAC;MACDQ,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAClD;KACD,CAAC;EACN;EAEQZ,YAAYA,CAAA;IAClB;IACA,IAAI,CAACjB,QAAQ,GAAG,CACd;MACE+B,EAAE,EAAE,GAAG;MACPjK,QAAQ,EAAE,OAAO;MACjBgD,KAAK,EAAE,mBAAmB;MAC1BC,QAAQ,EAAE;KACX,EACD;MACEgH,EAAE,EAAE,GAAG;MACPjK,QAAQ,EAAE,KAAK;MACfgD,KAAK,EAAE,iBAAiB;MACxBC,QAAQ,EAAE,KAAK;MACfiH,QAAQ,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,MAAM,CAAC,CAAC;KACzC,CACF;IACD,IAAI,CAACtG,gBAAgB,GAAG,CAAC,GAAG,IAAI,CAACoE,QAAQ,CAAC;EAC5C;EAEQkB,UAAUA,CAAA;IAChB;IACA;IACA,IAAI,CAAChC,MAAM,GAAG,CACZ;MACE6C,EAAE,EAAE,QAAQ;MACZ3G,IAAI,EAAE,WAAW;MACjBC,OAAO,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,cAAc,CAAC;MACnCE,QAAQ,EAAE;KACX,EACD;MACEwG,EAAE,EAAE,QAAQ;MACZ3G,IAAI,EAAE,QAAQ;MACdC,OAAO,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,cAAc,CAAC;MACnCE,QAAQ,EAAE;KACX,CACF;IAED;IACA;IAEA,IAAI,CAACM,cAAc,GAAG,CAAC,GAAG,IAAI,CAACqD,MAAM,CAAC;EACxC;EAEQiC,eAAeA,CAAA;IACrB,IAAI,IAAI,CAACtJ,WAAW,EAAE;MACpB,IAAI,CAACmG,WAAW,GAAG;QACjBC,MAAM,EAAE,EAAE;QACVnD,KAAK,EAAE,IAAI,CAACjD,WAAW,CAACiD,KAAK,IAAI,EAAE;QACnCkE,KAAK,EAAE,EAAE,CAAC;OACX;;EAEL;EAEA;EACAmD,aAAaA,CAAA;IACX;IACA,IAAI,IAAI,CAACvC,eAAe,EAAE;MACxB,IAAI,CAACwC,gBAAgB,EAAE;MACvB;;IAGF,QAAQ,IAAI,CAAC5C,WAAW;MACtB,KAAK,OAAO;QACV,IAAI,CAAC6C,cAAc,EAAE;QACrB;MACF,KAAK,eAAe;QAClB,IAAI,CAACC,mBAAmB,EAAE;QAC1B;MACF,KAAK,QAAQ;QACX,IAAI,CAACC,kBAAkB,EAAE;QACzB;MACF,KAAK,WAAW;QACd;QACA;;EAEN;EAEA;EACAC,kBAAkBA,CAACC,KAAiB;IAClCA,KAAK,CAACC,cAAc,EAAE;IAEtB;IACA,IAAI,IAAI,CAAClD,WAAW,KAAK,OAAO,EAAE;IAElC,IAAI,CAACI,eAAe,GAAG,IAAI;IAC3B,IAAI,CAACrH,mBAAmB,GAAG;MACzBC,CAAC,EAAEiK,KAAK,CAACE,OAAO;MAChBlK,CAAC,EAAEgK,KAAK,CAACG;KACV;EACH;EAEA;EACAC,kBAAkBA,CAACJ,KAAiB;IAClC;IACA,IAAI,IAAI,CAACjD,WAAW,KAAK,OAAO,EAAE;IAElC,IAAI,CAACS,cAAc,GAAG6C,UAAU,CAAC,MAAK;MACpC,MAAMC,KAAK,GAAGN,KAAK,CAACO,OAAO,CAAC,CAAC,CAAC;MAC9B,IAAI,CAACpD,eAAe,GAAG,IAAI;MAC3B,IAAI,CAACrH,mBAAmB,GAAG;QACzBC,CAAC,EAAEuK,KAAK,CAACJ,OAAO;QAChBlK,CAAC,EAAEsK,KAAK,CAACH;OACV;MAED;MACA,IAAIK,SAAS,CAACC,OAAO,EAAE;QACrBD,SAAS,CAACC,OAAO,CAAC,EAAE,CAAC;;IAEzB,CAAC,EAAE,IAAI,CAAChD,iBAAiB,CAAC;EAC5B;EAEAiD,gBAAgBA,CAAA;IACd,IAAI,IAAI,CAAClD,cAAc,EAAE;MACvBmD,YAAY,CAAC,IAAI,CAACnD,cAAc,CAAC;MACjC,IAAI,CAACA,cAAc,GAAG,IAAI;;EAE9B;EAEAoD,iBAAiBA,CAAA;IACf;IACA,IAAI,IAAI,CAACpD,cAAc,EAAE;MACvBmD,YAAY,CAAC,IAAI,CAACnD,cAAc,CAAC;MACjC,IAAI,CAACA,cAAc,GAAG,IAAI;;EAE9B;EAEA;EACAoC,cAAcA,CAAA;IACZ,IAAI,CAAC5C,cAAc,GAAG,IAAI;IAC1B,IAAI,CAACtG,gBAAgB,GAAG;MAAErB,QAAQ,EAAE,EAAE;MAAEyB,UAAU,EAAE;IAAE,CAAE;IACxD,IAAI,CAACb,UAAU,GAAG,EAAE;EACtB;EAEAG,eAAeA,CAAA;IACb,IAAI,CAAC4G,cAAc,GAAG,KAAK;IAC3B,IAAI,CAACtG,gBAAgB,GAAG;MAAErB,QAAQ,EAAE,EAAE;MAAEyB,UAAU,EAAE;IAAE,CAAE;IACxD,IAAI,CAACb,UAAU,GAAG,EAAE;EACtB;EAEAW,kBAAkBA,CAAA;IAChB;IACA,IAAI,IAAI,CAACiK,kBAAkB,EAAE,EAAE;MAC7B,IAAI,CAACC,YAAY,EAAE;;EAEvB;EAEQD,kBAAkBA,CAAA;IACxB,MAAME,aAAa,GAAG,IAAI,CAACrK,gBAAgB,CAACrB,QAAQ,CAACwD,MAAM,IAAI,CAAC;IAChE;IACA,MAAMmI,eAAe,GAAG,IAAI,CAACtK,gBAAgB,CAACI,UAAU,CAAC+B,MAAM,KAAK,CAAC,IAC9C,OAAO,CAACoI,IAAI,CAAC,IAAI,CAACvK,gBAAgB,CAACI,UAAU,CAAC,IAC9C,OAAO,CAACmK,IAAI,CAAC,IAAI,CAACvK,gBAAgB,CAACI,UAAU,CAAC,IAC9C,OAAO,CAACmK,IAAI,CAAC,IAAI,CAACvK,gBAAgB,CAACI,UAAU,CAAC,IAC9C,cAAc,CAACmK,IAAI,CAAC,IAAI,CAACvK,gBAAgB,CAACI,UAAU,CAAC;IAE5E,OAAOiK,aAAa,IAAIC,eAAe;EACzC;EAEQF,YAAYA,CAAA;IAClB,IAAI,IAAI,CAACxJ,SAAS,EAAE;IAEpB,IAAI,CAACA,SAAS,GAAG,IAAI;IACrB,IAAI,CAACrB,UAAU,GAAG,EAAE;IAEpB,IAAI,CAAC0G,WAAW,CAACuE,KAAK,CAAC,IAAI,CAACxK,gBAAgB,CAACrB,QAAQ,EAAE,IAAI,CAACqB,gBAAgB,CAACI,UAAU,CAAC,CACrFoH,IAAI,CAACjK,SAAS,CAAC,IAAI,CAAC6I,QAAQ,CAAC,CAAC,CAC9BqB,SAAS,CAAC;MACTJ,IAAI,EAAGoD,QAAQ,IAAI;QACjB,IAAI,CAAC7J,SAAS,GAAG,KAAK;QACtB;MACF,CAAC;MACD8H,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAAC9H,SAAS,GAAG,KAAK;QACtB,IAAI,CAACrB,UAAU,GAAGmJ,KAAK,CAACN,OAAO,IAAI,uBAAuB;MAC5D;KACD,CAAC;EACN;EAEA;EACAe,mBAAmBA,CAAA;IACjB,IAAI,CAAC5C,gBAAgB,GAAG,IAAI;IAC5B,IAAI,CAAC/C,cAAc,GAAG,EAAE;IACxB,IAAI,CAACmD,iBAAiB,GAAG,EAAE;IAC3B,IAAI,CAACC,aAAa,GAAG,EAAE;IACvB,IAAI,CAAC1F,WAAW,GAAG,QAAQ;IAC3B,IAAI,CAACgC,oBAAoB,GAAG,EAAE;IAC9B,IAAI,CAACT,gBAAgB,GAAG,CAAC,GAAG,IAAI,CAACoE,QAAQ,CAAC;IAC1C,IAAI,CAACnE,cAAc,GAAG,CAAC,GAAG,IAAI,CAACqD,MAAM,CAAC;IACtC,IAAI,CAACM,WAAW,GAAG,WAAW;EAChC;EAEAxD,oBAAoBA,CAAA;IAClB,IAAI,CAAC0D,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAAC/C,cAAc,GAAG,EAAE;IACxB,IAAI,CAACmD,iBAAiB,GAAG,EAAE;IAC3B,IAAI,CAACC,aAAa,GAAG,EAAE;IACvB,IAAI,CAAC1D,oBAAoB,GAAG,EAAE;IAC9B,IAAI,CAACmD,WAAW,GAAG,eAAe;EACpC;EAEA3C,WAAWA,CAAA;IACT,IAAI,CAAC,IAAI,CAACF,cAAc,CAACkH,IAAI,EAAE,EAAE;IAEjC;IACA,IAAI,IAAI,CAACxJ,WAAW,KAAK,QAAQ,IAAI,CAAC,IAAI,CAACyF,iBAAiB,EAAE;MAC5D,IAAI,CAACR,mBAAmB,CAACmC,gBAAgB,CAAC,2BAA2B,EAAE,SAAS,CAAC;MACjF;;IAGF,IAAI,IAAI,CAACpH,WAAW,KAAK,OAAO,IAAI,CAAC,IAAI,CAAC0F,aAAa,EAAE;MACvD,IAAI,CAACT,mBAAmB,CAACmC,gBAAgB,CAAC,uBAAuB,EAAE,SAAS,CAAC;MAC7E;;IAGF,MAAMF,OAAO,GAAqB;MAChClE,OAAO,EAAE,IAAI,CAACV,cAAc,CAACkH,IAAI,EAAE;MACnCzG,SAAS,EAAE,IAAI6E,IAAI,EAAE;MACrB/E,MAAM,EAAE,IAAI,CAACrF,WAAW,EAAEC,QAAQ,IAAI,SAAS;MAC/CgM,SAAS,EAAE,IAAI,CAACzJ,WAAW,KAAK,QAAQ,GAAG,IAAI,CAACyF,iBAAiB,GAAGiE,SAAS;MAC7EC,OAAO,EAAE,IAAI,CAAC3J,WAAW,KAAK,OAAO,GAAG,IAAI,CAAC0F,aAAa,GAAGgE;KAC9D;IAED,IAAI,CAAC1E,cAAc,CAACxC,WAAW,CAAC0E,OAAO,CAAC,CACrCZ,IAAI,CAACjK,SAAS,CAAC,IAAI,CAAC6I,QAAQ,CAAC,CAAC,CAC9BqB,SAAS,CAAC;MACTJ,IAAI,EAAEA,CAAA,KAAK;QACT,IAAI,CAACxE,oBAAoB,EAAE;QAC3B,IAAI,CAACsD,mBAAmB,CAACmC,gBAAgB,CAAC,cAAc,CAAC;MAC3D,CAAC;MACDI,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;QAC/C,IAAI,CAACvC,mBAAmB,CAACmC,gBAAgB,CAAC,wBAAwB,EAAE,OAAO,CAAC;MAC9E;KACD,CAAC;EACN;EAEA;EACAlF,uBAAuBA,CAAA;IACrB,MAAM0H,KAAK,GAAG,IAAI,CAAC5H,oBAAoB,CAAC6H,WAAW,EAAE;IAErD,IAAI,IAAI,CAAC7J,WAAW,KAAK,QAAQ,EAAE;MACjC,IAAI,CAACuB,gBAAgB,GAAG,IAAI,CAACoE,QAAQ,CAAC0B,MAAM,CAACyC,OAAO,IAClDA,OAAO,CAACrM,QAAQ,CAACoM,WAAW,EAAE,CAACE,QAAQ,CAACH,KAAK,CAAC,IAC9CE,OAAO,CAACrJ,KAAK,CAACoJ,WAAW,EAAE,CAACE,QAAQ,CAACH,KAAK,CAAC,CAC5C;KACF,MAAM;MACL,IAAI,CAACpI,cAAc,GAAG,IAAI,CAACqD,MAAM,CAACwC,MAAM,CAAC2C,KAAK,IAC5CA,KAAK,CAACjJ,IAAI,CAAC8I,WAAW,EAAE,CAACE,QAAQ,CAACH,KAAK,CAAC,CACzC;;EAEL;EAEApJ,aAAaA,CAACsJ,OAAgB;IAC5B,IAAI,CAACrE,iBAAiB,GAAGqE,OAAO,CAACpC,EAAE;IACnC,IAAI,CAAC1F,oBAAoB,GAAG8H,OAAO,CAACrM,QAAQ;IAC5C,IAAI,CAAC8D,gBAAgB,GAAG,EAAE;EAC5B;EAEAT,WAAWA,CAACkJ,KAAY;IACtB,IAAI,CAACtE,aAAa,GAAGsE,KAAK,CAACtC,EAAE;IAC7B,IAAI,CAAC1F,oBAAoB,GAAGgI,KAAK,CAACjJ,IAAI;IACtC,IAAI,CAACS,cAAc,GAAG,EAAE;EAC1B;EAEA3B,iBAAiBA,CAACoK,IAAwB;IACxC,IAAI,CAACjK,WAAW,GAAGiK,IAAI;IACvB,IAAI,CAACxE,iBAAiB,GAAG,EAAE;IAC3B,IAAI,CAACC,aAAa,GAAG,EAAE;IACvB,IAAI,CAAC1D,oBAAoB,GAAG,EAAE;IAC9B,IAAI,CAACE,uBAAuB,EAAE;EAChC;EAEA/B,wBAAwBA,CAAA;IACtB,IAAI,IAAI,CAACH,WAAW,KAAK,QAAQ,IAAI,IAAI,CAACyF,iBAAiB,EAAE;MAC3D,MAAMqE,OAAO,GAAG,IAAI,CAACnE,QAAQ,CAACuE,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACzC,EAAE,KAAK,IAAI,CAACjC,iBAAiB,CAAC;MACxE,OAAOqE,OAAO,EAAErM,QAAQ,IAAI,SAAS;;IAGvC,IAAI,IAAI,CAACuC,WAAW,KAAK,OAAO,IAAI,IAAI,CAAC0F,aAAa,EAAE;MACtD,MAAMsE,KAAK,GAAG,IAAI,CAACnF,MAAM,CAACqF,IAAI,CAACE,CAAC,IAAIA,CAAC,CAAC1C,EAAE,KAAK,IAAI,CAAChC,aAAa,CAAC;MAChE,OAAOsE,KAAK,EAAEjJ,IAAI,IAAI,eAAe;;IAGvC,OAAO,EAAE;EACX;EAEA4B,cAAcA,CAAA;IACZ,MAAM0H,UAAU,GAAG,IAAI,CAAC/H,cAAc,CAACkH,IAAI,EAAE,CAACvI,MAAM,GAAG,CAAC;IACxD,MAAMqJ,YAAY,GAAG,IAAI,CAACtK,WAAW,KAAK,QAAQ,GAAG,CAAC,CAAC,IAAI,CAACyF,iBAAiB,GAAG,CAAC,CAAC,IAAI,CAACC,aAAa;IACpG,OAAO2E,UAAU,IAAIC,YAAY;EACnC;EAEA;EACApC,kBAAkBA,CAAA;IAChB,IAAI,CAAC5C,iBAAiB,GAAG,IAAI;IAC7B,IAAI,CAACiF,kBAAkB,EAAE;EAC3B;EAEAjH,mBAAmBA,CAAA;IACjB,IAAI,CAACgC,iBAAiB,GAAG,KAAK;EAChC;EAEQiF,kBAAkBA,CAAA;IACxB,IAAI,CAACvF,cAAc,CAACwF,aAAa,EAAE,CAChClE,IAAI,CAACjK,SAAS,CAAC,IAAI,CAAC6I,QAAQ,CAAC,CAAC,CAC9BqB,SAAS,CAAC,MAAK;MACd,IAAI,CAACS,iBAAiB,EAAE;IAC1B,CAAC,CAAC;EACN;EAEA;EACAe,gBAAgBA,CAAA;IACd,IAAI,CAACxC,eAAe,GAAG,KAAK;EAC9B;EAEAxH,mBAAmBA,CAAA;IACjB,IAAI,CAACyH,mBAAmB,GAAG,IAAI;IAC/B,IAAI,CAACD,eAAe,GAAG,KAAK;IAC5B,IAAI,CAACuB,eAAe,EAAE;EACxB;EAEA3C,oBAAoBA,CAAA;IAClB,IAAI,CAACqB,mBAAmB,GAAG,KAAK;EAClC;EAEA;EAEAf,cAAcA,CAAC2D,KAAY;IACzB,MAAMqC,KAAK,GAAGrC,KAAK,CAACsC,MAA0B;IAC9C,IAAID,KAAK,CAACE,KAAK,IAAIF,KAAK,CAACE,KAAK,CAAC,CAAC,CAAC,EAAE;MACjC,MAAMC,IAAI,GAAGH,KAAK,CAACE,KAAK,CAAC,CAAC,CAAC;MAE3B;MACA,IAAI,CAACC,IAAI,CAACX,IAAI,CAACY,UAAU,CAAC,QAAQ,CAAC,EAAE;QACnC,IAAI,CAAC5F,mBAAmB,CAACmC,gBAAgB,CAAC,6BAA6B,EAAE,OAAO,CAAC;QACjF;;MAGF;MACA,IAAIwD,IAAI,CAACE,IAAI,GAAG,CAAC,GAAG,IAAI,GAAG,IAAI,EAAE;QAC/B,IAAI,CAAC7F,mBAAmB,CAACmC,gBAAgB,CAAC,gCAAgC,EAAE,OAAO,CAAC;QACpF;;MAGF,MAAM2D,MAAM,GAAG,IAAIC,UAAU,EAAE;MAC/BD,MAAM,CAACE,MAAM,GAAIC,CAAC,IAAI;QACpB,IAAI,CAACvH,WAAW,CAACC,MAAM,GAAGsH,CAAC,CAACR,MAAM,EAAES,MAAgB;QACpD,IAAI,CAACC,gBAAgB,EAAE;MACzB,CAAC;MACDL,MAAM,CAACM,aAAa,CAACT,IAAI,CAAC;;EAE9B;EAEAQ,gBAAgBA,CAAA;IACd;IACA,IAAI,CAACnG,mBAAmB,CAACmC,gBAAgB,CAAC,6BAA6B,EAAE,SAAS,CAAC;EACrF;EAEA;EACA9J,MAAMA,CAAA;IACJ,IAAI,CAACyK,gBAAgB,EAAE;IACvB,IAAI,CAAChD,WAAW,CAACzH,MAAM,EAAE;EAC3B;EAEA;EAEAgO,SAASA,CAAClD,KAAoB;IAC5B;IACA,IAAIA,KAAK,CAACmD,GAAG,KAAK,QAAQ,EAAE;MAC1B,IAAI,CAACC,cAAc,EAAE;;IAGvB;IACA,IAAIpD,KAAK,CAACmD,GAAG,KAAK,OAAO,IAAI,IAAI,CAACnG,cAAc,EAAE;MAChD,IAAI,IAAI,CAAC6D,kBAAkB,EAAE,EAAE;QAC7B,IAAI,CAACC,YAAY,EAAE;;;IAIvB;IACA,IAAId,KAAK,CAACmD,GAAG,KAAK,OAAO,IAAI,IAAI,CAAClG,gBAAgB,IAAI,CAAC+C,KAAK,CAACqD,QAAQ,EAAE;MACrErD,KAAK,CAACC,cAAc,EAAE;MACtB,IAAI,CAAC7F,WAAW,EAAE;;EAEtB;EAEA;EAEAkJ,eAAeA,CAACtD,KAAY;IAC1B;IACA,IAAI,IAAI,CAAC7C,eAAe,EAAE;MACxB,IAAI,CAACwC,gBAAgB,EAAE;;EAE3B;EAEQyD,cAAcA,CAAA;IACpB,IAAI,CAACpG,cAAc,GAAG,KAAK;IAC3B,IAAI,CAACC,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAACC,iBAAiB,GAAG,KAAK;IAC9B,IAAI,CAACC,eAAe,GAAG,KAAK;IAC5B,IAAI,CAACC,mBAAmB,GAAG,KAAK;IAChC,IAAI,IAAI,CAACL,WAAW,KAAK,WAAW,EAAE;MACpC,IAAI,CAACA,WAAW,GAAG,eAAe;;EAEtC;EAEA;EACAwG,cAAcA,CAAA;IACZ,OAAO,UAAU,IAAI,CAACxG,WAAW,EAAE;EACrC;EAEAyG,cAAcA,CAAA;IACZ,QAAQ,IAAI,CAACzG,WAAW;MACtB,KAAK,OAAO;QAAE,OAAO,kBAAkB;MACvC,KAAK,eAAe;QAAE,OAAO,0BAA0B;MACvD,KAAK,QAAQ;QAAE,OAAO,iBAAiB,IAAI,CAACpI,WAAW,oBAAoB;MAC3E,KAAK,WAAW;QAAE,OAAO,sBAAsB;MAC/C;QAAS,OAAO,EAAE;;EAEtB;EAEAoG,YAAYA,CAAC0I,KAAa,EAAE3E,OAAgB;IAC1C,OAAOA,OAAO,CAACQ,EAAE;EACnB;;;uBA5jBW9C,gBAAgB,EAAArI,EAAA,CAAAuP,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAzP,EAAA,CAAAuP,iBAAA,CAAAG,EAAA,CAAAC,cAAA,GAAA3P,EAAA,CAAAuP,iBAAA,CAAAK,EAAA,CAAAC,mBAAA;IAAA;EAAA;;;YAAhBxH,gBAAgB;MAAAyH,SAAA;MAAAC,YAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAhBjQ,EAAA,CAAAS,UAAA,qBAAA0P,4CAAA/O,MAAA;YAAA,OAAA8O,GAAA,CAAAnB,SAAA,CAAA3N,MAAA,CAAiB;UAAA,UAAApB,EAAA,CAAAoQ,iBAAA,CAAD,mBAAAC,0CAAAjP,MAAA;YAAA,OAAhB8O,GAAA,CAAAf,eAAA,CAAA/N,MAAA,CAAuB;UAAA,UAAApB,EAAA,CAAAoQ,iBAAA,CAAP;;;;;;;;;;UC7CzBpQ,EAHJ,CAAAE,cAAA,aAA2B,aAEK,aAU3B;UADCF,EAJA,CAAAS,UAAA,mBAAA6P,+CAAA;YAAA,OAASJ,GAAA,CAAA3E,aAAA,EAAe;UAAA,EAAC,yBAAAgF,qDAAAnP,MAAA;YAAA,OACV8O,GAAA,CAAAtE,kBAAA,CAAAxK,MAAA,CAA0B;UAAA,EAAC,wBAAAoP,oDAAApP,MAAA;YAAA,OAC5B8O,GAAA,CAAAjE,kBAAA,CAAA7K,MAAA,CAA0B;UAAA,EAAC,sBAAAqP,kDAAA;YAAA,OAC7BP,GAAA,CAAA3D,gBAAA,EAAkB;UAAA,EAAC,uBAAAmE,mDAAA;YAAA,OAClBR,GAAA,CAAAzD,iBAAA,EAAmB;UAAA,EAAC;UAEjCzM,EAAA,CAAAE,cAAA,aAA0B;UAExBF,EADA,CAAA6C,UAAA,IAAA8N,+BAAA,iBAAyD,IAAAC,+BAAA,iBACM;UAKrE5Q,EAFI,CAAAI,YAAA,EAAM,EACF,EACF;UASNJ,EANA,CAAA6C,UAAA,IAAAgO,+BAAA,iBAA2C,IAAAC,+BAAA,kBAY1C;UAWH9Q,EAAA,CAAAI,YAAA,EAAM;UA8TNJ,EA3TA,CAAA6C,UAAA,IAAAkO,+BAAA,kBAA8E,IAAAC,+BAAA,kBA+CO,KAAAC,gCAAA,iBAsJA,KAAAC,gCAAA,kBA2BG,KAAAC,gCAAA,iBA2F+B;;;UAvWjHnR,EAAA,CAAAK,SAAA,GAA0B;UAA1BL,EAAA,CAAAoR,UAAA,CAAAlB,GAAA,CAAAd,cAAA,GAA0B;UAC1BpP,EAAA,CAAAkD,UAAA,UAAAgN,GAAA,CAAAb,cAAA,GAA0B;UAQErP,EAAA,CAAAK,SAAA,GAA6B;UAA7BL,EAAA,CAAAkD,UAAA,SAAAgN,GAAA,CAAAtH,WAAA,aAA6B;UACxB5I,EAAA,CAAAK,SAAA,EAA8B;UAA9BL,EAAA,CAAAkD,UAAA,SAAAgN,GAAA,CAAAtH,WAAA,cAA8B;UAQ3C5I,EAAA,CAAAK,SAAA,EAAiB;UAAjBL,EAAA,CAAAkD,UAAA,SAAAgN,GAAA,CAAAjP,WAAA,CAAiB;UAQtCjB,EAAA,CAAAK,SAAA,EAAqB;UAArBL,EAAA,CAAAkD,UAAA,SAAAgN,GAAA,CAAAlH,eAAA,CAAqB;UAkBEhJ,EAAA,CAAAK,SAAA,EAAoB;UAApBL,EAAA,CAAAkD,UAAA,SAAAgN,GAAA,CAAArH,cAAA,CAAoB;UA+CpB7I,EAAA,CAAAK,SAAA,EAAsB;UAAtBL,EAAA,CAAAkD,UAAA,SAAAgN,GAAA,CAAApH,gBAAA,CAAsB;UAsJtB9I,EAAA,CAAAK,SAAA,EAAuB;UAAvBL,EAAA,CAAAkD,UAAA,SAAAgN,GAAA,CAAAnH,iBAAA,CAAuB;UA2BvB/I,EAAA,CAAAK,SAAA,EAAyB;UAAzBL,EAAA,CAAAkD,UAAA,SAAAgN,GAAA,CAAAjH,mBAAA,CAAyB;UA2FxBjJ,EAAA,CAAAK,SAAA,EAAwF;UAAxFL,EAAA,CAAAkD,UAAA,UAAAgN,GAAA,CAAArH,cAAA,KAAAqH,GAAA,CAAApH,gBAAA,KAAAoH,GAAA,CAAAnH,iBAAA,KAAAmH,GAAA,CAAAjH,mBAAA,CAAwF;;;qBDhUzGtJ,YAAY,EAAA0R,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAAF,EAAA,CAAAG,QAAA,EAAE5R,WAAW,EAAA6R,EAAA,CAAAC,oBAAA,EAAAD,EAAA,CAAAE,eAAA,EAAAF,EAAA,CAAAG,kBAAA,EAAAH,EAAA,CAAAI,OAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}