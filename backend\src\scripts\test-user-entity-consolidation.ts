#!/usr/bin/env ts-node

/**
 * QSC User Entity Consolidation Test
 * 
 * This script tests the consolidated user entity to ensure:
 * 1. User creation with username/secretWord works
 * 2. Authentication flows work correctly
 * 3. No legacy password fields are accessible
 * 4. All security features function properly
 */

import { NestFactory } from '@nestjs/core';
import { AppModule } from '../app.module';
import { UserService } from '../user/user.service';
import { AuthService } from '../auth/auth.service';
import { DataSource } from 'typeorm';
import { Logger } from '@nestjs/common';

const logger = new Logger('UserEntityConsolidationTest');

interface TestResult {
  testName: string;
  passed: boolean;
  message: string;
  details?: any;
}

class UserEntityConsolidationTest {
  private userService: UserService;
  private authService: AuthService;
  private dataSource: DataSource;
  private testResults: TestResult[] = [];

  constructor(userService: UserService, authService: AuthService, dataSource: DataSource) {
    this.userService = userService;
    this.authService = authService;
    this.dataSource = dataSource;
  }

  private addResult(testName: string, passed: boolean, message: string, details?: any): void {
    this.testResults.push({ testName, passed, message, details });
    
    if (passed) {
      logger.log(`✅ ${testName}: ${message}`);
    } else {
      logger.error(`❌ ${testName}: ${message}`);
      if (details) {
        logger.error(`   Details:`, details);
      }
    }
  }

  async testUserCreationWithSecretWord(): Promise<void> {
    try {
      const testUsername = `test_user_${Date.now()}`;
      const testEmail = `test_${Date.now()}@example.com`;
      const testSecretWord = 'T3st!';

      const user = await this.userService.createWithSecretWord(testUsername, testEmail, testSecretWord);

      // Verify user was created correctly
      if (!user.id || !user.username || !user.secretWordHash) {
        this.addResult(
          'User Creation',
          false,
          'User creation failed - missing required fields',
          { user: { id: user.id, username: user.username, hasSecretWordHash: !!user.secretWordHash } }
        );
        return;
      }

      // Verify secretWordHash structure
      if (!user.secretWordHash.hash || !user.secretWordHash.salt) {
        this.addResult(
          'User Creation',
          false,
          'SecretWordHash structure is invalid',
          { secretWordHash: user.secretWordHash }
        );
        return;
      }

      // Verify no password fields exist
      const userObj = user as any;
      const passwordFields = ['password', 'passwordHash', 'hashedPassword'];
      const foundPasswordFields = passwordFields.filter(field => userObj[field] !== undefined);

      if (foundPasswordFields.length > 0) {
        this.addResult(
          'User Creation',
          false,
          'User entity contains legacy password fields',
          { foundPasswordFields }
        );
        return;
      }

      this.addResult(
        'User Creation',
        true,
        'User created successfully with username/secretWord authentication'
      );

      // Clean up test user
      await this.dataSource.query('DELETE FROM users WHERE id = ?', [user.id]);

    } catch (error) {
      this.addResult(
        'User Creation',
        false,
        'User creation failed with error',
        error
      );
    }
  }

  async testAuthenticationFlow(): Promise<void> {
    try {
      const testUsername = `auth_test_${Date.now()}`;
      const testEmail = `auth_test_${Date.now()}@example.com`;
      const testSecretWord = 'A7th!';

      // Create test user
      const user = await this.userService.createWithSecretWord(testUsername, testEmail, testSecretWord);

      // Test authentication
      const authResult = await this.authService.validateUser(testUsername, testSecretWord);

      if (!authResult) {
        this.addResult(
          'Authentication Flow',
          false,
          'Authentication failed for valid credentials'
        );
        return;
      }

      // Test login
      const loginResult = await this.authService.login(authResult);

      if (!loginResult.accessToken || !loginResult.user) {
        this.addResult(
          'Authentication Flow',
          false,
          'Login failed to return required tokens and user data',
          { hasAccessToken: !!loginResult.accessToken, hasUser: !!loginResult.user }
        );
        return;
      }

      // Verify user data in login response
      if (loginResult.user.username !== testUsername) {
        this.addResult(
          'Authentication Flow',
          false,
          'Login response contains incorrect user data',
          { expected: testUsername, actual: loginResult.user.username }
        );
        return;
      }

      this.addResult(
        'Authentication Flow',
        true,
        'Authentication and login flow completed successfully'
      );

      // Clean up test user
      await this.dataSource.query('DELETE FROM users WHERE id = ?', [user.id]);

    } catch (error) {
      this.addResult(
        'Authentication Flow',
        false,
        'Authentication flow failed with error',
        error
      );
    }
  }

  async testSecurityFeatures(): Promise<void> {
    try {
      const testUsername = `security_test_${Date.now()}`;
      const testEmail = `security_test_${Date.now()}@example.com`;
      const testSecretWord = 'S3c!';

      // Create test user
      const user = await this.userService.createWithSecretWord(testUsername, testEmail, testSecretWord);

      // Test failed attempt tracking
      await this.userService.incrementFailedAttempts(user.id);
      const userAfterFailedAttempt = await this.userService.findById(user.id);

      if (userAfterFailedAttempt.failedAttempts !== 1) {
        this.addResult(
          'Security Features',
          false,
          'Failed attempt tracking not working correctly',
          { expected: 1, actual: userAfterFailedAttempt.failedAttempts }
        );
        return;
      }

      // Test account lockout after multiple failed attempts
      for (let i = 0; i < 4; i++) {
        await this.userService.incrementFailedAttempts(user.id);
      }

      const lockedUser = await this.userService.findById(user.id);
      if (lockedUser.accountStatus !== 'compromised' || lockedUser.isActive !== false) {
        this.addResult(
          'Security Features',
          false,
          'Account lockout not working correctly',
          { 
            accountStatus: lockedUser.accountStatus, 
            isActive: lockedUser.isActive,
            failedAttempts: lockedUser.failedAttempts 
          }
        );
        return;
      }

      // Test reset failed attempts
      await this.userService.resetFailedAttempts(user.id);
      const resetUser = await this.userService.findById(user.id);

      if (resetUser.failedAttempts !== 0) {
        this.addResult(
          'Security Features',
          false,
          'Reset failed attempts not working correctly',
          { expected: 0, actual: resetUser.failedAttempts }
        );
        return;
      }

      this.addResult(
        'Security Features',
        true,
        'All security features working correctly'
      );

      // Clean up test user
      await this.dataSource.query('DELETE FROM users WHERE id = ?', [user.id]);

    } catch (error) {
      this.addResult(
        'Security Features',
        false,
        'Security features test failed with error',
        error
      );
    }
  }

  async testLegacyMethodCompatibility(): Promise<void> {
    try {
      const testUsername = `legacy_test_${Date.now()}`;
      const testEmail = `legacy_test_${Date.now()}@example.com`;
      const testPassword = 'L3g@cy!';

      // Test that legacy create method converts to secret word
      const user = await this.userService.create(testUsername, testEmail, testPassword);

      if (!user.secretWordHash || !user.secretWordHash.hash) {
        this.addResult(
          'Legacy Method Compatibility',
          false,
          'Legacy create method did not create secretWordHash',
          { hasSecretWordHash: !!user.secretWordHash }
        );
        return;
      }

      // Verify authentication works with the converted secret word
      const authResult = await this.authService.validateUser(testUsername, testPassword);

      if (!authResult) {
        this.addResult(
          'Legacy Method Compatibility',
          false,
          'Authentication failed for legacy-created user'
        );
        return;
      }

      this.addResult(
        'Legacy Method Compatibility',
        true,
        'Legacy methods properly convert to secretWord authentication'
      );

      // Clean up test user
      await this.dataSource.query('DELETE FROM users WHERE id = ?', [user.id]);

    } catch (error) {
      this.addResult(
        'Legacy Method Compatibility',
        false,
        'Legacy method compatibility test failed with error',
        error
      );
    }
  }

  async runAllTests(): Promise<void> {
    logger.log('🧪 Starting QSC User Entity Consolidation Tests...');
    logger.log('');

    await this.testUserCreationWithSecretWord();
    await this.testAuthenticationFlow();
    await this.testSecurityFeatures();
    await this.testLegacyMethodCompatibility();

    logger.log('');
    this.printSummary();
  }

  private printSummary(): void {
    const totalTests = this.testResults.length;
    const passedTests = this.testResults.filter(result => result.passed).length;
    const failedTests = totalTests - passedTests;

    logger.log('📊 Test Summary:');
    logger.log(`   Total Tests: ${totalTests}`);
    logger.log(`   Passed: ${passedTests}`);
    logger.log(`   Failed: ${failedTests}`);
    logger.log('');

    if (failedTests === 0) {
      logger.log('🎉 All tests passed! User entity consolidation is working correctly.');
      logger.log('');
      logger.log('✨ Consolidation Benefits:');
      logger.log('   - Single ORM solution (TypeORM)');
      logger.log('   - Unified username/secretWord authentication');
      logger.log('   - No legacy password vulnerabilities');
      logger.log('   - Consistent security features');
      logger.log('   - Post-quantum cryptography ready');
    } else {
      logger.error('💥 Some tests failed. Please review the errors above.');
      logger.log('');
      logger.log('Failed tests:');
      this.testResults
        .filter(result => !result.passed)
        .forEach(result => {
          logger.log(`   - ${result.testName}: ${result.message}`);
        });
    }
  }
}

async function runConsolidationTests(): Promise<void> {
  const app = await NestFactory.create(AppModule);
  const userService = app.get(UserService);
  const authService = app.get(AuthService);
  const dataSource = app.get(DataSource);

  const tester = new UserEntityConsolidationTest(userService, authService, dataSource);
  
  try {
    await tester.runAllTests();
  } catch (error) {
    logger.error('Test suite failed:', error);
    process.exit(1);
  }

  await app.close();
}

if (require.main === module) {
  runConsolidationTests().catch(error => {
    logger.error('Test execution failed:', error);
    process.exit(1);
  });
}

export { runConsolidationTests };
