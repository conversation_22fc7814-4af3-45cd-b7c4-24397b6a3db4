{"ast": null, "code": "import { asyncScheduler } from '../scheduler/async';\nimport { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nexport function debounceTime(dueTime, scheduler = asyncScheduler) {\n  return operate((source, subscriber) => {\n    let activeTask = null;\n    let lastValue = null;\n    let lastTime = null;\n    const emit = () => {\n      if (activeTask) {\n        activeTask.unsubscribe();\n        activeTask = null;\n        const value = lastValue;\n        lastValue = null;\n        subscriber.next(value);\n      }\n    };\n    function emitWhenIdle() {\n      const targetTime = lastTime + dueTime;\n      const now = scheduler.now();\n      if (now < targetTime) {\n        activeTask = this.schedule(undefined, targetTime - now);\n        subscriber.add(activeTask);\n        return;\n      }\n      emit();\n    }\n    source.subscribe(createOperatorSubscriber(subscriber, value => {\n      lastValue = value;\n      lastTime = scheduler.now();\n      if (!activeTask) {\n        activeTask = scheduler.schedule(emitWhenIdle, dueTime);\n        subscriber.add(activeTask);\n      }\n    }, () => {\n      emit();\n      subscriber.complete();\n    }, undefined, () => {\n      lastValue = activeTask = null;\n    }));\n  });\n}", "map": {"version": 3, "names": ["asyncScheduler", "operate", "createOperatorSubscriber", "debounceTime", "dueTime", "scheduler", "source", "subscriber", "activeTask", "lastValue", "lastTime", "emit", "unsubscribe", "value", "next", "emitWhenIdle", "targetTime", "now", "schedule", "undefined", "add", "subscribe", "complete"], "sources": ["D:/TCL1/Projects/Projects/QSC1/frontend/node_modules/rxjs/dist/esm/internal/operators/debounceTime.js"], "sourcesContent": ["import { asyncScheduler } from '../scheduler/async';\nimport { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nexport function debounceTime(dueTime, scheduler = asyncScheduler) {\n    return operate((source, subscriber) => {\n        let activeTask = null;\n        let lastValue = null;\n        let lastTime = null;\n        const emit = () => {\n            if (activeTask) {\n                activeTask.unsubscribe();\n                activeTask = null;\n                const value = lastValue;\n                lastValue = null;\n                subscriber.next(value);\n            }\n        };\n        function emitWhenIdle() {\n            const targetTime = lastTime + dueTime;\n            const now = scheduler.now();\n            if (now < targetTime) {\n                activeTask = this.schedule(undefined, targetTime - now);\n                subscriber.add(activeTask);\n                return;\n            }\n            emit();\n        }\n        source.subscribe(createOperatorSubscriber(subscriber, (value) => {\n            lastValue = value;\n            lastTime = scheduler.now();\n            if (!activeTask) {\n                activeTask = scheduler.schedule(emitWhenIdle, dueTime);\n                subscriber.add(activeTask);\n            }\n        }, () => {\n            emit();\n            subscriber.complete();\n        }, undefined, () => {\n            lastValue = activeTask = null;\n        }));\n    });\n}\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,oBAAoB;AACnD,SAASC,OAAO,QAAQ,cAAc;AACtC,SAASC,wBAAwB,QAAQ,sBAAsB;AAC/D,OAAO,SAASC,YAAYA,CAACC,OAAO,EAAEC,SAAS,GAAGL,cAAc,EAAE;EAC9D,OAAOC,OAAO,CAAC,CAACK,MAAM,EAAEC,UAAU,KAAK;IACnC,IAAIC,UAAU,GAAG,IAAI;IACrB,IAAIC,SAAS,GAAG,IAAI;IACpB,IAAIC,QAAQ,GAAG,IAAI;IACnB,MAAMC,IAAI,GAAGA,CAAA,KAAM;MACf,IAAIH,UAAU,EAAE;QACZA,UAAU,CAACI,WAAW,CAAC,CAAC;QACxBJ,UAAU,GAAG,IAAI;QACjB,MAAMK,KAAK,GAAGJ,SAAS;QACvBA,SAAS,GAAG,IAAI;QAChBF,UAAU,CAACO,IAAI,CAACD,KAAK,CAAC;MAC1B;IACJ,CAAC;IACD,SAASE,YAAYA,CAAA,EAAG;MACpB,MAAMC,UAAU,GAAGN,QAAQ,GAAGN,OAAO;MACrC,MAAMa,GAAG,GAAGZ,SAAS,CAACY,GAAG,CAAC,CAAC;MAC3B,IAAIA,GAAG,GAAGD,UAAU,EAAE;QAClBR,UAAU,GAAG,IAAI,CAACU,QAAQ,CAACC,SAAS,EAAEH,UAAU,GAAGC,GAAG,CAAC;QACvDV,UAAU,CAACa,GAAG,CAACZ,UAAU,CAAC;QAC1B;MACJ;MACAG,IAAI,CAAC,CAAC;IACV;IACAL,MAAM,CAACe,SAAS,CAACnB,wBAAwB,CAACK,UAAU,EAAGM,KAAK,IAAK;MAC7DJ,SAAS,GAAGI,KAAK;MACjBH,QAAQ,GAAGL,SAAS,CAACY,GAAG,CAAC,CAAC;MAC1B,IAAI,CAACT,UAAU,EAAE;QACbA,UAAU,GAAGH,SAAS,CAACa,QAAQ,CAACH,YAAY,EAAEX,OAAO,CAAC;QACtDG,UAAU,CAACa,GAAG,CAACZ,UAAU,CAAC;MAC9B;IACJ,CAAC,EAAE,MAAM;MACLG,IAAI,CAAC,CAAC;MACNJ,UAAU,CAACe,QAAQ,CAAC,CAAC;IACzB,CAAC,EAAEH,SAAS,EAAE,MAAM;MAChBV,SAAS,GAAGD,UAAU,GAAG,IAAI;IACjC,CAAC,CAAC,CAAC;EACP,CAAC,CAAC;AACN", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}