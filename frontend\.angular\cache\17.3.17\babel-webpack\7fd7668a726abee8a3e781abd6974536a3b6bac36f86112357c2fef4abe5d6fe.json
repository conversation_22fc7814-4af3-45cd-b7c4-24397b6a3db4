{"ast": null, "code": "import { provideRouter } from '@angular/router';\nimport { routes } from './app.routes';\nimport { provideClientHydration } from '@angular/platform-browser';\nimport { WasmService } from './services/wasm.service';\nimport { ErrorHandlingService } from './services/error-handling.service';\nexport const appConfig = {\n  providers: [provideRouter(routes), provideClientHydration(), WasmService, ErrorHandlingService]\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}