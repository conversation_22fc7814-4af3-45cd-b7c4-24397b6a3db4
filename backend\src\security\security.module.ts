import { Modu<PERSON> } from '@nestjs/common';
import { PQCService } from './pqc.service';
import { EncryptionService } from './encryption.service';
import { KeyManagementService } from './key-management.service';
import { LibOQSService } from './services/liboqs.service';
import { KyberService } from './services/kyber.service';

@Module({
  providers: [PQCService, EncryptionService, KeyManagementService, LibOQSService, KyberService],
  exports: [PQCService, EncryptionService, KeyManagementService, LibOQSService, KyberService],
})
export class SecurityModule {}