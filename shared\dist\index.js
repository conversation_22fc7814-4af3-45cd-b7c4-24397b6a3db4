"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __exportStar = (this && this.__exportStar) || function(m, exports) {
    for (var p in m) if (p !== "default" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.QSC_CONSTANTS = exports.SHARED_VERSION = void 0;
// Export all types
__exportStar(require("./types"), exports);
// Export all utilities
__exportStar(require("./utils"), exports);
// Version information
exports.SHARED_VERSION = '1.0.0';
// Common constants used across the application
exports.QSC_CONSTANTS = {
    APP_NAME: 'Quantum-Secure Chat',
    APP_VERSION: '1.0.0',
    API_VERSION: 'v1',
    // Security constants
    MAX_LOGIN_ATTEMPTS: 3,
    LOCKOUT_DURATION: 15 * 60 * 1000, // 15 minutes
    SESSION_TIMEOUT: 24 * 60 * 60 * 1000, // 24 hours
    KEY_ROTATION_INTERVAL: 30 * 24 * 60 * 60 * 1000, // 30 days
    // Message constants
    MESSAGE_EXPIRY_DEFAULT: 7 * 24 * 60 * 60 * 1000, // 7 days
    MESSAGE_EXPIRY_MAX: 30 * 24 * 60 * 60 * 1000, // 30 days
    // File upload constants
    MAX_FILE_SIZE: 50 * 1024 * 1024, // 50MB
    ALLOWED_FILE_TYPES: [
        'image/jpeg',
        'image/png',
        'image/gif',
        'image/webp',
        'application/pdf',
        'text/plain',
        'application/json',
    ],
    // Rate limiting
    RATE_LIMIT_WINDOW: 15 * 60 * 1000, // 15 minutes
    RATE_LIMIT_MAX_REQUESTS: 100,
    // WebSocket events
    WS_EVENTS: {
        MESSAGE: 'message',
        MESSAGE_READ: 'message_read',
        MESSAGE_DELETED: 'message_deleted',
        USER_ONLINE: 'user_online',
        USER_OFFLINE: 'user_offline',
        USER_TYPING: 'user_typing',
        ROOM_JOINED: 'room_joined',
        ROOM_LEFT: 'room_left',
        KEY_ROTATION: 'key_rotation',
        SECURITY_ALERT: 'security_alert',
    },
    // Error codes
    ERROR_CODES: {
        // Authentication errors
        INVALID_CREDENTIALS: 'INVALID_CREDENTIALS',
        ACCOUNT_LOCKED: 'ACCOUNT_LOCKED',
        ACCOUNT_COMPROMISED: 'ACCOUNT_COMPROMISED',
        TOKEN_EXPIRED: 'TOKEN_EXPIRED',
        TOKEN_INVALID: 'TOKEN_INVALID',
        // Authorization errors
        INSUFFICIENT_PERMISSIONS: 'INSUFFICIENT_PERMISSIONS',
        ACCESS_DENIED: 'ACCESS_DENIED',
        // Validation errors
        VALIDATION_ERROR: 'VALIDATION_ERROR',
        INVALID_INPUT: 'INVALID_INPUT',
        MISSING_REQUIRED_FIELD: 'MISSING_REQUIRED_FIELD',
        // Crypto errors
        CRYPTO_ERROR: 'CRYPTO_ERROR',
        KEY_GENERATION_FAILED: 'KEY_GENERATION_FAILED',
        ENCRYPTION_FAILED: 'ENCRYPTION_FAILED',
        DECRYPTION_FAILED: 'DECRYPTION_FAILED',
        SIGNATURE_VERIFICATION_FAILED: 'SIGNATURE_VERIFICATION_FAILED',
        // Database errors
        DATABASE_ERROR: 'DATABASE_ERROR',
        RECORD_NOT_FOUND: 'RECORD_NOT_FOUND',
        DUPLICATE_RECORD: 'DUPLICATE_RECORD',
        // Network errors
        NETWORK_ERROR: 'NETWORK_ERROR',
        CONNECTION_TIMEOUT: 'CONNECTION_TIMEOUT',
        SERVICE_UNAVAILABLE: 'SERVICE_UNAVAILABLE',
        // File errors
        FILE_TOO_LARGE: 'FILE_TOO_LARGE',
        INVALID_FILE_TYPE: 'INVALID_FILE_TYPE',
        FILE_UPLOAD_FAILED: 'FILE_UPLOAD_FAILED',
        // Rate limiting
        RATE_LIMIT_EXCEEDED: 'RATE_LIMIT_EXCEEDED',
        // Generic errors
        INTERNAL_SERVER_ERROR: 'INTERNAL_SERVER_ERROR',
        BAD_REQUEST: 'BAD_REQUEST',
        NOT_FOUND: 'NOT_FOUND',
        CONFLICT: 'CONFLICT',
    },
    // HTTP status codes
    HTTP_STATUS: {
        OK: 200,
        CREATED: 201,
        NO_CONTENT: 204,
        BAD_REQUEST: 400,
        UNAUTHORIZED: 401,
        FORBIDDEN: 403,
        NOT_FOUND: 404,
        CONFLICT: 409,
        UNPROCESSABLE_ENTITY: 422,
        TOO_MANY_REQUESTS: 429,
        INTERNAL_SERVER_ERROR: 500,
        SERVICE_UNAVAILABLE: 503,
    },
};
//# sourceMappingURL=index.js.map