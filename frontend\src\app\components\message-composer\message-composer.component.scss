.composer-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 1rem;
}

.composer-card {
  background: white;
  border-radius: 20px;
  padding: 2rem;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  width: 100%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
}

.composer-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #eee;

  h2 {
    margin: 0;
    color: #333;
    font-weight: 500;
  }

  .close-btn {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: #999;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.2s ease;

    &:hover {
      background: #f5f5f5;
      color: #333;
    }
  }
}

.composer-form {
  .form-group {
    margin-bottom: 1.5rem;

    label {
      display: block;
      margin-bottom: 0.5rem;
      color: #333;
      font-weight: 500;
      font-size: 0.9rem;
    }

    .recipient-input,
    .expiration-select {
      width: 100%;
      padding: 0.75rem 1rem;
      border: 2px solid #e1e5e9;
      border-radius: 10px;
      font-size: 1rem;
      transition: all 0.3s ease;
      background: white;

      &:focus {
        outline: none;
        border-color: #667eea;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
      }

      &:disabled {
        background: #f5f5f5;
        cursor: not-allowed;
      }
    }

    &.message-group {
      position: relative;

      .message-textarea {
        width: 100%;
        padding: 1rem;
        border: 2px solid #e1e5e9;
        border-radius: 10px;
        font-size: 1rem;
        font-family: inherit;
        resize: vertical;
        min-height: 120px;
        transition: all 0.3s ease;

        &:focus {
          outline: none;
          border-color: #667eea;
          box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        &:disabled {
          background: #f5f5f5;
          cursor: not-allowed;
        }

        &::placeholder {
          color: #999;
        }
      }

      .character-counter {
        position: absolute;
        bottom: 0.5rem;
        right: 1rem;
        font-size: 0.8rem;
        color: #999;
        background: white;
        padding: 0.25rem 0.5rem;
        border-radius: 4px;

        .warning {
          color: #e74c3c;
        }
      }
    }
  }
}

.message-feedback {
  margin-bottom: 1.5rem;

  .error-message {
    background: #fee;
    color: #c33;
    padding: 0.75rem;
    border-radius: 8px;
    font-size: 0.9rem;
    border: 1px solid #fcc;
  }

  .success-message {
    background: #efe;
    color: #3c3;
    padding: 0.75rem;
    border-radius: 8px;
    font-size: 0.9rem;
    border: 1px solid #cfc;
  }
}

.composer-actions {
  display: flex;
  gap: 1rem;
  margin-bottom: 1.5rem;

  .cancel-btn,
  .send-btn {
    flex: 1;
    padding: 0.875rem;
    border: none;
    border-radius: 10px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;

    &:disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }
  }

  .cancel-btn {
    background: #f5f5f5;
    color: #666;

    &:hover:not(:disabled) {
      background: #e8e8e8;
    }
  }

  .send-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;

    &:hover:not(:disabled) {
      transform: translateY(-2px);
      box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
    }

    .loading-spinner {
      animation: pulse 1.5s ease-in-out infinite;
    }
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.security-info {
  text-align: center;
  padding-top: 1rem;
  border-top: 1px solid #eee;

  .security-badge {
    color: #667eea;
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
    font-weight: 500;
  }

  .keyboard-shortcuts {
    color: #999;
    font-size: 0.8rem;
  }
}

@media (max-width: 768px) {
  .composer-container {
    padding: 0.5rem;
  }

  .composer-card {
    padding: 1.5rem;
    border-radius: 15px;
  }

  .composer-actions {
    flex-direction: column;
  }

  .keyboard-shortcuts {
    display: none;
  }
}
