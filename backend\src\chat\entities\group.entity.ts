import { 
  <PERSON><PERSON><PERSON>, 
  Column, 
  PrimaryGeneratedColumn, 
  CreateDateColumn, 
  UpdateDateColumn,
  ManyToOne,
  OneToMany,
  JoinColumn
} from 'typeorm';
import { User } from '../../users/entities/user.entity';
import { GroupMember } from './group-member.entity';
import { IGroup } from '@qsc/shared';

@Entity('groups')
export class Group implements IGroup {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  ownerId: string;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'owner_id' })
  owner: User;

  @Column()
  onionAddress: string;

  @Column()
  encryptedConfig: string;

  @Column()
  masterKey: string;

  @Column()
  ownerDeviceHash: string;

  @CreateDateColumn()
  createdAt: Date;

  @Column()
  lastKeyRotation: Date;

  @Column({ default: true })
  isActive: boolean;

  @Column()
  securityThreshold: number;

  @OneToMany(() => GroupMember, member => member.group)
  members: GroupMember[];
}
