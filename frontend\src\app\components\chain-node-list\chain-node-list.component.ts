import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ChainDeletionService } from '../../services/chain-deletion.service';
import { ErrorHandlingService } from '../../services/error-handling.service';

interface ChainNode {
  id: string;
  messageId: string;
  timestamp: Date;
  deleted: boolean;
  deletionTimestamp?: Date;
}

@Component({
  selector: 'app-chain-node-list',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div class="chain-node-list">
      <h2>Message Chain</h2>
      <div *ngIf="errorMessage" class="error-message">
        {{ errorMessage }}
      </div>
      <div *ngIf="chainNodes.length === 0" class="no-nodes">
        No messages in chain
      </div>
      <div *ngFor="let node of chainNodes"
           class="chain-node"
           [class.deleted]="node.deleted">
        <div class="node-header">
          <span class="node-id">ID: {{ node.id }}</span>
          <span class="node-time">
            {{ node.timestamp | date:'medium' }}
          </span>
        </div>
        <div class="node-details">
          <div class="message-id">Message ID: {{ node.messageId }}</div>
          <div *ngIf="node.deleted" class="deletion-info">
            Deleted at: {{ node.deletionTimestamp | date:'medium' }}
          </div>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .chain-node-list {
      padding: 1rem;
      max-width: 800px;
      margin: 0 auto;
    }

    h2 {
      margin-bottom: 1rem;
      color: #333;
    }

    .error-message {
      color: #dc3545;
      padding: 0.5rem;
      margin-bottom: 1rem;
      border: 1px solid #dc3545;
      border-radius: 4px;
      background-color: #f8d7da;
    }

    .no-nodes {
      text-align: center;
      color: #6c757d;
      padding: 2rem;
    }

    .chain-node {
      padding: 1rem;
      margin-bottom: 1rem;
      border: 1px solid #dee2e6;
      border-radius: 4px;
      background-color: #fff;
      transition: all 0.2s ease;

      &:hover {
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
      }

      &.deleted {
        border-left: 4px solid #dc3545;
        background-color: #f8f9fa;
        opacity: 0.8;
      }
    }

    .node-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 0.5rem;
    }

    .node-id {
      font-weight: bold;
      color: #007bff;
    }

    .node-time {
      color: #6c757d;
      font-size: 0.875rem;
    }

    .node-details {
      color: #212529;
      line-height: 1.5;
    }

    .message-id {
      font-family: monospace;
      background-color: #f8f9fa;
      padding: 0.25rem 0.5rem;
      border-radius: 4px;
      margin-bottom: 0.5rem;
    }

    .deletion-info {
      color: #dc3545;
      font-size: 0.875rem;
      margin-top: 0.5rem;
    }
  `]
})
export class ChainNodeListComponent implements OnInit {
  chainNodes: ChainNode[] = [];
  errorMessage: string | null = null;

  constructor(
    private chainDeletionService: ChainDeletionService,
    private errorHandlingService: ErrorHandlingService
  ) {}

  async ngOnInit() {
    await this.loadChainNodes();
  }

  private async loadChainNodes() {
    try {
      this.chainNodes = await this.chainDeletionService.getChainNodes();
    } catch (error) {
      this.errorHandlingService.handleError(error as Error, 'STORAGE');
      this.errorMessage = 'Failed to load chain nodes';
    }
  }
}
