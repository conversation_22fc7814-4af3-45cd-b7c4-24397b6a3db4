{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { Subject, takeUntil, take } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../services/auth.service\";\nimport * as i2 from \"../../services/message.service\";\nimport * as i3 from \"../../services/notification.service\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/forms\";\nfunction QscMainComponent_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 10);\n  }\n}\nfunction QscMainComponent_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 11);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.unreadCount, \" \");\n  }\n}\nfunction QscMainComponent_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 12)(1, \"span\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 13);\n    i0.ɵɵlistener(\"click\", function QscMainComponent_div_6_Template_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.logout());\n    });\n    i0.ɵɵtext(4, \"\\u00D7\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r0.currentUser.username);\n  }\n}\nfunction QscMainComponent_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 14);\n    i0.ɵɵlistener(\"click\", function QscMainComponent_div_7_Template_div_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelementStart(1, \"div\", 15);\n    i0.ɵɵlistener(\"click\", function QscMainComponent_div_7_Template_div_click_1_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.openAccountSettings());\n    });\n    i0.ɵɵelementStart(2, \"span\", 16);\n    i0.ɵɵtext(3, \"\\uD83D\\uDC64\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 17);\n    i0.ɵɵtext(5, \"Account Settings\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(6, \"div\", 18);\n    i0.ɵɵelementStart(7, \"div\", 19);\n    i0.ɵɵlistener(\"click\", function QscMainComponent_div_7_Template_div_click_7_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.logout());\n    });\n    i0.ɵɵelementStart(8, \"span\", 16);\n    i0.ɵɵtext(9, \"\\uD83D\\uDEAA\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"span\", 17);\n    i0.ɵɵtext(11, \"Logout\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵstyleProp(\"left\", ctx_r0.contextMenuPosition.x, \"px\")(\"top\", ctx_r0.contextMenuPosition.y, \"px\");\n  }\n}\nfunction QscMainComponent_div_8_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 33);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.loginError, \" \");\n  }\n}\nfunction QscMainComponent_div_8_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 34);\n    i0.ɵɵelement(1, \"div\", 35);\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3, \"Authenticating...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction QscMainComponent_div_8_div_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 36)(1, \"p\");\n    i0.ɵɵtext(2, \"\\uD83D\\uDD12 Protected by post-quantum cryptography\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p\", 37);\n    i0.ɵɵtext(4, \"Form auto-submits when credentials are valid\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction QscMainComponent_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 20);\n    i0.ɵɵlistener(\"click\", function QscMainComponent_div_8_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.closeLoginModal());\n    });\n    i0.ɵɵelementStart(1, \"div\", 21);\n    i0.ɵɵlistener(\"click\", function QscMainComponent_div_8_Template_div_click_1_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelementStart(2, \"div\", 22)(3, \"h2\");\n    i0.ɵɵtext(4, \"Sign In\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 23);\n    i0.ɵɵlistener(\"click\", function QscMainComponent_div_8_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.closeLoginModal());\n    });\n    i0.ɵɵtext(6, \"\\u00D7\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 24)(8, \"div\", 25)(9, \"label\", 26);\n    i0.ɵɵtext(10, \"Username\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"input\", 27);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function QscMainComponent_div_8_Template_input_ngModelChange_11_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r0 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r0.loginCredentials.username, $event) || (ctx_r0.loginCredentials.username = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"input\", function QscMainComponent_div_8_Template_input_input_11_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.onLoginInputChange());\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"div\", 25)(13, \"label\", 28);\n    i0.ɵɵtext(14, \"Secret Word\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"input\", 29);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function QscMainComponent_div_8_Template_input_ngModelChange_15_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r0 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r0.loginCredentials.secretWord, $event) || (ctx_r0.loginCredentials.secretWord = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"input\", function QscMainComponent_div_8_Template_input_input_15_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.onLoginInputChange());\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(16, QscMainComponent_div_8_div_16_Template, 2, 1, \"div\", 30)(17, QscMainComponent_div_8_div_17_Template, 4, 0, \"div\", 31)(18, QscMainComponent_div_8_div_18_Template, 5, 0, \"div\", 32);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(11);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r0.loginCredentials.username);\n    i0.ɵɵproperty(\"disabled\", ctx_r0.isLoading);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r0.loginCredentials.secretWord);\n    i0.ɵɵproperty(\"disabled\", ctx_r0.isLoading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.loginError);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isLoading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.isLoading);\n  }\n}\nfunction QscMainComponent_div_9_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 64)(1, \"span\", 65);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 66);\n    i0.ɵɵlistener(\"click\", function QscMainComponent_div_9_div_19_Template_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.switchMessageType(ctx_r0.messageType));\n    });\n    i0.ɵɵtext(4, \"\\u00D7\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r0.getSelectedRecipientName());\n  }\n}\nfunction QscMainComponent_div_9_div_20_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 70);\n    i0.ɵɵlistener(\"click\", function QscMainComponent_div_9_div_20_div_1_Template_div_click_0_listener() {\n      const contact_r8 = i0.ɵɵrestoreView(_r7).$implicit;\n      const ctx_r0 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r0.selectContact(contact_r8));\n    });\n    i0.ɵɵelementStart(1, \"div\", 71)(2, \"span\", 72);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 73);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 74);\n    i0.ɵɵelement(7, \"span\", 75);\n    i0.ɵɵelementStart(8, \"span\", 76);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const contact_r8 = ctx.$implicit;\n    const ctx_r0 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"hidden\", ctx_r0.messageType !== \"direct\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(contact_r8.username);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(contact_r8.email);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"online\", contact_r8.isOnline)(\"offline\", !contact_r8.isOnline);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", contact_r8.isOnline ? \"Online\" : \"Offline\", \" \");\n  }\n}\nfunction QscMainComponent_div_9_div_20_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 70);\n    i0.ɵɵlistener(\"click\", function QscMainComponent_div_9_div_20_div_2_Template_div_click_0_listener() {\n      const group_r10 = i0.ɵɵrestoreView(_r9).$implicit;\n      const ctx_r0 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r0.selectGroup(group_r10));\n    });\n    i0.ɵɵelementStart(1, \"div\", 77)(2, \"span\", 78);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 79);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 80);\n    i0.ɵɵelement(7, \"span\", 75);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const group_r10 = ctx.$implicit;\n    const ctx_r0 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"hidden\", ctx_r0.messageType !== \"group\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(group_r10.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", group_r10.members.length, \" members\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"active\", group_r10.isActive);\n  }\n}\nfunction QscMainComponent_div_9_div_20_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 81);\n    i0.ɵɵtext(1, \" No contacts found \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction QscMainComponent_div_9_div_20_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 81);\n    i0.ɵɵtext(1, \" No groups found \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction QscMainComponent_div_9_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 67);\n    i0.ɵɵtemplate(1, QscMainComponent_div_9_div_20_div_1_Template, 10, 8, \"div\", 68)(2, QscMainComponent_div_9_div_20_div_2_Template, 8, 5, \"div\", 68)(3, QscMainComponent_div_9_div_20_div_3_Template, 2, 0, \"div\", 69)(4, QscMainComponent_div_9_div_20_div_4_Template, 2, 0, \"div\", 69);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.filteredContacts);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.filteredGroups);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.messageType === \"direct\" && ctx_r0.filteredContacts.length === 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.messageType === \"group\" && ctx_r0.filteredGroups.length === 0);\n  }\n}\nfunction QscMainComponent_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 20);\n    i0.ɵɵlistener(\"click\", function QscMainComponent_div_9_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.closeMessageComposer());\n    });\n    i0.ɵɵelementStart(1, \"div\", 38);\n    i0.ɵɵlistener(\"click\", function QscMainComponent_div_9_Template_div_click_1_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelementStart(2, \"div\", 24)(3, \"div\", 39)(4, \"button\", 40);\n    i0.ɵɵlistener(\"click\", function QscMainComponent_div_9_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.switchMessageType(\"direct\"));\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(5, \"svg\", 41);\n    i0.ɵɵelement(6, \"path\", 42)(7, \"circle\", 43);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(8, \" Direct \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(9, \"button\", 44);\n    i0.ɵɵlistener(\"click\", function QscMainComponent_div_9_Template_button_click_9_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.switchMessageType(\"group\"));\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(10, \"svg\", 41);\n    i0.ɵɵelement(11, \"path\", 45)(12, \"circle\", 46)(13, \"path\", 47)(14, \"path\", 48);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(15, \" Group \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(16, \"div\", 25)(17, \"div\", 49)(18, \"input\", 50);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function QscMainComponent_div_9_Template_input_ngModelChange_18_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r0 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r0.recipientSearchQuery, $event) || (ctx_r0.recipientSearchQuery = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"input\", function QscMainComponent_div_9_Template_input_input_18_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.onRecipientSearchChange());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(19, QscMainComponent_div_9_div_19_Template, 5, 1, \"div\", 51)(20, QscMainComponent_div_9_div_20_Template, 5, 4, \"div\", 52);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(21, \"div\", 25)(22, \"label\", 53);\n    i0.ɵɵtext(23, \"Message\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"textarea\", 54);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function QscMainComponent_div_9_Template_textarea_ngModelChange_24_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r0 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r0.messageContent, $event) || (ctx_r0.messageContent = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"div\", 55);\n    i0.ɵɵtext(26);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(27, \"div\", 56)(28, \"button\", 57);\n    i0.ɵɵlistener(\"click\", function QscMainComponent_div_9_Template_button_click_28_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.sendMessage());\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(29, \"svg\", 41);\n    i0.ɵɵelement(30, \"line\", 58)(31, \"polygon\", 59);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(32, \" Send \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(33, \"button\", 60);\n    i0.ɵɵlistener(\"click\", function QscMainComponent_div_9_Template_button_click_33_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.closeMessageComposer());\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(34, \"svg\", 41);\n    i0.ɵɵelement(35, \"line\", 61)(36, \"line\", 62);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(37, \" Cancel \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(38, \"div\", 63)(39, \"p\");\n    i0.ɵɵtext(40, \"Press Enter to send \\u2022 Shift+Enter for new line\");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"has-groups\", ctx_r0.userGroups.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"active\", ctx_r0.messageType === \"direct\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵclassProp(\"active\", ctx_r0.messageType === \"group\");\n    i0.ɵɵadvance(9);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r0.recipientSearchQuery);\n    i0.ɵɵproperty(\"placeholder\", ctx_r0.messageType === \"direct\" ? \"Search contacts...\" : \"Search groups...\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.getSelectedRecipientName());\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.recipientSearchQuery && !ctx_r0.getSelectedRecipientName());\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r0.messageContent);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r0.messageContent.length, \"/1000\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", !ctx_r0.isMessageValid());\n  }\n}\nfunction QscMainComponent_div_10_div_8_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 87)(1, \"div\", 88)(2, \"span\", 89);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 90);\n    i0.ɵɵtext(5);\n    i0.ɵɵpipe(6, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 91);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const message_r12 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(message_r12.sender);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(6, 3, message_r12.timestamp, \"short\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(message_r12.content);\n  }\n}\nfunction QscMainComponent_div_10_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 85);\n    i0.ɵɵtemplate(1, QscMainComponent_div_10_div_8_div_1_Template, 9, 6, \"div\", 86);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.messages)(\"ngForTrackBy\", ctx_r0.trackMessage);\n  }\n}\nfunction QscMainComponent_div_10_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 92)(1, \"p\");\n    i0.ɵɵtext(2, \"No messages yet\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p\", 93);\n    i0.ɵɵtext(4, \"Click the circle to compose your first message\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction QscMainComponent_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 20);\n    i0.ɵɵlistener(\"click\", function QscMainComponent_div_10_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.closeMessagesViewer());\n    });\n    i0.ɵɵelementStart(1, \"div\", 82);\n    i0.ɵɵlistener(\"click\", function QscMainComponent_div_10_Template_div_click_1_listener($event) {\n      i0.ɵɵrestoreView(_r11);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelementStart(2, \"div\", 22)(3, \"h2\");\n    i0.ɵɵtext(4, \"Messages\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 23);\n    i0.ɵɵlistener(\"click\", function QscMainComponent_div_10_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.closeMessagesViewer());\n    });\n    i0.ɵɵtext(6, \"\\u00D7\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 24);\n    i0.ɵɵtemplate(8, QscMainComponent_div_10_div_8_Template, 2, 2, \"div\", 83)(9, QscMainComponent_div_10_div_9_Template, 5, 0, \"div\", 84);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.messages.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.messages.length === 0);\n  }\n}\nfunction QscMainComponent_div_11_img_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 114);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"src\", ctx_r0.userProfile.avatar, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction QscMainComponent_div_11_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 115)(1, \"span\", 116);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    let tmp_2_0;\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", (ctx_r0.currentUser == null ? null : ctx_r0.currentUser.username == null ? null : (tmp_2_0 = ctx_r0.currentUser.username.charAt(0)) == null ? null : tmp_2_0.toUpperCase()) || \"?\", \" \");\n  }\n}\nfunction QscMainComponent_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 20);\n    i0.ɵɵlistener(\"click\", function QscMainComponent_div_11_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.closeAccountSettings());\n    });\n    i0.ɵɵelementStart(1, \"div\", 94);\n    i0.ɵɵlistener(\"click\", function QscMainComponent_div_11_Template_div_click_1_listener($event) {\n      i0.ɵɵrestoreView(_r13);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelementStart(2, \"div\", 22)(3, \"h2\");\n    i0.ɵɵtext(4, \"Account Settings\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 23);\n    i0.ɵɵlistener(\"click\", function QscMainComponent_div_11_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.closeAccountSettings());\n    });\n    i0.ɵɵtext(6, \"\\u00D7\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 24)(8, \"div\", 95)(9, \"div\", 96)(10, \"div\", 97);\n    i0.ɵɵtemplate(11, QscMainComponent_div_11_img_11_Template, 1, 1, \"img\", 98)(12, QscMainComponent_div_11_div_12_Template, 3, 1, \"div\", 99);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"div\", 100)(14, \"label\", 101);\n    i0.ɵɵtext(15, \" \\uD83D\\uDCF7 Change Avatar \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"input\", 102);\n    i0.ɵɵlistener(\"change\", function QscMainComponent_div_11_Template_input_change_16_listener($event) {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.onAvatarChange($event));\n    });\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(17, \"div\", 103)(18, \"div\", 25)(19, \"label\");\n    i0.ɵɵtext(20, \"Username\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"div\", 104)(22, \"span\");\n    i0.ɵɵtext(23);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(24, \"div\", 25)(25, \"label\");\n    i0.ɵɵtext(26, \"Email Address\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"div\", 104)(28, \"span\");\n    i0.ɵɵtext(29);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(30, \"span\", 105);\n    i0.ɵɵtext(31, \"Email cannot be changed for security reasons\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(32, \"div\", 25)(33, \"label\");\n    i0.ɵɵtext(34, \"Phone Number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(35, \"div\", 104)(36, \"span\");\n    i0.ɵɵtext(37);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(38, \"span\", 105);\n    i0.ɵɵtext(39, \"Phone number cannot be changed for security reasons\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(40, \"div\", 106)(41, \"h3\");\n    i0.ɵɵtext(42, \"Security Information\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(43, \"div\", 107)(44, \"span\", 108);\n    i0.ɵɵtext(45, \"\\uD83D\\uDD10 Encryption:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(46, \"span\", 109);\n    i0.ɵɵtext(47, \"Post-Quantum Cryptography (ML-DSA, ML-KEM)\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(48, \"div\", 107)(49, \"span\", 108);\n    i0.ɵɵtext(50, \"\\uD83D\\uDEE1\\uFE0F Security Level:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(51, \"span\", 109);\n    i0.ɵɵtext(52, \"NIST Level 3 (AES-192 equivalent)\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(53, \"div\", 107)(54, \"span\", 108);\n    i0.ɵɵtext(55, \"\\uD83D\\uDD11 Key Rotation:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(56, \"span\", 109);\n    i0.ɵɵtext(57, \"Every 30 days\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(58, \"div\", 110)(59, \"button\", 60);\n    i0.ɵɵlistener(\"click\", function QscMainComponent_div_11_Template_button_click_59_listener() {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.closeAccountSettings());\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(60, \"svg\", 41);\n    i0.ɵɵelement(61, \"path\", 111)(62, \"polyline\", 112)(63, \"line\", 113);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(64, \" Close \");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(11);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.userProfile.avatar);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.userProfile.avatar);\n    i0.ɵɵadvance(11);\n    i0.ɵɵtextInterpolate((ctx_r0.currentUser == null ? null : ctx_r0.currentUser.username) || \"Not set\");\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r0.userProfile.email || \"Not set\");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate(ctx_r0.userProfile.phoneNumber || \"Not set\");\n  }\n}\nfunction QscMainComponent_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 117)(1, \"span\");\n    i0.ɵɵtext(2, \"ESC to close modals \\u2022 Right-click circle for menu\");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport class QscMainComponent {\n  // Getter for template access\n  get userGroups() {\n    return this.groups;\n  }\n  constructor(authService, messageService, notificationService) {\n    this.authService = authService;\n    this.messageService = messageService;\n    this.notificationService = notificationService;\n    this.destroy$ = new Subject();\n    // Circle state management\n    this.circleState = 'guest';\n    // Modal states\n    this.showLoginModal = false;\n    this.showMessageModal = false;\n    this.showMessagesModal = false;\n    this.showContextMenu = false;\n    this.showAccountSettings = false;\n    // Context menu position\n    this.contextMenuPosition = {\n      x: 0,\n      y: 0\n    };\n    // Authentication\n    this.loginCredentials = {\n      username: '',\n      secretWord: ''\n    };\n    this.loginError = '';\n    this.isLoading = false;\n    // Messaging\n    this.messageContent = '';\n    this.selectedRecipient = '';\n    this.selectedGroup = '';\n    this.messageType = 'direct';\n    this.messages = [];\n    this.unreadCount = 0;\n    // Contacts and Groups\n    this.contacts = [];\n    this.groups = [];\n    this.filteredContacts = [];\n    this.filteredGroups = [];\n    this.recipientSearchQuery = '';\n    // User info\n    this.currentUser = null;\n    // Account settings\n    this.userProfile = {\n      avatar: '',\n      email: '',\n      phone: ''\n    };\n    // Long press handling\n    this.longPressTimer = null;\n    this.longPressDuration = 500; // 500ms for long press\n  }\n  ngOnInit() {\n    this.initializeApp();\n    this.setupMessageListener();\n    this.setupAuthListener();\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  initializeApp() {\n    // The auth guard ensures we only reach here if authenticated\n    // But we still need to set up the initial state\n    this.authService.authState$.pipe(take(1)).subscribe(authState => {\n      if (authState.isAuthenticated && authState.user) {\n        this.currentUser = authState.user;\n        this.circleState = 'authenticated';\n        this.loadMessages();\n        this.loadContacts();\n        this.loadGroups();\n      } else {\n        // This should not happen due to auth guard, but handle gracefully\n        this.circleState = 'guest';\n        this.openLoginModal();\n      }\n    });\n  }\n  setupAuthListener() {\n    this.authService.authState$.pipe(takeUntil(this.destroy$)).subscribe(authState => {\n      if (authState.isAuthenticated && authState.user) {\n        this.currentUser = authState.user;\n        this.circleState = 'authenticated';\n        this.showLoginModal = false;\n        this.loadMessages();\n      } else {\n        this.currentUser = null;\n        this.circleState = 'guest';\n        this.messages = [];\n        this.unreadCount = 0;\n      }\n    });\n  }\n  setupMessageListener() {\n    this.messageService.messages$.pipe(takeUntil(this.destroy$)).subscribe(messages => {\n      this.messages = messages;\n      this.updateUnreadCount();\n    });\n    this.messageService.newMessage$.pipe(takeUntil(this.destroy$)).subscribe(message => {\n      this.messages.unshift(message);\n      this.updateUnreadCount();\n      this.notificationService.showNotification('New message received');\n    });\n  }\n  updateUnreadCount() {\n    this.unreadCount = this.messages.filter(m => !m.read).length;\n    if (this.unreadCount > 0 && this.circleState === 'authenticated') {\n      this.circleState = 'unread';\n    } else if (this.unreadCount === 0 && this.circleState === 'unread') {\n      this.circleState = 'authenticated';\n    }\n  }\n  loadMessages() {\n    this.messageService.loadMessages().pipe(takeUntil(this.destroy$)).subscribe({\n      next: messages => {\n        this.messages = messages;\n        this.updateUnreadCount();\n      },\n      error: error => {\n        console.error('Failed to load messages:', error);\n      }\n    });\n  }\n  loadContacts() {\n    // TODO: Replace with actual API call\n    this.contacts = [{\n      id: '1',\n      username: 'alice',\n      email: '<EMAIL>',\n      isOnline: true\n    }, {\n      id: '2',\n      username: 'bob',\n      email: '<EMAIL>',\n      isOnline: false,\n      lastSeen: new Date(Date.now() - 300000) // 5 minutes ago\n    }];\n    this.filteredContacts = [...this.contacts];\n  }\n  loadGroups() {\n    // TODO: Replace with actual API call\n    // Temporarily enable groups to test the interface\n    this.groups = [{\n      id: 'group1',\n      name: 'Work Team',\n      members: ['1', '2', 'current-user'],\n      isActive: true\n    }, {\n      id: 'group2',\n      name: 'Family',\n      members: ['3', '4', 'current-user'],\n      isActive: true\n    }];\n    // Set to empty array to hide group selector:\n    // this.groups = [];\n    this.filteredGroups = [...this.groups];\n  }\n  // Circle click handler - main interaction point\n  onCircleClick() {\n    // Don't handle click if context menu is showing\n    if (this.showContextMenu) {\n      this.closeContextMenu();\n      return;\n    }\n    switch (this.circleState) {\n      case 'guest':\n        this.openLoginModal();\n        break;\n      case 'authenticated':\n        this.openMessageComposer();\n        break;\n      case 'unread':\n        this.openMessagesViewer();\n        break;\n      case 'composing':\n        // Already composing, do nothing or close\n        break;\n    }\n  }\n  // Right click handler\n  onCircleRightClick(event) {\n    event.preventDefault();\n    // Only show context menu for authenticated users\n    if (this.circleState === 'guest') return;\n    this.showContextMenu = true;\n    this.contextMenuPosition = {\n      x: event.clientX,\n      y: event.clientY\n    };\n  }\n  // Touch event handlers for long press\n  onCircleTouchStart(event) {\n    // Only for authenticated users\n    if (this.circleState === 'guest') return;\n    this.longPressTimer = setTimeout(() => {\n      const touch = event.touches[0];\n      this.showContextMenu = true;\n      this.contextMenuPosition = {\n        x: touch.clientX,\n        y: touch.clientY\n      };\n      // Provide haptic feedback if available\n      if (navigator.vibrate) {\n        navigator.vibrate(50);\n      }\n    }, this.longPressDuration);\n  }\n  onCircleTouchEnd() {\n    if (this.longPressTimer) {\n      clearTimeout(this.longPressTimer);\n      this.longPressTimer = null;\n    }\n  }\n  onCircleTouchMove() {\n    // Cancel long press if user moves finger\n    if (this.longPressTimer) {\n      clearTimeout(this.longPressTimer);\n      this.longPressTimer = null;\n    }\n  }\n  // Authentication methods\n  openLoginModal() {\n    this.showLoginModal = true;\n    this.loginCredentials = {\n      username: '',\n      secretWord: ''\n    };\n    this.loginError = '';\n  }\n  closeLoginModal() {\n    this.showLoginModal = false;\n    this.loginCredentials = {\n      username: '',\n      secretWord: ''\n    };\n    this.loginError = '';\n  }\n  onLoginInputChange() {\n    // Auto-submit when both fields are valid\n    if (this.isValidCredentials()) {\n      this.performLogin();\n    }\n  }\n  isValidCredentials() {\n    const usernameValid = this.loginCredentials.username.length >= 3;\n    const secretWordValid = this.loginCredentials.secretWord.length >= 4 && /[A-Z]/.test(this.loginCredentials.secretWord) && /[a-z]/.test(this.loginCredentials.secretWord) && /[0-9]/.test(this.loginCredentials.secretWord) && /[^A-Za-z0-9]/.test(this.loginCredentials.secretWord);\n    return usernameValid && secretWordValid;\n  }\n  performLogin() {\n    if (this.isLoading) return;\n    this.isLoading = true;\n    this.loginError = '';\n    this.authService.login(this.loginCredentials.username, this.loginCredentials.secretWord).pipe(takeUntil(this.destroy$)).subscribe({\n      next: response => {\n        this.isLoading = false;\n        // Auth state will be updated via authState$ subscription\n      },\n      error: error => {\n        this.isLoading = false;\n        this.loginError = error.message || 'Authentication failed';\n      }\n    });\n  }\n  // Message composition methods\n  openMessageComposer() {\n    this.showMessageModal = true;\n    this.messageContent = '';\n    this.selectedRecipient = '';\n    this.selectedGroup = '';\n    this.messageType = 'direct';\n    this.recipientSearchQuery = '';\n    this.filteredContacts = [...this.contacts];\n    this.filteredGroups = [...this.groups];\n    this.circleState = 'composing';\n  }\n  closeMessageComposer() {\n    this.showMessageModal = false;\n    this.messageContent = '';\n    this.selectedRecipient = '';\n    this.selectedGroup = '';\n    this.recipientSearchQuery = '';\n    this.circleState = 'authenticated';\n  }\n  sendMessage() {\n    if (!this.messageContent.trim()) return;\n    // Validate recipient selection\n    if (this.messageType === 'direct' && !this.selectedRecipient) {\n      this.notificationService.showNotification('Please select a recipient', 'warning');\n      return;\n    }\n    if (this.messageType === 'group' && !this.selectedGroup) {\n      this.notificationService.showNotification('Please select a group', 'warning');\n      return;\n    }\n    const message = {\n      content: this.messageContent.trim(),\n      timestamp: new Date(),\n      sender: this.currentUser?.username || 'Unknown',\n      recipient: this.messageType === 'direct' ? this.selectedRecipient : undefined,\n      groupId: this.messageType === 'group' ? this.selectedGroup : undefined\n    };\n    this.messageService.sendMessage(message).pipe(takeUntil(this.destroy$)).subscribe({\n      next: () => {\n        this.closeMessageComposer();\n        this.notificationService.showNotification('Message sent');\n      },\n      error: error => {\n        console.error('Failed to send message:', error);\n        this.notificationService.showNotification('Failed to send message', 'error');\n      }\n    });\n  }\n  // Recipient selection methods\n  onRecipientSearchChange() {\n    const query = this.recipientSearchQuery.toLowerCase();\n    if (this.messageType === 'direct') {\n      this.filteredContacts = this.contacts.filter(contact => contact.username.toLowerCase().includes(query) || contact.email.toLowerCase().includes(query));\n    } else {\n      this.filteredGroups = this.groups.filter(group => group.name.toLowerCase().includes(query));\n    }\n  }\n  selectContact(contact) {\n    this.selectedRecipient = contact.id;\n    this.recipientSearchQuery = contact.username;\n    this.filteredContacts = [];\n  }\n  selectGroup(group) {\n    this.selectedGroup = group.id;\n    this.recipientSearchQuery = group.name;\n    this.filteredGroups = [];\n  }\n  switchMessageType(type) {\n    this.messageType = type;\n    this.selectedRecipient = '';\n    this.selectedGroup = '';\n    this.recipientSearchQuery = '';\n    this.onRecipientSearchChange();\n  }\n  getSelectedRecipientName() {\n    if (this.messageType === 'direct' && this.selectedRecipient) {\n      const contact = this.contacts.find(c => c.id === this.selectedRecipient);\n      return contact?.username || 'Unknown';\n    }\n    if (this.messageType === 'group' && this.selectedGroup) {\n      const group = this.groups.find(g => g.id === this.selectedGroup);\n      return group?.name || 'Unknown Group';\n    }\n    return '';\n  }\n  isMessageValid() {\n    const hasContent = this.messageContent.trim().length > 0;\n    const hasRecipient = this.messageType === 'direct' ? !!this.selectedRecipient : !!this.selectedGroup;\n    return hasContent && hasRecipient;\n  }\n  // Message viewing methods\n  openMessagesViewer() {\n    this.showMessagesModal = true;\n    this.markMessagesAsRead();\n  }\n  closeMessagesViewer() {\n    this.showMessagesModal = false;\n  }\n  markMessagesAsRead() {\n    this.messageService.markAllAsRead().pipe(takeUntil(this.destroy$)).subscribe(() => {\n      this.updateUnreadCount();\n    });\n  }\n  // Context menu methods\n  closeContextMenu() {\n    this.showContextMenu = false;\n  }\n  openAccountSettings() {\n    this.showAccountSettings = true;\n    this.showContextMenu = false;\n    this.loadUserProfile();\n  }\n  closeAccountSettings() {\n    this.showAccountSettings = false;\n  }\n  // Account settings methods\n  loadUserProfile() {\n    if (this.currentUser) {\n      this.userProfile = {\n        avatar: '',\n        email: this.currentUser.email || '',\n        phone: this.currentUser.phone || ''\n      };\n    }\n  }\n  onAvatarChange(event) {\n    const input = event.target;\n    if (input.files && input.files[0]) {\n      const file = input.files[0];\n      // Validate file type\n      if (!file.type.startsWith('image/')) {\n        this.notificationService.showNotification('Please select an image file', 'error');\n        return;\n      }\n      // Validate file size (max 2MB)\n      if (file.size > 2 * 1024 * 1024) {\n        this.notificationService.showNotification('Image must be smaller than 2MB', 'error');\n        return;\n      }\n      const reader = new FileReader();\n      reader.onload = e => {\n        this.userProfile.avatar = e.target?.result;\n        this.saveAvatarChange();\n      };\n      reader.readAsDataURL(file);\n    }\n  }\n  saveAvatarChange() {\n    // TODO: Implement API call to save avatar\n    this.notificationService.showNotification('Avatar updated successfully', 'success');\n  }\n  // Logout\n  logout() {\n    this.closeContextMenu();\n    this.authService.logout();\n  }\n  // Keyboard shortcuts\n  onKeyDown(event) {\n    // Escape key closes modals\n    if (event.key === 'Escape') {\n      this.closeAllModals();\n    }\n    // Enter key in login modal\n    if (event.key === 'Enter' && this.showLoginModal) {\n      if (this.isValidCredentials()) {\n        this.performLogin();\n      }\n    }\n    // Enter key in message modal\n    if (event.key === 'Enter' && this.showMessageModal && !event.shiftKey) {\n      event.preventDefault();\n      this.sendMessage();\n    }\n  }\n  // Click outside handler to close context menu\n  onDocumentClick(event) {\n    // Close context menu when clicking outside\n    if (this.showContextMenu) {\n      this.closeContextMenu();\n    }\n  }\n  closeAllModals() {\n    this.showLoginModal = false;\n    this.showMessageModal = false;\n    this.showMessagesModal = false;\n    this.showContextMenu = false;\n    this.showAccountSettings = false;\n    if (this.circleState === 'composing') {\n      this.circleState = 'authenticated';\n    }\n  }\n  // Utility methods\n  getCircleClass() {\n    return `circle-${this.circleState}`;\n  }\n  getCircleTitle() {\n    switch (this.circleState) {\n      case 'guest':\n        return 'Click to sign in';\n      case 'authenticated':\n        return 'Click to compose message';\n      case 'unread':\n        return `Click to view ${this.unreadCount} unread message(s)`;\n      case 'composing':\n        return 'Composing message...';\n      default:\n        return '';\n    }\n  }\n  trackMessage(index, message) {\n    return message.id;\n  }\n  static {\n    this.ɵfac = function QscMainComponent_Factory(t) {\n      return new (t || QscMainComponent)(i0.ɵɵdirectiveInject(i1.AuthService), i0.ɵɵdirectiveInject(i2.MessageService), i0.ɵɵdirectiveInject(i3.NotificationService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: QscMainComponent,\n      selectors: [[\"app-qsc-main\"]],\n      hostBindings: function QscMainComponent_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"keydown\", function QscMainComponent_keydown_HostBindingHandler($event) {\n            return ctx.onKeyDown($event);\n          }, false, i0.ɵɵresolveDocument)(\"click\", function QscMainComponent_click_HostBindingHandler($event) {\n            return ctx.onDocumentClick($event);\n          }, false, i0.ɵɵresolveDocument);\n        }\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 13,\n      vars: 12,\n      consts: [[1, \"qsc-container\"], [1, \"circle-container\"], [1, \"qsc-circle\", 3, \"click\", \"contextmenu\", \"touchstart\", \"touchend\", \"touchmove\", \"title\"], [1, \"circle-inner\"], [\"class\", \"wind-effect\", 4, \"ngIf\"], [\"class\", \"unread-indicator\", 4, \"ngIf\"], [\"class\", \"user-info\", 4, \"ngIf\"], [\"class\", \"context-menu\", 3, \"left\", \"top\", \"click\", 4, \"ngIf\"], [\"class\", \"modal-overlay\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"keyboard-hints\", 4, \"ngIf\"], [1, \"wind-effect\"], [1, \"unread-indicator\"], [1, \"user-info\"], [\"title\", \"Logout\", 1, \"logout-btn\", 3, \"click\"], [1, \"context-menu\", 3, \"click\"], [1, \"context-menu-item\", 3, \"click\"], [1, \"menu-icon\"], [1, \"menu-text\"], [1, \"context-menu-divider\"], [1, \"context-menu-item\", \"logout-item\", 3, \"click\"], [1, \"modal-overlay\", 3, \"click\"], [1, \"modal\", \"login-modal\", 3, \"click\"], [1, \"modal-header\"], [1, \"close-btn\", 3, \"click\"], [1, \"modal-content\"], [1, \"form-group\"], [\"for\", \"username\"], [\"type\", \"text\", \"id\", \"username\", \"placeholder\", \"Enter your username\", \"autocomplete\", \"username\", 3, \"ngModelChange\", \"input\", \"ngModel\", \"disabled\"], [\"for\", \"secretWord\"], [\"type\", \"password\", \"id\", \"secretWord\", \"placeholder\", \"4+ chars: A-Z, a-z, 0-9, symbol\", \"autocomplete\", \"current-password\", 3, \"ngModelChange\", \"input\", \"ngModel\", \"disabled\"], [\"class\", \"error-message\", 4, \"ngIf\"], [\"class\", \"loading-indicator\", 4, \"ngIf\"], [\"class\", \"auth-info\", 4, \"ngIf\"], [1, \"error-message\"], [1, \"loading-indicator\"], [1, \"spinner\"], [1, \"auth-info\"], [1, \"auto-submit-hint\"], [1, \"modal\", \"message-modal\", 3, \"click\"], [1, \"message-type-selector\"], [1, \"type-btn\", \"direct-btn\", 3, \"click\"], [\"viewBox\", \"0 0 24 24\", 1, \"icon\"], [\"d\", \"M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2\"], [\"cx\", \"12\", \"cy\", \"7\", \"r\", \"4\"], [1, \"type-btn\", \"group-btn\", 3, \"click\"], [\"d\", \"M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2\"], [\"cx\", \"9\", \"cy\", \"7\", \"r\", \"4\"], [\"d\", \"M23 21v-2a4 4 0 0 0-3-3.87\"], [\"d\", \"M16 3.13a4 4 0 0 1 0 7.75\"], [1, \"recipient-selector\"], [\"type\", \"text\", \"id\", \"recipientSearch\", \"autocomplete\", \"off\", 3, \"ngModelChange\", \"input\", \"ngModel\", \"placeholder\"], [\"class\", \"selected-recipient\", 4, \"ngIf\"], [\"class\", \"recipient-dropdown\", 4, \"ngIf\"], [\"for\", \"messageContent\"], [\"id\", \"messageContent\", \"placeholder\", \"Type your message here...\", \"rows\", \"6\", \"maxlength\", \"1000\", 3, \"ngModelChange\", \"ngModel\"], [1, \"char-count\"], [1, \"message-actions\"], [1, \"btn\", \"btn-primary\", 3, \"click\", \"disabled\"], [\"x1\", \"22\", \"y1\", \"2\", \"x2\", \"11\", \"y2\", \"13\"], [\"points\", \"22,2 15,22 11,13 2,9\"], [1, \"btn\", \"btn-secondary\", 3, \"click\"], [\"x1\", \"18\", \"y1\", \"6\", \"x2\", \"6\", \"y2\", \"18\"], [\"x1\", \"6\", \"y1\", \"6\", \"x2\", \"18\", \"y2\", \"18\"], [1, \"send-hint\"], [1, \"selected-recipient\"], [1, \"recipient-name\"], [\"title\", \"Clear selection\", 1, \"clear-recipient\", 3, \"click\"], [1, \"recipient-dropdown\"], [\"class\", \"recipient-item\", 3, \"hidden\", \"click\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"no-results\", 4, \"ngIf\"], [1, \"recipient-item\", 3, \"click\", \"hidden\"], [1, \"contact-info\"], [1, \"contact-name\"], [1, \"contact-email\"], [1, \"contact-status\"], [1, \"status-indicator\"], [1, \"status-text\"], [1, \"group-info\"], [1, \"group-name\"], [1, \"group-members\"], [1, \"group-status\"], [1, \"no-results\"], [1, \"modal\", \"messages-modal\", 3, \"click\"], [\"class\", \"messages-list\", 4, \"ngIf\"], [\"class\", \"empty-state\", 4, \"ngIf\"], [1, \"messages-list\"], [\"class\", \"message-item\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [1, \"message-item\"], [1, \"message-header\"], [1, \"sender\"], [1, \"timestamp\"], [1, \"message-content\"], [1, \"empty-state\"], [1, \"hint\"], [1, \"modal\", \"account-settings-modal\", 3, \"click\"], [1, \"avatar-section\"], [1, \"avatar-container\"], [1, \"avatar-display\"], [\"alt\", \"Profile Avatar\", \"class\", \"avatar-image\", 3, \"src\", 4, \"ngIf\"], [\"class\", \"avatar-placeholder\", 4, \"ngIf\"], [1, \"avatar-actions\"], [\"for\", \"avatarInput\", 1, \"avatar-upload-btn\"], [\"type\", \"file\", \"id\", \"avatarInput\", \"accept\", \"image/*\", 2, \"display\", \"none\", 3, \"change\"], [1, \"profile-info\"], [1, \"readonly-field\"], [1, \"field-note\"], [1, \"security-info\"], [1, \"security-item\"], [1, \"security-label\"], [1, \"security-value\"], [1, \"account-actions\"], [\"d\", \"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4\"], [\"points\", \"16,17 21,12 16,7\"], [\"x1\", \"21\", \"y1\", \"12\", \"x2\", \"9\", \"y2\", \"12\"], [\"alt\", \"Profile Avatar\", 1, \"avatar-image\", 3, \"src\"], [1, \"avatar-placeholder\"], [1, \"avatar-initials\"], [1, \"keyboard-hints\"]],\n      template: function QscMainComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2);\n          i0.ɵɵlistener(\"click\", function QscMainComponent_Template_div_click_2_listener() {\n            return ctx.onCircleClick();\n          })(\"contextmenu\", function QscMainComponent_Template_div_contextmenu_2_listener($event) {\n            return ctx.onCircleRightClick($event);\n          })(\"touchstart\", function QscMainComponent_Template_div_touchstart_2_listener($event) {\n            return ctx.onCircleTouchStart($event);\n          })(\"touchend\", function QscMainComponent_Template_div_touchend_2_listener() {\n            return ctx.onCircleTouchEnd();\n          })(\"touchmove\", function QscMainComponent_Template_div_touchmove_2_listener() {\n            return ctx.onCircleTouchMove();\n          });\n          i0.ɵɵelementStart(3, \"div\", 3);\n          i0.ɵɵtemplate(4, QscMainComponent_div_4_Template, 1, 0, \"div\", 4)(5, QscMainComponent_div_5_Template, 2, 1, \"div\", 5);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(6, QscMainComponent_div_6_Template, 5, 1, \"div\", 6)(7, QscMainComponent_div_7_Template, 12, 4, \"div\", 7);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(8, QscMainComponent_div_8_Template, 19, 7, \"div\", 8)(9, QscMainComponent_div_9_Template, 41, 13, \"div\", 8)(10, QscMainComponent_div_10_Template, 10, 2, \"div\", 8)(11, QscMainComponent_div_11_Template, 65, 5, \"div\", 8)(12, QscMainComponent_div_12_Template, 3, 0, \"div\", 9);\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(2);\n          i0.ɵɵclassMap(ctx.getCircleClass());\n          i0.ɵɵproperty(\"title\", ctx.getCircleTitle());\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.circleState !== \"guest\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.circleState === \"unread\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.currentUser);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.showContextMenu);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.showLoginModal);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.showMessageModal);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.showMessagesModal);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.showAccountSettings);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.showLoginModal && !ctx.showMessageModal && !ctx.showMessagesModal && !ctx.showAccountSettings);\n        }\n      },\n      dependencies: [CommonModule, i4.NgForOf, i4.NgIf, i4.DatePipe, FormsModule, i5.DefaultValueAccessor, i5.NgControlStatus, i5.MaxLengthValidator, i5.NgModel],\n      styles: [\".qsc-container[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  width: 100vw;\\n  height: 100vh;\\n  background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  overflow: hidden;\\n  font-family: \\\"Inter\\\", -apple-system, BlinkMacSystemFont, sans-serif;\\n}\\n\\n.circle-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  z-index: 1;\\n}\\n\\n.qsc-circle[_ngcontent-%COMP%] {\\n  width: 200px;\\n  height: 200px;\\n  border-radius: 50%;\\n  cursor: pointer;\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n  position: relative;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);\\n}\\n.qsc-circle[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.05);\\n  box-shadow: 0 12px 48px rgba(0, 0, 0, 0.4);\\n}\\n.qsc-circle[_ngcontent-%COMP%]:active {\\n  transform: scale(0.98);\\n}\\n.qsc-circle.circle-guest[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #ff4757 0%, #ff3742 100%);\\n  border: 3px solid rgba(255, 71, 87, 0.3);\\n}\\n.qsc-circle.circle-guest[_ngcontent-%COMP%]:hover {\\n  box-shadow: 0 12px 48px rgba(255, 71, 87, 0.4);\\n}\\n.qsc-circle.circle-authenticated[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #3742fa 0%, #2f3542 100%);\\n  border: 3px solid rgba(55, 66, 250, 0.3);\\n}\\n.qsc-circle.circle-authenticated[_ngcontent-%COMP%]:hover {\\n  box-shadow: 0 12px 48px rgba(55, 66, 250, 0.4);\\n}\\n.qsc-circle.circle-unread[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #2ed573 0%, #1e90ff 100%);\\n  border: 3px solid rgba(46, 213, 115, 0.3);\\n  animation: _ngcontent-%COMP%_pulse 2s infinite;\\n}\\n.qsc-circle.circle-unread[_ngcontent-%COMP%]:hover {\\n  box-shadow: 0 12px 48px rgba(46, 213, 115, 0.4);\\n}\\n.qsc-circle.circle-composing[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #a55eea 0%, #8854d0 100%);\\n  border: 3px solid rgba(165, 94, 234, 0.3);\\n}\\n.qsc-circle.circle-composing[_ngcontent-%COMP%]:hover {\\n  box-shadow: 0 12px 48px rgba(165, 94, 234, 0.4);\\n}\\n\\n.circle-inner[_ngcontent-%COMP%] {\\n  position: relative;\\n  width: 100%;\\n  height: 100%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n\\n.wind-effect[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 20%;\\n  right: 15%;\\n  width: 60px;\\n  height: 60px;\\n  opacity: 0.3;\\n}\\n.wind-effect[_ngcontent-%COMP%]::before, .wind-effect[_ngcontent-%COMP%]::after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  background: rgba(255, 255, 255, 0.6);\\n  border-radius: 50%;\\n  animation: _ngcontent-%COMP%_windFlow 3s ease-in-out infinite;\\n}\\n.wind-effect[_ngcontent-%COMP%]::before {\\n  width: 8px;\\n  height: 8px;\\n  top: 10px;\\n  left: 0;\\n  animation-delay: 0s;\\n}\\n.wind-effect[_ngcontent-%COMP%]::after {\\n  width: 6px;\\n  height: 6px;\\n  top: 25px;\\n  left: 15px;\\n  animation-delay: 1s;\\n}\\n\\n.unread-indicator[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: -10px;\\n  right: -10px;\\n  background: #ff4757;\\n  color: white;\\n  border-radius: 50%;\\n  width: 40px;\\n  height: 40px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  font-weight: 600;\\n  font-size: 14px;\\n  border: 3px solid #0f0f23;\\n  animation: _ngcontent-%COMP%_bounce 1s infinite;\\n}\\n\\n.user-info[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 20px;\\n  right: 20px;\\n  display: flex;\\n  align-items: center;\\n  gap: 10px;\\n  color: rgba(255, 255, 255, 0.7);\\n  font-size: 14px;\\n  z-index: 10;\\n}\\n.user-info[_ngcontent-%COMP%]   .logout-btn[_ngcontent-%COMP%] {\\n  background: none;\\n  border: none;\\n  color: rgba(255, 255, 255, 0.5);\\n  font-size: 20px;\\n  cursor: pointer;\\n  padding: 5px;\\n  border-radius: 50%;\\n  transition: all 0.2s ease;\\n}\\n.user-info[_ngcontent-%COMP%]   .logout-btn[_ngcontent-%COMP%]:hover {\\n  color: #ff4757;\\n  background: rgba(255, 71, 87, 0.1);\\n}\\n\\n.modal-overlay[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  width: 100vw;\\n  height: 100vh;\\n  background: rgba(0, 0, 0, 0.3);\\n  -webkit-backdrop-filter: blur(8px);\\n          backdrop-filter: blur(8px);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  z-index: 1000;\\n  animation: _ngcontent-%COMP%_fadeIn 0.2s ease;\\n}\\n\\n.modal[_ngcontent-%COMP%] {\\n  background: #fafafa;\\n  border-radius: 16px;\\n  box-shadow: 0 0 40px rgba(255, 255, 255, 0.1), 0 8px 32px rgba(0, 0, 0, 0.12), inset 0 1px 0 rgba(255, 255, 255, 0.2);\\n  max-width: 400px;\\n  width: 90vw;\\n  max-height: 80vh;\\n  border: 1px solid rgba(255, 255, 255, 0.1);\\n  position: relative;\\n  animation: _ngcontent-%COMP%_slideUp 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n  display: flex;\\n  flex-direction: column;\\n}\\n\\n.modal[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: -2px;\\n  left: -2px;\\n  right: -2px;\\n  bottom: -2px;\\n  background: linear-gradient(45deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 50%, rgba(255, 255, 255, 0.1) 100%);\\n  border-radius: 18px;\\n  z-index: -1;\\n}\\n\\n.modal-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 20px 24px;\\n  border-bottom: 1px solid rgba(0, 0, 0, 0.1);\\n}\\n.modal-header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 20px;\\n  font-weight: 600;\\n  color: #2f3542;\\n}\\n.modal-header[_ngcontent-%COMP%]   .close-btn[_ngcontent-%COMP%] {\\n  background: none;\\n  border: none;\\n  font-size: 24px;\\n  color: #a4b0be;\\n  cursor: pointer;\\n  padding: 0;\\n  width: 30px;\\n  height: 30px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  border-radius: 50%;\\n  transition: all 0.2s ease;\\n}\\n.modal-header[_ngcontent-%COMP%]   .close-btn[_ngcontent-%COMP%]:hover {\\n  background: rgba(0, 0, 0, 0.1);\\n  color: #2f3542;\\n}\\n\\n.modal-content[_ngcontent-%COMP%] {\\n  padding: 2rem;\\n  flex: 1;\\n  overflow-y: auto;\\n}\\n.modal-content[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  width: 4px;\\n}\\n.modal-content[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: transparent;\\n}\\n.modal-content[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background: rgba(0, 0, 0, 0.1);\\n  border-radius: 2px;\\n}\\n.modal-content[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover {\\n  background: rgba(0, 0, 0, 0.2);\\n}\\n\\n.form-group[_ngcontent-%COMP%] {\\n  margin-bottom: 2rem;\\n  position: relative;\\n}\\n.form-group[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  display: block;\\n  margin-bottom: 0.5rem;\\n  font-size: 0.875rem;\\n  font-weight: 500;\\n  color: #6b7280;\\n  letter-spacing: 0.025em;\\n  text-transform: uppercase;\\n}\\n.form-group[_ngcontent-%COMP%]   input[_ngcontent-%COMP%], .form-group[_ngcontent-%COMP%]   textarea[_ngcontent-%COMP%] {\\n  width: 100%;\\n  padding: 0.75rem 0;\\n  background: transparent;\\n  border: none;\\n  border-bottom: 1px solid #e5e7eb;\\n  color: #1f2937;\\n  font-size: 1rem;\\n  font-weight: 400;\\n  transition: all 0.3s ease;\\n}\\n.form-group[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]:focus, .form-group[_ngcontent-%COMP%]   textarea[_ngcontent-%COMP%]:focus {\\n  outline: none;\\n  border-bottom-color: #1e40af;\\n}\\n.form-group[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]:disabled, .form-group[_ngcontent-%COMP%]   textarea[_ngcontent-%COMP%]:disabled {\\n  background: transparent;\\n  color: #9ca3af;\\n  border-bottom-color: #f3f4f6;\\n}\\n.form-group[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]::placeholder, .form-group[_ngcontent-%COMP%]   textarea[_ngcontent-%COMP%]::placeholder {\\n  color: #9ca3af;\\n  font-weight: 300;\\n}\\n.form-group[_ngcontent-%COMP%]   textarea[_ngcontent-%COMP%] {\\n  resize: vertical;\\n  min-height: 120px;\\n  font-family: inherit;\\n  border: 1px solid #e5e7eb;\\n  border-radius: 8px;\\n  padding: 0.75rem;\\n}\\n.form-group[_ngcontent-%COMP%]   textarea[_ngcontent-%COMP%]:focus {\\n  border-color: #1e40af;\\n}\\n\\n.char-count[_ngcontent-%COMP%] {\\n  text-align: right;\\n  font-size: 12px;\\n  color: #a4b0be;\\n  margin-top: 4px;\\n}\\n\\n.type-btn[_ngcontent-%COMP%] {\\n  display: inline-flex;\\n  align-items: center;\\n  justify-content: center;\\n  padding: 0.875rem 1.5rem;\\n  border: none;\\n  border-radius: 50px;\\n  font-size: 0.875rem;\\n  font-weight: 500;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  text-decoration: none;\\n  min-width: 120px;\\n  gap: 0.5rem;\\n  background: #e5e7eb;\\n  color: #6b7280;\\n}\\n.type-btn[_ngcontent-%COMP%]:hover {\\n  background: #d1d5db;\\n  transform: translateY(-1px);\\n}\\n.type-btn.active[_ngcontent-%COMP%] {\\n  background: #1e40af;\\n  color: white;\\n}\\n.type-btn.active[_ngcontent-%COMP%]:hover {\\n  background: #1d4ed8;\\n  box-shadow: 0 4px 12px rgba(30, 64, 175, 0.3);\\n}\\n.type-btn[_ngcontent-%COMP%]:active {\\n  transform: translateY(0);\\n}\\n.type-btn[_ngcontent-%COMP%]   .icon[_ngcontent-%COMP%] {\\n  width: 16px;\\n  height: 16px;\\n  stroke: currentColor;\\n  fill: none;\\n  stroke-width: 1.5;\\n}\\n\\n.btn[_ngcontent-%COMP%] {\\n  display: inline-flex;\\n  align-items: center;\\n  justify-content: center;\\n  padding: 0.75rem 0;\\n  border: none;\\n  background: transparent;\\n  font-size: 0.875rem;\\n  font-weight: 500;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  text-decoration: none;\\n  gap: 0.5rem;\\n  position: relative;\\n  color: #374151;\\n}\\n.btn[_ngcontent-%COMP%]::after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  bottom: 0;\\n  left: 0;\\n  width: 0;\\n  height: 1px;\\n  background: currentColor;\\n  transition: width 0.3s ease;\\n}\\n.btn[_ngcontent-%COMP%]:hover::after {\\n  width: 100%;\\n}\\n.btn.btn-primary[_ngcontent-%COMP%] {\\n  color: #1e40af;\\n}\\n.btn.btn-primary[_ngcontent-%COMP%]:disabled {\\n  color: #9ca3af;\\n  cursor: not-allowed;\\n}\\n.btn.btn-primary[_ngcontent-%COMP%]:disabled::after {\\n  display: none;\\n}\\n.btn.btn-secondary[_ngcontent-%COMP%] {\\n  color: #6b7280;\\n}\\n.btn[_ngcontent-%COMP%]   .icon[_ngcontent-%COMP%] {\\n  width: 16px;\\n  height: 16px;\\n  stroke: currentColor;\\n  fill: none;\\n  stroke-width: 1.5;\\n}\\n\\n.message-type-selector[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 1rem;\\n  margin-bottom: 2rem;\\n  justify-content: center;\\n  display: none;\\n}\\n.message-type-selector.has-groups[_ngcontent-%COMP%] {\\n  display: flex;\\n}\\n\\n.recipient-selector[_ngcontent-%COMP%] {\\n  position: relative;\\n}\\n\\n.selected-recipient[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  padding: 8px 12px;\\n  background: rgba(55, 66, 250, 0.1);\\n  border: 1px solid #3742fa;\\n  border-radius: 6px;\\n  margin-top: 8px;\\n}\\n.selected-recipient[_ngcontent-%COMP%]   .recipient-name[_ngcontent-%COMP%] {\\n  color: #3742fa;\\n  font-weight: 500;\\n  font-size: 14px;\\n}\\n.selected-recipient[_ngcontent-%COMP%]   .clear-recipient[_ngcontent-%COMP%] {\\n  background: none;\\n  border: none;\\n  color: #3742fa;\\n  font-size: 16px;\\n  cursor: pointer;\\n  padding: 0;\\n  width: 20px;\\n  height: 20px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  border-radius: 50%;\\n  transition: all 0.2s ease;\\n}\\n.selected-recipient[_ngcontent-%COMP%]   .clear-recipient[_ngcontent-%COMP%]:hover {\\n  background: rgba(55, 66, 250, 0.2);\\n}\\n\\n.recipient-dropdown[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 100%;\\n  left: 0;\\n  right: 0;\\n  background: white;\\n  border: 1px solid #e1e8ed;\\n  border-radius: 8px;\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\\n  max-height: 200px;\\n  overflow-y: auto;\\n  z-index: 1000;\\n  margin-top: 4px;\\n}\\n\\n.recipient-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  padding: 12px 16px;\\n  cursor: pointer;\\n  border-bottom: 1px solid rgba(0, 0, 0, 0.05);\\n  transition: background 0.2s ease;\\n}\\n.recipient-item[_ngcontent-%COMP%]:hover {\\n  background: rgba(55, 66, 250, 0.05);\\n}\\n.recipient-item[_ngcontent-%COMP%]:last-child {\\n  border-bottom: none;\\n}\\n\\n.contact-info[_ngcontent-%COMP%], .group-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  flex: 1;\\n}\\n.contact-info[_ngcontent-%COMP%]   .contact-name[_ngcontent-%COMP%], .contact-info[_ngcontent-%COMP%]   .group-name[_ngcontent-%COMP%], .group-info[_ngcontent-%COMP%]   .contact-name[_ngcontent-%COMP%], .group-info[_ngcontent-%COMP%]   .group-name[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: #2f3542;\\n  font-size: 14px;\\n  margin-bottom: 2px;\\n}\\n.contact-info[_ngcontent-%COMP%]   .contact-email[_ngcontent-%COMP%], .contact-info[_ngcontent-%COMP%]   .group-members[_ngcontent-%COMP%], .group-info[_ngcontent-%COMP%]   .contact-email[_ngcontent-%COMP%], .group-info[_ngcontent-%COMP%]   .group-members[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: #a4b0be;\\n}\\n\\n.contact-status[_ngcontent-%COMP%], .group-status[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 6px;\\n}\\n.contact-status[_ngcontent-%COMP%]   .status-indicator[_ngcontent-%COMP%], .group-status[_ngcontent-%COMP%]   .status-indicator[_ngcontent-%COMP%] {\\n  width: 8px;\\n  height: 8px;\\n  border-radius: 50%;\\n}\\n.contact-status[_ngcontent-%COMP%]   .status-indicator.online[_ngcontent-%COMP%], .group-status[_ngcontent-%COMP%]   .status-indicator.online[_ngcontent-%COMP%] {\\n  background: #2ed573;\\n}\\n.contact-status[_ngcontent-%COMP%]   .status-indicator.offline[_ngcontent-%COMP%], .group-status[_ngcontent-%COMP%]   .status-indicator.offline[_ngcontent-%COMP%] {\\n  background: #a4b0be;\\n}\\n.contact-status[_ngcontent-%COMP%]   .status-indicator.active[_ngcontent-%COMP%], .group-status[_ngcontent-%COMP%]   .status-indicator.active[_ngcontent-%COMP%] {\\n  background: #3742fa;\\n}\\n.contact-status[_ngcontent-%COMP%]   .status-text[_ngcontent-%COMP%], .group-status[_ngcontent-%COMP%]   .status-text[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: #a4b0be;\\n}\\n\\n.no-results[_ngcontent-%COMP%] {\\n  padding: 16px;\\n  text-align: center;\\n  color: #a4b0be;\\n  font-size: 14px;\\n  font-style: italic;\\n}\\n\\n.message-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 2rem;\\n  margin-top: 2rem;\\n  justify-content: center;\\n}\\n\\n.error-message[_ngcontent-%COMP%] {\\n  color: #ff4757;\\n  font-size: 14px;\\n  margin-top: 8px;\\n  padding: 8px 12px;\\n  background: rgba(255, 71, 87, 0.1);\\n  border-radius: 6px;\\n  border-left: 3px solid #ff4757;\\n}\\n\\n.auth-info[_ngcontent-%COMP%] {\\n  margin-top: 20px;\\n  text-align: center;\\n}\\n.auth-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 8px 0;\\n  font-size: 13px;\\n  color: #57606f;\\n}\\n.auth-info[_ngcontent-%COMP%]   p.auto-submit-hint[_ngcontent-%COMP%] {\\n  font-style: italic;\\n  color: #a4b0be;\\n}\\n\\n.send-hint[_ngcontent-%COMP%] {\\n  margin-top: 16px;\\n  text-align: center;\\n}\\n.send-hint[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: #a4b0be;\\n  margin: 0;\\n}\\n\\n.loading-indicator[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  gap: 12px;\\n  padding: 20px;\\n}\\n.loading-indicator[_ngcontent-%COMP%]   .spinner[_ngcontent-%COMP%] {\\n  width: 20px;\\n  height: 20px;\\n  border: 2px solid #e1e8ed;\\n  border-top: 2px solid #3742fa;\\n  border-radius: 50%;\\n  animation: _ngcontent-%COMP%_spin 1s linear infinite;\\n}\\n.loading-indicator[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  color: #57606f;\\n  font-size: 14px;\\n}\\n\\n.messages-list[_ngcontent-%COMP%] {\\n  max-height: 400px;\\n  overflow-y: auto;\\n  margin: -8px;\\n  padding: 8px;\\n}\\n\\n.message-item[_ngcontent-%COMP%] {\\n  padding: 16px;\\n  border-bottom: 1px solid rgba(0, 0, 0, 0.05);\\n}\\n.message-item[_ngcontent-%COMP%]:last-child {\\n  border-bottom: none;\\n}\\n.message-item[_ngcontent-%COMP%]   .message-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 8px;\\n}\\n.message-item[_ngcontent-%COMP%]   .message-header[_ngcontent-%COMP%]   .sender[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #2f3542;\\n  font-size: 14px;\\n}\\n.message-item[_ngcontent-%COMP%]   .message-header[_ngcontent-%COMP%]   .timestamp[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: #a4b0be;\\n}\\n.message-item[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%] {\\n  color: #57606f;\\n  line-height: 1.5;\\n  word-wrap: break-word;\\n}\\n\\n.empty-state[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 40px 20px;\\n  color: #a4b0be;\\n}\\n.empty-state[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 8px 0;\\n}\\n.empty-state[_ngcontent-%COMP%]   p.hint[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-style: italic;\\n}\\n\\n.context-menu[_ngcontent-%COMP%] {\\n  position: fixed;\\n  background: rgba(255, 255, 255, 0.95);\\n  -webkit-backdrop-filter: blur(20px);\\n          backdrop-filter: blur(20px);\\n  border-radius: 12px;\\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);\\n  padding: 8px 0;\\n  min-width: 180px;\\n  z-index: 2000;\\n  animation: _ngcontent-%COMP%_contextMenuSlide 0.2s cubic-bezier(0.4, 0, 0.2, 1);\\n}\\n.context-menu[_ngcontent-%COMP%]   .context-menu-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n  padding: 12px 16px;\\n  cursor: pointer;\\n  transition: background 0.2s ease;\\n  color: #2f3542;\\n}\\n.context-menu[_ngcontent-%COMP%]   .context-menu-item[_ngcontent-%COMP%]:hover {\\n  background: rgba(55, 66, 250, 0.1);\\n}\\n.context-menu[_ngcontent-%COMP%]   .context-menu-item.logout-item[_ngcontent-%COMP%] {\\n  color: #ff4757;\\n}\\n.context-menu[_ngcontent-%COMP%]   .context-menu-item.logout-item[_ngcontent-%COMP%]:hover {\\n  background: rgba(255, 71, 87, 0.1);\\n}\\n.context-menu[_ngcontent-%COMP%]   .context-menu-item[_ngcontent-%COMP%]   .menu-icon[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  width: 20px;\\n  text-align: center;\\n}\\n.context-menu[_ngcontent-%COMP%]   .context-menu-item[_ngcontent-%COMP%]   .menu-text[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 500;\\n}\\n.context-menu[_ngcontent-%COMP%]   .context-menu-divider[_ngcontent-%COMP%] {\\n  height: 1px;\\n  background: rgba(0, 0, 0, 0.1);\\n  margin: 4px 0;\\n}\\n\\n.account-settings-modal[_ngcontent-%COMP%] {\\n  max-width: 500px;\\n  width: 90vw;\\n}\\n\\n.avatar-section[_ngcontent-%COMP%] {\\n  text-align: center;\\n  margin-bottom: 32px;\\n}\\n.avatar-section[_ngcontent-%COMP%]   .avatar-container[_ngcontent-%COMP%] {\\n  display: inline-block;\\n}\\n.avatar-section[_ngcontent-%COMP%]   .avatar-display[_ngcontent-%COMP%] {\\n  width: 120px;\\n  height: 120px;\\n  border-radius: 50%;\\n  margin: 0 auto 16px;\\n  position: relative;\\n  overflow: hidden;\\n  border: 4px solid #e1e8ed;\\n}\\n.avatar-section[_ngcontent-%COMP%]   .avatar-display[_ngcontent-%COMP%]   .avatar-image[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  object-fit: cover;\\n}\\n.avatar-section[_ngcontent-%COMP%]   .avatar-display[_ngcontent-%COMP%]   .avatar-placeholder[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  background: linear-gradient(135deg, #3742fa 0%, #2f3542 100%);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n.avatar-section[_ngcontent-%COMP%]   .avatar-display[_ngcontent-%COMP%]   .avatar-placeholder[_ngcontent-%COMP%]   .avatar-initials[_ngcontent-%COMP%] {\\n  font-size: 48px;\\n  font-weight: 600;\\n  color: white;\\n}\\n.avatar-section[_ngcontent-%COMP%]   .avatar-actions[_ngcontent-%COMP%]   .avatar-upload-btn[_ngcontent-%COMP%] {\\n  display: inline-block;\\n  padding: 8px 16px;\\n  background: #3742fa;\\n  color: white;\\n  border-radius: 6px;\\n  font-size: 14px;\\n  font-weight: 500;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n}\\n.avatar-section[_ngcontent-%COMP%]   .avatar-actions[_ngcontent-%COMP%]   .avatar-upload-btn[_ngcontent-%COMP%]:hover {\\n  background: #2f3542;\\n  transform: translateY(-1px);\\n}\\n\\n.profile-info[_ngcontent-%COMP%] {\\n  margin-bottom: 32px;\\n}\\n.profile-info[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%] {\\n  margin-bottom: 20px;\\n}\\n.profile-info[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  display: block;\\n  margin-bottom: 8px;\\n  font-weight: 500;\\n  color: #2f3542;\\n  font-size: 14px;\\n}\\n.profile-info[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .readonly-field[_ngcontent-%COMP%] {\\n  padding: 12px 16px;\\n  background: #f8f9fa;\\n  border: 2px solid #e1e8ed;\\n  border-radius: 8px;\\n  color: #57606f;\\n}\\n.profile-info[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .readonly-field[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  display: block;\\n}\\n.profile-info[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .readonly-field[_ngcontent-%COMP%]   span.field-note[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: #a4b0be;\\n  margin-top: 4px;\\n  font-style: italic;\\n}\\n\\n.security-info[_ngcontent-%COMP%] {\\n  background: rgba(55, 66, 250, 0.05);\\n  border: 1px solid rgba(55, 66, 250, 0.2);\\n  border-radius: 8px;\\n  padding: 20px;\\n  margin-bottom: 24px;\\n}\\n.security-info[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0 0 16px 0;\\n  font-size: 16px;\\n  color: #2f3542;\\n  font-weight: 600;\\n}\\n.security-info[_ngcontent-%COMP%]   .security-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 12px;\\n}\\n.security-info[_ngcontent-%COMP%]   .security-item[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0;\\n}\\n.security-info[_ngcontent-%COMP%]   .security-item[_ngcontent-%COMP%]   .security-label[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: #57606f;\\n  font-weight: 500;\\n}\\n.security-info[_ngcontent-%COMP%]   .security-item[_ngcontent-%COMP%]   .security-value[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: #3742fa;\\n  font-weight: 500;\\n  text-align: right;\\n  flex: 1;\\n  margin-left: 16px;\\n}\\n\\n.account-actions[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding-top: 16px;\\n  border-top: 1px solid rgba(0, 0, 0, 0.1);\\n}\\n\\n.keyboard-hints[_ngcontent-%COMP%] {\\n  position: fixed;\\n  bottom: 20px;\\n  left: 50%;\\n  transform: translateX(-50%);\\n  color: rgba(255, 255, 255, 0.4);\\n  font-size: 12px;\\n  z-index: 5;\\n}\\n\\n@keyframes _ngcontent-%COMP%_fadeIn {\\n  from {\\n    opacity: 0;\\n  }\\n  to {\\n    opacity: 1;\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_slideUp {\\n  from {\\n    opacity: 0;\\n    transform: translateY(20px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_pulse {\\n  0%, 100% {\\n    transform: scale(1);\\n  }\\n  50% {\\n    transform: scale(1.02);\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_bounce {\\n  0%, 20%, 50%, 80%, 100% {\\n    transform: translateY(0);\\n  }\\n  40% {\\n    transform: translateY(-5px);\\n  }\\n  60% {\\n    transform: translateY(-3px);\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_windFlow {\\n  0% {\\n    transform: translateX(0) translateY(0) scale(1);\\n    opacity: 0.3;\\n  }\\n  50% {\\n    transform: translateX(20px) translateY(-10px) scale(0.8);\\n    opacity: 0.6;\\n  }\\n  100% {\\n    transform: translateX(40px) translateY(-20px) scale(0.5);\\n    opacity: 0;\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_spin {\\n  0% {\\n    transform: rotate(0deg);\\n  }\\n  100% {\\n    transform: rotate(360deg);\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_contextMenuSlide {\\n  from {\\n    opacity: 0;\\n    transform: scale(0.95) translateY(-10px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: scale(1) translateY(0);\\n  }\\n}\\n@media (max-width: 768px) {\\n  .qsc-circle[_ngcontent-%COMP%] {\\n    width: 150px;\\n    height: 150px;\\n  }\\n  .modal[_ngcontent-%COMP%] {\\n    width: 95vw;\\n    margin: 20px;\\n  }\\n  .user-info[_ngcontent-%COMP%] {\\n    top: 15px;\\n    right: 15px;\\n    font-size: 12px;\\n  }\\n}\\n@media (max-width: 480px) {\\n  .qsc-circle[_ngcontent-%COMP%] {\\n    width: 120px;\\n    height: 120px;\\n  }\\n  .modal-content[_ngcontent-%COMP%] {\\n    padding: 20px;\\n  }\\n  .modal-header[_ngcontent-%COMP%] {\\n    padding: 16px 20px;\\n  }\\n  .context-menu[_ngcontent-%COMP%] {\\n    min-width: 160px;\\n  }\\n  .context-menu[_ngcontent-%COMP%]   .context-menu-item[_ngcontent-%COMP%] {\\n    padding: 14px 16px;\\n  }\\n  .context-menu[_ngcontent-%COMP%]   .context-menu-item[_ngcontent-%COMP%]   .menu-text[_ngcontent-%COMP%] {\\n    font-size: 16px;\\n  }\\n  .avatar-section[_ngcontent-%COMP%]   .avatar-display[_ngcontent-%COMP%] {\\n    width: 100px;\\n    height: 100px;\\n  }\\n  .avatar-section[_ngcontent-%COMP%]   .avatar-display[_ngcontent-%COMP%]   .avatar-initials[_ngcontent-%COMP%] {\\n    font-size: 40px;\\n  }\\n  .security-info[_ngcontent-%COMP%]   .security-item[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: flex-start;\\n    gap: 4px;\\n  }\\n  .security-info[_ngcontent-%COMP%]   .security-item[_ngcontent-%COMP%]   .security-value[_ngcontent-%COMP%] {\\n    text-align: left;\\n    margin-left: 0;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvY29tcG9uZW50cy9xc2MtbWFpbi9xc2MtbWFpbi5jb21wb25lbnQuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFDQTtFQUNFLGVBQUE7RUFDQSxNQUFBO0VBQ0EsT0FBQTtFQUNBLFlBQUE7RUFDQSxhQUFBO0VBQ0EsMEVBQUE7RUFDQSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSx1QkFBQTtFQUNBLGdCQUFBO0VBQ0EsbUVBQUE7QUFBRjs7QUFJQTtFQUNFLGtCQUFBO0VBQ0EsVUFBQTtBQURGOztBQUlBO0VBQ0UsWUFBQTtFQUNBLGFBQUE7RUFDQSxrQkFBQTtFQUNBLGVBQUE7RUFDQSxpREFBQTtFQUNBLGtCQUFBO0VBQ0EsYUFBQTtFQUNBLG1CQUFBO0VBQ0EsdUJBQUE7RUFDQSx5Q0FBQTtBQURGO0FBR0U7RUFDRSxzQkFBQTtFQUNBLDBDQUFBO0FBREo7QUFJRTtFQUNFLHNCQUFBO0FBRko7QUFNRTtFQUNFLDZEQUFBO0VBQ0Esd0NBQUE7QUFKSjtBQU1JO0VBQ0UsOENBQUE7QUFKTjtBQVNFO0VBQ0UsNkRBQUE7RUFDQSx3Q0FBQTtBQVBKO0FBU0k7RUFDRSw4Q0FBQTtBQVBOO0FBWUU7RUFDRSw2REFBQTtFQUNBLHlDQUFBO0VBQ0EsNEJBQUE7QUFWSjtBQVlJO0VBQ0UsK0NBQUE7QUFWTjtBQWVFO0VBQ0UsNkRBQUE7RUFDQSx5Q0FBQTtBQWJKO0FBZUk7RUFDRSwrQ0FBQTtBQWJOOztBQWtCQTtFQUNFLGtCQUFBO0VBQ0EsV0FBQTtFQUNBLFlBQUE7RUFDQSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSx1QkFBQTtBQWZGOztBQW1CQTtFQUNFLGtCQUFBO0VBQ0EsUUFBQTtFQUNBLFVBQUE7RUFDQSxXQUFBO0VBQ0EsWUFBQTtFQUNBLFlBQUE7QUFoQkY7QUFrQkU7RUFFRSxXQUFBO0VBQ0Esa0JBQUE7RUFDQSxvQ0FBQTtFQUNBLGtCQUFBO0VBQ0EsMkNBQUE7QUFqQko7QUFvQkU7RUFDRSxVQUFBO0VBQ0EsV0FBQTtFQUNBLFNBQUE7RUFDQSxPQUFBO0VBQ0EsbUJBQUE7QUFsQko7QUFxQkU7RUFDRSxVQUFBO0VBQ0EsV0FBQTtFQUNBLFNBQUE7RUFDQSxVQUFBO0VBQ0EsbUJBQUE7QUFuQko7O0FBd0JBO0VBQ0Usa0JBQUE7RUFDQSxVQUFBO0VBQ0EsWUFBQTtFQUNBLG1CQUFBO0VBQ0EsWUFBQTtFQUNBLGtCQUFBO0VBQ0EsV0FBQTtFQUNBLFlBQUE7RUFDQSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSx1QkFBQTtFQUNBLGdCQUFBO0VBQ0EsZUFBQTtFQUNBLHlCQUFBO0VBQ0EsNkJBQUE7QUFyQkY7O0FBeUJBO0VBQ0Usa0JBQUE7RUFDQSxTQUFBO0VBQ0EsV0FBQTtFQUNBLGFBQUE7RUFDQSxtQkFBQTtFQUNBLFNBQUE7RUFDQSwrQkFBQTtFQUNBLGVBQUE7RUFDQSxXQUFBO0FBdEJGO0FBd0JFO0VBQ0UsZ0JBQUE7RUFDQSxZQUFBO0VBQ0EsK0JBQUE7RUFDQSxlQUFBO0VBQ0EsZUFBQTtFQUNBLFlBQUE7RUFDQSxrQkFBQTtFQUNBLHlCQUFBO0FBdEJKO0FBd0JJO0VBQ0UsY0FBQTtFQUNBLGtDQUFBO0FBdEJOOztBQTRCQTtFQUNFLGVBQUE7RUFDQSxNQUFBO0VBQ0EsT0FBQTtFQUNBLFlBQUE7RUFDQSxhQUFBO0VBQ0EsOEJBQUE7RUFDQSxrQ0FBQTtVQUFBLDBCQUFBO0VBQ0EsYUFBQTtFQUNBLG1CQUFBO0VBQ0EsdUJBQUE7RUFDQSxhQUFBO0VBQ0EsMkJBQUE7QUF6QkY7O0FBNEJBO0VBQ0UsbUJBQUE7RUFDQSxtQkFBQTtFQUNBLHFIQUNFO0VBR0YsZ0JBQUE7RUFDQSxXQUFBO0VBQ0EsZ0JBQUE7RUFDQSwwQ0FBQTtFQUNBLGtCQUFBO0VBQ0Esb0RBQUE7RUFDQSxhQUFBO0VBQ0Esc0JBQUE7QUE1QkY7O0FBK0JBO0VBQ0UsV0FBQTtFQUNBLGtCQUFBO0VBQ0EsU0FBQTtFQUNBLFVBQUE7RUFDQSxXQUFBO0VBQ0EsWUFBQTtFQUNBLDZIQUFBO0VBSUEsbUJBQUE7RUFDQSxXQUFBO0FBL0JGOztBQWtDQTtFQUNFLGFBQUE7RUFDQSw4QkFBQTtFQUNBLG1CQUFBO0VBQ0Esa0JBQUE7RUFDQSwyQ0FBQTtBQS9CRjtBQWlDRTtFQUNFLFNBQUE7RUFDQSxlQUFBO0VBQ0EsZ0JBQUE7RUFDQSxjQUFBO0FBL0JKO0FBa0NFO0VBQ0UsZ0JBQUE7RUFDQSxZQUFBO0VBQ0EsZUFBQTtFQUNBLGNBQUE7RUFDQSxlQUFBO0VBQ0EsVUFBQTtFQUNBLFdBQUE7RUFDQSxZQUFBO0VBQ0EsYUFBQTtFQUNBLG1CQUFBO0VBQ0EsdUJBQUE7RUFDQSxrQkFBQTtFQUNBLHlCQUFBO0FBaENKO0FBa0NJO0VBQ0UsOEJBQUE7RUFDQSxjQUFBO0FBaENOOztBQXFDQTtFQUNFLGFBQUE7RUFDQSxPQUFBO0VBQ0EsZ0JBQUE7QUFsQ0Y7QUFxQ0U7RUFDRSxVQUFBO0FBbkNKO0FBc0NFO0VBQ0UsdUJBQUE7QUFwQ0o7QUF1Q0U7RUFDRSw4QkFBQTtFQUNBLGtCQUFBO0FBckNKO0FBd0NFO0VBQ0UsOEJBQUE7QUF0Q0o7O0FBMkNBO0VBQ0UsbUJBQUE7RUFDQSxrQkFBQTtBQXhDRjtBQTBDRTtFQUNFLGNBQUE7RUFDQSxxQkFBQTtFQUNBLG1CQUFBO0VBQ0EsZ0JBQUE7RUFDQSxjQUFBO0VBQ0EsdUJBQUE7RUFDQSx5QkFBQTtBQXhDSjtBQTJDRTtFQUNFLFdBQUE7RUFDQSxrQkFBQTtFQUNBLHVCQUFBO0VBQ0EsWUFBQTtFQUNBLGdDQUFBO0VBQ0EsY0FBQTtFQUNBLGVBQUE7RUFDQSxnQkFBQTtFQUNBLHlCQUFBO0FBekNKO0FBMkNJO0VBQ0UsYUFBQTtFQUNBLDRCQUFBO0FBekNOO0FBNENJO0VBQ0UsdUJBQUE7RUFDQSxjQUFBO0VBQ0EsNEJBQUE7QUExQ047QUE2Q0k7RUFDRSxjQUFBO0VBQ0EsZ0JBQUE7QUEzQ047QUErQ0U7RUFDRSxnQkFBQTtFQUNBLGlCQUFBO0VBQ0Esb0JBQUE7RUFDQSx5QkFBQTtFQUNBLGtCQUFBO0VBQ0EsZ0JBQUE7QUE3Q0o7QUErQ0k7RUFDRSxxQkFBQTtBQTdDTjs7QUFrREE7RUFDRSxpQkFBQTtFQUNBLGVBQUE7RUFDQSxjQUFBO0VBQ0EsZUFBQTtBQS9DRjs7QUFtREE7RUFDRSxvQkFBQTtFQUNBLG1CQUFBO0VBQ0EsdUJBQUE7RUFDQSx3QkFBQTtFQUNBLFlBQUE7RUFDQSxtQkFBQTtFQUNBLG1CQUFBO0VBQ0EsZ0JBQUE7RUFDQSxlQUFBO0VBQ0EseUJBQUE7RUFDQSxxQkFBQTtFQUNBLGdCQUFBO0VBQ0EsV0FBQTtFQUNBLG1CQUFBO0VBQ0EsY0FBQTtBQWhERjtBQWtERTtFQUNFLG1CQUFBO0VBQ0EsMkJBQUE7QUFoREo7QUFtREU7RUFDRSxtQkFBQTtFQUNBLFlBQUE7QUFqREo7QUFtREk7RUFDRSxtQkFBQTtFQUNBLDZDQUFBO0FBakROO0FBcURFO0VBQ0Usd0JBQUE7QUFuREo7QUFzREU7RUFDRSxXQUFBO0VBQ0EsWUFBQTtFQUNBLG9CQUFBO0VBQ0EsVUFBQTtFQUNBLGlCQUFBO0FBcERKOztBQXlEQTtFQUNFLG9CQUFBO0VBQ0EsbUJBQUE7RUFDQSx1QkFBQTtFQUNBLGtCQUFBO0VBQ0EsWUFBQTtFQUNBLHVCQUFBO0VBQ0EsbUJBQUE7RUFDQSxnQkFBQTtFQUNBLGVBQUE7RUFDQSx5QkFBQTtFQUNBLHFCQUFBO0VBQ0EsV0FBQTtFQUNBLGtCQUFBO0VBQ0EsY0FBQTtBQXRERjtBQXdERTtFQUNFLFdBQUE7RUFDQSxrQkFBQTtFQUNBLFNBQUE7RUFDQSxPQUFBO0VBQ0EsUUFBQTtFQUNBLFdBQUE7RUFDQSx3QkFBQTtFQUNBLDJCQUFBO0FBdERKO0FBeURFO0VBQ0UsV0FBQTtBQXZESjtBQTBERTtFQUNFLGNBQUE7QUF4REo7QUEwREk7RUFDRSxjQUFBO0VBQ0EsbUJBQUE7QUF4RE47QUEwRE07RUFDRSxhQUFBO0FBeERSO0FBNkRFO0VBQ0UsY0FBQTtBQTNESjtBQThERTtFQUNFLFdBQUE7RUFDQSxZQUFBO0VBQ0Esb0JBQUE7RUFDQSxVQUFBO0VBQ0EsaUJBQUE7QUE1REo7O0FBaUVBO0VBQ0UsYUFBQTtFQUNBLFNBQUE7RUFDQSxtQkFBQTtFQUNBLHVCQUFBO0VBR0EsYUFBQTtBQWhFRjtBQW1FRTtFQUNFLGFBQUE7QUFqRUo7O0FBc0VBO0VBQ0Usa0JBQUE7QUFuRUY7O0FBc0VBO0VBQ0UsYUFBQTtFQUNBLG1CQUFBO0VBQ0EsOEJBQUE7RUFDQSxpQkFBQTtFQUNBLGtDQUFBO0VBQ0EseUJBQUE7RUFDQSxrQkFBQTtFQUNBLGVBQUE7QUFuRUY7QUFxRUU7RUFDRSxjQUFBO0VBQ0EsZ0JBQUE7RUFDQSxlQUFBO0FBbkVKO0FBc0VFO0VBQ0UsZ0JBQUE7RUFDQSxZQUFBO0VBQ0EsY0FBQTtFQUNBLGVBQUE7RUFDQSxlQUFBO0VBQ0EsVUFBQTtFQUNBLFdBQUE7RUFDQSxZQUFBO0VBQ0EsYUFBQTtFQUNBLG1CQUFBO0VBQ0EsdUJBQUE7RUFDQSxrQkFBQTtFQUNBLHlCQUFBO0FBcEVKO0FBc0VJO0VBQ0Usa0NBQUE7QUFwRU47O0FBeUVBO0VBQ0Usa0JBQUE7RUFDQSxTQUFBO0VBQ0EsT0FBQTtFQUNBLFFBQUE7RUFDQSxpQkFBQTtFQUNBLHlCQUFBO0VBQ0Esa0JBQUE7RUFDQSwwQ0FBQTtFQUNBLGlCQUFBO0VBQ0EsZ0JBQUE7RUFDQSxhQUFBO0VBQ0EsZUFBQTtBQXRFRjs7QUF5RUE7RUFDRSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSw4QkFBQTtFQUNBLGtCQUFBO0VBQ0EsZUFBQTtFQUNBLDRDQUFBO0VBQ0EsZ0NBQUE7QUF0RUY7QUF3RUU7RUFDRSxtQ0FBQTtBQXRFSjtBQXlFRTtFQUNFLG1CQUFBO0FBdkVKOztBQTJFQTtFQUNFLGFBQUE7RUFDQSxzQkFBQTtFQUNBLE9BQUE7QUF4RUY7QUEwRUU7RUFDRSxnQkFBQTtFQUNBLGNBQUE7RUFDQSxlQUFBO0VBQ0Esa0JBQUE7QUF4RUo7QUEyRUU7RUFDRSxlQUFBO0VBQ0EsY0FBQTtBQXpFSjs7QUE2RUE7RUFDRSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSxRQUFBO0FBMUVGO0FBNEVFO0VBQ0UsVUFBQTtFQUNBLFdBQUE7RUFDQSxrQkFBQTtBQTFFSjtBQTRFSTtFQUNFLG1CQUFBO0FBMUVOO0FBNkVJO0VBQ0UsbUJBQUE7QUEzRU47QUE4RUk7RUFDRSxtQkFBQTtBQTVFTjtBQWdGRTtFQUNFLGVBQUE7RUFDQSxjQUFBO0FBOUVKOztBQWtGQTtFQUNFLGFBQUE7RUFDQSxrQkFBQTtFQUNBLGNBQUE7RUFDQSxlQUFBO0VBQ0Esa0JBQUE7QUEvRUY7O0FBa0ZBO0VBQ0UsYUFBQTtFQUNBLFNBQUE7RUFDQSxnQkFBQTtFQUNBLHVCQUFBO0FBL0VGOztBQW1GQTtFQUNFLGNBQUE7RUFDQSxlQUFBO0VBQ0EsZUFBQTtFQUNBLGlCQUFBO0VBQ0Esa0NBQUE7RUFDQSxrQkFBQTtFQUNBLDhCQUFBO0FBaEZGOztBQW1GQTtFQUNFLGdCQUFBO0VBQ0Esa0JBQUE7QUFoRkY7QUFrRkU7RUFDRSxhQUFBO0VBQ0EsZUFBQTtFQUNBLGNBQUE7QUFoRko7QUFrRkk7RUFDRSxrQkFBQTtFQUNBLGNBQUE7QUFoRk47O0FBcUZBO0VBQ0UsZ0JBQUE7RUFDQSxrQkFBQTtBQWxGRjtBQW9GRTtFQUNFLGVBQUE7RUFDQSxjQUFBO0VBQ0EsU0FBQTtBQWxGSjs7QUF1RkE7RUFDRSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSx1QkFBQTtFQUNBLFNBQUE7RUFDQSxhQUFBO0FBcEZGO0FBc0ZFO0VBQ0UsV0FBQTtFQUNBLFlBQUE7RUFDQSx5QkFBQTtFQUNBLDZCQUFBO0VBQ0Esa0JBQUE7RUFDQSxrQ0FBQTtBQXBGSjtBQXVGRTtFQUNFLGNBQUE7RUFDQSxlQUFBO0FBckZKOztBQTBGQTtFQUNFLGlCQUFBO0VBQ0EsZ0JBQUE7RUFDQSxZQUFBO0VBQ0EsWUFBQTtBQXZGRjs7QUEwRkE7RUFDRSxhQUFBO0VBQ0EsNENBQUE7QUF2RkY7QUF5RkU7RUFDRSxtQkFBQTtBQXZGSjtBQTBGRTtFQUNFLGFBQUE7RUFDQSw4QkFBQTtFQUNBLG1CQUFBO0VBQ0Esa0JBQUE7QUF4Rko7QUEwRkk7RUFDRSxnQkFBQTtFQUNBLGNBQUE7RUFDQSxlQUFBO0FBeEZOO0FBMkZJO0VBQ0UsZUFBQTtFQUNBLGNBQUE7QUF6Rk47QUE2RkU7RUFDRSxjQUFBO0VBQ0EsZ0JBQUE7RUFDQSxxQkFBQTtBQTNGSjs7QUErRkE7RUFDRSxrQkFBQTtFQUNBLGtCQUFBO0VBQ0EsY0FBQTtBQTVGRjtBQThGRTtFQUNFLGFBQUE7QUE1Rko7QUE4Rkk7RUFDRSxlQUFBO0VBQ0Esa0JBQUE7QUE1Rk47O0FBa0dBO0VBQ0UsZUFBQTtFQUNBLHFDQUFBO0VBQ0EsbUNBQUE7VUFBQSwyQkFBQTtFQUNBLG1CQUFBO0VBQ0EseUNBQUE7RUFDQSxjQUFBO0VBQ0EsZ0JBQUE7RUFDQSxhQUFBO0VBQ0EsNkRBQUE7QUEvRkY7QUFpR0U7RUFDRSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSxTQUFBO0VBQ0Esa0JBQUE7RUFDQSxlQUFBO0VBQ0EsZ0NBQUE7RUFDQSxjQUFBO0FBL0ZKO0FBaUdJO0VBQ0Usa0NBQUE7QUEvRk47QUFrR0k7RUFDRSxjQUFBO0FBaEdOO0FBa0dNO0VBQ0Usa0NBQUE7QUFoR1I7QUFvR0k7RUFDRSxlQUFBO0VBQ0EsV0FBQTtFQUNBLGtCQUFBO0FBbEdOO0FBcUdJO0VBQ0UsZUFBQTtFQUNBLGdCQUFBO0FBbkdOO0FBdUdFO0VBQ0UsV0FBQTtFQUNBLDhCQUFBO0VBQ0EsYUFBQTtBQXJHSjs7QUEwR0E7RUFDRSxnQkFBQTtFQUNBLFdBQUE7QUF2R0Y7O0FBMEdBO0VBQ0Usa0JBQUE7RUFDQSxtQkFBQTtBQXZHRjtBQXlHRTtFQUNFLHFCQUFBO0FBdkdKO0FBMEdFO0VBQ0UsWUFBQTtFQUNBLGFBQUE7RUFDQSxrQkFBQTtFQUNBLG1CQUFBO0VBQ0Esa0JBQUE7RUFDQSxnQkFBQTtFQUNBLHlCQUFBO0FBeEdKO0FBMEdJO0VBQ0UsV0FBQTtFQUNBLFlBQUE7RUFDQSxpQkFBQTtBQXhHTjtBQTJHSTtFQUNFLFdBQUE7RUFDQSxZQUFBO0VBQ0EsNkRBQUE7RUFDQSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSx1QkFBQTtBQXpHTjtBQTJHTTtFQUNFLGVBQUE7RUFDQSxnQkFBQTtFQUNBLFlBQUE7QUF6R1I7QUErR0k7RUFDRSxxQkFBQTtFQUNBLGlCQUFBO0VBQ0EsbUJBQUE7RUFDQSxZQUFBO0VBQ0Esa0JBQUE7RUFDQSxlQUFBO0VBQ0EsZ0JBQUE7RUFDQSxlQUFBO0VBQ0EseUJBQUE7QUE3R047QUErR007RUFDRSxtQkFBQTtFQUNBLDJCQUFBO0FBN0dSOztBQW1IQTtFQUNFLG1CQUFBO0FBaEhGO0FBa0hFO0VBQ0UsbUJBQUE7QUFoSEo7QUFrSEk7RUFDRSxjQUFBO0VBQ0Esa0JBQUE7RUFDQSxnQkFBQTtFQUNBLGNBQUE7RUFDQSxlQUFBO0FBaEhOO0FBbUhJO0VBQ0Usa0JBQUE7RUFDQSxtQkFBQTtFQUNBLHlCQUFBO0VBQ0Esa0JBQUE7RUFDQSxjQUFBO0FBakhOO0FBbUhNO0VBQ0UsY0FBQTtBQWpIUjtBQW1IUTtFQUNFLGVBQUE7RUFDQSxjQUFBO0VBQ0EsZUFBQTtFQUNBLGtCQUFBO0FBakhWOztBQXdIQTtFQUNFLG1DQUFBO0VBQ0Esd0NBQUE7RUFDQSxrQkFBQTtFQUNBLGFBQUE7RUFDQSxtQkFBQTtBQXJIRjtBQXVIRTtFQUNFLGtCQUFBO0VBQ0EsZUFBQTtFQUNBLGNBQUE7RUFDQSxnQkFBQTtBQXJISjtBQXdIRTtFQUNFLGFBQUE7RUFDQSw4QkFBQTtFQUNBLG1CQUFBO0VBQ0EsbUJBQUE7QUF0SEo7QUF3SEk7RUFDRSxnQkFBQTtBQXRITjtBQXlISTtFQUNFLGVBQUE7RUFDQSxjQUFBO0VBQ0EsZ0JBQUE7QUF2SE47QUEwSEk7RUFDRSxlQUFBO0VBQ0EsY0FBQTtFQUNBLGdCQUFBO0VBQ0EsaUJBQUE7RUFDQSxPQUFBO0VBQ0EsaUJBQUE7QUF4SE47O0FBNkhBO0VBQ0Usa0JBQUE7RUFDQSxpQkFBQTtFQUNBLHdDQUFBO0FBMUhGOztBQThIQTtFQUNFLGVBQUE7RUFDQSxZQUFBO0VBQ0EsU0FBQTtFQUNBLDJCQUFBO0VBQ0EsK0JBQUE7RUFDQSxlQUFBO0VBQ0EsVUFBQTtBQTNIRjs7QUErSEE7RUFDRTtJQUFPLFVBQUE7RUEzSFA7RUE0SEE7SUFBSyxVQUFBO0VBekhMO0FBQ0Y7QUEySEE7RUFDRTtJQUNFLFVBQUE7SUFDQSwyQkFBQTtFQXpIRjtFQTJIQTtJQUNFLFVBQUE7SUFDQSx3QkFBQTtFQXpIRjtBQUNGO0FBNEhBO0VBQ0U7SUFBVyxtQkFBQTtFQXpIWDtFQTBIQTtJQUFNLHNCQUFBO0VBdkhOO0FBQ0Y7QUF5SEE7RUFDRTtJQUEwQix3QkFBQTtFQXRIMUI7RUF1SEE7SUFBTSwyQkFBQTtFQXBITjtFQXFIQTtJQUFNLDJCQUFBO0VBbEhOO0FBQ0Y7QUFvSEE7RUFDRTtJQUFLLCtDQUFBO0lBQWlELFlBQUE7RUFoSHREO0VBaUhBO0lBQU0sd0RBQUE7SUFBMEQsWUFBQTtFQTdHaEU7RUE4R0E7SUFBTyx3REFBQTtJQUEwRCxVQUFBO0VBMUdqRTtBQUNGO0FBNEdBO0VBQ0U7SUFBSyx1QkFBQTtFQXpHTDtFQTBHQTtJQUFPLHlCQUFBO0VBdkdQO0FBQ0Y7QUF5R0E7RUFDRTtJQUNFLFVBQUE7SUFDQSx3Q0FBQTtFQXZHRjtFQXlHQTtJQUNFLFVBQUE7SUFDQSxpQ0FBQTtFQXZHRjtBQUNGO0FBMkdBO0VBQ0U7SUFDRSxZQUFBO0lBQ0EsYUFBQTtFQXpHRjtFQTRHQTtJQUNFLFdBQUE7SUFDQSxZQUFBO0VBMUdGO0VBNkdBO0lBQ0UsU0FBQTtJQUNBLFdBQUE7SUFDQSxlQUFBO0VBM0dGO0FBQ0Y7QUE4R0E7RUFDRTtJQUNFLFlBQUE7SUFDQSxhQUFBO0VBNUdGO0VBK0dBO0lBQ0UsYUFBQTtFQTdHRjtFQWdIQTtJQUNFLGtCQUFBO0VBOUdGO0VBaUhBO0lBQ0UsZ0JBQUE7RUEvR0Y7RUFpSEU7SUFDRSxrQkFBQTtFQS9HSjtFQWlISTtJQUNFLGVBQUE7RUEvR047RUFxSEU7SUFDRSxZQUFBO0lBQ0EsYUFBQTtFQW5ISjtFQXFISTtJQUNFLGVBQUE7RUFuSE47RUF5SEU7SUFDRSxzQkFBQTtJQUNBLHVCQUFBO0lBQ0EsUUFBQTtFQXZISjtFQXlISTtJQUNFLGdCQUFBO0lBQ0EsY0FBQTtFQXZITjtBQUNGO0FBR0EsZ3FuQ0FBZ3FuQyIsInNvdXJjZXNDb250ZW50IjpbIi8vIFFTQyBNYWluIENvbXBvbmVudCAtIE1pbmltYWxpc3RpYyBNb2Rlcm4gRGVzaWduXG4ucXNjLWNvbnRhaW5lciB7XG4gIHBvc2l0aW9uOiBmaXhlZDtcbiAgdG9wOiAwO1xuICBsZWZ0OiAwO1xuICB3aWR0aDogMTAwdnc7XG4gIGhlaWdodDogMTAwdmg7XG4gIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICMwZjBmMjMgMCUsICMxYTFhMmUgNTAlLCAjMTYyMTNlIDEwMCUpO1xuICBkaXNwbGF5OiBmbGV4O1xuICBhbGlnbi1pdGVtczogY2VudGVyO1xuICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjtcbiAgb3ZlcmZsb3c6IGhpZGRlbjtcbiAgZm9udC1mYW1pbHk6ICdJbnRlcicsIC1hcHBsZS1zeXN0ZW0sIEJsaW5rTWFjU3lzdGVtRm9udCwgc2Fucy1zZXJpZjtcbn1cblxuLy8gQ2VudHJhbCBDaXJjbGVcbi5jaXJjbGUtY29udGFpbmVyIHtcbiAgcG9zaXRpb246IHJlbGF0aXZlO1xuICB6LWluZGV4OiAxO1xufVxuXG4ucXNjLWNpcmNsZSB7XG4gIHdpZHRoOiAyMDBweDtcbiAgaGVpZ2h0OiAyMDBweDtcbiAgYm9yZGVyLXJhZGl1czogNTAlO1xuICBjdXJzb3I6IHBvaW50ZXI7XG4gIHRyYW5zaXRpb246IGFsbCAwLjNzIGN1YmljLWJlemllcigwLjQsIDAsIDAuMiwgMSk7XG4gIHBvc2l0aW9uOiByZWxhdGl2ZTtcbiAgZGlzcGxheTogZmxleDtcbiAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7XG4gIGJveC1zaGFkb3c6IDAgOHB4IDMycHggcmdiYSgwLCAwLCAwLCAwLjMpO1xuXG4gICY6aG92ZXIge1xuICAgIHRyYW5zZm9ybTogc2NhbGUoMS4wNSk7XG4gICAgYm94LXNoYWRvdzogMCAxMnB4IDQ4cHggcmdiYSgwLCAwLCAwLCAwLjQpO1xuICB9XG5cbiAgJjphY3RpdmUge1xuICAgIHRyYW5zZm9ybTogc2NhbGUoMC45OCk7XG4gIH1cblxuICAvLyBHdWVzdCBzdGF0ZSAtIFJlZFxuICAmLmNpcmNsZS1ndWVzdCB7XG4gICAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgI2ZmNDc1NyAwJSwgI2ZmMzc0MiAxMDAlKTtcbiAgICBib3JkZXI6IDNweCBzb2xpZCByZ2JhKDI1NSwgNzEsIDg3LCAwLjMpO1xuXG4gICAgJjpob3ZlciB7XG4gICAgICBib3gtc2hhZG93OiAwIDEycHggNDhweCByZ2JhKDI1NSwgNzEsIDg3LCAwLjQpO1xuICAgIH1cbiAgfVxuXG4gIC8vIEF1dGhlbnRpY2F0ZWQgc3RhdGUgLSBCbHVlXG4gICYuY2lyY2xlLWF1dGhlbnRpY2F0ZWQge1xuICAgIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICMzNzQyZmEgMCUsICMyZjM1NDIgMTAwJSk7XG4gICAgYm9yZGVyOiAzcHggc29saWQgcmdiYSg1NSwgNjYsIDI1MCwgMC4zKTtcblxuICAgICY6aG92ZXIge1xuICAgICAgYm94LXNoYWRvdzogMCAxMnB4IDQ4cHggcmdiYSg1NSwgNjYsIDI1MCwgMC40KTtcbiAgICB9XG4gIH1cblxuICAvLyBVbnJlYWQgbWVzc2FnZXMgc3RhdGUgLSBHcmVlblxuICAmLmNpcmNsZS11bnJlYWQge1xuICAgIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICMyZWQ1NzMgMCUsICMxZTkwZmYgMTAwJSk7XG4gICAgYm9yZGVyOiAzcHggc29saWQgcmdiYSg0NiwgMjEzLCAxMTUsIDAuMyk7XG4gICAgYW5pbWF0aW9uOiBwdWxzZSAycyBpbmZpbml0ZTtcblxuICAgICY6aG92ZXIge1xuICAgICAgYm94LXNoYWRvdzogMCAxMnB4IDQ4cHggcmdiYSg0NiwgMjEzLCAxMTUsIDAuNCk7XG4gICAgfVxuICB9XG5cbiAgLy8gQ29tcG9zaW5nIHN0YXRlIC0gUHVycGxlXG4gICYuY2lyY2xlLWNvbXBvc2luZyB7XG4gICAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgI2E1NWVlYSAwJSwgIzg4NTRkMCAxMDAlKTtcbiAgICBib3JkZXI6IDNweCBzb2xpZCByZ2JhKDE2NSwgOTQsIDIzNCwgMC4zKTtcblxuICAgICY6aG92ZXIge1xuICAgICAgYm94LXNoYWRvdzogMCAxMnB4IDQ4cHggcmdiYSgxNjUsIDk0LCAyMzQsIDAuNCk7XG4gICAgfVxuICB9XG59XG5cbi5jaXJjbGUtaW5uZXIge1xuICBwb3NpdGlvbjogcmVsYXRpdmU7XG4gIHdpZHRoOiAxMDAlO1xuICBoZWlnaHQ6IDEwMCU7XG4gIGRpc3BsYXk6IGZsZXg7XG4gIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gIGp1c3RpZnktY29udGVudDogY2VudGVyO1xufVxuXG4vLyBXaW5kIGVmZmVjdCBhbmltYXRpb25cbi53aW5kLWVmZmVjdCB7XG4gIHBvc2l0aW9uOiBhYnNvbHV0ZTtcbiAgdG9wOiAyMCU7XG4gIHJpZ2h0OiAxNSU7XG4gIHdpZHRoOiA2MHB4O1xuICBoZWlnaHQ6IDYwcHg7XG4gIG9wYWNpdHk6IDAuMztcblxuICAmOjpiZWZvcmUsXG4gICY6OmFmdGVyIHtcbiAgICBjb250ZW50OiAnJztcbiAgICBwb3NpdGlvbjogYWJzb2x1dGU7XG4gICAgYmFja2dyb3VuZDogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjYpO1xuICAgIGJvcmRlci1yYWRpdXM6IDUwJTtcbiAgICBhbmltYXRpb246IHdpbmRGbG93IDNzIGVhc2UtaW4tb3V0IGluZmluaXRlO1xuICB9XG5cbiAgJjo6YmVmb3JlIHtcbiAgICB3aWR0aDogOHB4O1xuICAgIGhlaWdodDogOHB4O1xuICAgIHRvcDogMTBweDtcbiAgICBsZWZ0OiAwO1xuICAgIGFuaW1hdGlvbi1kZWxheTogMHM7XG4gIH1cblxuICAmOjphZnRlciB7XG4gICAgd2lkdGg6IDZweDtcbiAgICBoZWlnaHQ6IDZweDtcbiAgICB0b3A6IDI1cHg7XG4gICAgbGVmdDogMTVweDtcbiAgICBhbmltYXRpb24tZGVsYXk6IDFzO1xuICB9XG59XG5cbi8vIFVucmVhZCBpbmRpY2F0b3Jcbi51bnJlYWQtaW5kaWNhdG9yIHtcbiAgcG9zaXRpb246IGFic29sdXRlO1xuICB0b3A6IC0xMHB4O1xuICByaWdodDogLTEwcHg7XG4gIGJhY2tncm91bmQ6ICNmZjQ3NTc7XG4gIGNvbG9yOiB3aGl0ZTtcbiAgYm9yZGVyLXJhZGl1czogNTAlO1xuICB3aWR0aDogNDBweDtcbiAgaGVpZ2h0OiA0MHB4O1xuICBkaXNwbGF5OiBmbGV4O1xuICBhbGlnbi1pdGVtczogY2VudGVyO1xuICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjtcbiAgZm9udC13ZWlnaHQ6IDYwMDtcbiAgZm9udC1zaXplOiAxNHB4O1xuICBib3JkZXI6IDNweCBzb2xpZCAjMGYwZjIzO1xuICBhbmltYXRpb246IGJvdW5jZSAxcyBpbmZpbml0ZTtcbn1cblxuLy8gVXNlciBpbmZvXG4udXNlci1pbmZvIHtcbiAgcG9zaXRpb246IGFic29sdXRlO1xuICB0b3A6IDIwcHg7XG4gIHJpZ2h0OiAyMHB4O1xuICBkaXNwbGF5OiBmbGV4O1xuICBhbGlnbi1pdGVtczogY2VudGVyO1xuICBnYXA6IDEwcHg7XG4gIGNvbG9yOiByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuNyk7XG4gIGZvbnQtc2l6ZTogMTRweDtcbiAgei1pbmRleDogMTA7XG5cbiAgLmxvZ291dC1idG4ge1xuICAgIGJhY2tncm91bmQ6IG5vbmU7XG4gICAgYm9yZGVyOiBub25lO1xuICAgIGNvbG9yOiByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuNSk7XG4gICAgZm9udC1zaXplOiAyMHB4O1xuICAgIGN1cnNvcjogcG9pbnRlcjtcbiAgICBwYWRkaW5nOiA1cHg7XG4gICAgYm9yZGVyLXJhZGl1czogNTAlO1xuICAgIHRyYW5zaXRpb246IGFsbCAwLjJzIGVhc2U7XG5cbiAgICAmOmhvdmVyIHtcbiAgICAgIGNvbG9yOiAjZmY0NzU3O1xuICAgICAgYmFja2dyb3VuZDogcmdiYSgyNTUsIDcxLCA4NywgMC4xKTtcbiAgICB9XG4gIH1cbn1cblxuLy8gTW9kYWwgc3R5bGVzIC0gRnV0dXJpc3RpYyBNaW5pbWFsaXNtXG4ubW9kYWwtb3ZlcmxheSB7XG4gIHBvc2l0aW9uOiBmaXhlZDtcbiAgdG9wOiAwO1xuICBsZWZ0OiAwO1xuICB3aWR0aDogMTAwdnc7XG4gIGhlaWdodDogMTAwdmg7XG4gIGJhY2tncm91bmQ6IHJnYmEoMCwgMCwgMCwgMC4zKTtcbiAgYmFja2Ryb3AtZmlsdGVyOiBibHVyKDhweCk7XG4gIGRpc3BsYXk6IGZsZXg7XG4gIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gIGp1c3RpZnktY29udGVudDogY2VudGVyO1xuICB6LWluZGV4OiAxMDAwO1xuICBhbmltYXRpb246IGZhZGVJbiAwLjJzIGVhc2U7XG59XG5cbi5tb2RhbCB7XG4gIGJhY2tncm91bmQ6ICNmYWZhZmE7XG4gIGJvcmRlci1yYWRpdXM6IDE2cHg7XG4gIGJveC1zaGFkb3c6XG4gICAgMCAwIDQwcHggcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjEpLFxuICAgIDAgOHB4IDMycHggcmdiYSgwLCAwLCAwLCAwLjEyKSxcbiAgICBpbnNldCAwIDFweCAwIHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4yKTtcbiAgbWF4LXdpZHRoOiA0MDBweDtcbiAgd2lkdGg6IDkwdnc7XG4gIG1heC1oZWlnaHQ6IDgwdmg7XG4gIGJvcmRlcjogMXB4IHNvbGlkIHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4xKTtcbiAgcG9zaXRpb246IHJlbGF0aXZlO1xuICBhbmltYXRpb246IHNsaWRlVXAgMC4zcyBjdWJpYy1iZXppZXIoMC40LCAwLCAwLjIsIDEpO1xuICBkaXNwbGF5OiBmbGV4O1xuICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xufVxuXG4ubW9kYWw6OmJlZm9yZSB7XG4gIGNvbnRlbnQ6ICcnO1xuICBwb3NpdGlvbjogYWJzb2x1dGU7XG4gIHRvcDogLTJweDtcbiAgbGVmdDogLTJweDtcbiAgcmlnaHQ6IC0ycHg7XG4gIGJvdHRvbTogLTJweDtcbiAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDQ1ZGVnLFxuICAgIHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4xKSAwJSxcbiAgICByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMDUpIDUwJSxcbiAgICByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMSkgMTAwJSk7XG4gIGJvcmRlci1yYWRpdXM6IDE4cHg7XG4gIHotaW5kZXg6IC0xO1xufVxuXG4ubW9kYWwtaGVhZGVyIHtcbiAgZGlzcGxheTogZmxleDtcbiAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuO1xuICBhbGlnbi1pdGVtczogY2VudGVyO1xuICBwYWRkaW5nOiAyMHB4IDI0cHg7XG4gIGJvcmRlci1ib3R0b206IDFweCBzb2xpZCByZ2JhKDAsIDAsIDAsIDAuMSk7XG5cbiAgaDIge1xuICAgIG1hcmdpbjogMDtcbiAgICBmb250LXNpemU6IDIwcHg7XG4gICAgZm9udC13ZWlnaHQ6IDYwMDtcbiAgICBjb2xvcjogIzJmMzU0MjtcbiAgfVxuXG4gIC5jbG9zZS1idG4ge1xuICAgIGJhY2tncm91bmQ6IG5vbmU7XG4gICAgYm9yZGVyOiBub25lO1xuICAgIGZvbnQtc2l6ZTogMjRweDtcbiAgICBjb2xvcjogI2E0YjBiZTtcbiAgICBjdXJzb3I6IHBvaW50ZXI7XG4gICAgcGFkZGluZzogMDtcbiAgICB3aWR0aDogMzBweDtcbiAgICBoZWlnaHQ6IDMwcHg7XG4gICAgZGlzcGxheTogZmxleDtcbiAgICBhbGlnbi1pdGVtczogY2VudGVyO1xuICAgIGp1c3RpZnktY29udGVudDogY2VudGVyO1xuICAgIGJvcmRlci1yYWRpdXM6IDUwJTtcbiAgICB0cmFuc2l0aW9uOiBhbGwgMC4ycyBlYXNlO1xuXG4gICAgJjpob3ZlciB7XG4gICAgICBiYWNrZ3JvdW5kOiByZ2JhKDAsIDAsIDAsIDAuMSk7XG4gICAgICBjb2xvcjogIzJmMzU0MjtcbiAgICB9XG4gIH1cbn1cblxuLm1vZGFsLWNvbnRlbnQge1xuICBwYWRkaW5nOiAycmVtO1xuICBmbGV4OiAxO1xuICBvdmVyZmxvdy15OiBhdXRvO1xuXG4gIC8vIEN1c3RvbSBzY3JvbGxiYXIgZm9yIG1vZGFsIGNvbnRlbnRcbiAgJjo6LXdlYmtpdC1zY3JvbGxiYXIge1xuICAgIHdpZHRoOiA0cHg7XG4gIH1cblxuICAmOjotd2Via2l0LXNjcm9sbGJhci10cmFjayB7XG4gICAgYmFja2dyb3VuZDogdHJhbnNwYXJlbnQ7XG4gIH1cblxuICAmOjotd2Via2l0LXNjcm9sbGJhci10aHVtYiB7XG4gICAgYmFja2dyb3VuZDogcmdiYSgwLCAwLCAwLCAwLjEpO1xuICAgIGJvcmRlci1yYWRpdXM6IDJweDtcbiAgfVxuXG4gICY6Oi13ZWJraXQtc2Nyb2xsYmFyLXRodW1iOmhvdmVyIHtcbiAgICBiYWNrZ3JvdW5kOiByZ2JhKDAsIDAsIDAsIDAuMik7XG4gIH1cbn1cblxuLy8gRm9ybSBzdHlsZXMgLSBGdXR1cmlzdGljIE1pbmltYWxpc21cbi5mb3JtLWdyb3VwIHtcbiAgbWFyZ2luLWJvdHRvbTogMnJlbTtcbiAgcG9zaXRpb246IHJlbGF0aXZlO1xuXG4gIGxhYmVsIHtcbiAgICBkaXNwbGF5OiBibG9jaztcbiAgICBtYXJnaW4tYm90dG9tOiAwLjVyZW07XG4gICAgZm9udC1zaXplOiAwLjg3NXJlbTtcbiAgICBmb250LXdlaWdodDogNTAwO1xuICAgIGNvbG9yOiAjNmI3MjgwO1xuICAgIGxldHRlci1zcGFjaW5nOiAwLjAyNWVtO1xuICAgIHRleHQtdHJhbnNmb3JtOiB1cHBlcmNhc2U7XG4gIH1cblxuICBpbnB1dCwgdGV4dGFyZWEge1xuICAgIHdpZHRoOiAxMDAlO1xuICAgIHBhZGRpbmc6IDAuNzVyZW0gMDtcbiAgICBiYWNrZ3JvdW5kOiB0cmFuc3BhcmVudDtcbiAgICBib3JkZXI6IG5vbmU7XG4gICAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkICNlNWU3ZWI7XG4gICAgY29sb3I6ICMxZjI5Mzc7XG4gICAgZm9udC1zaXplOiAxcmVtO1xuICAgIGZvbnQtd2VpZ2h0OiA0MDA7XG4gICAgdHJhbnNpdGlvbjogYWxsIDAuM3MgZWFzZTtcblxuICAgICY6Zm9jdXMge1xuICAgICAgb3V0bGluZTogbm9uZTtcbiAgICAgIGJvcmRlci1ib3R0b20tY29sb3I6ICMxZTQwYWY7XG4gICAgfVxuXG4gICAgJjpkaXNhYmxlZCB7XG4gICAgICBiYWNrZ3JvdW5kOiB0cmFuc3BhcmVudDtcbiAgICAgIGNvbG9yOiAjOWNhM2FmO1xuICAgICAgYm9yZGVyLWJvdHRvbS1jb2xvcjogI2YzZjRmNjtcbiAgICB9XG5cbiAgICAmOjpwbGFjZWhvbGRlciB7XG4gICAgICBjb2xvcjogIzljYTNhZjtcbiAgICAgIGZvbnQtd2VpZ2h0OiAzMDA7XG4gICAgfVxuICB9XG5cbiAgdGV4dGFyZWEge1xuICAgIHJlc2l6ZTogdmVydGljYWw7XG4gICAgbWluLWhlaWdodDogMTIwcHg7XG4gICAgZm9udC1mYW1pbHk6IGluaGVyaXQ7XG4gICAgYm9yZGVyOiAxcHggc29saWQgI2U1ZTdlYjtcbiAgICBib3JkZXItcmFkaXVzOiA4cHg7XG4gICAgcGFkZGluZzogMC43NXJlbTtcblxuICAgICY6Zm9jdXMge1xuICAgICAgYm9yZGVyLWNvbG9yOiAjMWU0MGFmO1xuICAgIH1cbiAgfVxufVxuXG4uY2hhci1jb3VudCB7XG4gIHRleHQtYWxpZ246IHJpZ2h0O1xuICBmb250LXNpemU6IDEycHg7XG4gIGNvbG9yOiAjYTRiMGJlO1xuICBtYXJnaW4tdG9wOiA0cHg7XG59XG5cbi8vIEJ1dHRvbiBzdHlsZXMgLSBGdXR1cmlzdGljIFBpbGxzIChmb3IgdHlwZSBzZWxlY3RvcnMpXG4udHlwZS1idG4ge1xuICBkaXNwbGF5OiBpbmxpbmUtZmxleDtcbiAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7XG4gIHBhZGRpbmc6IDAuODc1cmVtIDEuNXJlbTtcbiAgYm9yZGVyOiBub25lO1xuICBib3JkZXItcmFkaXVzOiA1MHB4O1xuICBmb250LXNpemU6IDAuODc1cmVtO1xuICBmb250LXdlaWdodDogNTAwO1xuICBjdXJzb3I6IHBvaW50ZXI7XG4gIHRyYW5zaXRpb246IGFsbCAwLjNzIGVhc2U7XG4gIHRleHQtZGVjb3JhdGlvbjogbm9uZTtcbiAgbWluLXdpZHRoOiAxMjBweDtcbiAgZ2FwOiAwLjVyZW07XG4gIGJhY2tncm91bmQ6ICNlNWU3ZWI7XG4gIGNvbG9yOiAjNmI3MjgwO1xuXG4gICY6aG92ZXIge1xuICAgIGJhY2tncm91bmQ6ICNkMWQ1ZGI7XG4gICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKC0xcHgpO1xuICB9XG5cbiAgJi5hY3RpdmUge1xuICAgIGJhY2tncm91bmQ6ICMxZTQwYWY7XG4gICAgY29sb3I6IHdoaXRlO1xuXG4gICAgJjpob3ZlciB7XG4gICAgICBiYWNrZ3JvdW5kOiAjMWQ0ZWQ4O1xuICAgICAgYm94LXNoYWRvdzogMCA0cHggMTJweCByZ2JhKDMwLCA2NCwgMTc1LCAwLjMpO1xuICAgIH1cbiAgfVxuXG4gICY6YWN0aXZlIHtcbiAgICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoMCk7XG4gIH1cblxuICAuaWNvbiB7XG4gICAgd2lkdGg6IDE2cHg7XG4gICAgaGVpZ2h0OiAxNnB4O1xuICAgIHN0cm9rZTogY3VycmVudENvbG9yO1xuICAgIGZpbGw6IG5vbmU7XG4gICAgc3Ryb2tlLXdpZHRoOiAxLjU7XG4gIH1cbn1cblxuLy8gQWN0aW9uIGJ1dHRvbnMgLSBNaW5pbWFsaXN0IHVuZGVybGluZWQgc3R5bGVcbi5idG4ge1xuICBkaXNwbGF5OiBpbmxpbmUtZmxleDtcbiAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7XG4gIHBhZGRpbmc6IDAuNzVyZW0gMDtcbiAgYm9yZGVyOiBub25lO1xuICBiYWNrZ3JvdW5kOiB0cmFuc3BhcmVudDtcbiAgZm9udC1zaXplOiAwLjg3NXJlbTtcbiAgZm9udC13ZWlnaHQ6IDUwMDtcbiAgY3Vyc29yOiBwb2ludGVyO1xuICB0cmFuc2l0aW9uOiBhbGwgMC4zcyBlYXNlO1xuICB0ZXh0LWRlY29yYXRpb246IG5vbmU7XG4gIGdhcDogMC41cmVtO1xuICBwb3NpdGlvbjogcmVsYXRpdmU7XG4gIGNvbG9yOiAjMzc0MTUxO1xuXG4gICY6OmFmdGVyIHtcbiAgICBjb250ZW50OiAnJztcbiAgICBwb3NpdGlvbjogYWJzb2x1dGU7XG4gICAgYm90dG9tOiAwO1xuICAgIGxlZnQ6IDA7XG4gICAgd2lkdGg6IDA7XG4gICAgaGVpZ2h0OiAxcHg7XG4gICAgYmFja2dyb3VuZDogY3VycmVudENvbG9yO1xuICAgIHRyYW5zaXRpb246IHdpZHRoIDAuM3MgZWFzZTtcbiAgfVxuXG4gICY6aG92ZXI6OmFmdGVyIHtcbiAgICB3aWR0aDogMTAwJTtcbiAgfVxuXG4gICYuYnRuLXByaW1hcnkge1xuICAgIGNvbG9yOiAjMWU0MGFmO1xuXG4gICAgJjpkaXNhYmxlZCB7XG4gICAgICBjb2xvcjogIzljYTNhZjtcbiAgICAgIGN1cnNvcjogbm90LWFsbG93ZWQ7XG5cbiAgICAgICY6OmFmdGVyIHtcbiAgICAgICAgZGlzcGxheTogbm9uZTtcbiAgICAgIH1cbiAgICB9XG4gIH1cblxuICAmLmJ0bi1zZWNvbmRhcnkge1xuICAgIGNvbG9yOiAjNmI3MjgwO1xuICB9XG5cbiAgLmljb24ge1xuICAgIHdpZHRoOiAxNnB4O1xuICAgIGhlaWdodDogMTZweDtcbiAgICBzdHJva2U6IGN1cnJlbnRDb2xvcjtcbiAgICBmaWxsOiBub25lO1xuICAgIHN0cm9rZS13aWR0aDogMS41O1xuICB9XG59XG5cbi8vIE1lc3NhZ2UgdHlwZSBzZWxlY3RvciAtIE9ubHkgc2hvdyB3aGVuIHVzZXIgaGFzIGdyb3Vwc1xuLm1lc3NhZ2UtdHlwZS1zZWxlY3RvciB7XG4gIGRpc3BsYXk6IGZsZXg7XG4gIGdhcDogMXJlbTtcbiAgbWFyZ2luLWJvdHRvbTogMnJlbTtcbiAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7XG5cbiAgLy8gSGlkZSBlbnRpcmUgc2VsZWN0b3IgYnkgZGVmYXVsdFxuICBkaXNwbGF5OiBub25lO1xuXG4gIC8vIE9ubHkgc2hvdyB3aGVuIHVzZXIgaGFzIGdyb3Vwc1xuICAmLmhhcy1ncm91cHMge1xuICAgIGRpc3BsYXk6IGZsZXg7XG4gIH1cbn1cblxuLy8gUmVjaXBpZW50IHNlbGVjdG9yXG4ucmVjaXBpZW50LXNlbGVjdG9yIHtcbiAgcG9zaXRpb246IHJlbGF0aXZlO1xufVxuXG4uc2VsZWN0ZWQtcmVjaXBpZW50IHtcbiAgZGlzcGxheTogZmxleDtcbiAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuO1xuICBwYWRkaW5nOiA4cHggMTJweDtcbiAgYmFja2dyb3VuZDogcmdiYSg1NSwgNjYsIDI1MCwgMC4xKTtcbiAgYm9yZGVyOiAxcHggc29saWQgIzM3NDJmYTtcbiAgYm9yZGVyLXJhZGl1czogNnB4O1xuICBtYXJnaW4tdG9wOiA4cHg7XG5cbiAgLnJlY2lwaWVudC1uYW1lIHtcbiAgICBjb2xvcjogIzM3NDJmYTtcbiAgICBmb250LXdlaWdodDogNTAwO1xuICAgIGZvbnQtc2l6ZTogMTRweDtcbiAgfVxuXG4gIC5jbGVhci1yZWNpcGllbnQge1xuICAgIGJhY2tncm91bmQ6IG5vbmU7XG4gICAgYm9yZGVyOiBub25lO1xuICAgIGNvbG9yOiAjMzc0MmZhO1xuICAgIGZvbnQtc2l6ZTogMTZweDtcbiAgICBjdXJzb3I6IHBvaW50ZXI7XG4gICAgcGFkZGluZzogMDtcbiAgICB3aWR0aDogMjBweDtcbiAgICBoZWlnaHQ6IDIwcHg7XG4gICAgZGlzcGxheTogZmxleDtcbiAgICBhbGlnbi1pdGVtczogY2VudGVyO1xuICAgIGp1c3RpZnktY29udGVudDogY2VudGVyO1xuICAgIGJvcmRlci1yYWRpdXM6IDUwJTtcbiAgICB0cmFuc2l0aW9uOiBhbGwgMC4ycyBlYXNlO1xuXG4gICAgJjpob3ZlciB7XG4gICAgICBiYWNrZ3JvdW5kOiByZ2JhKDU1LCA2NiwgMjUwLCAwLjIpO1xuICAgIH1cbiAgfVxufVxuXG4ucmVjaXBpZW50LWRyb3Bkb3duIHtcbiAgcG9zaXRpb246IGFic29sdXRlO1xuICB0b3A6IDEwMCU7XG4gIGxlZnQ6IDA7XG4gIHJpZ2h0OiAwO1xuICBiYWNrZ3JvdW5kOiB3aGl0ZTtcbiAgYm9yZGVyOiAxcHggc29saWQgI2UxZThlZDtcbiAgYm9yZGVyLXJhZGl1czogOHB4O1xuICBib3gtc2hhZG93OiAwIDRweCAxMnB4IHJnYmEoMCwgMCwgMCwgMC4xNSk7XG4gIG1heC1oZWlnaHQ6IDIwMHB4O1xuICBvdmVyZmxvdy15OiBhdXRvO1xuICB6LWluZGV4OiAxMDAwO1xuICBtYXJnaW4tdG9wOiA0cHg7XG59XG5cbi5yZWNpcGllbnQtaXRlbSB7XG4gIGRpc3BsYXk6IGZsZXg7XG4gIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjtcbiAgcGFkZGluZzogMTJweCAxNnB4O1xuICBjdXJzb3I6IHBvaW50ZXI7XG4gIGJvcmRlci1ib3R0b206IDFweCBzb2xpZCByZ2JhKDAsIDAsIDAsIDAuMDUpO1xuICB0cmFuc2l0aW9uOiBiYWNrZ3JvdW5kIDAuMnMgZWFzZTtcblxuICAmOmhvdmVyIHtcbiAgICBiYWNrZ3JvdW5kOiByZ2JhKDU1LCA2NiwgMjUwLCAwLjA1KTtcbiAgfVxuXG4gICY6bGFzdC1jaGlsZCB7XG4gICAgYm9yZGVyLWJvdHRvbTogbm9uZTtcbiAgfVxufVxuXG4uY29udGFjdC1pbmZvLCAuZ3JvdXAtaW5mbyB7XG4gIGRpc3BsYXk6IGZsZXg7XG4gIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XG4gIGZsZXg6IDE7XG5cbiAgLmNvbnRhY3QtbmFtZSwgLmdyb3VwLW5hbWUge1xuICAgIGZvbnQtd2VpZ2h0OiA1MDA7XG4gICAgY29sb3I6ICMyZjM1NDI7XG4gICAgZm9udC1zaXplOiAxNHB4O1xuICAgIG1hcmdpbi1ib3R0b206IDJweDtcbiAgfVxuXG4gIC5jb250YWN0LWVtYWlsLCAuZ3JvdXAtbWVtYmVycyB7XG4gICAgZm9udC1zaXplOiAxMnB4O1xuICAgIGNvbG9yOiAjYTRiMGJlO1xuICB9XG59XG5cbi5jb250YWN0LXN0YXR1cywgLmdyb3VwLXN0YXR1cyB7XG4gIGRpc3BsYXk6IGZsZXg7XG4gIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gIGdhcDogNnB4O1xuXG4gIC5zdGF0dXMtaW5kaWNhdG9yIHtcbiAgICB3aWR0aDogOHB4O1xuICAgIGhlaWdodDogOHB4O1xuICAgIGJvcmRlci1yYWRpdXM6IDUwJTtcblxuICAgICYub25saW5lIHtcbiAgICAgIGJhY2tncm91bmQ6ICMyZWQ1NzM7XG4gICAgfVxuXG4gICAgJi5vZmZsaW5lIHtcbiAgICAgIGJhY2tncm91bmQ6ICNhNGIwYmU7XG4gICAgfVxuXG4gICAgJi5hY3RpdmUge1xuICAgICAgYmFja2dyb3VuZDogIzM3NDJmYTtcbiAgICB9XG4gIH1cblxuICAuc3RhdHVzLXRleHQge1xuICAgIGZvbnQtc2l6ZTogMTJweDtcbiAgICBjb2xvcjogI2E0YjBiZTtcbiAgfVxufVxuXG4ubm8tcmVzdWx0cyB7XG4gIHBhZGRpbmc6IDE2cHg7XG4gIHRleHQtYWxpZ246IGNlbnRlcjtcbiAgY29sb3I6ICNhNGIwYmU7XG4gIGZvbnQtc2l6ZTogMTRweDtcbiAgZm9udC1zdHlsZTogaXRhbGljO1xufVxuXG4ubWVzc2FnZS1hY3Rpb25zIHtcbiAgZGlzcGxheTogZmxleDtcbiAgZ2FwOiAycmVtO1xuICBtYXJnaW4tdG9wOiAycmVtO1xuICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjtcbn1cblxuLy8gRXJyb3IgYW5kIGluZm8gbWVzc2FnZXNcbi5lcnJvci1tZXNzYWdlIHtcbiAgY29sb3I6ICNmZjQ3NTc7XG4gIGZvbnQtc2l6ZTogMTRweDtcbiAgbWFyZ2luLXRvcDogOHB4O1xuICBwYWRkaW5nOiA4cHggMTJweDtcbiAgYmFja2dyb3VuZDogcmdiYSgyNTUsIDcxLCA4NywgMC4xKTtcbiAgYm9yZGVyLXJhZGl1czogNnB4O1xuICBib3JkZXItbGVmdDogM3B4IHNvbGlkICNmZjQ3NTc7XG59XG5cbi5hdXRoLWluZm8ge1xuICBtYXJnaW4tdG9wOiAyMHB4O1xuICB0ZXh0LWFsaWduOiBjZW50ZXI7XG5cbiAgcCB7XG4gICAgbWFyZ2luOiA4cHggMDtcbiAgICBmb250LXNpemU6IDEzcHg7XG4gICAgY29sb3I6ICM1NzYwNmY7XG5cbiAgICAmLmF1dG8tc3VibWl0LWhpbnQge1xuICAgICAgZm9udC1zdHlsZTogaXRhbGljO1xuICAgICAgY29sb3I6ICNhNGIwYmU7XG4gICAgfVxuICB9XG59XG5cbi5zZW5kLWhpbnQge1xuICBtYXJnaW4tdG9wOiAxNnB4O1xuICB0ZXh0LWFsaWduOiBjZW50ZXI7XG5cbiAgcCB7XG4gICAgZm9udC1zaXplOiAxMnB4O1xuICAgIGNvbG9yOiAjYTRiMGJlO1xuICAgIG1hcmdpbjogMDtcbiAgfVxufVxuXG4vLyBMb2FkaW5nIHN0YXRlc1xuLmxvYWRpbmctaW5kaWNhdG9yIHtcbiAgZGlzcGxheTogZmxleDtcbiAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7XG4gIGdhcDogMTJweDtcbiAgcGFkZGluZzogMjBweDtcblxuICAuc3Bpbm5lciB7XG4gICAgd2lkdGg6IDIwcHg7XG4gICAgaGVpZ2h0OiAyMHB4O1xuICAgIGJvcmRlcjogMnB4IHNvbGlkICNlMWU4ZWQ7XG4gICAgYm9yZGVyLXRvcDogMnB4IHNvbGlkICMzNzQyZmE7XG4gICAgYm9yZGVyLXJhZGl1czogNTAlO1xuICAgIGFuaW1hdGlvbjogc3BpbiAxcyBsaW5lYXIgaW5maW5pdGU7XG4gIH1cblxuICBzcGFuIHtcbiAgICBjb2xvcjogIzU3NjA2ZjtcbiAgICBmb250LXNpemU6IDE0cHg7XG4gIH1cbn1cblxuLy8gTWVzc2FnZXMgbGlzdFxuLm1lc3NhZ2VzLWxpc3Qge1xuICBtYXgtaGVpZ2h0OiA0MDBweDtcbiAgb3ZlcmZsb3cteTogYXV0bztcbiAgbWFyZ2luOiAtOHB4O1xuICBwYWRkaW5nOiA4cHg7XG59XG5cbi5tZXNzYWdlLWl0ZW0ge1xuICBwYWRkaW5nOiAxNnB4O1xuICBib3JkZXItYm90dG9tOiAxcHggc29saWQgcmdiYSgwLCAwLCAwLCAwLjA1KTtcblxuICAmOmxhc3QtY2hpbGQge1xuICAgIGJvcmRlci1ib3R0b206IG5vbmU7XG4gIH1cblxuICAubWVzc2FnZS1oZWFkZXIge1xuICAgIGRpc3BsYXk6IGZsZXg7XG4gICAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuO1xuICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gICAgbWFyZ2luLWJvdHRvbTogOHB4O1xuXG4gICAgLnNlbmRlciB7XG4gICAgICBmb250LXdlaWdodDogNjAwO1xuICAgICAgY29sb3I6ICMyZjM1NDI7XG4gICAgICBmb250LXNpemU6IDE0cHg7XG4gICAgfVxuXG4gICAgLnRpbWVzdGFtcCB7XG4gICAgICBmb250LXNpemU6IDEycHg7XG4gICAgICBjb2xvcjogI2E0YjBiZTtcbiAgICB9XG4gIH1cblxuICAubWVzc2FnZS1jb250ZW50IHtcbiAgICBjb2xvcjogIzU3NjA2ZjtcbiAgICBsaW5lLWhlaWdodDogMS41O1xuICAgIHdvcmQtd3JhcDogYnJlYWstd29yZDtcbiAgfVxufVxuXG4uZW1wdHktc3RhdGUge1xuICB0ZXh0LWFsaWduOiBjZW50ZXI7XG4gIHBhZGRpbmc6IDQwcHggMjBweDtcbiAgY29sb3I6ICNhNGIwYmU7XG5cbiAgcCB7XG4gICAgbWFyZ2luOiA4cHggMDtcblxuICAgICYuaGludCB7XG4gICAgICBmb250LXNpemU6IDE0cHg7XG4gICAgICBmb250LXN0eWxlOiBpdGFsaWM7XG4gICAgfVxuICB9XG59XG5cbi8vIENvbnRleHQgbWVudVxuLmNvbnRleHQtbWVudSB7XG4gIHBvc2l0aW9uOiBmaXhlZDtcbiAgYmFja2dyb3VuZDogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjk1KTtcbiAgYmFja2Ryb3AtZmlsdGVyOiBibHVyKDIwcHgpO1xuICBib3JkZXItcmFkaXVzOiAxMnB4O1xuICBib3gtc2hhZG93OiAwIDhweCAzMnB4IHJnYmEoMCwgMCwgMCwgMC4zKTtcbiAgcGFkZGluZzogOHB4IDA7XG4gIG1pbi13aWR0aDogMTgwcHg7XG4gIHotaW5kZXg6IDIwMDA7XG4gIGFuaW1hdGlvbjogY29udGV4dE1lbnVTbGlkZSAwLjJzIGN1YmljLWJlemllcigwLjQsIDAsIDAuMiwgMSk7XG5cbiAgLmNvbnRleHQtbWVudS1pdGVtIHtcbiAgICBkaXNwbGF5OiBmbGV4O1xuICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gICAgZ2FwOiAxMnB4O1xuICAgIHBhZGRpbmc6IDEycHggMTZweDtcbiAgICBjdXJzb3I6IHBvaW50ZXI7XG4gICAgdHJhbnNpdGlvbjogYmFja2dyb3VuZCAwLjJzIGVhc2U7XG4gICAgY29sb3I6ICMyZjM1NDI7XG5cbiAgICAmOmhvdmVyIHtcbiAgICAgIGJhY2tncm91bmQ6IHJnYmEoNTUsIDY2LCAyNTAsIDAuMSk7XG4gICAgfVxuXG4gICAgJi5sb2dvdXQtaXRlbSB7XG4gICAgICBjb2xvcjogI2ZmNDc1NztcblxuICAgICAgJjpob3ZlciB7XG4gICAgICAgIGJhY2tncm91bmQ6IHJnYmEoMjU1LCA3MSwgODcsIDAuMSk7XG4gICAgICB9XG4gICAgfVxuXG4gICAgLm1lbnUtaWNvbiB7XG4gICAgICBmb250LXNpemU6IDE2cHg7XG4gICAgICB3aWR0aDogMjBweDtcbiAgICAgIHRleHQtYWxpZ246IGNlbnRlcjtcbiAgICB9XG5cbiAgICAubWVudS10ZXh0IHtcbiAgICAgIGZvbnQtc2l6ZTogMTRweDtcbiAgICAgIGZvbnQtd2VpZ2h0OiA1MDA7XG4gICAgfVxuICB9XG5cbiAgLmNvbnRleHQtbWVudS1kaXZpZGVyIHtcbiAgICBoZWlnaHQ6IDFweDtcbiAgICBiYWNrZ3JvdW5kOiByZ2JhKDAsIDAsIDAsIDAuMSk7XG4gICAgbWFyZ2luOiA0cHggMDtcbiAgfVxufVxuXG4vLyBBY2NvdW50IHNldHRpbmdzIG1vZGFsXG4uYWNjb3VudC1zZXR0aW5ncy1tb2RhbCB7XG4gIG1heC13aWR0aDogNTAwcHg7XG4gIHdpZHRoOiA5MHZ3O1xufVxuXG4uYXZhdGFyLXNlY3Rpb24ge1xuICB0ZXh0LWFsaWduOiBjZW50ZXI7XG4gIG1hcmdpbi1ib3R0b206IDMycHg7XG5cbiAgLmF2YXRhci1jb250YWluZXIge1xuICAgIGRpc3BsYXk6IGlubGluZS1ibG9jaztcbiAgfVxuXG4gIC5hdmF0YXItZGlzcGxheSB7XG4gICAgd2lkdGg6IDEyMHB4O1xuICAgIGhlaWdodDogMTIwcHg7XG4gICAgYm9yZGVyLXJhZGl1czogNTAlO1xuICAgIG1hcmdpbjogMCBhdXRvIDE2cHg7XG4gICAgcG9zaXRpb246IHJlbGF0aXZlO1xuICAgIG92ZXJmbG93OiBoaWRkZW47XG4gICAgYm9yZGVyOiA0cHggc29saWQgI2UxZThlZDtcblxuICAgIC5hdmF0YXItaW1hZ2Uge1xuICAgICAgd2lkdGg6IDEwMCU7XG4gICAgICBoZWlnaHQ6IDEwMCU7XG4gICAgICBvYmplY3QtZml0OiBjb3ZlcjtcbiAgICB9XG5cbiAgICAuYXZhdGFyLXBsYWNlaG9sZGVyIHtcbiAgICAgIHdpZHRoOiAxMDAlO1xuICAgICAgaGVpZ2h0OiAxMDAlO1xuICAgICAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgIzM3NDJmYSAwJSwgIzJmMzU0MiAxMDAlKTtcbiAgICAgIGRpc3BsYXk6IGZsZXg7XG4gICAgICBhbGlnbi1pdGVtczogY2VudGVyO1xuICAgICAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7XG5cbiAgICAgIC5hdmF0YXItaW5pdGlhbHMge1xuICAgICAgICBmb250LXNpemU6IDQ4cHg7XG4gICAgICAgIGZvbnQtd2VpZ2h0OiA2MDA7XG4gICAgICAgIGNvbG9yOiB3aGl0ZTtcbiAgICAgIH1cbiAgICB9XG4gIH1cblxuICAuYXZhdGFyLWFjdGlvbnMge1xuICAgIC5hdmF0YXItdXBsb2FkLWJ0biB7XG4gICAgICBkaXNwbGF5OiBpbmxpbmUtYmxvY2s7XG4gICAgICBwYWRkaW5nOiA4cHggMTZweDtcbiAgICAgIGJhY2tncm91bmQ6ICMzNzQyZmE7XG4gICAgICBjb2xvcjogd2hpdGU7XG4gICAgICBib3JkZXItcmFkaXVzOiA2cHg7XG4gICAgICBmb250LXNpemU6IDE0cHg7XG4gICAgICBmb250LXdlaWdodDogNTAwO1xuICAgICAgY3Vyc29yOiBwb2ludGVyO1xuICAgICAgdHJhbnNpdGlvbjogYWxsIDAuMnMgZWFzZTtcblxuICAgICAgJjpob3ZlciB7XG4gICAgICAgIGJhY2tncm91bmQ6ICMyZjM1NDI7XG4gICAgICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgtMXB4KTtcbiAgICAgIH1cbiAgICB9XG4gIH1cbn1cblxuLnByb2ZpbGUtaW5mbyB7XG4gIG1hcmdpbi1ib3R0b206IDMycHg7XG5cbiAgLmZvcm0tZ3JvdXAge1xuICAgIG1hcmdpbi1ib3R0b206IDIwcHg7XG5cbiAgICBsYWJlbCB7XG4gICAgICBkaXNwbGF5OiBibG9jaztcbiAgICAgIG1hcmdpbi1ib3R0b206IDhweDtcbiAgICAgIGZvbnQtd2VpZ2h0OiA1MDA7XG4gICAgICBjb2xvcjogIzJmMzU0MjtcbiAgICAgIGZvbnQtc2l6ZTogMTRweDtcbiAgICB9XG5cbiAgICAucmVhZG9ubHktZmllbGQge1xuICAgICAgcGFkZGluZzogMTJweCAxNnB4O1xuICAgICAgYmFja2dyb3VuZDogI2Y4ZjlmYTtcbiAgICAgIGJvcmRlcjogMnB4IHNvbGlkICNlMWU4ZWQ7XG4gICAgICBib3JkZXItcmFkaXVzOiA4cHg7XG4gICAgICBjb2xvcjogIzU3NjA2ZjtcblxuICAgICAgc3BhbiB7XG4gICAgICAgIGRpc3BsYXk6IGJsb2NrO1xuXG4gICAgICAgICYuZmllbGQtbm90ZSB7XG4gICAgICAgICAgZm9udC1zaXplOiAxMnB4O1xuICAgICAgICAgIGNvbG9yOiAjYTRiMGJlO1xuICAgICAgICAgIG1hcmdpbi10b3A6IDRweDtcbiAgICAgICAgICBmb250LXN0eWxlOiBpdGFsaWM7XG4gICAgICAgIH1cbiAgICAgIH1cbiAgICB9XG4gIH1cbn1cblxuLnNlY3VyaXR5LWluZm8ge1xuICBiYWNrZ3JvdW5kOiByZ2JhKDU1LCA2NiwgMjUwLCAwLjA1KTtcbiAgYm9yZGVyOiAxcHggc29saWQgcmdiYSg1NSwgNjYsIDI1MCwgMC4yKTtcbiAgYm9yZGVyLXJhZGl1czogOHB4O1xuICBwYWRkaW5nOiAyMHB4O1xuICBtYXJnaW4tYm90dG9tOiAyNHB4O1xuXG4gIGgzIHtcbiAgICBtYXJnaW46IDAgMCAxNnB4IDA7XG4gICAgZm9udC1zaXplOiAxNnB4O1xuICAgIGNvbG9yOiAjMmYzNTQyO1xuICAgIGZvbnQtd2VpZ2h0OiA2MDA7XG4gIH1cblxuICAuc2VjdXJpdHktaXRlbSB7XG4gICAgZGlzcGxheTogZmxleDtcbiAgICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47XG4gICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgICBtYXJnaW4tYm90dG9tOiAxMnB4O1xuXG4gICAgJjpsYXN0LWNoaWxkIHtcbiAgICAgIG1hcmdpbi1ib3R0b206IDA7XG4gICAgfVxuXG4gICAgLnNlY3VyaXR5LWxhYmVsIHtcbiAgICAgIGZvbnQtc2l6ZTogMTRweDtcbiAgICAgIGNvbG9yOiAjNTc2MDZmO1xuICAgICAgZm9udC13ZWlnaHQ6IDUwMDtcbiAgICB9XG5cbiAgICAuc2VjdXJpdHktdmFsdWUge1xuICAgICAgZm9udC1zaXplOiAxNHB4O1xuICAgICAgY29sb3I6ICMzNzQyZmE7XG4gICAgICBmb250LXdlaWdodDogNTAwO1xuICAgICAgdGV4dC1hbGlnbjogcmlnaHQ7XG4gICAgICBmbGV4OiAxO1xuICAgICAgbWFyZ2luLWxlZnQ6IDE2cHg7XG4gICAgfVxuICB9XG59XG5cbi5hY2NvdW50LWFjdGlvbnMge1xuICB0ZXh0LWFsaWduOiBjZW50ZXI7XG4gIHBhZGRpbmctdG9wOiAxNnB4O1xuICBib3JkZXItdG9wOiAxcHggc29saWQgcmdiYSgwLCAwLCAwLCAwLjEpO1xufVxuXG4vLyBLZXlib2FyZCBoaW50c1xuLmtleWJvYXJkLWhpbnRzIHtcbiAgcG9zaXRpb246IGZpeGVkO1xuICBib3R0b206IDIwcHg7XG4gIGxlZnQ6IDUwJTtcbiAgdHJhbnNmb3JtOiB0cmFuc2xhdGVYKC01MCUpO1xuICBjb2xvcjogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjQpO1xuICBmb250LXNpemU6IDEycHg7XG4gIHotaW5kZXg6IDU7XG59XG5cbi8vIEFuaW1hdGlvbnNcbkBrZXlmcmFtZXMgZmFkZUluIHtcbiAgZnJvbSB7IG9wYWNpdHk6IDA7IH1cbiAgdG8geyBvcGFjaXR5OiAxOyB9XG59XG5cbkBrZXlmcmFtZXMgc2xpZGVVcCB7XG4gIGZyb20ge1xuICAgIG9wYWNpdHk6IDA7XG4gICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKDIwcHgpO1xuICB9XG4gIHRvIHtcbiAgICBvcGFjaXR5OiAxO1xuICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgwKTtcbiAgfVxufVxuXG5Aa2V5ZnJhbWVzIHB1bHNlIHtcbiAgMCUsIDEwMCUgeyB0cmFuc2Zvcm06IHNjYWxlKDEpOyB9XG4gIDUwJSB7IHRyYW5zZm9ybTogc2NhbGUoMS4wMik7IH1cbn1cblxuQGtleWZyYW1lcyBib3VuY2Uge1xuICAwJSwgMjAlLCA1MCUsIDgwJSwgMTAwJSB7IHRyYW5zZm9ybTogdHJhbnNsYXRlWSgwKTsgfVxuICA0MCUgeyB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoLTVweCk7IH1cbiAgNjAlIHsgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKC0zcHgpOyB9XG59XG5cbkBrZXlmcmFtZXMgd2luZEZsb3cge1xuICAwJSB7IHRyYW5zZm9ybTogdHJhbnNsYXRlWCgwKSB0cmFuc2xhdGVZKDApIHNjYWxlKDEpOyBvcGFjaXR5OiAwLjM7IH1cbiAgNTAlIHsgdHJhbnNmb3JtOiB0cmFuc2xhdGVYKDIwcHgpIHRyYW5zbGF0ZVkoLTEwcHgpIHNjYWxlKDAuOCk7IG9wYWNpdHk6IDAuNjsgfVxuICAxMDAlIHsgdHJhbnNmb3JtOiB0cmFuc2xhdGVYKDQwcHgpIHRyYW5zbGF0ZVkoLTIwcHgpIHNjYWxlKDAuNSk7IG9wYWNpdHk6IDA7IH1cbn1cblxuQGtleWZyYW1lcyBzcGluIHtcbiAgMCUgeyB0cmFuc2Zvcm06IHJvdGF0ZSgwZGVnKTsgfVxuICAxMDAlIHsgdHJhbnNmb3JtOiByb3RhdGUoMzYwZGVnKTsgfVxufVxuXG5Aa2V5ZnJhbWVzIGNvbnRleHRNZW51U2xpZGUge1xuICBmcm9tIHtcbiAgICBvcGFjaXR5OiAwO1xuICAgIHRyYW5zZm9ybTogc2NhbGUoMC45NSkgdHJhbnNsYXRlWSgtMTBweCk7XG4gIH1cbiAgdG8ge1xuICAgIG9wYWNpdHk6IDE7XG4gICAgdHJhbnNmb3JtOiBzY2FsZSgxKSB0cmFuc2xhdGVZKDApO1xuICB9XG59XG5cbi8vIFJlc3BvbnNpdmUgZGVzaWduXG5AbWVkaWEgKG1heC13aWR0aDogNzY4cHgpIHtcbiAgLnFzYy1jaXJjbGUge1xuICAgIHdpZHRoOiAxNTBweDtcbiAgICBoZWlnaHQ6IDE1MHB4O1xuICB9XG5cbiAgLm1vZGFsIHtcbiAgICB3aWR0aDogOTV2dztcbiAgICBtYXJnaW46IDIwcHg7XG4gIH1cblxuICAudXNlci1pbmZvIHtcbiAgICB0b3A6IDE1cHg7XG4gICAgcmlnaHQ6IDE1cHg7XG4gICAgZm9udC1zaXplOiAxMnB4O1xuICB9XG59XG5cbkBtZWRpYSAobWF4LXdpZHRoOiA0ODBweCkge1xuICAucXNjLWNpcmNsZSB7XG4gICAgd2lkdGg6IDEyMHB4O1xuICAgIGhlaWdodDogMTIwcHg7XG4gIH1cblxuICAubW9kYWwtY29udGVudCB7XG4gICAgcGFkZGluZzogMjBweDtcbiAgfVxuXG4gIC5tb2RhbC1oZWFkZXIge1xuICAgIHBhZGRpbmc6IDE2cHggMjBweDtcbiAgfVxuXG4gIC5jb250ZXh0LW1lbnUge1xuICAgIG1pbi13aWR0aDogMTYwcHg7XG5cbiAgICAuY29udGV4dC1tZW51LWl0ZW0ge1xuICAgICAgcGFkZGluZzogMTRweCAxNnB4OyAvLyBMYXJnZXIgdG91Y2ggdGFyZ2V0c1xuXG4gICAgICAubWVudS10ZXh0IHtcbiAgICAgICAgZm9udC1zaXplOiAxNnB4OyAvLyBMYXJnZXIgdGV4dCBmb3IgbW9iaWxlXG4gICAgICB9XG4gICAgfVxuICB9XG5cbiAgLmF2YXRhci1zZWN0aW9uIHtcbiAgICAuYXZhdGFyLWRpc3BsYXkge1xuICAgICAgd2lkdGg6IDEwMHB4O1xuICAgICAgaGVpZ2h0OiAxMDBweDtcblxuICAgICAgLmF2YXRhci1pbml0aWFscyB7XG4gICAgICAgIGZvbnQtc2l6ZTogNDBweDtcbiAgICAgIH1cbiAgICB9XG4gIH1cblxuICAuc2VjdXJpdHktaW5mbyB7XG4gICAgLnNlY3VyaXR5LWl0ZW0ge1xuICAgICAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjtcbiAgICAgIGFsaWduLWl0ZW1zOiBmbGV4LXN0YXJ0O1xuICAgICAgZ2FwOiA0cHg7XG5cbiAgICAgIC5zZWN1cml0eS12YWx1ZSB7XG4gICAgICAgIHRleHQtYWxpZ246IGxlZnQ7XG4gICAgICAgIG1hcmdpbi1sZWZ0OiAwO1xuICAgICAgfVxuICAgIH1cbiAgfVxufVxuIl0sInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "FormsModule", "Subject", "takeUntil", "take", "i0", "ɵɵelement", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r0", "unreadCount", "ɵɵlistener", "QscMainComponent_div_6_Template_button_click_3_listener", "ɵɵrestoreView", "_r2", "ɵɵnextContext", "ɵɵresetView", "logout", "ɵɵtextInterpolate", "currentUser", "username", "QscMainComponent_div_7_Template_div_click_0_listener", "$event", "_r3", "stopPropagation", "QscMainComponent_div_7_Template_div_click_1_listener", "openAccountSettings", "QscMainComponent_div_7_Template_div_click_7_listener", "ɵɵstyleProp", "contextMenuPosition", "x", "y", "loginError", "QscMainComponent_div_8_Template_div_click_0_listener", "_r4", "closeLoginModal", "QscMainComponent_div_8_Template_div_click_1_listener", "QscMainComponent_div_8_Template_button_click_5_listener", "ɵɵtwoWayListener", "QscMainComponent_div_8_Template_input_ngModelChange_11_listener", "ɵɵtwoWayBindingSet", "loginCredentials", "QscMainComponent_div_8_Template_input_input_11_listener", "onLoginInputChange", "QscMainComponent_div_8_Template_input_ngModelChange_15_listener", "secretWord", "QscMainComponent_div_8_Template_input_input_15_listener", "ɵɵtemplate", "QscMainComponent_div_8_div_16_Template", "QscMainComponent_div_8_div_17_Template", "QscMainComponent_div_8_div_18_Template", "ɵɵtwoWayProperty", "ɵɵproperty", "isLoading", "QscMainComponent_div_9_div_19_Template_button_click_3_listener", "_r6", "switchMessageType", "messageType", "getSelectedRecipientName", "QscMainComponent_div_9_div_20_div_1_Template_div_click_0_listener", "contact_r8", "_r7", "$implicit", "selectContact", "email", "ɵɵclassProp", "isOnline", "QscMainComponent_div_9_div_20_div_2_Template_div_click_0_listener", "group_r10", "_r9", "selectGroup", "name", "members", "length", "isActive", "QscMainComponent_div_9_div_20_div_1_Template", "QscMainComponent_div_9_div_20_div_2_Template", "QscMainComponent_div_9_div_20_div_3_Template", "QscMainComponent_div_9_div_20_div_4_Template", "filteredContacts", "filteredGroups", "QscMainComponent_div_9_Template_div_click_0_listener", "_r5", "closeMessageComposer", "QscMainComponent_div_9_Template_div_click_1_listener", "QscMainComponent_div_9_Template_button_click_4_listener", "QscMainComponent_div_9_Template_button_click_9_listener", "QscMainComponent_div_9_Template_input_ngModelChange_18_listener", "recipient<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "QscMainComponent_div_9_Template_input_input_18_listener", "onRecipientSearchChange", "QscMainComponent_div_9_div_19_Template", "QscMainComponent_div_9_div_20_Template", "QscMainComponent_div_9_Template_textarea_ngModelChange_24_listener", "messageContent", "QscMainComponent_div_9_Template_button_click_28_listener", "sendMessage", "QscMainComponent_div_9_Template_button_click_33_listener", "userGroups", "isMessageValid", "message_r12", "sender", "ɵɵpipeBind2", "timestamp", "content", "QscMainComponent_div_10_div_8_div_1_Template", "messages", "trackMessage", "QscMainComponent_div_10_Template_div_click_0_listener", "_r11", "closeMessagesViewer", "QscMainComponent_div_10_Template_div_click_1_listener", "QscMainComponent_div_10_Template_button_click_5_listener", "QscMainComponent_div_10_div_8_Template", "QscMainComponent_div_10_div_9_Template", "userProfile", "avatar", "ɵɵsanitizeUrl", "tmp_2_0", "char<PERSON>t", "toUpperCase", "QscMainComponent_div_11_Template_div_click_0_listener", "_r13", "closeAccountSettings", "QscMainComponent_div_11_Template_div_click_1_listener", "QscMainComponent_div_11_Template_button_click_5_listener", "QscMainComponent_div_11_img_11_Template", "QscMainComponent_div_11_div_12_Template", "QscMainComponent_div_11_Template_input_change_16_listener", "onAvatarChange", "QscMainComponent_div_11_Template_button_click_59_listener", "phoneNumber", "QscMainComponent", "groups", "constructor", "authService", "messageService", "notificationService", "destroy$", "circleState", "showLoginModal", "showMessageModal", "showMessagesModal", "showContextMenu", "showAccountSettings", "selected<PERSON><PERSON><PERSON><PERSON>", "selectedGroup", "contacts", "phone", "longPressTimer", "longPressDuration", "ngOnInit", "initializeApp", "setupMessageListener", "setupAuthListener", "ngOnDestroy", "next", "complete", "authState$", "pipe", "subscribe", "authState", "isAuthenticated", "user", "loadMessages", "loadContacts", "loadGroups", "openLoginModal", "messages$", "updateUnreadCount", "newMessage$", "message", "unshift", "showNotification", "filter", "m", "read", "error", "console", "id", "lastSeen", "Date", "now", "onCircleClick", "closeContextMenu", "openMessageComposer", "openMessagesViewer", "onCircleRightClick", "event", "preventDefault", "clientX", "clientY", "onCircleTouchStart", "setTimeout", "touch", "touches", "navigator", "vibrate", "onCircleTouchEnd", "clearTimeout", "onCircleTouchMove", "isValidCredentials", "performLogin", "usernameValid", "secretWordValid", "test", "login", "response", "trim", "recipient", "undefined", "groupId", "query", "toLowerCase", "contact", "includes", "group", "type", "find", "c", "g", "<PERSON><PERSON><PERSON><PERSON>", "hasRecipient", "markMessagesAsRead", "markAllAsRead", "loadUserProfile", "input", "target", "files", "file", "startsWith", "size", "reader", "FileReader", "onload", "e", "result", "saveAvatarChange", "readAsDataURL", "onKeyDown", "key", "closeAllModals", "shift<PERSON>ey", "onDocumentClick", "getCircleClass", "getCircleTitle", "index", "ɵɵdirectiveInject", "i1", "AuthService", "i2", "MessageService", "i3", "NotificationService", "selectors", "hostBindings", "QscMainComponent_HostBindings", "rf", "ctx", "QscMainComponent_keydown_HostBindingHandler", "ɵɵresolveDocument", "QscMainComponent_click_HostBindingHandler", "QscMainComponent_Template_div_click_2_listener", "QscMainComponent_Template_div_contextmenu_2_listener", "QscMainComponent_Template_div_touchstart_2_listener", "QscMainComponent_Template_div_touchend_2_listener", "QscMainComponent_Template_div_touchmove_2_listener", "QscMainComponent_div_4_Template", "QscMainComponent_div_5_Template", "QscMainComponent_div_6_Template", "QscMainComponent_div_7_Template", "QscMainComponent_div_8_Template", "QscMainComponent_div_9_Template", "QscMainComponent_div_10_Template", "QscMainComponent_div_11_Template", "QscMainComponent_div_12_Template", "ɵɵclassMap", "i4", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "DatePipe", "i5", "DefaultValueAccessor", "NgControlStatus", "MaxLengthValidator", "NgModel", "styles"], "sources": ["C:\\Users\\<USER>\\Projects\\QSC1\\frontend\\src\\app\\components\\qsc-main\\qsc-main.component.ts", "C:\\Users\\<USER>\\Projects\\QSC1\\frontend\\src\\app\\components\\qsc-main\\qsc-main.component.html"], "sourcesContent": ["import { Component, OnInit, On<PERSON><PERSON>roy, HostListener } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { Subject, takeUntil, take } from 'rxjs';\nimport { AuthService } from '../../services/auth.service';\nimport { MessageService } from '../../services/message.service';\nimport { NotificationService } from '../../services/notification.service';\nimport { AuthState, User } from '../../types/auth.types';\n\nexport type CircleState = 'guest' | 'authenticated' | 'unread' | 'composing';\n\ninterface LoginCredentials {\n  username: string;\n  secretWord: string;\n}\n\ninterface Message {\n  id: string;\n  content: string;\n  timestamp: Date;\n  sender: string;\n  recipient?: string;\n  groupId?: string;\n  read?: boolean;\n}\n\ninterface Contact {\n  id: string;\n  username: string;\n  email: string;\n  publicKey?: string;\n  isOnline?: boolean;\n  lastSeen?: Date;\n}\n\ninterface Group {\n  id: string;\n  name: string;\n  members: string[];\n  isActive: boolean;\n}\n\n@Component({\n  selector: 'app-qsc-main',\n  standalone: true,\n  imports: [CommonModule, FormsModule],\n  templateUrl: './qsc-main.component.html',\n  styleUrls: ['./qsc-main.component.scss']\n})\nexport class QscMainComponent implements OnInit, OnDestroy {\n  private destroy$ = new Subject<void>();\n\n  // Circle state management\n  circleState: CircleState = 'guest';\n\n  // Modal states\n  showLoginModal = false;\n  showMessageModal = false;\n  showMessagesModal = false;\n  showContextMenu = false;\n  showAccountSettings = false;\n\n  // Context menu position\n  contextMenuPosition = { x: 0, y: 0 };\n\n  // Authentication\n  loginCredentials: LoginCredentials = { username: '', secretWord: '' };\n  loginError = '';\n  isLoading = false;\n\n  // Messaging\n  messageContent = '';\n  selectedRecipient = '';\n  selectedGroup = '';\n  messageType: 'direct' | 'group' = 'direct';\n  messages: Message[] = [];\n  unreadCount = 0;\n\n  // Contacts and Groups\n  contacts: Contact[] = [];\n  groups: Group[] = [];\n  filteredContacts: Contact[] = [];\n  filteredGroups: Group[] = [];\n  recipientSearchQuery = '';\n\n  // Getter for template access\n  get userGroups(): Group[] {\n    return this.groups;\n  }\n\n  // User info\n  currentUser: User | null = null;\n\n  // Account settings\n  userProfile = {\n    avatar: '',\n    email: '',\n    phone: ''\n  };\n\n  // Long press handling\n  private longPressTimer: any = null;\n  private longPressDuration = 500; // 500ms for long press\n\n  constructor(\n    private authService: AuthService,\n    private messageService: MessageService,\n    private notificationService: NotificationService\n  ) {}\n\n  ngOnInit() {\n    this.initializeApp();\n    this.setupMessageListener();\n    this.setupAuthListener();\n  }\n\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n\n  private initializeApp() {\n    // The auth guard ensures we only reach here if authenticated\n    // But we still need to set up the initial state\n    this.authService.authState$.pipe(take(1)).subscribe((authState: AuthState) => {\n      if (authState.isAuthenticated && authState.user) {\n        this.currentUser = authState.user;\n        this.circleState = 'authenticated';\n        this.loadMessages();\n        this.loadContacts();\n        this.loadGroups();\n      } else {\n        // This should not happen due to auth guard, but handle gracefully\n        this.circleState = 'guest';\n        this.openLoginModal();\n      }\n    });\n  }\n\n  private setupAuthListener() {\n    this.authService.authState$\n      .pipe(takeUntil(this.destroy$))\n      .subscribe((authState: AuthState) => {\n        if (authState.isAuthenticated && authState.user) {\n          this.currentUser = authState.user;\n          this.circleState = 'authenticated';\n          this.showLoginModal = false;\n          this.loadMessages();\n        } else {\n          this.currentUser = null;\n          this.circleState = 'guest';\n          this.messages = [];\n          this.unreadCount = 0;\n        }\n      });\n  }\n\n  private setupMessageListener() {\n    this.messageService.messages$\n      .pipe(takeUntil(this.destroy$))\n      .subscribe(messages => {\n        this.messages = messages;\n        this.updateUnreadCount();\n      });\n\n    this.messageService.newMessage$\n      .pipe(takeUntil(this.destroy$))\n      .subscribe(message => {\n        this.messages.unshift(message);\n        this.updateUnreadCount();\n        this.notificationService.showNotification('New message received');\n      });\n  }\n\n  private updateUnreadCount() {\n    this.unreadCount = this.messages.filter(m => !m.read).length;\n    if (this.unreadCount > 0 && this.circleState === 'authenticated') {\n      this.circleState = 'unread';\n    } else if (this.unreadCount === 0 && this.circleState === 'unread') {\n      this.circleState = 'authenticated';\n    }\n  }\n\n  private loadMessages() {\n    this.messageService.loadMessages()\n      .pipe(takeUntil(this.destroy$))\n      .subscribe({\n        next: (messages) => {\n          this.messages = messages;\n          this.updateUnreadCount();\n        },\n        error: (error) => {\n          console.error('Failed to load messages:', error);\n        }\n      });\n  }\n\n  private loadContacts() {\n    // TODO: Replace with actual API call\n    this.contacts = [\n      {\n        id: '1',\n        username: 'alice',\n        email: '<EMAIL>',\n        isOnline: true\n      },\n      {\n        id: '2',\n        username: 'bob',\n        email: '<EMAIL>',\n        isOnline: false,\n        lastSeen: new Date(Date.now() - 300000) // 5 minutes ago\n      }\n    ];\n    this.filteredContacts = [...this.contacts];\n  }\n\n  private loadGroups() {\n    // TODO: Replace with actual API call\n    // Temporarily enable groups to test the interface\n    this.groups = [\n      {\n        id: 'group1',\n        name: 'Work Team',\n        members: ['1', '2', 'current-user'],\n        isActive: true\n      },\n      {\n        id: 'group2',\n        name: 'Family',\n        members: ['3', '4', 'current-user'],\n        isActive: true\n      }\n    ];\n\n    // Set to empty array to hide group selector:\n    // this.groups = [];\n\n    this.filteredGroups = [...this.groups];\n  }\n\n  // Circle click handler - main interaction point\n  onCircleClick() {\n    // Don't handle click if context menu is showing\n    if (this.showContextMenu) {\n      this.closeContextMenu();\n      return;\n    }\n\n    switch (this.circleState) {\n      case 'guest':\n        this.openLoginModal();\n        break;\n      case 'authenticated':\n        this.openMessageComposer();\n        break;\n      case 'unread':\n        this.openMessagesViewer();\n        break;\n      case 'composing':\n        // Already composing, do nothing or close\n        break;\n    }\n  }\n\n  // Right click handler\n  onCircleRightClick(event: MouseEvent) {\n    event.preventDefault();\n\n    // Only show context menu for authenticated users\n    if (this.circleState === 'guest') return;\n\n    this.showContextMenu = true;\n    this.contextMenuPosition = {\n      x: event.clientX,\n      y: event.clientY\n    };\n  }\n\n  // Touch event handlers for long press\n  onCircleTouchStart(event: TouchEvent) {\n    // Only for authenticated users\n    if (this.circleState === 'guest') return;\n\n    this.longPressTimer = setTimeout(() => {\n      const touch = event.touches[0];\n      this.showContextMenu = true;\n      this.contextMenuPosition = {\n        x: touch.clientX,\n        y: touch.clientY\n      };\n\n      // Provide haptic feedback if available\n      if (navigator.vibrate) {\n        navigator.vibrate(50);\n      }\n    }, this.longPressDuration);\n  }\n\n  onCircleTouchEnd() {\n    if (this.longPressTimer) {\n      clearTimeout(this.longPressTimer);\n      this.longPressTimer = null;\n    }\n  }\n\n  onCircleTouchMove() {\n    // Cancel long press if user moves finger\n    if (this.longPressTimer) {\n      clearTimeout(this.longPressTimer);\n      this.longPressTimer = null;\n    }\n  }\n\n  // Authentication methods\n  openLoginModal() {\n    this.showLoginModal = true;\n    this.loginCredentials = { username: '', secretWord: '' };\n    this.loginError = '';\n  }\n\n  closeLoginModal() {\n    this.showLoginModal = false;\n    this.loginCredentials = { username: '', secretWord: '' };\n    this.loginError = '';\n  }\n\n  onLoginInputChange() {\n    // Auto-submit when both fields are valid\n    if (this.isValidCredentials()) {\n      this.performLogin();\n    }\n  }\n\n  private isValidCredentials(): boolean {\n    const usernameValid = this.loginCredentials.username.length >= 3;\n    const secretWordValid = this.loginCredentials.secretWord.length >= 4 &&\n                           /[A-Z]/.test(this.loginCredentials.secretWord) &&\n                           /[a-z]/.test(this.loginCredentials.secretWord) &&\n                           /[0-9]/.test(this.loginCredentials.secretWord) &&\n                           /[^A-Za-z0-9]/.test(this.loginCredentials.secretWord);\n\n    return usernameValid && secretWordValid;\n  }\n\n  private performLogin() {\n    if (this.isLoading) return;\n\n    this.isLoading = true;\n    this.loginError = '';\n\n    this.authService.login(this.loginCredentials.username, this.loginCredentials.secretWord)\n      .pipe(takeUntil(this.destroy$))\n      .subscribe({\n        next: (response) => {\n          this.isLoading = false;\n          // Auth state will be updated via authState$ subscription\n        },\n        error: (error) => {\n          this.isLoading = false;\n          this.loginError = error.message || 'Authentication failed';\n        }\n      });\n  }\n\n  // Message composition methods\n  openMessageComposer() {\n    this.showMessageModal = true;\n    this.messageContent = '';\n    this.selectedRecipient = '';\n    this.selectedGroup = '';\n    this.messageType = 'direct';\n    this.recipientSearchQuery = '';\n    this.filteredContacts = [...this.contacts];\n    this.filteredGroups = [...this.groups];\n    this.circleState = 'composing';\n  }\n\n  closeMessageComposer() {\n    this.showMessageModal = false;\n    this.messageContent = '';\n    this.selectedRecipient = '';\n    this.selectedGroup = '';\n    this.recipientSearchQuery = '';\n    this.circleState = 'authenticated';\n  }\n\n  sendMessage() {\n    if (!this.messageContent.trim()) return;\n\n    // Validate recipient selection\n    if (this.messageType === 'direct' && !this.selectedRecipient) {\n      this.notificationService.showNotification('Please select a recipient', 'warning');\n      return;\n    }\n\n    if (this.messageType === 'group' && !this.selectedGroup) {\n      this.notificationService.showNotification('Please select a group', 'warning');\n      return;\n    }\n\n    const message: Partial<Message> = {\n      content: this.messageContent.trim(),\n      timestamp: new Date(),\n      sender: this.currentUser?.username || 'Unknown',\n      recipient: this.messageType === 'direct' ? this.selectedRecipient : undefined,\n      groupId: this.messageType === 'group' ? this.selectedGroup : undefined\n    };\n\n    this.messageService.sendMessage(message)\n      .pipe(takeUntil(this.destroy$))\n      .subscribe({\n        next: () => {\n          this.closeMessageComposer();\n          this.notificationService.showNotification('Message sent');\n        },\n        error: (error) => {\n          console.error('Failed to send message:', error);\n          this.notificationService.showNotification('Failed to send message', 'error');\n        }\n      });\n  }\n\n  // Recipient selection methods\n  onRecipientSearchChange() {\n    const query = this.recipientSearchQuery.toLowerCase();\n\n    if (this.messageType === 'direct') {\n      this.filteredContacts = this.contacts.filter(contact =>\n        contact.username.toLowerCase().includes(query) ||\n        contact.email.toLowerCase().includes(query)\n      );\n    } else {\n      this.filteredGroups = this.groups.filter(group =>\n        group.name.toLowerCase().includes(query)\n      );\n    }\n  }\n\n  selectContact(contact: Contact) {\n    this.selectedRecipient = contact.id;\n    this.recipientSearchQuery = contact.username;\n    this.filteredContacts = [];\n  }\n\n  selectGroup(group: Group) {\n    this.selectedGroup = group.id;\n    this.recipientSearchQuery = group.name;\n    this.filteredGroups = [];\n  }\n\n  switchMessageType(type: 'direct' | 'group') {\n    this.messageType = type;\n    this.selectedRecipient = '';\n    this.selectedGroup = '';\n    this.recipientSearchQuery = '';\n    this.onRecipientSearchChange();\n  }\n\n  getSelectedRecipientName(): string {\n    if (this.messageType === 'direct' && this.selectedRecipient) {\n      const contact = this.contacts.find(c => c.id === this.selectedRecipient);\n      return contact?.username || 'Unknown';\n    }\n\n    if (this.messageType === 'group' && this.selectedGroup) {\n      const group = this.groups.find(g => g.id === this.selectedGroup);\n      return group?.name || 'Unknown Group';\n    }\n\n    return '';\n  }\n\n  isMessageValid(): boolean {\n    const hasContent = this.messageContent.trim().length > 0;\n    const hasRecipient = this.messageType === 'direct' ? !!this.selectedRecipient : !!this.selectedGroup;\n    return hasContent && hasRecipient;\n  }\n\n  // Message viewing methods\n  openMessagesViewer() {\n    this.showMessagesModal = true;\n    this.markMessagesAsRead();\n  }\n\n  closeMessagesViewer() {\n    this.showMessagesModal = false;\n  }\n\n  private markMessagesAsRead() {\n    this.messageService.markAllAsRead()\n      .pipe(takeUntil(this.destroy$))\n      .subscribe(() => {\n        this.updateUnreadCount();\n      });\n  }\n\n  // Context menu methods\n  closeContextMenu() {\n    this.showContextMenu = false;\n  }\n\n  openAccountSettings() {\n    this.showAccountSettings = true;\n    this.showContextMenu = false;\n    this.loadUserProfile();\n  }\n\n  closeAccountSettings() {\n    this.showAccountSettings = false;\n  }\n\n  // Account settings methods\n  loadUserProfile() {\n    if (this.currentUser) {\n      this.userProfile = {\n        avatar: '', // Avatar not part of User interface, will be handled separately\n        email: this.currentUser.email || '',\n        phone: this.currentUser.phone || ''\n      };\n    }\n  }\n\n  onAvatarChange(event: Event) {\n    const input = event.target as HTMLInputElement;\n    if (input.files && input.files[0]) {\n      const file = input.files[0];\n\n      // Validate file type\n      if (!file.type.startsWith('image/')) {\n        this.notificationService.showNotification('Please select an image file', 'error');\n        return;\n      }\n\n      // Validate file size (max 2MB)\n      if (file.size > 2 * 1024 * 1024) {\n        this.notificationService.showNotification('Image must be smaller than 2MB', 'error');\n        return;\n      }\n\n      const reader = new FileReader();\n      reader.onload = (e) => {\n        this.userProfile.avatar = e.target?.result as string;\n        this.saveAvatarChange();\n      };\n      reader.readAsDataURL(file);\n    }\n  }\n\n  saveAvatarChange() {\n    // TODO: Implement API call to save avatar\n    this.notificationService.showNotification('Avatar updated successfully', 'success');\n  }\n\n  // Logout\n  logout() {\n    this.closeContextMenu();\n    this.authService.logout();\n  }\n\n  // Keyboard shortcuts\n  @HostListener('document:keydown', ['$event'])\n  onKeyDown(event: KeyboardEvent) {\n    // Escape key closes modals\n    if (event.key === 'Escape') {\n      this.closeAllModals();\n    }\n\n    // Enter key in login modal\n    if (event.key === 'Enter' && this.showLoginModal) {\n      if (this.isValidCredentials()) {\n        this.performLogin();\n      }\n    }\n\n    // Enter key in message modal\n    if (event.key === 'Enter' && this.showMessageModal && !event.shiftKey) {\n      event.preventDefault();\n      this.sendMessage();\n    }\n  }\n\n  // Click outside handler to close context menu\n  @HostListener('document:click', ['$event'])\n  onDocumentClick(event: Event) {\n    // Close context menu when clicking outside\n    if (this.showContextMenu) {\n      this.closeContextMenu();\n    }\n  }\n\n  private closeAllModals() {\n    this.showLoginModal = false;\n    this.showMessageModal = false;\n    this.showMessagesModal = false;\n    this.showContextMenu = false;\n    this.showAccountSettings = false;\n    if (this.circleState === 'composing') {\n      this.circleState = 'authenticated';\n    }\n  }\n\n  // Utility methods\n  getCircleClass(): string {\n    return `circle-${this.circleState}`;\n  }\n\n  getCircleTitle(): string {\n    switch (this.circleState) {\n      case 'guest': return 'Click to sign in';\n      case 'authenticated': return 'Click to compose message';\n      case 'unread': return `Click to view ${this.unreadCount} unread message(s)`;\n      case 'composing': return 'Composing message...';\n      default: return '';\n    }\n  }\n\n  trackMessage(index: number, message: Message): string {\n    return message.id;\n  }\n}\n", "<!-- Main Container -->\n<div class=\"qsc-container\">\n  <!-- Central Circle -->\n  <div class=\"circle-container\">\n    <div\n      class=\"qsc-circle\"\n      [class]=\"getCircleClass()\"\n      [title]=\"getCircleTitle()\"\n      (click)=\"onCircleClick()\"\n      (contextmenu)=\"onCircleRightClick($event)\"\n      (touchstart)=\"onCircleTouchStart($event)\"\n      (touchend)=\"onCircleTouchEnd()\"\n      (touchmove)=\"onCircleTouchMove()\"\n    >\n      <div class=\"circle-inner\">\n        <div class=\"wind-effect\" *ngIf=\"circleState !== 'guest'\"></div>\n        <div class=\"unread-indicator\" *ngIf=\"circleState === 'unread'\">\n          {{ unreadCount }}\n        </div>\n      </div>\n    </div>\n  </div>\n\n  <!-- User Info (subtle, top-right) -->\n  <div class=\"user-info\" *ngIf=\"currentUser\">\n    <span>{{ currentUser.username }}</span>\n    <button class=\"logout-btn\" (click)=\"logout()\" title=\"Logout\">×</button>\n  </div>\n\n  <!-- Context Menu -->\n  <div\n    class=\"context-menu\"\n    *ngIf=\"showContextMenu\"\n    [style.left.px]=\"contextMenuPosition.x\"\n    [style.top.px]=\"contextMenuPosition.y\"\n    (click)=\"$event.stopPropagation()\"\n  >\n    <div class=\"context-menu-item\" (click)=\"openAccountSettings()\">\n      <span class=\"menu-icon\">👤</span>\n      <span class=\"menu-text\">Account Settings</span>\n    </div>\n    <div class=\"context-menu-divider\"></div>\n    <div class=\"context-menu-item logout-item\" (click)=\"logout()\">\n      <span class=\"menu-icon\">🚪</span>\n      <span class=\"menu-text\">Logout</span>\n    </div>\n  </div>\n</div>\n\n<!-- Login Modal -->\n<div class=\"modal-overlay\" *ngIf=\"showLoginModal\" (click)=\"closeLoginModal()\">\n  <div class=\"modal login-modal\" (click)=\"$event.stopPropagation()\">\n    <div class=\"modal-header\">\n      <h2>Sign In</h2>\n      <button class=\"close-btn\" (click)=\"closeLoginModal()\">×</button>\n    </div>\n\n    <div class=\"modal-content\">\n      <div class=\"form-group\">\n        <label for=\"username\">Username</label>\n        <input\n          type=\"text\"\n          id=\"username\"\n          [(ngModel)]=\"loginCredentials.username\"\n          (input)=\"onLoginInputChange()\"\n          placeholder=\"Enter your username\"\n          [disabled]=\"isLoading\"\n          autocomplete=\"username\"\n        />\n      </div>\n\n      <div class=\"form-group\">\n        <label for=\"secretWord\">Secret Word</label>\n        <input\n          type=\"password\"\n          id=\"secretWord\"\n          [(ngModel)]=\"loginCredentials.secretWord\"\n          (input)=\"onLoginInputChange()\"\n          placeholder=\"4+ chars: A-Z, a-z, 0-9, symbol\"\n          [disabled]=\"isLoading\"\n          autocomplete=\"current-password\"\n        />\n      </div>\n\n      <div class=\"error-message\" *ngIf=\"loginError\">\n        {{ loginError }}\n      </div>\n\n      <div class=\"loading-indicator\" *ngIf=\"isLoading\">\n        <div class=\"spinner\"></div>\n        <span>Authenticating...</span>\n      </div>\n\n      <div class=\"auth-info\" *ngIf=\"!isLoading\">\n        <p>🔒 Protected by post-quantum cryptography</p>\n        <p class=\"auto-submit-hint\">Form auto-submits when credentials are valid</p>\n      </div>\n    </div>\n  </div>\n</div>\n\n<!-- Message Composer Modal -->\n<div class=\"modal-overlay\" *ngIf=\"showMessageModal\" (click)=\"closeMessageComposer()\">\n  <div class=\"modal message-modal\" (click)=\"$event.stopPropagation()\">\n    <div class=\"modal-content\">\n      <!-- Message Type Selector - Only show when user has groups -->\n      <div class=\"message-type-selector\" [class.has-groups]=\"userGroups.length > 0\">\n        <button\n          class=\"type-btn direct-btn\"\n          (click)=\"switchMessageType('direct')\"\n          [class.active]=\"messageType === 'direct'\"\n        >\n          <svg class=\"icon\" viewBox=\"0 0 24 24\">\n            <path d=\"M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2\"/>\n            <circle cx=\"12\" cy=\"7\" r=\"4\"/>\n          </svg>\n          Direct\n        </button>\n        <button\n          class=\"type-btn group-btn\"\n          (click)=\"switchMessageType('group')\"\n          [class.active]=\"messageType === 'group'\"\n        >\n          <svg class=\"icon\" viewBox=\"0 0 24 24\">\n            <path d=\"M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2\"/>\n            <circle cx=\"9\" cy=\"7\" r=\"4\"/>\n            <path d=\"M23 21v-2a4 4 0 0 0-3-3.87\"/>\n            <path d=\"M16 3.13a4 4 0 0 1 0 7.75\"/>\n          </svg>\n          Group\n        </button>\n      </div>\n\n      <!-- Recipient Selection -->\n      <div class=\"form-group\">\n        <div class=\"recipient-selector\">\n          <input\n            type=\"text\"\n            id=\"recipientSearch\"\n            [(ngModel)]=\"recipientSearchQuery\"\n            (input)=\"onRecipientSearchChange()\"\n            [placeholder]=\"messageType === 'direct' ? 'Search contacts...' : 'Search groups...'\"\n            autocomplete=\"off\"\n          />\n\n          <!-- Selected Recipient Display -->\n          <div class=\"selected-recipient\" *ngIf=\"getSelectedRecipientName()\">\n            <span class=\"recipient-name\">{{ getSelectedRecipientName() }}</span>\n            <button\n              class=\"clear-recipient\"\n              (click)=\"switchMessageType(messageType)\"\n              title=\"Clear selection\"\n            >×</button>\n          </div>\n\n          <!-- Contact/Group Dropdown -->\n          <div class=\"recipient-dropdown\" *ngIf=\"recipientSearchQuery && !getSelectedRecipientName()\">\n            <!-- Direct Message Contacts -->\n            <div\n              class=\"recipient-item\"\n              *ngFor=\"let contact of filteredContacts\"\n              (click)=\"selectContact(contact)\"\n              [hidden]=\"messageType !== 'direct'\"\n            >\n              <div class=\"contact-info\">\n                <span class=\"contact-name\">{{ contact.username }}</span>\n                <span class=\"contact-email\">{{ contact.email }}</span>\n              </div>\n              <div class=\"contact-status\">\n                <span\n                  class=\"status-indicator\"\n                  [class.online]=\"contact.isOnline\"\n                  [class.offline]=\"!contact.isOnline\"\n                ></span>\n                <span class=\"status-text\">\n                  {{ contact.isOnline ? 'Online' : 'Offline' }}\n                </span>\n              </div>\n            </div>\n\n            <!-- Group Chats -->\n            <div\n              class=\"recipient-item\"\n              *ngFor=\"let group of filteredGroups\"\n              (click)=\"selectGroup(group)\"\n              [hidden]=\"messageType !== 'group'\"\n            >\n              <div class=\"group-info\">\n                <span class=\"group-name\">{{ group.name }}</span>\n                <span class=\"group-members\">{{ group.members.length }} members</span>\n              </div>\n              <div class=\"group-status\">\n                <span\n                  class=\"status-indicator\"\n                  [class.active]=\"group.isActive\"\n                ></span>\n              </div>\n            </div>\n\n            <!-- No Results -->\n            <div class=\"no-results\" *ngIf=\"messageType === 'direct' && filteredContacts.length === 0\">\n              No contacts found\n            </div>\n            <div class=\"no-results\" *ngIf=\"messageType === 'group' && filteredGroups.length === 0\">\n              No groups found\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- Message Content -->\n      <div class=\"form-group\">\n        <label for=\"messageContent\">Message</label>\n        <textarea\n          id=\"messageContent\"\n          [(ngModel)]=\"messageContent\"\n          placeholder=\"Type your message here...\"\n          rows=\"6\"\n          maxlength=\"1000\"\n        ></textarea>\n        <div class=\"char-count\">{{ messageContent.length }}/1000</div>\n      </div>\n\n      <div class=\"message-actions\">\n        <button\n          class=\"btn btn-primary\"\n          (click)=\"sendMessage()\"\n          [disabled]=\"!isMessageValid()\"\n        >\n          <svg class=\"icon\" viewBox=\"0 0 24 24\">\n            <line x1=\"22\" y1=\"2\" x2=\"11\" y2=\"13\"/>\n            <polygon points=\"22,2 15,22 11,13 2,9\"/>\n          </svg>\n          Send\n        </button>\n        <button class=\"btn btn-secondary\" (click)=\"closeMessageComposer()\">\n          <svg class=\"icon\" viewBox=\"0 0 24 24\">\n            <line x1=\"18\" y1=\"6\" x2=\"6\" y2=\"18\"/>\n            <line x1=\"6\" y1=\"6\" x2=\"18\" y2=\"18\"/>\n          </svg>\n          Cancel\n        </button>\n      </div>\n\n      <div class=\"send-hint\">\n        <p>Press Enter to send • Shift+Enter for new line</p>\n      </div>\n    </div>\n  </div>\n</div>\n\n<!-- Messages Viewer Modal -->\n<div class=\"modal-overlay\" *ngIf=\"showMessagesModal\" (click)=\"closeMessagesViewer()\">\n  <div class=\"modal messages-modal\" (click)=\"$event.stopPropagation()\">\n    <div class=\"modal-header\">\n      <h2>Messages</h2>\n      <button class=\"close-btn\" (click)=\"closeMessagesViewer()\">×</button>\n    </div>\n\n    <div class=\"modal-content\">\n      <div class=\"messages-list\" *ngIf=\"messages.length > 0\">\n        <div\n          class=\"message-item\"\n          *ngFor=\"let message of messages; trackBy: trackMessage\"\n        >\n          <div class=\"message-header\">\n            <span class=\"sender\">{{ message.sender }}</span>\n            <span class=\"timestamp\">{{ message.timestamp | date:'short' }}</span>\n          </div>\n          <div class=\"message-content\">{{ message.content }}</div>\n        </div>\n      </div>\n\n      <div class=\"empty-state\" *ngIf=\"messages.length === 0\">\n        <p>No messages yet</p>\n        <p class=\"hint\">Click the circle to compose your first message</p>\n      </div>\n    </div>\n  </div>\n</div>\n\n<!-- Account Settings Modal -->\n<div class=\"modal-overlay\" *ngIf=\"showAccountSettings\" (click)=\"closeAccountSettings()\">\n  <div class=\"modal account-settings-modal\" (click)=\"$event.stopPropagation()\">\n    <div class=\"modal-header\">\n      <h2>Account Settings</h2>\n      <button class=\"close-btn\" (click)=\"closeAccountSettings()\">×</button>\n    </div>\n\n    <div class=\"modal-content\">\n      <!-- Avatar Section -->\n      <div class=\"avatar-section\">\n        <div class=\"avatar-container\">\n          <div class=\"avatar-display\">\n            <img\n              *ngIf=\"userProfile.avatar\"\n              [src]=\"userProfile.avatar\"\n              alt=\"Profile Avatar\"\n              class=\"avatar-image\"\n            />\n            <div *ngIf=\"!userProfile.avatar\" class=\"avatar-placeholder\">\n              <span class=\"avatar-initials\">\n                {{ currentUser?.username?.charAt(0)?.toUpperCase() || '?' }}\n              </span>\n            </div>\n          </div>\n          <div class=\"avatar-actions\">\n            <label for=\"avatarInput\" class=\"avatar-upload-btn\">\n              📷 Change Avatar\n            </label>\n            <input\n              type=\"file\"\n              id=\"avatarInput\"\n              accept=\"image/*\"\n              (change)=\"onAvatarChange($event)\"\n              style=\"display: none;\"\n            />\n          </div>\n        </div>\n      </div>\n\n      <!-- Profile Information -->\n      <div class=\"profile-info\">\n        <div class=\"form-group\">\n          <label>Username</label>\n          <div class=\"readonly-field\">\n            <span>{{ currentUser?.username || 'Not set' }}</span>\n          </div>\n        </div>\n\n        <div class=\"form-group\">\n          <label>Email Address</label>\n          <div class=\"readonly-field\">\n            <span>{{ userProfile.email || 'Not set' }}</span>\n            <span class=\"field-note\">Email cannot be changed for security reasons</span>\n          </div>\n        </div>\n\n        <div class=\"form-group\">\n          <label>Phone Number</label>\n          <div class=\"readonly-field\">\n            <span>{{ userProfile.phoneNumber || 'Not set' }}</span>\n            <span class=\"field-note\">Phone number cannot be changed for security reasons</span>\n          </div>\n        </div>\n      </div>\n\n      <!-- Security Information -->\n      <div class=\"security-info\">\n        <h3>Security Information</h3>\n        <div class=\"security-item\">\n          <span class=\"security-label\">🔐 Encryption:</span>\n          <span class=\"security-value\">Post-Quantum Cryptography (ML-DSA, ML-KEM)</span>\n        </div>\n        <div class=\"security-item\">\n          <span class=\"security-label\">🛡️ Security Level:</span>\n          <span class=\"security-value\">NIST Level 3 (AES-192 equivalent)</span>\n        </div>\n        <div class=\"security-item\">\n          <span class=\"security-label\">🔑 Key Rotation:</span>\n          <span class=\"security-value\">Every 30 days</span>\n        </div>\n      </div>\n\n      <div class=\"account-actions\">\n        <button class=\"btn btn-secondary\" (click)=\"closeAccountSettings()\">\n          <svg class=\"icon\" viewBox=\"0 0 24 24\">\n            <path d=\"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4\"/>\n            <polyline points=\"16,17 21,12 16,7\"/>\n            <line x1=\"21\" y1=\"12\" x2=\"9\" y2=\"12\"/>\n          </svg>\n          Close\n        </button>\n      </div>\n    </div>\n  </div>\n</div>\n\n<!-- Keyboard Hints (bottom) -->\n<div class=\"keyboard-hints\" *ngIf=\"!showLoginModal && !showMessageModal && !showMessagesModal && !showAccountSettings\">\n  <span>ESC to close modals • Right-click circle for menu</span>\n</div>\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,OAAO,EAAEC,SAAS,EAAEC,IAAI,QAAQ,MAAM;;;;;;;;;ICYvCC,EAAA,CAAAC,SAAA,cAA+D;;;;;IAC/DD,EAAA,CAAAE,cAAA,cAA+D;IAC7DF,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;IADJJ,EAAA,CAAAK,SAAA,EACF;IADEL,EAAA,CAAAM,kBAAA,MAAAC,MAAA,CAAAC,WAAA,MACF;;;;;;IAOJR,EADF,CAAAE,cAAA,cAA2C,WACnC;IAAAF,EAAA,CAAAG,MAAA,GAA0B;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACvCJ,EAAA,CAAAE,cAAA,iBAA6D;IAAlCF,EAAA,CAAAS,UAAA,mBAAAC,wDAAA;MAAAV,EAAA,CAAAW,aAAA,CAAAC,GAAA;MAAA,MAAAL,MAAA,GAAAP,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASP,MAAA,CAAAQ,MAAA,EAAQ;IAAA,EAAC;IAAgBf,EAAA,CAAAG,MAAA,aAAC;IAChEH,EADgE,CAAAI,YAAA,EAAS,EACnE;;;;IAFEJ,EAAA,CAAAK,SAAA,GAA0B;IAA1BL,EAAA,CAAAgB,iBAAA,CAAAT,MAAA,CAAAU,WAAA,CAAAC,QAAA,CAA0B;;;;;;IAKlClB,EAAA,CAAAE,cAAA,cAMC;IADCF,EAAA,CAAAS,UAAA,mBAAAU,qDAAAC,MAAA;MAAApB,EAAA,CAAAW,aAAA,CAAAU,GAAA;MAAA,OAAArB,EAAA,CAAAc,WAAA,CAASM,MAAA,CAAAE,eAAA,EAAwB;IAAA,EAAC;IAElCtB,EAAA,CAAAE,cAAA,cAA+D;IAAhCF,EAAA,CAAAS,UAAA,mBAAAc,qDAAA;MAAAvB,EAAA,CAAAW,aAAA,CAAAU,GAAA;MAAA,MAAAd,MAAA,GAAAP,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASP,MAAA,CAAAiB,mBAAA,EAAqB;IAAA,EAAC;IAC5DxB,EAAA,CAAAE,cAAA,eAAwB;IAAAF,EAAA,CAAAG,MAAA,mBAAE;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACjCJ,EAAA,CAAAE,cAAA,eAAwB;IAAAF,EAAA,CAAAG,MAAA,uBAAgB;IAC1CH,EAD0C,CAAAI,YAAA,EAAO,EAC3C;IACNJ,EAAA,CAAAC,SAAA,cAAwC;IACxCD,EAAA,CAAAE,cAAA,cAA8D;IAAnBF,EAAA,CAAAS,UAAA,mBAAAgB,qDAAA;MAAAzB,EAAA,CAAAW,aAAA,CAAAU,GAAA;MAAA,MAAAd,MAAA,GAAAP,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASP,MAAA,CAAAQ,MAAA,EAAQ;IAAA,EAAC;IAC3Df,EAAA,CAAAE,cAAA,eAAwB;IAAAF,EAAA,CAAAG,MAAA,mBAAE;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACjCJ,EAAA,CAAAE,cAAA,gBAAwB;IAAAF,EAAA,CAAAG,MAAA,cAAM;IAElCH,EAFkC,CAAAI,YAAA,EAAO,EACjC,EACF;;;;IAZJJ,EADA,CAAA0B,WAAA,SAAAnB,MAAA,CAAAoB,mBAAA,CAAAC,CAAA,OAAuC,QAAArB,MAAA,CAAAoB,mBAAA,CAAAE,CAAA,OACD;;;;;IAkDpC7B,EAAA,CAAAE,cAAA,cAA8C;IAC5CF,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;IADJJ,EAAA,CAAAK,SAAA,EACF;IADEL,EAAA,CAAAM,kBAAA,MAAAC,MAAA,CAAAuB,UAAA,MACF;;;;;IAEA9B,EAAA,CAAAE,cAAA,cAAiD;IAC/CF,EAAA,CAAAC,SAAA,cAA2B;IAC3BD,EAAA,CAAAE,cAAA,WAAM;IAAAF,EAAA,CAAAG,MAAA,wBAAiB;IACzBH,EADyB,CAAAI,YAAA,EAAO,EAC1B;;;;;IAGJJ,EADF,CAAAE,cAAA,cAA0C,QACrC;IAAAF,EAAA,CAAAG,MAAA,0DAAyC;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAChDJ,EAAA,CAAAE,cAAA,YAA4B;IAAAF,EAAA,CAAAG,MAAA,mDAA4C;IAC1EH,EAD0E,CAAAI,YAAA,EAAI,EACxE;;;;;;IA9CZJ,EAAA,CAAAE,cAAA,cAA8E;IAA5BF,EAAA,CAAAS,UAAA,mBAAAsB,qDAAA;MAAA/B,EAAA,CAAAW,aAAA,CAAAqB,GAAA;MAAA,MAAAzB,MAAA,GAAAP,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASP,MAAA,CAAA0B,eAAA,EAAiB;IAAA,EAAC;IAC3EjC,EAAA,CAAAE,cAAA,cAAkE;IAAnCF,EAAA,CAAAS,UAAA,mBAAAyB,qDAAAd,MAAA;MAAApB,EAAA,CAAAW,aAAA,CAAAqB,GAAA;MAAA,OAAAhC,EAAA,CAAAc,WAAA,CAASM,MAAA,CAAAE,eAAA,EAAwB;IAAA,EAAC;IAE7DtB,EADF,CAAAE,cAAA,cAA0B,SACpB;IAAAF,EAAA,CAAAG,MAAA,cAAO;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAChBJ,EAAA,CAAAE,cAAA,iBAAsD;IAA5BF,EAAA,CAAAS,UAAA,mBAAA0B,wDAAA;MAAAnC,EAAA,CAAAW,aAAA,CAAAqB,GAAA;MAAA,MAAAzB,MAAA,GAAAP,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASP,MAAA,CAAA0B,eAAA,EAAiB;IAAA,EAAC;IAACjC,EAAA,CAAAG,MAAA,aAAC;IACzDH,EADyD,CAAAI,YAAA,EAAS,EAC5D;IAIFJ,EAFJ,CAAAE,cAAA,cAA2B,cACD,gBACA;IAAAF,EAAA,CAAAG,MAAA,gBAAQ;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IACtCJ,EAAA,CAAAE,cAAA,iBAQE;IALAF,EAAA,CAAAoC,gBAAA,2BAAAC,gEAAAjB,MAAA;MAAApB,EAAA,CAAAW,aAAA,CAAAqB,GAAA;MAAA,MAAAzB,MAAA,GAAAP,EAAA,CAAAa,aAAA;MAAAb,EAAA,CAAAsC,kBAAA,CAAA/B,MAAA,CAAAgC,gBAAA,CAAArB,QAAA,EAAAE,MAAA,MAAAb,MAAA,CAAAgC,gBAAA,CAAArB,QAAA,GAAAE,MAAA;MAAA,OAAApB,EAAA,CAAAc,WAAA,CAAAM,MAAA;IAAA,EAAuC;IACvCpB,EAAA,CAAAS,UAAA,mBAAA+B,wDAAA;MAAAxC,EAAA,CAAAW,aAAA,CAAAqB,GAAA;MAAA,MAAAzB,MAAA,GAAAP,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASP,MAAA,CAAAkC,kBAAA,EAAoB;IAAA,EAAC;IAKlCzC,EATE,CAAAI,YAAA,EAQE,EACE;IAGJJ,EADF,CAAAE,cAAA,eAAwB,iBACE;IAAAF,EAAA,CAAAG,MAAA,mBAAW;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IAC3CJ,EAAA,CAAAE,cAAA,iBAQE;IALAF,EAAA,CAAAoC,gBAAA,2BAAAM,gEAAAtB,MAAA;MAAApB,EAAA,CAAAW,aAAA,CAAAqB,GAAA;MAAA,MAAAzB,MAAA,GAAAP,EAAA,CAAAa,aAAA;MAAAb,EAAA,CAAAsC,kBAAA,CAAA/B,MAAA,CAAAgC,gBAAA,CAAAI,UAAA,EAAAvB,MAAA,MAAAb,MAAA,CAAAgC,gBAAA,CAAAI,UAAA,GAAAvB,MAAA;MAAA,OAAApB,EAAA,CAAAc,WAAA,CAAAM,MAAA;IAAA,EAAyC;IACzCpB,EAAA,CAAAS,UAAA,mBAAAmC,wDAAA;MAAA5C,EAAA,CAAAW,aAAA,CAAAqB,GAAA;MAAA,MAAAzB,MAAA,GAAAP,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASP,MAAA,CAAAkC,kBAAA,EAAoB;IAAA,EAAC;IAKlCzC,EATE,CAAAI,YAAA,EAQE,EACE;IAWNJ,EATA,CAAA6C,UAAA,KAAAC,sCAAA,kBAA8C,KAAAC,sCAAA,kBAIG,KAAAC,sCAAA,kBAKP;IAMhDhD,EAFI,CAAAI,YAAA,EAAM,EACF,EACF;;;;IApCIJ,EAAA,CAAAK,SAAA,IAAuC;IAAvCL,EAAA,CAAAiD,gBAAA,YAAA1C,MAAA,CAAAgC,gBAAA,CAAArB,QAAA,CAAuC;IAGvClB,EAAA,CAAAkD,UAAA,aAAA3C,MAAA,CAAA4C,SAAA,CAAsB;IAUtBnD,EAAA,CAAAK,SAAA,GAAyC;IAAzCL,EAAA,CAAAiD,gBAAA,YAAA1C,MAAA,CAAAgC,gBAAA,CAAAI,UAAA,CAAyC;IAGzC3C,EAAA,CAAAkD,UAAA,aAAA3C,MAAA,CAAA4C,SAAA,CAAsB;IAKEnD,EAAA,CAAAK,SAAA,EAAgB;IAAhBL,EAAA,CAAAkD,UAAA,SAAA3C,MAAA,CAAAuB,UAAA,CAAgB;IAIZ9B,EAAA,CAAAK,SAAA,EAAe;IAAfL,EAAA,CAAAkD,UAAA,SAAA3C,MAAA,CAAA4C,SAAA,CAAe;IAKvBnD,EAAA,CAAAK,SAAA,EAAgB;IAAhBL,EAAA,CAAAkD,UAAA,UAAA3C,MAAA,CAAA4C,SAAA,CAAgB;;;;;;IAsDlCnD,EADF,CAAAE,cAAA,cAAmE,eACpC;IAAAF,EAAA,CAAAG,MAAA,GAAgC;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACpEJ,EAAA,CAAAE,cAAA,iBAIC;IAFCF,EAAA,CAAAS,UAAA,mBAAA2C,+DAAA;MAAApD,EAAA,CAAAW,aAAA,CAAA0C,GAAA;MAAA,MAAA9C,MAAA,GAAAP,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASP,MAAA,CAAA+C,iBAAA,CAAA/C,MAAA,CAAAgD,WAAA,CAA8B;IAAA,EAAC;IAEzCvD,EAAA,CAAAG,MAAA,aAAC;IACJH,EADI,CAAAI,YAAA,EAAS,EACP;;;;IANyBJ,EAAA,CAAAK,SAAA,GAAgC;IAAhCL,EAAA,CAAAgB,iBAAA,CAAAT,MAAA,CAAAiD,wBAAA,GAAgC;;;;;;IAW7DxD,EAAA,CAAAE,cAAA,cAKC;IAFCF,EAAA,CAAAS,UAAA,mBAAAgD,kEAAA;MAAA,MAAAC,UAAA,GAAA1D,EAAA,CAAAW,aAAA,CAAAgD,GAAA,EAAAC,SAAA;MAAA,MAAArD,MAAA,GAAAP,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASP,MAAA,CAAAsD,aAAA,CAAAH,UAAA,CAAsB;IAAA,EAAC;IAI9B1D,EADF,CAAAE,cAAA,cAA0B,eACG;IAAAF,EAAA,CAAAG,MAAA,GAAsB;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACxDJ,EAAA,CAAAE,cAAA,eAA4B;IAAAF,EAAA,CAAAG,MAAA,GAAmB;IACjDH,EADiD,CAAAI,YAAA,EAAO,EAClD;IACNJ,EAAA,CAAAE,cAAA,cAA4B;IAC1BF,EAAA,CAAAC,SAAA,eAIQ;IACRD,EAAA,CAAAE,cAAA,eAA0B;IACxBF,EAAA,CAAAG,MAAA,GACF;IAEJH,EAFI,CAAAI,YAAA,EAAO,EACH,EACF;;;;;IAhBJJ,EAAA,CAAAkD,UAAA,WAAA3C,MAAA,CAAAgD,WAAA,cAAmC;IAGNvD,EAAA,CAAAK,SAAA,GAAsB;IAAtBL,EAAA,CAAAgB,iBAAA,CAAA0C,UAAA,CAAAxC,QAAA,CAAsB;IACrBlB,EAAA,CAAAK,SAAA,GAAmB;IAAnBL,EAAA,CAAAgB,iBAAA,CAAA0C,UAAA,CAAAI,KAAA,CAAmB;IAK7C9D,EAAA,CAAAK,SAAA,GAAiC;IACjCL,EADA,CAAA+D,WAAA,WAAAL,UAAA,CAAAM,QAAA,CAAiC,aAAAN,UAAA,CAAAM,QAAA,CACE;IAGnChE,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAoD,UAAA,CAAAM,QAAA,6BACF;;;;;;IAKJhE,EAAA,CAAAE,cAAA,cAKC;IAFCF,EAAA,CAAAS,UAAA,mBAAAwD,kEAAA;MAAA,MAAAC,SAAA,GAAAlE,EAAA,CAAAW,aAAA,CAAAwD,GAAA,EAAAP,SAAA;MAAA,MAAArD,MAAA,GAAAP,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASP,MAAA,CAAA6D,WAAA,CAAAF,SAAA,CAAkB;IAAA,EAAC;IAI1BlE,EADF,CAAAE,cAAA,cAAwB,eACG;IAAAF,EAAA,CAAAG,MAAA,GAAgB;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAChDJ,EAAA,CAAAE,cAAA,eAA4B;IAAAF,EAAA,CAAAG,MAAA,GAAkC;IAChEH,EADgE,CAAAI,YAAA,EAAO,EACjE;IACNJ,EAAA,CAAAE,cAAA,cAA0B;IACxBF,EAAA,CAAAC,SAAA,eAGQ;IAEZD,EADE,CAAAI,YAAA,EAAM,EACF;;;;;IAZJJ,EAAA,CAAAkD,UAAA,WAAA3C,MAAA,CAAAgD,WAAA,aAAkC;IAGPvD,EAAA,CAAAK,SAAA,GAAgB;IAAhBL,EAAA,CAAAgB,iBAAA,CAAAkD,SAAA,CAAAG,IAAA,CAAgB;IACbrE,EAAA,CAAAK,SAAA,GAAkC;IAAlCL,EAAA,CAAAM,kBAAA,KAAA4D,SAAA,CAAAI,OAAA,CAAAC,MAAA,aAAkC;IAK5DvE,EAAA,CAAAK,SAAA,GAA+B;IAA/BL,EAAA,CAAA+D,WAAA,WAAAG,SAAA,CAAAM,QAAA,CAA+B;;;;;IAMrCxE,EAAA,CAAAE,cAAA,cAA0F;IACxFF,EAAA,CAAAG,MAAA,0BACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;;IACNJ,EAAA,CAAAE,cAAA,cAAuF;IACrFF,EAAA,CAAAG,MAAA,wBACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;;IAjDRJ,EAAA,CAAAE,cAAA,cAA4F;IA+C1FF,EA7CA,CAAA6C,UAAA,IAAA4B,4CAAA,mBAKC,IAAAC,4CAAA,kBAuBA,IAAAC,4CAAA,kBAcyF,IAAAC,4CAAA,kBAGH;IAGzF5E,EAAA,CAAAI,YAAA,EAAM;;;;IA9CkBJ,EAAA,CAAAK,SAAA,EAAmB;IAAnBL,EAAA,CAAAkD,UAAA,YAAA3C,MAAA,CAAAsE,gBAAA,CAAmB;IAuBrB7E,EAAA,CAAAK,SAAA,EAAiB;IAAjBL,EAAA,CAAAkD,UAAA,YAAA3C,MAAA,CAAAuE,cAAA,CAAiB;IAiBZ9E,EAAA,CAAAK,SAAA,EAA+D;IAA/DL,EAAA,CAAAkD,UAAA,SAAA3C,MAAA,CAAAgD,WAAA,iBAAAhD,MAAA,CAAAsE,gBAAA,CAAAN,MAAA,OAA+D;IAG/DvE,EAAA,CAAAK,SAAA,EAA4D;IAA5DL,EAAA,CAAAkD,UAAA,SAAA3C,MAAA,CAAAgD,WAAA,gBAAAhD,MAAA,CAAAuE,cAAA,CAAAP,MAAA,OAA4D;;;;;;IArGjGvE,EAAA,CAAAE,cAAA,cAAqF;IAAjCF,EAAA,CAAAS,UAAA,mBAAAsE,qDAAA;MAAA/E,EAAA,CAAAW,aAAA,CAAAqE,GAAA;MAAA,MAAAzE,MAAA,GAAAP,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASP,MAAA,CAAA0E,oBAAA,EAAsB;IAAA,EAAC;IAClFjF,EAAA,CAAAE,cAAA,cAAoE;IAAnCF,EAAA,CAAAS,UAAA,mBAAAyE,qDAAA9D,MAAA;MAAApB,EAAA,CAAAW,aAAA,CAAAqE,GAAA;MAAA,OAAAhF,EAAA,CAAAc,WAAA,CAASM,MAAA,CAAAE,eAAA,EAAwB;IAAA,EAAC;IAI7DtB,EAHJ,CAAAE,cAAA,cAA2B,cAEqD,iBAK3E;IAFCF,EAAA,CAAAS,UAAA,mBAAA0E,wDAAA;MAAAnF,EAAA,CAAAW,aAAA,CAAAqE,GAAA;MAAA,MAAAzE,MAAA,GAAAP,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASP,MAAA,CAAA+C,iBAAA,CAAkB,QAAQ,CAAC;IAAA,EAAC;;IAGrCtD,EAAA,CAAAE,cAAA,cAAsC;IAEpCF,EADA,CAAAC,SAAA,eAAqD,iBACvB;IAChCD,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAG,MAAA,eACF;IAAAH,EAAA,CAAAI,YAAA,EAAS;;IACTJ,EAAA,CAAAE,cAAA,iBAIC;IAFCF,EAAA,CAAAS,UAAA,mBAAA2E,wDAAA;MAAApF,EAAA,CAAAW,aAAA,CAAAqE,GAAA;MAAA,MAAAzE,MAAA,GAAAP,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASP,MAAA,CAAA+C,iBAAA,CAAkB,OAAO,CAAC;IAAA,EAAC;;IAGpCtD,EAAA,CAAAE,cAAA,eAAsC;IAIpCF,EAHA,CAAAC,SAAA,gBAAqD,kBACxB,gBACS,gBACD;IACvCD,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAG,MAAA,eACF;IACFH,EADE,CAAAI,YAAA,EAAS,EACL;;IAKFJ,EAFJ,CAAAE,cAAA,eAAwB,eACU,iBAQ5B;IAJAF,EAAA,CAAAoC,gBAAA,2BAAAiD,gEAAAjE,MAAA;MAAApB,EAAA,CAAAW,aAAA,CAAAqE,GAAA;MAAA,MAAAzE,MAAA,GAAAP,EAAA,CAAAa,aAAA;MAAAb,EAAA,CAAAsC,kBAAA,CAAA/B,MAAA,CAAA+E,oBAAA,EAAAlE,MAAA,MAAAb,MAAA,CAAA+E,oBAAA,GAAAlE,MAAA;MAAA,OAAApB,EAAA,CAAAc,WAAA,CAAAM,MAAA;IAAA,EAAkC;IAClCpB,EAAA,CAAAS,UAAA,mBAAA8E,wDAAA;MAAAvF,EAAA,CAAAW,aAAA,CAAAqE,GAAA;MAAA,MAAAzE,MAAA,GAAAP,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASP,MAAA,CAAAiF,uBAAA,EAAyB;IAAA,EAAC;IAJrCxF,EAAA,CAAAI,YAAA,EAOE;IAaFJ,EAVA,CAAA6C,UAAA,KAAA4C,sCAAA,kBAAmE,KAAAC,sCAAA,kBAUyB;IAoDhG1F,EADE,CAAAI,YAAA,EAAM,EACF;IAIJJ,EADF,CAAAE,cAAA,eAAwB,iBACM;IAAAF,EAAA,CAAAG,MAAA,eAAO;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IAC3CJ,EAAA,CAAAE,cAAA,oBAMC;IAJCF,EAAA,CAAAoC,gBAAA,2BAAAuD,mEAAAvE,MAAA;MAAApB,EAAA,CAAAW,aAAA,CAAAqE,GAAA;MAAA,MAAAzE,MAAA,GAAAP,EAAA,CAAAa,aAAA;MAAAb,EAAA,CAAAsC,kBAAA,CAAA/B,MAAA,CAAAqF,cAAA,EAAAxE,MAAA,MAAAb,MAAA,CAAAqF,cAAA,GAAAxE,MAAA;MAAA,OAAApB,EAAA,CAAAc,WAAA,CAAAM,MAAA;IAAA,EAA4B;IAI7BpB,EAAA,CAAAI,YAAA,EAAW;IACZJ,EAAA,CAAAE,cAAA,eAAwB;IAAAF,EAAA,CAAAG,MAAA,IAAgC;IAC1DH,EAD0D,CAAAI,YAAA,EAAM,EAC1D;IAGJJ,EADF,CAAAE,cAAA,eAA6B,kBAK1B;IAFCF,EAAA,CAAAS,UAAA,mBAAAoF,yDAAA;MAAA7F,EAAA,CAAAW,aAAA,CAAAqE,GAAA;MAAA,MAAAzE,MAAA,GAAAP,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASP,MAAA,CAAAuF,WAAA,EAAa;IAAA,EAAC;;IAGvB9F,EAAA,CAAAE,cAAA,eAAsC;IAEpCF,EADA,CAAAC,SAAA,gBAAsC,mBACE;IAC1CD,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAG,MAAA,cACF;IAAAH,EAAA,CAAAI,YAAA,EAAS;;IACTJ,EAAA,CAAAE,cAAA,kBAAmE;IAAjCF,EAAA,CAAAS,UAAA,mBAAAsF,yDAAA;MAAA/F,EAAA,CAAAW,aAAA,CAAAqE,GAAA;MAAA,MAAAzE,MAAA,GAAAP,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASP,MAAA,CAAA0E,oBAAA,EAAsB;IAAA,EAAC;;IAChEjF,EAAA,CAAAE,cAAA,eAAsC;IAEpCF,EADA,CAAAC,SAAA,gBAAqC,gBACA;IACvCD,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAG,MAAA,gBACF;IACFH,EADE,CAAAI,YAAA,EAAS,EACL;;IAGJJ,EADF,CAAAE,cAAA,eAAuB,SAClB;IAAAF,EAAA,CAAAG,MAAA,2DAA8C;IAIzDH,EAJyD,CAAAI,YAAA,EAAI,EACjD,EACF,EACF,EACF;;;;IA/ImCJ,EAAA,CAAAK,SAAA,GAA0C;IAA1CL,EAAA,CAAA+D,WAAA,eAAAxD,MAAA,CAAAyF,UAAA,CAAAzB,MAAA,KAA0C;IAIzEvE,EAAA,CAAAK,SAAA,EAAyC;IAAzCL,EAAA,CAAA+D,WAAA,WAAAxD,MAAA,CAAAgD,WAAA,cAAyC;IAWzCvD,EAAA,CAAAK,SAAA,GAAwC;IAAxCL,EAAA,CAAA+D,WAAA,WAAAxD,MAAA,CAAAgD,WAAA,aAAwC;IAkBtCvD,EAAA,CAAAK,SAAA,GAAkC;IAAlCL,EAAA,CAAAiD,gBAAA,YAAA1C,MAAA,CAAA+E,oBAAA,CAAkC;IAElCtF,EAAA,CAAAkD,UAAA,gBAAA3C,MAAA,CAAAgD,WAAA,0DAAoF;IAKrDvD,EAAA,CAAAK,SAAA,EAAgC;IAAhCL,EAAA,CAAAkD,UAAA,SAAA3C,MAAA,CAAAiD,wBAAA,GAAgC;IAUhCxD,EAAA,CAAAK,SAAA,EAAyD;IAAzDL,EAAA,CAAAkD,UAAA,SAAA3C,MAAA,CAAA+E,oBAAA,KAAA/E,MAAA,CAAAiD,wBAAA,GAAyD;IA2D1FxD,EAAA,CAAAK,SAAA,GAA4B;IAA5BL,EAAA,CAAAiD,gBAAA,YAAA1C,MAAA,CAAAqF,cAAA,CAA4B;IAKN5F,EAAA,CAAAK,SAAA,GAAgC;IAAhCL,EAAA,CAAAM,kBAAA,KAAAC,MAAA,CAAAqF,cAAA,CAAArB,MAAA,UAAgC;IAOtDvE,EAAA,CAAAK,SAAA,GAA8B;IAA9BL,EAAA,CAAAkD,UAAA,cAAA3C,MAAA,CAAA0F,cAAA,GAA8B;;;;;IAuC5BjG,EALJ,CAAAE,cAAA,cAGC,cAC6B,eACL;IAAAF,EAAA,CAAAG,MAAA,GAAoB;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAChDJ,EAAA,CAAAE,cAAA,eAAwB;IAAAF,EAAA,CAAAG,MAAA,GAAsC;;IAChEH,EADgE,CAAAI,YAAA,EAAO,EACjE;IACNJ,EAAA,CAAAE,cAAA,cAA6B;IAAAF,EAAA,CAAAG,MAAA,GAAqB;IACpDH,EADoD,CAAAI,YAAA,EAAM,EACpD;;;;IAJmBJ,EAAA,CAAAK,SAAA,GAAoB;IAApBL,EAAA,CAAAgB,iBAAA,CAAAkF,WAAA,CAAAC,MAAA,CAAoB;IACjBnG,EAAA,CAAAK,SAAA,GAAsC;IAAtCL,EAAA,CAAAgB,iBAAA,CAAAhB,EAAA,CAAAoG,WAAA,OAAAF,WAAA,CAAAG,SAAA,WAAsC;IAEnCrG,EAAA,CAAAK,SAAA,GAAqB;IAArBL,EAAA,CAAAgB,iBAAA,CAAAkF,WAAA,CAAAI,OAAA,CAAqB;;;;;IATtDtG,EAAA,CAAAE,cAAA,cAAuD;IACrDF,EAAA,CAAA6C,UAAA,IAAA0D,4CAAA,kBAGC;IAOHvG,EAAA,CAAAI,YAAA,EAAM;;;;IARkBJ,EAAA,CAAAK,SAAA,EAAa;IAAAL,EAAb,CAAAkD,UAAA,YAAA3C,MAAA,CAAAiG,QAAA,CAAa,iBAAAjG,MAAA,CAAAkG,YAAA,CAAqB;;;;;IAWxDzG,EADF,CAAAE,cAAA,cAAuD,QAClD;IAAAF,EAAA,CAAAG,MAAA,sBAAe;IAAAH,EAAA,CAAAI,YAAA,EAAI;IACtBJ,EAAA,CAAAE,cAAA,YAAgB;IAAAF,EAAA,CAAAG,MAAA,qDAA8C;IAChEH,EADgE,CAAAI,YAAA,EAAI,EAC9D;;;;;;IAxBZJ,EAAA,CAAAE,cAAA,cAAqF;IAAhCF,EAAA,CAAAS,UAAA,mBAAAiG,sDAAA;MAAA1G,EAAA,CAAAW,aAAA,CAAAgG,IAAA;MAAA,MAAApG,MAAA,GAAAP,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASP,MAAA,CAAAqG,mBAAA,EAAqB;IAAA,EAAC;IAClF5G,EAAA,CAAAE,cAAA,cAAqE;IAAnCF,EAAA,CAAAS,UAAA,mBAAAoG,sDAAAzF,MAAA;MAAApB,EAAA,CAAAW,aAAA,CAAAgG,IAAA;MAAA,OAAA3G,EAAA,CAAAc,WAAA,CAASM,MAAA,CAAAE,eAAA,EAAwB;IAAA,EAAC;IAEhEtB,EADF,CAAAE,cAAA,cAA0B,SACpB;IAAAF,EAAA,CAAAG,MAAA,eAAQ;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACjBJ,EAAA,CAAAE,cAAA,iBAA0D;IAAhCF,EAAA,CAAAS,UAAA,mBAAAqG,yDAAA;MAAA9G,EAAA,CAAAW,aAAA,CAAAgG,IAAA;MAAA,MAAApG,MAAA,GAAAP,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASP,MAAA,CAAAqG,mBAAA,EAAqB;IAAA,EAAC;IAAC5G,EAAA,CAAAG,MAAA,aAAC;IAC7DH,EAD6D,CAAAI,YAAA,EAAS,EAChE;IAENJ,EAAA,CAAAE,cAAA,cAA2B;IAczBF,EAbA,CAAA6C,UAAA,IAAAkE,sCAAA,kBAAuD,IAAAC,sCAAA,kBAaA;IAM7DhH,EAFI,CAAAI,YAAA,EAAM,EACF,EACF;;;;IAnB4BJ,EAAA,CAAAK,SAAA,GAAyB;IAAzBL,EAAA,CAAAkD,UAAA,SAAA3C,MAAA,CAAAiG,QAAA,CAAAjC,MAAA,KAAyB;IAa3BvE,EAAA,CAAAK,SAAA,EAA2B;IAA3BL,EAAA,CAAAkD,UAAA,SAAA3C,MAAA,CAAAiG,QAAA,CAAAjC,MAAA,OAA2B;;;;;IAqB/CvE,EAAA,CAAAC,SAAA,eAKE;;;;IAHAD,EAAA,CAAAkD,UAAA,QAAA3C,MAAA,CAAA0G,WAAA,CAAAC,MAAA,EAAAlH,EAAA,CAAAmH,aAAA,CAA0B;;;;;IAK1BnH,EADF,CAAAE,cAAA,eAA4D,gBAC5B;IAC5BF,EAAA,CAAAG,MAAA,GACF;IACFH,EADE,CAAAI,YAAA,EAAO,EACH;;;;;IAFFJ,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,OAAAC,MAAA,CAAAU,WAAA,kBAAAV,MAAA,CAAAU,WAAA,CAAAC,QAAA,mBAAAkG,OAAA,GAAA7G,MAAA,CAAAU,WAAA,CAAAC,QAAA,CAAAmG,MAAA,sBAAAD,OAAA,CAAAE,WAAA,gBACF;;;;;;IArBdtH,EAAA,CAAAE,cAAA,cAAwF;IAAjCF,EAAA,CAAAS,UAAA,mBAAA8G,sDAAA;MAAAvH,EAAA,CAAAW,aAAA,CAAA6G,IAAA;MAAA,MAAAjH,MAAA,GAAAP,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASP,MAAA,CAAAkH,oBAAA,EAAsB;IAAA,EAAC;IACrFzH,EAAA,CAAAE,cAAA,cAA6E;IAAnCF,EAAA,CAAAS,UAAA,mBAAAiH,sDAAAtG,MAAA;MAAApB,EAAA,CAAAW,aAAA,CAAA6G,IAAA;MAAA,OAAAxH,EAAA,CAAAc,WAAA,CAASM,MAAA,CAAAE,eAAA,EAAwB;IAAA,EAAC;IAExEtB,EADF,CAAAE,cAAA,cAA0B,SACpB;IAAAF,EAAA,CAAAG,MAAA,uBAAgB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACzBJ,EAAA,CAAAE,cAAA,iBAA2D;IAAjCF,EAAA,CAAAS,UAAA,mBAAAkH,yDAAA;MAAA3H,EAAA,CAAAW,aAAA,CAAA6G,IAAA;MAAA,MAAAjH,MAAA,GAAAP,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASP,MAAA,CAAAkH,oBAAA,EAAsB;IAAA,EAAC;IAACzH,EAAA,CAAAG,MAAA,aAAC;IAC9DH,EAD8D,CAAAI,YAAA,EAAS,EACjE;IAMAJ,EAJN,CAAAE,cAAA,cAA2B,cAEG,cACI,eACA;IAO1BF,EANA,CAAA6C,UAAA,KAAA+E,uCAAA,kBAKE,KAAAC,uCAAA,kBAC0D;IAK9D7H,EAAA,CAAAI,YAAA,EAAM;IAEJJ,EADF,CAAAE,cAAA,gBAA4B,kBACyB;IACjDF,EAAA,CAAAG,MAAA,oCACF;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IACRJ,EAAA,CAAAE,cAAA,kBAME;IAFAF,EAAA,CAAAS,UAAA,oBAAAqH,0DAAA1G,MAAA;MAAApB,EAAA,CAAAW,aAAA,CAAA6G,IAAA;MAAA,MAAAjH,MAAA,GAAAP,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAAUP,MAAA,CAAAwH,cAAA,CAAA3G,MAAA,CAAsB;IAAA,EAAC;IAKzCpB,EATM,CAAAI,YAAA,EAME,EACE,EACF,EACF;IAKFJ,EAFJ,CAAAE,cAAA,gBAA0B,eACA,aACf;IAAAF,EAAA,CAAAG,MAAA,gBAAQ;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IAErBJ,EADF,CAAAE,cAAA,gBAA4B,YACpB;IAAAF,EAAA,CAAAG,MAAA,IAAwC;IAElDH,EAFkD,CAAAI,YAAA,EAAO,EACjD,EACF;IAGJJ,EADF,CAAAE,cAAA,eAAwB,aACf;IAAAF,EAAA,CAAAG,MAAA,qBAAa;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IAE1BJ,EADF,CAAAE,cAAA,gBAA4B,YACpB;IAAAF,EAAA,CAAAG,MAAA,IAAoC;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACjDJ,EAAA,CAAAE,cAAA,iBAAyB;IAAAF,EAAA,CAAAG,MAAA,oDAA4C;IAEzEH,EAFyE,CAAAI,YAAA,EAAO,EACxE,EACF;IAGJJ,EADF,CAAAE,cAAA,eAAwB,aACf;IAAAF,EAAA,CAAAG,MAAA,oBAAY;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IAEzBJ,EADF,CAAAE,cAAA,gBAA4B,YACpB;IAAAF,EAAA,CAAAG,MAAA,IAA0C;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACvDJ,EAAA,CAAAE,cAAA,iBAAyB;IAAAF,EAAA,CAAAG,MAAA,2DAAmD;IAGlFH,EAHkF,CAAAI,YAAA,EAAO,EAC/E,EACF,EACF;IAIJJ,EADF,CAAAE,cAAA,gBAA2B,UACrB;IAAAF,EAAA,CAAAG,MAAA,4BAAoB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAE3BJ,EADF,CAAAE,cAAA,gBAA2B,iBACI;IAAAF,EAAA,CAAAG,MAAA,gCAAc;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAClDJ,EAAA,CAAAE,cAAA,iBAA6B;IAAAF,EAAA,CAAAG,MAAA,kDAA0C;IACzEH,EADyE,CAAAI,YAAA,EAAO,EAC1E;IAEJJ,EADF,CAAAE,cAAA,gBAA2B,iBACI;IAAAF,EAAA,CAAAG,MAAA,0CAAmB;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACvDJ,EAAA,CAAAE,cAAA,iBAA6B;IAAAF,EAAA,CAAAG,MAAA,yCAAiC;IAChEH,EADgE,CAAAI,YAAA,EAAO,EACjE;IAEJJ,EADF,CAAAE,cAAA,gBAA2B,iBACI;IAAAF,EAAA,CAAAG,MAAA,kCAAgB;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACpDJ,EAAA,CAAAE,cAAA,iBAA6B;IAAAF,EAAA,CAAAG,MAAA,qBAAa;IAE9CH,EAF8C,CAAAI,YAAA,EAAO,EAC7C,EACF;IAGJJ,EADF,CAAAE,cAAA,gBAA6B,kBACwC;IAAjCF,EAAA,CAAAS,UAAA,mBAAAuH,0DAAA;MAAAhI,EAAA,CAAAW,aAAA,CAAA6G,IAAA;MAAA,MAAAjH,MAAA,GAAAP,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASP,MAAA,CAAAkH,oBAAA,EAAsB;IAAA,EAAC;;IAChEzH,EAAA,CAAAE,cAAA,eAAsC;IAGpCF,EAFA,CAAAC,SAAA,iBAAmD,qBACd,iBACC;IACxCD,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAG,MAAA,eACF;IAIRH,EAJQ,CAAAI,YAAA,EAAS,EACL,EACF,EACF,EACF;;;;IAjFSJ,EAAA,CAAAK,SAAA,IAAwB;IAAxBL,EAAA,CAAAkD,UAAA,SAAA3C,MAAA,CAAA0G,WAAA,CAAAC,MAAA,CAAwB;IAKrBlH,EAAA,CAAAK,SAAA,EAAyB;IAAzBL,EAAA,CAAAkD,UAAA,UAAA3C,MAAA,CAAA0G,WAAA,CAAAC,MAAA,CAAyB;IA0BzBlH,EAAA,CAAAK,SAAA,IAAwC;IAAxCL,EAAA,CAAAgB,iBAAA,EAAAT,MAAA,CAAAU,WAAA,kBAAAV,MAAA,CAAAU,WAAA,CAAAC,QAAA,eAAwC;IAOxClB,EAAA,CAAAK,SAAA,GAAoC;IAApCL,EAAA,CAAAgB,iBAAA,CAAAT,MAAA,CAAA0G,WAAA,CAAAnD,KAAA,cAAoC;IAQpC9D,EAAA,CAAAK,SAAA,GAA0C;IAA1CL,EAAA,CAAAgB,iBAAA,CAAAT,MAAA,CAAA0G,WAAA,CAAAgB,WAAA,cAA0C;;;;;IAuC1DjI,EADF,CAAAE,cAAA,eAAuH,WAC/G;IAAAF,EAAA,CAAAG,MAAA,6DAAiD;IACzDH,EADyD,CAAAI,YAAA,EAAO,EAC1D;;;AD5UN,OAAM,MAAO8H,gBAAgB;EAoC3B;EACA,IAAIlC,UAAUA,CAAA;IACZ,OAAO,IAAI,CAACmC,MAAM;EACpB;EAgBAC,YACUC,WAAwB,EACxBC,cAA8B,EAC9BC,mBAAwC;IAFxC,KAAAF,WAAW,GAAXA,WAAW;IACX,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,mBAAmB,GAAnBA,mBAAmB;IAzDrB,KAAAC,QAAQ,GAAG,IAAI3I,OAAO,EAAQ;IAEtC;IACA,KAAA4I,WAAW,GAAgB,OAAO;IAElC;IACA,KAAAC,cAAc,GAAG,KAAK;IACtB,KAAAC,gBAAgB,GAAG,KAAK;IACxB,KAAAC,iBAAiB,GAAG,KAAK;IACzB,KAAAC,eAAe,GAAG,KAAK;IACvB,KAAAC,mBAAmB,GAAG,KAAK;IAE3B;IACA,KAAAnH,mBAAmB,GAAG;MAAEC,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAC,CAAE;IAEpC;IACA,KAAAU,gBAAgB,GAAqB;MAAErB,QAAQ,EAAE,EAAE;MAAEyB,UAAU,EAAE;IAAE,CAAE;IACrE,KAAAb,UAAU,GAAG,EAAE;IACf,KAAAqB,SAAS,GAAG,KAAK;IAEjB;IACA,KAAAyC,cAAc,GAAG,EAAE;IACnB,KAAAmD,iBAAiB,GAAG,EAAE;IACtB,KAAAC,aAAa,GAAG,EAAE;IAClB,KAAAzF,WAAW,GAAuB,QAAQ;IAC1C,KAAAiD,QAAQ,GAAc,EAAE;IACxB,KAAAhG,WAAW,GAAG,CAAC;IAEf;IACA,KAAAyI,QAAQ,GAAc,EAAE;IACxB,KAAAd,MAAM,GAAY,EAAE;IACpB,KAAAtD,gBAAgB,GAAc,EAAE;IAChC,KAAAC,cAAc,GAAY,EAAE;IAC5B,KAAAQ,oBAAoB,GAAG,EAAE;IAOzB;IACA,KAAArE,WAAW,GAAgB,IAAI;IAE/B;IACA,KAAAgG,WAAW,GAAG;MACZC,MAAM,EAAE,EAAE;MACVpD,KAAK,EAAE,EAAE;MACToF,KAAK,EAAE;KACR;IAED;IACQ,KAAAC,cAAc,GAAQ,IAAI;IAC1B,KAAAC,iBAAiB,GAAG,GAAG,CAAC,CAAC;EAM9B;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACC,aAAa,EAAE;IACpB,IAAI,CAACC,oBAAoB,EAAE;IAC3B,IAAI,CAACC,iBAAiB,EAAE;EAC1B;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACjB,QAAQ,CAACkB,IAAI,EAAE;IACpB,IAAI,CAAClB,QAAQ,CAACmB,QAAQ,EAAE;EAC1B;EAEQL,aAAaA,CAAA;IACnB;IACA;IACA,IAAI,CAACjB,WAAW,CAACuB,UAAU,CAACC,IAAI,CAAC9J,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC+J,SAAS,CAAEC,SAAoB,IAAI;MAC3E,IAAIA,SAAS,CAACC,eAAe,IAAID,SAAS,CAACE,IAAI,EAAE;QAC/C,IAAI,CAAChJ,WAAW,GAAG8I,SAAS,CAACE,IAAI;QACjC,IAAI,CAACxB,WAAW,GAAG,eAAe;QAClC,IAAI,CAACyB,YAAY,EAAE;QACnB,IAAI,CAACC,YAAY,EAAE;QACnB,IAAI,CAACC,UAAU,EAAE;OAClB,MAAM;QACL;QACA,IAAI,CAAC3B,WAAW,GAAG,OAAO;QAC1B,IAAI,CAAC4B,cAAc,EAAE;;IAEzB,CAAC,CAAC;EACJ;EAEQb,iBAAiBA,CAAA;IACvB,IAAI,CAACnB,WAAW,CAACuB,UAAU,CACxBC,IAAI,CAAC/J,SAAS,CAAC,IAAI,CAAC0I,QAAQ,CAAC,CAAC,CAC9BsB,SAAS,CAAEC,SAAoB,IAAI;MAClC,IAAIA,SAAS,CAACC,eAAe,IAAID,SAAS,CAACE,IAAI,EAAE;QAC/C,IAAI,CAAChJ,WAAW,GAAG8I,SAAS,CAACE,IAAI;QACjC,IAAI,CAACxB,WAAW,GAAG,eAAe;QAClC,IAAI,CAACC,cAAc,GAAG,KAAK;QAC3B,IAAI,CAACwB,YAAY,EAAE;OACpB,MAAM;QACL,IAAI,CAACjJ,WAAW,GAAG,IAAI;QACvB,IAAI,CAACwH,WAAW,GAAG,OAAO;QAC1B,IAAI,CAACjC,QAAQ,GAAG,EAAE;QAClB,IAAI,CAAChG,WAAW,GAAG,CAAC;;IAExB,CAAC,CAAC;EACN;EAEQ+I,oBAAoBA,CAAA;IAC1B,IAAI,CAACjB,cAAc,CAACgC,SAAS,CAC1BT,IAAI,CAAC/J,SAAS,CAAC,IAAI,CAAC0I,QAAQ,CAAC,CAAC,CAC9BsB,SAAS,CAACtD,QAAQ,IAAG;MACpB,IAAI,CAACA,QAAQ,GAAGA,QAAQ;MACxB,IAAI,CAAC+D,iBAAiB,EAAE;IAC1B,CAAC,CAAC;IAEJ,IAAI,CAACjC,cAAc,CAACkC,WAAW,CAC5BX,IAAI,CAAC/J,SAAS,CAAC,IAAI,CAAC0I,QAAQ,CAAC,CAAC,CAC9BsB,SAAS,CAACW,OAAO,IAAG;MACnB,IAAI,CAACjE,QAAQ,CAACkE,OAAO,CAACD,OAAO,CAAC;MAC9B,IAAI,CAACF,iBAAiB,EAAE;MACxB,IAAI,CAAChC,mBAAmB,CAACoC,gBAAgB,CAAC,sBAAsB,CAAC;IACnE,CAAC,CAAC;EACN;EAEQJ,iBAAiBA,CAAA;IACvB,IAAI,CAAC/J,WAAW,GAAG,IAAI,CAACgG,QAAQ,CAACoE,MAAM,CAACC,CAAC,IAAI,CAACA,CAAC,CAACC,IAAI,CAAC,CAACvG,MAAM;IAC5D,IAAI,IAAI,CAAC/D,WAAW,GAAG,CAAC,IAAI,IAAI,CAACiI,WAAW,KAAK,eAAe,EAAE;MAChE,IAAI,CAACA,WAAW,GAAG,QAAQ;KAC5B,MAAM,IAAI,IAAI,CAACjI,WAAW,KAAK,CAAC,IAAI,IAAI,CAACiI,WAAW,KAAK,QAAQ,EAAE;MAClE,IAAI,CAACA,WAAW,GAAG,eAAe;;EAEtC;EAEQyB,YAAYA,CAAA;IAClB,IAAI,CAAC5B,cAAc,CAAC4B,YAAY,EAAE,CAC/BL,IAAI,CAAC/J,SAAS,CAAC,IAAI,CAAC0I,QAAQ,CAAC,CAAC,CAC9BsB,SAAS,CAAC;MACTJ,IAAI,EAAGlD,QAAQ,IAAI;QACjB,IAAI,CAACA,QAAQ,GAAGA,QAAQ;QACxB,IAAI,CAAC+D,iBAAiB,EAAE;MAC1B,CAAC;MACDQ,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAClD;KACD,CAAC;EACN;EAEQZ,YAAYA,CAAA;IAClB;IACA,IAAI,CAAClB,QAAQ,GAAG,CACd;MACEgC,EAAE,EAAE,GAAG;MACP/J,QAAQ,EAAE,OAAO;MACjB4C,KAAK,EAAE,mBAAmB;MAC1BE,QAAQ,EAAE;KACX,EACD;MACEiH,EAAE,EAAE,GAAG;MACP/J,QAAQ,EAAE,KAAK;MACf4C,KAAK,EAAE,iBAAiB;MACxBE,QAAQ,EAAE,KAAK;MACfkH,QAAQ,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,MAAM,CAAC,CAAC;KACzC,CACF;IACD,IAAI,CAACvG,gBAAgB,GAAG,CAAC,GAAG,IAAI,CAACoE,QAAQ,CAAC;EAC5C;EAEQmB,UAAUA,CAAA;IAChB;IACA;IACA,IAAI,CAACjC,MAAM,GAAG,CACZ;MACE8C,EAAE,EAAE,QAAQ;MACZ5G,IAAI,EAAE,WAAW;MACjBC,OAAO,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,cAAc,CAAC;MACnCE,QAAQ,EAAE;KACX,EACD;MACEyG,EAAE,EAAE,QAAQ;MACZ5G,IAAI,EAAE,QAAQ;MACdC,OAAO,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,cAAc,CAAC;MACnCE,QAAQ,EAAE;KACX,CACF;IAED;IACA;IAEA,IAAI,CAACM,cAAc,GAAG,CAAC,GAAG,IAAI,CAACqD,MAAM,CAAC;EACxC;EAEA;EACAkD,aAAaA,CAAA;IACX;IACA,IAAI,IAAI,CAACxC,eAAe,EAAE;MACxB,IAAI,CAACyC,gBAAgB,EAAE;MACvB;;IAGF,QAAQ,IAAI,CAAC7C,WAAW;MACtB,KAAK,OAAO;QACV,IAAI,CAAC4B,cAAc,EAAE;QACrB;MACF,KAAK,eAAe;QAClB,IAAI,CAACkB,mBAAmB,EAAE;QAC1B;MACF,KAAK,QAAQ;QACX,IAAI,CAACC,kBAAkB,EAAE;QACzB;MACF,KAAK,WAAW;QACd;QACA;;EAEN;EAEA;EACAC,kBAAkBA,CAACC,KAAiB;IAClCA,KAAK,CAACC,cAAc,EAAE;IAEtB;IACA,IAAI,IAAI,CAAClD,WAAW,KAAK,OAAO,EAAE;IAElC,IAAI,CAACI,eAAe,GAAG,IAAI;IAC3B,IAAI,CAAClH,mBAAmB,GAAG;MACzBC,CAAC,EAAE8J,KAAK,CAACE,OAAO;MAChB/J,CAAC,EAAE6J,KAAK,CAACG;KACV;EACH;EAEA;EACAC,kBAAkBA,CAACJ,KAAiB;IAClC;IACA,IAAI,IAAI,CAACjD,WAAW,KAAK,OAAO,EAAE;IAElC,IAAI,CAACU,cAAc,GAAG4C,UAAU,CAAC,MAAK;MACpC,MAAMC,KAAK,GAAGN,KAAK,CAACO,OAAO,CAAC,CAAC,CAAC;MAC9B,IAAI,CAACpD,eAAe,GAAG,IAAI;MAC3B,IAAI,CAAClH,mBAAmB,GAAG;QACzBC,CAAC,EAAEoK,KAAK,CAACJ,OAAO;QAChB/J,CAAC,EAAEmK,KAAK,CAACH;OACV;MAED;MACA,IAAIK,SAAS,CAACC,OAAO,EAAE;QACrBD,SAAS,CAACC,OAAO,CAAC,EAAE,CAAC;;IAEzB,CAAC,EAAE,IAAI,CAAC/C,iBAAiB,CAAC;EAC5B;EAEAgD,gBAAgBA,CAAA;IACd,IAAI,IAAI,CAACjD,cAAc,EAAE;MACvBkD,YAAY,CAAC,IAAI,CAAClD,cAAc,CAAC;MACjC,IAAI,CAACA,cAAc,GAAG,IAAI;;EAE9B;EAEAmD,iBAAiBA,CAAA;IACf;IACA,IAAI,IAAI,CAACnD,cAAc,EAAE;MACvBkD,YAAY,CAAC,IAAI,CAAClD,cAAc,CAAC;MACjC,IAAI,CAACA,cAAc,GAAG,IAAI;;EAE9B;EAEA;EACAkB,cAAcA,CAAA;IACZ,IAAI,CAAC3B,cAAc,GAAG,IAAI;IAC1B,IAAI,CAACnG,gBAAgB,GAAG;MAAErB,QAAQ,EAAE,EAAE;MAAEyB,UAAU,EAAE;IAAE,CAAE;IACxD,IAAI,CAACb,UAAU,GAAG,EAAE;EACtB;EAEAG,eAAeA,CAAA;IACb,IAAI,CAACyG,cAAc,GAAG,KAAK;IAC3B,IAAI,CAACnG,gBAAgB,GAAG;MAAErB,QAAQ,EAAE,EAAE;MAAEyB,UAAU,EAAE;IAAE,CAAE;IACxD,IAAI,CAACb,UAAU,GAAG,EAAE;EACtB;EAEAW,kBAAkBA,CAAA;IAChB;IACA,IAAI,IAAI,CAAC8J,kBAAkB,EAAE,EAAE;MAC7B,IAAI,CAACC,YAAY,EAAE;;EAEvB;EAEQD,kBAAkBA,CAAA;IACxB,MAAME,aAAa,GAAG,IAAI,CAAClK,gBAAgB,CAACrB,QAAQ,CAACqD,MAAM,IAAI,CAAC;IAChE,MAAMmI,eAAe,GAAG,IAAI,CAACnK,gBAAgB,CAACI,UAAU,CAAC4B,MAAM,IAAI,CAAC,IAC7C,OAAO,CAACoI,IAAI,CAAC,IAAI,CAACpK,gBAAgB,CAACI,UAAU,CAAC,IAC9C,OAAO,CAACgK,IAAI,CAAC,IAAI,CAACpK,gBAAgB,CAACI,UAAU,CAAC,IAC9C,OAAO,CAACgK,IAAI,CAAC,IAAI,CAACpK,gBAAgB,CAACI,UAAU,CAAC,IAC9C,cAAc,CAACgK,IAAI,CAAC,IAAI,CAACpK,gBAAgB,CAACI,UAAU,CAAC;IAE5E,OAAO8J,aAAa,IAAIC,eAAe;EACzC;EAEQF,YAAYA,CAAA;IAClB,IAAI,IAAI,CAACrJ,SAAS,EAAE;IAEpB,IAAI,CAACA,SAAS,GAAG,IAAI;IACrB,IAAI,CAACrB,UAAU,GAAG,EAAE;IAEpB,IAAI,CAACuG,WAAW,CAACuE,KAAK,CAAC,IAAI,CAACrK,gBAAgB,CAACrB,QAAQ,EAAE,IAAI,CAACqB,gBAAgB,CAACI,UAAU,CAAC,CACrFkH,IAAI,CAAC/J,SAAS,CAAC,IAAI,CAAC0I,QAAQ,CAAC,CAAC,CAC9BsB,SAAS,CAAC;MACTJ,IAAI,EAAGmD,QAAQ,IAAI;QACjB,IAAI,CAAC1J,SAAS,GAAG,KAAK;QACtB;MACF,CAAC;MACD4H,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAAC5H,SAAS,GAAG,KAAK;QACtB,IAAI,CAACrB,UAAU,GAAGiJ,KAAK,CAACN,OAAO,IAAI,uBAAuB;MAC5D;KACD,CAAC;EACN;EAEA;EACAc,mBAAmBA,CAAA;IACjB,IAAI,CAAC5C,gBAAgB,GAAG,IAAI;IAC5B,IAAI,CAAC/C,cAAc,GAAG,EAAE;IACxB,IAAI,CAACmD,iBAAiB,GAAG,EAAE;IAC3B,IAAI,CAACC,aAAa,GAAG,EAAE;IACvB,IAAI,CAACzF,WAAW,GAAG,QAAQ;IAC3B,IAAI,CAAC+B,oBAAoB,GAAG,EAAE;IAC9B,IAAI,CAACT,gBAAgB,GAAG,CAAC,GAAG,IAAI,CAACoE,QAAQ,CAAC;IAC1C,IAAI,CAACnE,cAAc,GAAG,CAAC,GAAG,IAAI,CAACqD,MAAM,CAAC;IACtC,IAAI,CAACM,WAAW,GAAG,WAAW;EAChC;EAEAxD,oBAAoBA,CAAA;IAClB,IAAI,CAAC0D,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAAC/C,cAAc,GAAG,EAAE;IACxB,IAAI,CAACmD,iBAAiB,GAAG,EAAE;IAC3B,IAAI,CAACC,aAAa,GAAG,EAAE;IACvB,IAAI,CAAC1D,oBAAoB,GAAG,EAAE;IAC9B,IAAI,CAACmD,WAAW,GAAG,eAAe;EACpC;EAEA3C,WAAWA,CAAA;IACT,IAAI,CAAC,IAAI,CAACF,cAAc,CAACkH,IAAI,EAAE,EAAE;IAEjC;IACA,IAAI,IAAI,CAACvJ,WAAW,KAAK,QAAQ,IAAI,CAAC,IAAI,CAACwF,iBAAiB,EAAE;MAC5D,IAAI,CAACR,mBAAmB,CAACoC,gBAAgB,CAAC,2BAA2B,EAAE,SAAS,CAAC;MACjF;;IAGF,IAAI,IAAI,CAACpH,WAAW,KAAK,OAAO,IAAI,CAAC,IAAI,CAACyF,aAAa,EAAE;MACvD,IAAI,CAACT,mBAAmB,CAACoC,gBAAgB,CAAC,uBAAuB,EAAE,SAAS,CAAC;MAC7E;;IAGF,MAAMF,OAAO,GAAqB;MAChCnE,OAAO,EAAE,IAAI,CAACV,cAAc,CAACkH,IAAI,EAAE;MACnCzG,SAAS,EAAE,IAAI8E,IAAI,EAAE;MACrBhF,MAAM,EAAE,IAAI,CAAClF,WAAW,EAAEC,QAAQ,IAAI,SAAS;MAC/C6L,SAAS,EAAE,IAAI,CAACxJ,WAAW,KAAK,QAAQ,GAAG,IAAI,CAACwF,iBAAiB,GAAGiE,SAAS;MAC7EC,OAAO,EAAE,IAAI,CAAC1J,WAAW,KAAK,OAAO,GAAG,IAAI,CAACyF,aAAa,GAAGgE;KAC9D;IAED,IAAI,CAAC1E,cAAc,CAACxC,WAAW,CAAC2E,OAAO,CAAC,CACrCZ,IAAI,CAAC/J,SAAS,CAAC,IAAI,CAAC0I,QAAQ,CAAC,CAAC,CAC9BsB,SAAS,CAAC;MACTJ,IAAI,EAAEA,CAAA,KAAK;QACT,IAAI,CAACzE,oBAAoB,EAAE;QAC3B,IAAI,CAACsD,mBAAmB,CAACoC,gBAAgB,CAAC,cAAc,CAAC;MAC3D,CAAC;MACDI,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;QAC/C,IAAI,CAACxC,mBAAmB,CAACoC,gBAAgB,CAAC,wBAAwB,EAAE,OAAO,CAAC;MAC9E;KACD,CAAC;EACN;EAEA;EACAnF,uBAAuBA,CAAA;IACrB,MAAM0H,KAAK,GAAG,IAAI,CAAC5H,oBAAoB,CAAC6H,WAAW,EAAE;IAErD,IAAI,IAAI,CAAC5J,WAAW,KAAK,QAAQ,EAAE;MACjC,IAAI,CAACsB,gBAAgB,GAAG,IAAI,CAACoE,QAAQ,CAAC2B,MAAM,CAACwC,OAAO,IAClDA,OAAO,CAAClM,QAAQ,CAACiM,WAAW,EAAE,CAACE,QAAQ,CAACH,KAAK,CAAC,IAC9CE,OAAO,CAACtJ,KAAK,CAACqJ,WAAW,EAAE,CAACE,QAAQ,CAACH,KAAK,CAAC,CAC5C;KACF,MAAM;MACL,IAAI,CAACpI,cAAc,GAAG,IAAI,CAACqD,MAAM,CAACyC,MAAM,CAAC0C,KAAK,IAC5CA,KAAK,CAACjJ,IAAI,CAAC8I,WAAW,EAAE,CAACE,QAAQ,CAACH,KAAK,CAAC,CACzC;;EAEL;EAEArJ,aAAaA,CAACuJ,OAAgB;IAC5B,IAAI,CAACrE,iBAAiB,GAAGqE,OAAO,CAACnC,EAAE;IACnC,IAAI,CAAC3F,oBAAoB,GAAG8H,OAAO,CAAClM,QAAQ;IAC5C,IAAI,CAAC2D,gBAAgB,GAAG,EAAE;EAC5B;EAEAT,WAAWA,CAACkJ,KAAY;IACtB,IAAI,CAACtE,aAAa,GAAGsE,KAAK,CAACrC,EAAE;IAC7B,IAAI,CAAC3F,oBAAoB,GAAGgI,KAAK,CAACjJ,IAAI;IACtC,IAAI,CAACS,cAAc,GAAG,EAAE;EAC1B;EAEAxB,iBAAiBA,CAACiK,IAAwB;IACxC,IAAI,CAAChK,WAAW,GAAGgK,IAAI;IACvB,IAAI,CAACxE,iBAAiB,GAAG,EAAE;IAC3B,IAAI,CAACC,aAAa,GAAG,EAAE;IACvB,IAAI,CAAC1D,oBAAoB,GAAG,EAAE;IAC9B,IAAI,CAACE,uBAAuB,EAAE;EAChC;EAEAhC,wBAAwBA,CAAA;IACtB,IAAI,IAAI,CAACD,WAAW,KAAK,QAAQ,IAAI,IAAI,CAACwF,iBAAiB,EAAE;MAC3D,MAAMqE,OAAO,GAAG,IAAI,CAACnE,QAAQ,CAACuE,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACxC,EAAE,KAAK,IAAI,CAAClC,iBAAiB,CAAC;MACxE,OAAOqE,OAAO,EAAElM,QAAQ,IAAI,SAAS;;IAGvC,IAAI,IAAI,CAACqC,WAAW,KAAK,OAAO,IAAI,IAAI,CAACyF,aAAa,EAAE;MACtD,MAAMsE,KAAK,GAAG,IAAI,CAACnF,MAAM,CAACqF,IAAI,CAACE,CAAC,IAAIA,CAAC,CAACzC,EAAE,KAAK,IAAI,CAACjC,aAAa,CAAC;MAChE,OAAOsE,KAAK,EAAEjJ,IAAI,IAAI,eAAe;;IAGvC,OAAO,EAAE;EACX;EAEA4B,cAAcA,CAAA;IACZ,MAAM0H,UAAU,GAAG,IAAI,CAAC/H,cAAc,CAACkH,IAAI,EAAE,CAACvI,MAAM,GAAG,CAAC;IACxD,MAAMqJ,YAAY,GAAG,IAAI,CAACrK,WAAW,KAAK,QAAQ,GAAG,CAAC,CAAC,IAAI,CAACwF,iBAAiB,GAAG,CAAC,CAAC,IAAI,CAACC,aAAa;IACpG,OAAO2E,UAAU,IAAIC,YAAY;EACnC;EAEA;EACApC,kBAAkBA,CAAA;IAChB,IAAI,CAAC5C,iBAAiB,GAAG,IAAI;IAC7B,IAAI,CAACiF,kBAAkB,EAAE;EAC3B;EAEAjH,mBAAmBA,CAAA;IACjB,IAAI,CAACgC,iBAAiB,GAAG,KAAK;EAChC;EAEQiF,kBAAkBA,CAAA;IACxB,IAAI,CAACvF,cAAc,CAACwF,aAAa,EAAE,CAChCjE,IAAI,CAAC/J,SAAS,CAAC,IAAI,CAAC0I,QAAQ,CAAC,CAAC,CAC9BsB,SAAS,CAAC,MAAK;MACd,IAAI,CAACS,iBAAiB,EAAE;IAC1B,CAAC,CAAC;EACN;EAEA;EACAe,gBAAgBA,CAAA;IACd,IAAI,CAACzC,eAAe,GAAG,KAAK;EAC9B;EAEArH,mBAAmBA,CAAA;IACjB,IAAI,CAACsH,mBAAmB,GAAG,IAAI;IAC/B,IAAI,CAACD,eAAe,GAAG,KAAK;IAC5B,IAAI,CAACkF,eAAe,EAAE;EACxB;EAEAtG,oBAAoBA,CAAA;IAClB,IAAI,CAACqB,mBAAmB,GAAG,KAAK;EAClC;EAEA;EACAiF,eAAeA,CAAA;IACb,IAAI,IAAI,CAAC9M,WAAW,EAAE;MACpB,IAAI,CAACgG,WAAW,GAAG;QACjBC,MAAM,EAAE,EAAE;QACVpD,KAAK,EAAE,IAAI,CAAC7C,WAAW,CAAC6C,KAAK,IAAI,EAAE;QACnCoF,KAAK,EAAE,IAAI,CAACjI,WAAW,CAACiI,KAAK,IAAI;OAClC;;EAEL;EAEAnB,cAAcA,CAAC2D,KAAY;IACzB,MAAMsC,KAAK,GAAGtC,KAAK,CAACuC,MAA0B;IAC9C,IAAID,KAAK,CAACE,KAAK,IAAIF,KAAK,CAACE,KAAK,CAAC,CAAC,CAAC,EAAE;MACjC,MAAMC,IAAI,GAAGH,KAAK,CAACE,KAAK,CAAC,CAAC,CAAC;MAE3B;MACA,IAAI,CAACC,IAAI,CAACZ,IAAI,CAACa,UAAU,CAAC,QAAQ,CAAC,EAAE;QACnC,IAAI,CAAC7F,mBAAmB,CAACoC,gBAAgB,CAAC,6BAA6B,EAAE,OAAO,CAAC;QACjF;;MAGF;MACA,IAAIwD,IAAI,CAACE,IAAI,GAAG,CAAC,GAAG,IAAI,GAAG,IAAI,EAAE;QAC/B,IAAI,CAAC9F,mBAAmB,CAACoC,gBAAgB,CAAC,gCAAgC,EAAE,OAAO,CAAC;QACpF;;MAGF,MAAM2D,MAAM,GAAG,IAAIC,UAAU,EAAE;MAC/BD,MAAM,CAACE,MAAM,GAAIC,CAAC,IAAI;QACpB,IAAI,CAACxH,WAAW,CAACC,MAAM,GAAGuH,CAAC,CAACR,MAAM,EAAES,MAAgB;QACpD,IAAI,CAACC,gBAAgB,EAAE;MACzB,CAAC;MACDL,MAAM,CAACM,aAAa,CAACT,IAAI,CAAC;;EAE9B;EAEAQ,gBAAgBA,CAAA;IACd;IACA,IAAI,CAACpG,mBAAmB,CAACoC,gBAAgB,CAAC,6BAA6B,EAAE,SAAS,CAAC;EACrF;EAEA;EACA5J,MAAMA,CAAA;IACJ,IAAI,CAACuK,gBAAgB,EAAE;IACvB,IAAI,CAACjD,WAAW,CAACtH,MAAM,EAAE;EAC3B;EAEA;EAEA8N,SAASA,CAACnD,KAAoB;IAC5B;IACA,IAAIA,KAAK,CAACoD,GAAG,KAAK,QAAQ,EAAE;MAC1B,IAAI,CAACC,cAAc,EAAE;;IAGvB;IACA,IAAIrD,KAAK,CAACoD,GAAG,KAAK,OAAO,IAAI,IAAI,CAACpG,cAAc,EAAE;MAChD,IAAI,IAAI,CAAC6D,kBAAkB,EAAE,EAAE;QAC7B,IAAI,CAACC,YAAY,EAAE;;;IAIvB;IACA,IAAId,KAAK,CAACoD,GAAG,KAAK,OAAO,IAAI,IAAI,CAACnG,gBAAgB,IAAI,CAAC+C,KAAK,CAACsD,QAAQ,EAAE;MACrEtD,KAAK,CAACC,cAAc,EAAE;MACtB,IAAI,CAAC7F,WAAW,EAAE;;EAEtB;EAEA;EAEAmJ,eAAeA,CAACvD,KAAY;IAC1B;IACA,IAAI,IAAI,CAAC7C,eAAe,EAAE;MACxB,IAAI,CAACyC,gBAAgB,EAAE;;EAE3B;EAEQyD,cAAcA,CAAA;IACpB,IAAI,CAACrG,cAAc,GAAG,KAAK;IAC3B,IAAI,CAACC,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAACC,iBAAiB,GAAG,KAAK;IAC9B,IAAI,CAACC,eAAe,GAAG,KAAK;IAC5B,IAAI,CAACC,mBAAmB,GAAG,KAAK;IAChC,IAAI,IAAI,CAACL,WAAW,KAAK,WAAW,EAAE;MACpC,IAAI,CAACA,WAAW,GAAG,eAAe;;EAEtC;EAEA;EACAyG,cAAcA,CAAA;IACZ,OAAO,UAAU,IAAI,CAACzG,WAAW,EAAE;EACrC;EAEA0G,cAAcA,CAAA;IACZ,QAAQ,IAAI,CAAC1G,WAAW;MACtB,KAAK,OAAO;QAAE,OAAO,kBAAkB;MACvC,KAAK,eAAe;QAAE,OAAO,0BAA0B;MACvD,KAAK,QAAQ;QAAE,OAAO,iBAAiB,IAAI,CAACjI,WAAW,oBAAoB;MAC3E,KAAK,WAAW;QAAE,OAAO,sBAAsB;MAC/C;QAAS,OAAO,EAAE;;EAEtB;EAEAiG,YAAYA,CAAC2I,KAAa,EAAE3E,OAAgB;IAC1C,OAAOA,OAAO,CAACQ,EAAE;EACnB;;;uBA1jBW/C,gBAAgB,EAAAlI,EAAA,CAAAqP,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAvP,EAAA,CAAAqP,iBAAA,CAAAG,EAAA,CAAAC,cAAA,GAAAzP,EAAA,CAAAqP,iBAAA,CAAAK,EAAA,CAAAC,mBAAA;IAAA;EAAA;;;YAAhBzH,gBAAgB;MAAA0H,SAAA;MAAAC,YAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAhB/P,EAAA,CAAAS,UAAA,qBAAAwP,4CAAA7O,MAAA;YAAA,OAAA4O,GAAA,CAAAnB,SAAA,CAAAzN,MAAA,CAAiB;UAAA,UAAApB,EAAA,CAAAkQ,iBAAA,CAAD,mBAAAC,0CAAA/O,MAAA;YAAA,OAAhB4O,GAAA,CAAAf,eAAA,CAAA7N,MAAA,CAAuB;UAAA,UAAApB,EAAA,CAAAkQ,iBAAA,CAAP;;;;;;;;;;UC7CzBlQ,EAHJ,CAAAE,cAAA,aAA2B,aAEK,aAU3B;UADCF,EAJA,CAAAS,UAAA,mBAAA2P,+CAAA;YAAA,OAASJ,GAAA,CAAA3E,aAAA,EAAe;UAAA,EAAC,yBAAAgF,qDAAAjP,MAAA;YAAA,OACV4O,GAAA,CAAAvE,kBAAA,CAAArK,MAAA,CAA0B;UAAA,EAAC,wBAAAkP,oDAAAlP,MAAA;YAAA,OAC5B4O,GAAA,CAAAlE,kBAAA,CAAA1K,MAAA,CAA0B;UAAA,EAAC,sBAAAmP,kDAAA;YAAA,OAC7BP,GAAA,CAAA5D,gBAAA,EAAkB;UAAA,EAAC,uBAAAoE,mDAAA;YAAA,OAClBR,GAAA,CAAA1D,iBAAA,EAAmB;UAAA,EAAC;UAEjCtM,EAAA,CAAAE,cAAA,aAA0B;UAExBF,EADA,CAAA6C,UAAA,IAAA4N,+BAAA,iBAAyD,IAAAC,+BAAA,iBACM;UAKrE1Q,EAFI,CAAAI,YAAA,EAAM,EACF,EACF;UASNJ,EANA,CAAA6C,UAAA,IAAA8N,+BAAA,iBAA2C,IAAAC,+BAAA,kBAY1C;UAWH5Q,EAAA,CAAAI,YAAA,EAAM;UA4UNJ,EAzUA,CAAA6C,UAAA,IAAAgO,+BAAA,kBAA8E,IAAAC,+BAAA,mBAoDO,KAAAC,gCAAA,kBAsJA,KAAAC,gCAAA,kBA8BG,KAAAC,gCAAA,iBAiG+B;;;UArXjHjR,EAAA,CAAAK,SAAA,GAA0B;UAA1BL,EAAA,CAAAkR,UAAA,CAAAlB,GAAA,CAAAd,cAAA,GAA0B;UAC1BlP,EAAA,CAAAkD,UAAA,UAAA8M,GAAA,CAAAb,cAAA,GAA0B;UAQEnP,EAAA,CAAAK,SAAA,GAA6B;UAA7BL,EAAA,CAAAkD,UAAA,SAAA8M,GAAA,CAAAvH,WAAA,aAA6B;UACxBzI,EAAA,CAAAK,SAAA,EAA8B;UAA9BL,EAAA,CAAAkD,UAAA,SAAA8M,GAAA,CAAAvH,WAAA,cAA8B;UAQ3CzI,EAAA,CAAAK,SAAA,EAAiB;UAAjBL,EAAA,CAAAkD,UAAA,SAAA8M,GAAA,CAAA/O,WAAA,CAAiB;UAQtCjB,EAAA,CAAAK,SAAA,EAAqB;UAArBL,EAAA,CAAAkD,UAAA,SAAA8M,GAAA,CAAAnH,eAAA,CAAqB;UAkBE7I,EAAA,CAAAK,SAAA,EAAoB;UAApBL,EAAA,CAAAkD,UAAA,SAAA8M,GAAA,CAAAtH,cAAA,CAAoB;UAoDpB1I,EAAA,CAAAK,SAAA,EAAsB;UAAtBL,EAAA,CAAAkD,UAAA,SAAA8M,GAAA,CAAArH,gBAAA,CAAsB;UAsJtB3I,EAAA,CAAAK,SAAA,EAAuB;UAAvBL,EAAA,CAAAkD,UAAA,SAAA8M,GAAA,CAAApH,iBAAA,CAAuB;UA8BvB5I,EAAA,CAAAK,SAAA,EAAyB;UAAzBL,EAAA,CAAAkD,UAAA,SAAA8M,GAAA,CAAAlH,mBAAA,CAAyB;UAiGxB9I,EAAA,CAAAK,SAAA,EAAwF;UAAxFL,EAAA,CAAAkD,UAAA,UAAA8M,GAAA,CAAAtH,cAAA,KAAAsH,GAAA,CAAArH,gBAAA,KAAAqH,GAAA,CAAApH,iBAAA,KAAAoH,GAAA,CAAAlH,mBAAA,CAAwF;;;qBD9UzGnJ,YAAY,EAAAwR,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAAF,EAAA,CAAAG,QAAA,EAAE1R,WAAW,EAAA2R,EAAA,CAAAC,oBAAA,EAAAD,EAAA,CAAAE,eAAA,EAAAF,EAAA,CAAAG,kBAAA,EAAAH,EAAA,CAAAI,OAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}