import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable, Subject, map, tap, catchError, throwError } from 'rxjs';
import { ApiService } from './api.service';
import { WebSocketService } from './websocket.service';

export interface Message {
  id: string;
  content: string;
  timestamp: Date;
  sender: string;
  recipient?: string;
  groupId?: string;
  read?: boolean;
  encrypted?: boolean;
}

@Injectable({
  providedIn: 'root'
})
export class MessageService {
  private messagesSubject = new BehaviorSubject<Message[]>([]);
  private newMessageSubject = new Subject<Message>();

  public messages$ = this.messagesSubject.asObservable();
  public newMessage$ = this.newMessageSubject.asObservable();

  constructor(
    private apiService: ApiService,
    private webSocketService: WebSocketService
  ) {
    this.initializeWebSocketListeners();
  }

  private initializeWebSocketListeners(): void {
    // Listen for incoming messages via WebSocket
    this.webSocketService.on('message').subscribe((message: Message) => {
      this.addMessage(message);
      this.newMessageSubject.next(message);
    });

    // Listen for message status updates
    this.webSocketService.on('messageStatus').subscribe((status: any) => {
      this.updateMessageStatus(status.messageId, status);
    });
  }

  /**
   * Load messages from the server
   */
  loadMessages(): Observable<Message[]> {
    return this.apiService.get<Message[]>('/messages').pipe(
      map(messages => messages.map(msg => ({
        ...msg,
        timestamp: new Date(msg.timestamp),
        read: msg.read || false
      }))),
      tap(messages => {
        this.messagesSubject.next(messages);
      }),
      catchError(error => {
        console.error('Failed to load messages:', error);
        return throwError(() => error);
      })
    );
  }

  /**
   * Send a new message
   */
  sendMessage(message: Partial<Message>): Observable<Message> {
    const messageToSend = {
      ...message,
      timestamp: new Date(),
      id: this.generateMessageId()
    };

    return this.apiService.post<Message>('/messages', messageToSend).pipe(
      map(response => ({
        ...response,
        timestamp: new Date(response.timestamp)
      })),
      tap(sentMessage => {
        this.addMessage(sentMessage);
      }),
      catchError(error => {
        console.error('Failed to send message:', error);
        return throwError(() => error);
      })
    );
  }

  /**
   * Mark all messages as read
   */
  markAllAsRead(): Observable<void> {
    return this.apiService.post<void>('/messages/mark-read', {}).pipe(
      tap(() => {
        const currentMessages = this.messagesSubject.value;
        const updatedMessages = currentMessages.map(msg => ({
          ...msg,
          read: true
        }));
        this.messagesSubject.next(updatedMessages);
      }),
      catchError(error => {
        console.error('Failed to mark messages as read:', error);
        return throwError(() => error);
      })
    );
  }

  /**
   * Mark a specific message as read
   */
  markMessageAsRead(messageId: string): Observable<void> {
    return this.apiService.post<void>(`/messages/${messageId}/read`, {}).pipe(
      tap(() => {
        this.updateMessageStatus(messageId, { read: true });
      }),
      catchError(error => {
        console.error('Failed to mark message as read:', error);
        return throwError(() => error);
      })
    );
  }

  /**
   * Delete a message
   */
  deleteMessage(messageId: string): Observable<void> {
    return this.apiService.delete<void>(`/messages/${messageId}`).pipe(
      tap(() => {
        const currentMessages = this.messagesSubject.value;
        const updatedMessages = currentMessages.filter(msg => msg.id !== messageId);
        this.messagesSubject.next(updatedMessages);
      }),
      catchError(error => {
        console.error('Failed to delete message:', error);
        return throwError(() => error);
      })
    );
  }

  /**
   * Get unread message count
   */
  getUnreadCount(): number {
    return this.messagesSubject.value.filter(msg => !msg.read).length;
  }

  /**
   * Clear all messages (for logout)
   */
  clearMessages(): void {
    this.messagesSubject.next([]);
  }

  /**
   * Add a message to the local store
   */
  private addMessage(message: Message): void {
    const currentMessages = this.messagesSubject.value;
    const messageExists = currentMessages.some(msg => msg.id === message.id);
    
    if (!messageExists) {
      const updatedMessages = [message, ...currentMessages];
      this.messagesSubject.next(updatedMessages);
    }
  }

  /**
   * Update message status
   */
  private updateMessageStatus(messageId: string, status: Partial<Message>): void {
    const currentMessages = this.messagesSubject.value;
    const updatedMessages = currentMessages.map(msg => 
      msg.id === messageId ? { ...msg, ...status } : msg
    );
    this.messagesSubject.next(updatedMessages);
  }

  /**
   * Generate a unique message ID
   */
  private generateMessageId(): string {
    return `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Search messages
   */
  searchMessages(query: string): Observable<Message[]> {
    return this.messages$.pipe(
      map(messages => messages.filter(msg => 
        msg.content.toLowerCase().includes(query.toLowerCase()) ||
        msg.sender.toLowerCase().includes(query.toLowerCase())
      ))
    );
  }

  /**
   * Get messages for a specific group
   */
  getGroupMessages(groupId: string): Observable<Message[]> {
    return this.messages$.pipe(
      map(messages => messages.filter(msg => msg.groupId === groupId))
    );
  }

  /**
   * Get direct messages with a specific user
   */
  getDirectMessages(userId: string): Observable<Message[]> {
    return this.messages$.pipe(
      map(messages => messages.filter(msg => 
        msg.recipient === userId || msg.sender === userId
      ))
    );
  }
}
