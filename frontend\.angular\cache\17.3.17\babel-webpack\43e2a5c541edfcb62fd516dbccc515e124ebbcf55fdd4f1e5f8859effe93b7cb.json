{"ast": null, "code": "import { map } from './map';\nexport function pluck(...properties) {\n  const length = properties.length;\n  if (length === 0) {\n    throw new Error('list of properties cannot be empty.');\n  }\n  return map(x => {\n    let currentProp = x;\n    for (let i = 0; i < length; i++) {\n      const p = currentProp === null || currentProp === void 0 ? void 0 : currentProp[properties[i]];\n      if (typeof p !== 'undefined') {\n        currentProp = p;\n      } else {\n        return undefined;\n      }\n    }\n    return currentProp;\n  });\n}", "map": {"version": 3, "names": ["map", "pluck", "properties", "length", "Error", "x", "currentProp", "i", "p", "undefined"], "sources": ["D:/TCL1/Projects/Projects/QSC1/frontend/node_modules/rxjs/dist/esm/internal/operators/pluck.js"], "sourcesContent": ["import { map } from './map';\nexport function pluck(...properties) {\n    const length = properties.length;\n    if (length === 0) {\n        throw new Error('list of properties cannot be empty.');\n    }\n    return map((x) => {\n        let currentProp = x;\n        for (let i = 0; i < length; i++) {\n            const p = currentProp === null || currentProp === void 0 ? void 0 : currentProp[properties[i]];\n            if (typeof p !== 'undefined') {\n                currentProp = p;\n            }\n            else {\n                return undefined;\n            }\n        }\n        return currentProp;\n    });\n}\n"], "mappings": "AAAA,SAASA,GAAG,QAAQ,OAAO;AAC3B,OAAO,SAASC,KAAKA,CAAC,GAAGC,UAAU,EAAE;EACjC,MAAMC,MAAM,GAAGD,UAAU,CAACC,MAAM;EAChC,IAAIA,MAAM,KAAK,CAAC,EAAE;IACd,MAAM,IAAIC,KAAK,CAAC,qCAAqC,CAAC;EAC1D;EACA,OAAOJ,GAAG,CAAEK,CAAC,IAAK;IACd,IAAIC,WAAW,GAAGD,CAAC;IACnB,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,MAAM,EAAEI,CAAC,EAAE,EAAE;MAC7B,MAAMC,CAAC,GAAGF,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,WAAW,CAACJ,UAAU,CAACK,CAAC,CAAC,CAAC;MAC9F,IAAI,OAAOC,CAAC,KAAK,WAAW,EAAE;QAC1BF,WAAW,GAAGE,CAAC;MACnB,CAAC,MACI;QACD,OAAOC,SAAS;MACpB;IACJ;IACA,OAAOH,WAAW;EACtB,CAAC,CAAC;AACN", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}