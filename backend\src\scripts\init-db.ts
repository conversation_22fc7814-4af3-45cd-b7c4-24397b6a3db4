import { NestFactory } from '@nestjs/core';
import { AppModule } from '../app.module';
import { DatabaseService } from '../database/database.service';
import * as crypto from 'crypto';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);
  const dbService = app.get(DatabaseService);

  try {
    // Verify database integrity
    const isIntegrityValid = await dbService.verifyDatabaseIntegrity();
    console.log('Database integrity check:', isIntegrityValid ? 'PASSED' : 'FAILED');

    if (!isIntegrityValid) {
      console.error('Database integrity check failed. Please check your encryption key.');
      process.exit(1);
    }

    console.log('Database initialized successfully with encryption.');
  } catch (error) {
    console.error('Failed to initialize database:', error);
    process.exit(1);
  } finally {
    await app.close();
  }
}

bootstrap(); 