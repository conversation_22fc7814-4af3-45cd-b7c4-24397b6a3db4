{"ast": null, "code": "import { signal } from '@angular/core';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/platform-browser\";\nexport let StateService = /*#__PURE__*/(() => {\n  class StateService {\n    constructor(sanitizer) {\n      this.sanitizer = sanitizer;\n      this._messages = signal([]);\n      this._isAuthenticated = signal(false);\n      this._attempts = signal(0);\n      // Messages state\n      this.messages = this._messages.asReadonly();\n      this.isAuthenticated = this._isAuthenticated.asReadonly();\n      this.attempts = this._attempts.asReadonly();\n    }\n    addMessage(message) {\n      this._messages.update(messages => [...messages, message]);\n    }\n    removeMessage(id) {\n      this._messages.update(messages => messages.filter(m => m.id !== id));\n    }\n    clearMessages() {\n      this._messages.set([]);\n    }\n    // Authentication state\n    setAuthenticated(value) {\n      this._isAuthenticated.set(value);\n    }\n    incrementAttempts() {\n      this._attempts.update(attempts => attempts + 1);\n    }\n    resetAttempts() {\n      this._attempts.set(0);\n    }\n    // Security utilities\n    sanitizeContent(content) {\n      return this.sanitizer.bypassSecurityTrustHtml(content);\n    }\n    static {\n      this.ɵfac = function StateService_Factory(t) {\n        return new (t || StateService)(i0.ɵɵinject(i1.DomSanitizer));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: StateService,\n        factory: StateService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return StateService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}