{"ast": null, "code": "/**\n * This file includes polyfills needed by Angular and is loaded before the app.\n * You can add your own extra polyfills to this file.\n *\n * This file is divided into 2 sections:\n *   1. Browser polyfills. These are applied before loading ZoneJS and are sorted by browsers.\n *   2. Application imports. Files imported after ZoneJS that should be loaded before your main\n *      file.\n *\n * The current setup is for so-called \"evergreen\" browsers; the last versions of browsers that\n * automatically update themselves. This includes recent versions of Safari, Chrome (including\n * Opera), Edge, and Firefox.\n */\n/***************************************************************************************************\n * Load `$localize` onto the global scope - used if i18n tags appear in Angular templates.\n */\n// import '@angular/localize/init'; // Commented out - not needed for this project\n/**\n * Zone JS is required by default for Angular itself.\n */\nimport 'zone.js'; // Included with Angular CLI.\n/***************************************************************************************************\n * APPLICATION IMPORTS\n */", "map": {"version": 3, "names": [], "sources": ["D:\\TCL1\\Projects\\Projects\\QSC1\\frontend\\src\\polyfills.ts"], "sourcesContent": ["/**\r\n * This file includes polyfills needed by Angular and is loaded before the app.\r\n * You can add your own extra polyfills to this file.\r\n *\r\n * This file is divided into 2 sections:\r\n *   1. Browser polyfills. These are applied before loading ZoneJS and are sorted by browsers.\r\n *   2. Application imports. Files imported after ZoneJS that should be loaded before your main\r\n *      file.\r\n *\r\n * The current setup is for so-called \"evergreen\" browsers; the last versions of browsers that\r\n * automatically update themselves. This includes recent versions of Safari, Chrome (including\r\n * Opera), Edge, and Firefox.\r\n */\r\n\r\n/***************************************************************************************************\r\n * Load `$localize` onto the global scope - used if i18n tags appear in Angular templates.\r\n */\r\n// import '@angular/localize/init'; // Commented out - not needed for this project\r\n\r\n/**\r\n * Zone JS is required by default for Angular itself.\r\n */\r\nimport 'zone.js';  // Included with Angular CLI.\r\n\r\n/***************************************************************************************************\r\n * APPLICATION IMPORTS\r\n */\r\n"], "mappings": "AAAA;;;;;;;;;;;;;AAcA;;;AAGA;AAEA;;;AAGA,OAAO,SAAS,CAAC,CAAE;AAEnB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}