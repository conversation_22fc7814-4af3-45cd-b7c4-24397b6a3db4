/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = function(knex) {
  return knex.schema.createTable('groups', function(table) {
    table.uuid('id').primary();
    table.uuid('owner_id').references('id').inTable('users').onDelete('CASCADE');
    table.string('onion_address').notNullable();
    table.text('encrypted_config').notNullable();
    table.text('master_key').notNullable();
    table.string('owner_device_hash').notNullable();
    table.timestamp('created_at').defaultTo(knex.fn.now());
    table.timestamp('last_key_rotation').defaultTo(knex.fn.now());
    table.boolean('is_active').defaultTo(true);
    table.integer('security_threshold').notNullable();

    // Index for faster lookups
    table.index(['owner_id', 'is_active']);
    table.index('onion_address');
  });
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function(knex) {
  return knex.schema.dropTable('groups');
}; 