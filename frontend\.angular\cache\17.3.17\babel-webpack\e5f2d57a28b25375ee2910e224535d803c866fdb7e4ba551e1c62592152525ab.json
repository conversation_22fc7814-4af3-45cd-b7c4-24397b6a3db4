{"ast": null, "code": "import { authGuard, loginGuard } from './guards/auth.guard';\nexport const routes = [{\n  path: '',\n  redirectTo: 'login',\n  pathMatch: 'full'\n}, {\n  path: 'login',\n  loadComponent: () => import('./components/login/login.component').then(m => m.LoginComponent),\n  canActivate: [loginGuard]\n}, {\n  path: 'home',\n  loadComponent: () => import('./components/home/<USER>').then(m => m.HomeComponent),\n  canActivate: [authGuard]\n}, {\n  path: 'main',\n  loadComponent: () => import('./components/main-screen/main-screen.component').then(m => m.MainScreenComponent),\n  canActivate: [authGuard]\n}, {\n  path: 'key-management',\n  loadComponent: () => import('./components/key-management/key-management.component').then(m => m.KeyManagementComponent),\n  canActivate: [authGuard]\n}, {\n  path: '**',\n  redirectTo: 'login'\n}];", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}