import { Injectable, Logger } from '@nestjs/common';
import { createCipheriv, createDecipheriv, randomBytes } from 'crypto';
import { KeyManagementService } from './key-management.service';
import * as sqlite3 from 'better-sqlite3';
import * as path from 'path';

interface EncryptedData {
  id: number;
  data: string;
  iv: string;
  auth_tag: string;
}

@Injectable()
export class EncryptionService {
  private readonly logger = new Logger(EncryptionService.name);
  private readonly algorithm = 'aes-256-gcm';
  private readonly keyLength = 32; // 256 bits
  private readonly ivLength = 12;
  private db: sqlite3.Database;

  constructor(private readonly keyManagementService: KeyManagementService) {
    this.initializeDatabase();
  }

  private async initializeDatabase() {
    try {
      const dbPath = process.env.DB_PATH || path.join(process.cwd(), 'data', 'quantumshield.db');
      this.db = new sqlite3(dbPath);

      // Try to enable SQLCipher if available (optional for development)
      try {
        this.db.pragma('cipher = aes-256-gcm');
        this.db.pragma('kdf_iter = 64000');
        this.db.pragma('hmac_algorithm = HMAC_SHA256');
        this.db.pragma('page_size = 4096');
        this.logger.log('SQLCipher encryption enabled');
      } catch (error) {
        this.logger.warn('SQLCipher not available, using standard SQLite (development only)');
      }
    } catch (error) {
      this.logger.error('Failed to initialize encryption database:', error);
      // Don't fail the application startup for encryption service issues
    }
  }

  async setDatabaseKey(key: string) {
    this.db.pragma(`key = '${key}'`);
  }

  async encrypt(data: string, key: string): Promise<{
    encryptedData: string;
    iv: string;
    authTag: string;
  }> {
    const iv = randomBytes(this.ivLength);
    const cipher = createCipheriv(this.algorithm, Buffer.from(key, 'base64'), iv);
    
    const encryptedData = Buffer.concat([
      cipher.update(data, 'utf8'),
      cipher.final()
    ]);

    return {
      encryptedData: encryptedData.toString('base64'),
      iv: iv.toString('base64'),
      authTag: cipher.getAuthTag().toString('base64')
    };
  }

  async decrypt(
    encryptedData: string,
    key: string,
    iv: string,
    authTag: string
  ): Promise<string> {
    const decipher = createDecipheriv(
      this.algorithm,
      Buffer.from(key, 'base64'),
      Buffer.from(iv, 'base64')
    );

    decipher.setAuthTag(Buffer.from(authTag, 'base64'));

    const decrypted = Buffer.concat([
      decipher.update(Buffer.from(encryptedData, 'base64')),
      decipher.final()
    ]);

    return decrypted.toString('utf8');
  }

  async rotateDatabaseKey(oldKey: string): Promise<string> {
    const masterKey = process.env.MASTER_KEY;
    if (!masterKey) {
      throw new Error('MASTER_KEY environment variable is not set');
    }

    const { newKey, encryptedNewKey, nonce } = await this.keyManagementService.rotateKey(
      oldKey,
      masterKey
    );

    // Re-encrypt all data with the new key
    await this.reencryptDatabaseData(oldKey, newKey);

    return newKey;
  }

  private async reencryptDatabaseData(oldKey: string, newKey: string) {
    // Get all encrypted data
    const rows = this.db.prepare('SELECT * FROM encrypted_data').all() as EncryptedData[];

    for (const row of rows) {
      // Decrypt with old key
      const decrypted = await this.decrypt(
        row.data,
        oldKey,
        row.iv,
        row.auth_tag
      );

      // Encrypt with new key
      const { encryptedData, iv, authTag } = await this.encrypt(decrypted, newKey);

      // Update in database
      this.db.prepare(`
        UPDATE encrypted_data 
        SET data = ?, iv = ?, auth_tag = ?
        WHERE id = ?
      `).run(encryptedData, iv, authTag, row.id);
    }
  }

  async secureWipe() {
    // Wipe sensitive data from memory
    if (this.db) {
      this.db.close();
    }
  }
} 