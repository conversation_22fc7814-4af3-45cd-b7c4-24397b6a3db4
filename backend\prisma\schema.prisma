// DEPRECATED: This Prisma schema is no longer used.
// The QSC project has standardized on TypeORM with better-sqlite3.
//
// Current user entity: backend/src/users/entities/user.entity.ts
// Database configuration: backend/src/database/data-source.ts
//
// This file is kept for reference only and should be removed
// once migration to TypeORM is fully verified.

// Legacy schema - DO NOT USE
generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// DEPRECATED: Use TypeORM User entity instead
model User {
  id        String   @id @default(uuid())
  email     String   @unique
  password  String   // DEPRECATED: Use secretWordHash in TypeORM entity
  role      Role     @default(USER)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

// DEPRECATED: Admin functionality is now handled via isAdmin flag in User entity
model Admin {
  id         String   @id @default(uuid())
  email      String   @unique
  password   String   // DEPRECATED: Use secretWordHash in TypeORM entity
  secretWord String   // DEPRECATED: Use secretWordHash in TypeORM entity
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt
}

enum Role {
  USER
  ADMIN
}