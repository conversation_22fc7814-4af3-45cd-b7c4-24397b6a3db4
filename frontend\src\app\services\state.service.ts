import { Injectable, signal } from '@angular/core';
import { DomSanitizer, SafeHtml } from '@angular/platform-browser';

export interface Message {
  id: string;
  content: string;
  timestamp: Date;
  isEncrypted: boolean;
}

@Injectable({
  providedIn: 'root'
})
export class StateService {
  private readonly _messages = signal<Message[]>([]);
  private readonly _isAuthenticated = signal<boolean>(false);
  private readonly _attempts = signal<number>(0);

  constructor(private sanitizer: DomSanitizer) {}

  // Messages state
  public readonly messages = this._messages.asReadonly();
  public readonly isAuthenticated = this._isAuthenticated.asReadonly();
  public readonly attempts = this._attempts.asReadonly();

  public addMessage(message: Message): void {
    this._messages.update(messages => [...messages, message]);
  }

  public removeMessage(id: string): void {
    this._messages.update(messages => messages.filter(m => m.id !== id));
  }

  public clearMessages(): void {
    this._messages.set([]);
  }

  // Authentication state
  public setAuthenticated(value: boolean): void {
    this._isAuthenticated.set(value);
  }

  public incrementAttempts(): void {
    this._attempts.update(attempts => attempts + 1);
  }

  public resetAttempts(): void {
    this._attempts.set(0);
  }

  // Security utilities
  public sanitizeContent(content: string): SafeHtml {
    return this.sanitizer.bypassSecurityTrustHtml(content);
  }
}
