import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateSchemaForTypeORM1703000000000 implements MigrationInterface {
  name = 'UpdateSchemaForTypeORM1703000000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Update users table to match new schema
    await queryRunner.query(`
      CREATE TABLE "users_new" (
        "id" varchar PRIMARY KEY NOT NULL,
        "username" varchar NOT NULL,
        "email" varchar,
        "publicKey" varchar,
        "isAdmin" boolean NOT NULL DEFAULT (0),
        "isActive" boolean NOT NULL DEFAULT (1),
        "createdAt" datetime NOT NULL DEFAULT (datetime('now')),
        "updatedAt" datetime NOT NULL DEFAULT (datetime('now')),
        "lastLoginAt" datetime,
        "deviceIds" text NOT NULL DEFAULT ('[]'),
        "secretWordHash" text NOT NULL,
        "isVerified" boolean NOT NULL DEFAULT (0),
        "accountStatus" varchar NOT NULL DEFAULT ('active'),
        "deactivatedBy" varchar,
        "deactivatedAt" datetime,
        "deactivationReason" varchar,
        "reinvitedBy" varchar,
        "reinvitedAt" datetime,
        "newInviteCode" varchar,
        "failedAttempts" integer NOT NULL DEFAULT (0),
        "lastAttemptAt" datetime,
        "isCompromised" boolean NOT NULL DEFAULT (0),
        CONSTRAINT "UQ_users_username" UNIQUE ("username"),
        CONSTRAINT "UQ_users_email" UNIQUE ("email")
      )
    `);

    // Copy data from old users table if it exists and has the expected schema
    try {
      const tableExists = await queryRunner.query(`
        SELECT name FROM sqlite_master WHERE type='table' AND name='users'
      `);

      if (tableExists.length > 0) {
        // Check if old table has expected columns
        const columns = await queryRunner.query(`PRAGMA table_info(users)`);
        const columnNames = columns.map((col: any) => col.name);

        if (columnNames.includes('public_key') && columnNames.includes('secret_word_hash')) {
          await queryRunner.query(`
            INSERT INTO "users_new" (
              "id", "username", "email", "publicKey", "isAdmin", "isActive",
              "createdAt", "updatedAt", "lastLoginAt", "deviceIds", "secretWordHash"
            )
            SELECT
              "id", "username", "email", "public_key", "is_admin", "is_active",
              "created_at", "updated_at", "last_login_at", "device_ids", "secret_word_hash"
            FROM "users"
          `);
        }
      }
    } catch (error) {
      // If copying fails, continue with empty table
      console.log('Note: Could not copy data from old users table, starting with fresh schema');
    }

    // Drop old table and rename new one
    await queryRunner.query(`DROP TABLE IF EXISTS "users"`);
    await queryRunner.query(`ALTER TABLE "users_new" RENAME TO "users"`);

    // Update messages table to match new schema
    await queryRunner.query(`
      CREATE TABLE "messages_new" (
        "id" varchar PRIMARY KEY NOT NULL,
        "senderId" varchar NOT NULL,
        "recipientId" varchar,
        "roomId" varchar,
        "encryptedContent" varchar NOT NULL,
        "symmetricKey" varchar,
        "iv" varchar,
        "authTag" varchar,
        "pqcSignature" varchar,
        "messageHash" varchar NOT NULL,
        "isDeleted" boolean NOT NULL DEFAULT (0),
        "deletedAt" datetime,
        "expiresAt" datetime,
        "isRead" boolean NOT NULL DEFAULT (0),
        "readAt" datetime,
        "isCompromised" boolean NOT NULL DEFAULT (0),
        "compromiseAttempts" integer NOT NULL DEFAULT (0),
        "createdAt" datetime NOT NULL DEFAULT (datetime('now')),
        "updatedAt" datetime NOT NULL DEFAULT (datetime('now')),
        CONSTRAINT "FK_messages_sender" FOREIGN KEY ("senderId") REFERENCES "users" ("id") ON DELETE CASCADE,
        CONSTRAINT "FK_messages_recipient" FOREIGN KEY ("recipientId") REFERENCES "users" ("id") ON DELETE CASCADE,
        CONSTRAINT "FK_messages_room" FOREIGN KEY ("roomId") REFERENCES "chat_rooms" ("id") ON DELETE CASCADE
      )
    `);

    // Copy data from old messages table if it exists and has the expected schema
    try {
      const messagesTableExists = await queryRunner.query(`
        SELECT name FROM sqlite_master WHERE type='table' AND name='messages'
      `);

      if (messagesTableExists.length > 0) {
        // Check if old table has expected columns
        const messageColumns = await queryRunner.query(`PRAGMA table_info(messages)`);
        const messageColumnNames = messageColumns.map((col: any) => col.name);

        if (messageColumnNames.includes('sender_id') && messageColumnNames.includes('encrypted_content')) {
          await queryRunner.query(`
            INSERT INTO "messages_new" (
              "id", "senderId", "recipientId", "roomId", "encryptedContent",
              "symmetricKey", "iv", "authTag", "pqcSignature", "messageHash",
              "isDeleted", "deletedAt", "expiresAt", "isRead", "readAt",
              "isCompromised", "compromiseAttempts", "createdAt", "updatedAt"
            )
            SELECT
              "id", "sender_id", "recipient_id", "room_id", "encrypted_content",
              "symmetric_key", "iv", "auth_tag", "pqc_signature",
              COALESCE("message_hash", ''), "is_deleted", "deleted_at", "expires_at",
              COALESCE("is_read", 0), "read_at", COALESCE("is_compromised", 0),
              COALESCE("compromise_attempts", 0), "created_at", "updated_at"
            FROM "messages"
          `);
        }
      }
    } catch (error) {
      // If copying fails, continue with empty table
      console.log('Note: Could not copy data from old messages table, starting with fresh schema');
    }

    // Drop old table and rename new one
    await queryRunner.query(`DROP TABLE IF EXISTS "messages"`);
    await queryRunner.query(`ALTER TABLE "messages_new" RENAME TO "messages"`);

    // Create groups table if it doesn't exist
    await queryRunner.query(`
      CREATE TABLE IF NOT EXISTS "groups" (
        "id" varchar PRIMARY KEY NOT NULL,
        "ownerId" varchar NOT NULL,
        "onionAddress" varchar NOT NULL,
        "encryptedConfig" varchar NOT NULL,
        "masterKey" varchar NOT NULL,
        "ownerDeviceHash" varchar NOT NULL,
        "createdAt" datetime NOT NULL DEFAULT (datetime('now')),
        "lastKeyRotation" datetime NOT NULL,
        "isActive" boolean NOT NULL DEFAULT (1),
        "securityThreshold" integer NOT NULL,
        CONSTRAINT "FK_groups_owner" FOREIGN KEY ("ownerId") REFERENCES "users" ("id") ON DELETE CASCADE
      )
    `);

    // Create group_members table if it doesn't exist
    await queryRunner.query(`
      CREATE TABLE IF NOT EXISTS "group_members" (
        "id" varchar PRIMARY KEY NOT NULL,
        "groupId" varchar NOT NULL,
        "userId" varchar NOT NULL,
        "role" varchar NOT NULL DEFAULT ('member'),
        "joinedAt" datetime NOT NULL DEFAULT (datetime('now')),
        "publicKey" varchar NOT NULL,
        "deviceHash" varchar NOT NULL,
        "isActive" boolean NOT NULL DEFAULT (1),
        CONSTRAINT "FK_group_members_group" FOREIGN KEY ("groupId") REFERENCES "groups" ("id") ON DELETE CASCADE,
        CONSTRAINT "FK_group_members_user" FOREIGN KEY ("userId") REFERENCES "users" ("id") ON DELETE CASCADE
      )
    `);

    // Create invites table if it doesn't exist
    await queryRunner.query(`
      CREATE TABLE IF NOT EXISTS "invites" (
        "id" varchar PRIMARY KEY NOT NULL,
        "email" varchar NOT NULL,
        "inviteCode" varchar NOT NULL,
        "invitedBy" varchar NOT NULL,
        "expiresAt" datetime NOT NULL,
        "isUsed" boolean NOT NULL DEFAULT (0),
        "usedAt" datetime,
        "usedBy" varchar,
        "createdAt" datetime NOT NULL DEFAULT (datetime('now')),
        CONSTRAINT "UQ_invites_inviteCode" UNIQUE ("inviteCode"),
        CONSTRAINT "FK_invites_inviter" FOREIGN KEY ("invitedBy") REFERENCES "users" ("id") ON DELETE CASCADE,
        CONSTRAINT "FK_invites_usedBy" FOREIGN KEY ("usedBy") REFERENCES "users" ("id") ON DELETE SET NULL
      )
    `);

    // Update session_keys table to match new schema
    await queryRunner.query(`
      CREATE TABLE "session_keys_new" (
        "id" varchar PRIMARY KEY NOT NULL,
        "user1Id" varchar NOT NULL,
        "user2Id" varchar NOT NULL,
        "encryptedKey" varchar NOT NULL,
        "keyHash" varchar NOT NULL,
        "expiresAt" datetime NOT NULL,
        "isActive" boolean NOT NULL DEFAULT (1),
        "createdAt" datetime NOT NULL DEFAULT (datetime('now')),
        "lastUsed" datetime NOT NULL DEFAULT (datetime('now')),
        CONSTRAINT "FK_session_keys_user1" FOREIGN KEY ("user1Id") REFERENCES "users" ("id") ON DELETE CASCADE,
        CONSTRAINT "FK_session_keys_user2" FOREIGN KEY ("user2Id") REFERENCES "users" ("id") ON DELETE CASCADE
      )
    `);

    // Copy data from old session_keys table if it exists and has the expected schema
    try {
      const sessionKeysTableExists = await queryRunner.query(`
        SELECT name FROM sqlite_master WHERE type='table' AND name='session_keys'
      `);

      if (sessionKeysTableExists.length > 0) {
        // Check if old table has expected columns
        const sessionKeyColumns = await queryRunner.query(`PRAGMA table_info(session_keys)`);
        const sessionKeyColumnNames = sessionKeyColumns.map((col: any) => col.name);

        if (sessionKeyColumnNames.includes('user1_id') && sessionKeyColumnNames.includes('encrypted_shared_key')) {
          await queryRunner.query(`
            INSERT INTO "session_keys_new" (
              "id", "user1Id", "user2Id", "encryptedKey", "keyHash",
              "expiresAt", "isActive", "createdAt", "lastUsed"
            )
            SELECT
              "id", "user1_id", "user2_id", "encrypted_shared_key",
              COALESCE("key_hash", ''), "expires_at", "is_active", "created_at", "last_used"
            FROM "session_keys"
          `);
        }
      }
    } catch (error) {
      // If copying fails, continue with empty table
      console.log('Note: Could not copy data from old session_keys table, starting with fresh schema');
    }

    // Drop old table and rename new one
    await queryRunner.query(`DROP TABLE IF EXISTS "session_keys"`);
    await queryRunner.query(`ALTER TABLE "session_keys_new" RENAME TO "session_keys"`);

    // Create indexes for performance
    await queryRunner.query(`CREATE INDEX "IDX_messages_senderId" ON "messages" ("senderId")`);
    await queryRunner.query(`CREATE INDEX "IDX_messages_recipientId" ON "messages" ("recipientId")`);
    await queryRunner.query(`CREATE INDEX "IDX_messages_roomId" ON "messages" ("roomId")`);
    await queryRunner.query(`CREATE INDEX "IDX_messages_createdAt" ON "messages" ("createdAt")`);
    await queryRunner.query(`CREATE INDEX "IDX_messages_expiresAt" ON "messages" ("expiresAt")`);
    await queryRunner.query(`CREATE INDEX "IDX_session_keys_users" ON "session_keys" ("user1Id", "user2Id")`);
    await queryRunner.query(`CREATE INDEX "IDX_group_members_group" ON "group_members" ("groupId")`);
    await queryRunner.query(`CREATE INDEX "IDX_group_members_user" ON "group_members" ("userId")`);
    await queryRunner.query(`CREATE INDEX "IDX_invites_code" ON "invites" ("inviteCode")`);
    await queryRunner.query(`CREATE INDEX "IDX_invites_email" ON "invites" ("email")`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop new tables
    await queryRunner.query(`DROP TABLE IF EXISTS "invites"`);
    await queryRunner.query(`DROP TABLE IF EXISTS "group_members"`);
    await queryRunner.query(`DROP TABLE IF EXISTS "groups"`);
    
    // Drop indexes
    await queryRunner.query(`DROP INDEX IF EXISTS "IDX_messages_senderId"`);
    await queryRunner.query(`DROP INDEX IF EXISTS "IDX_messages_recipientId"`);
    await queryRunner.query(`DROP INDEX IF EXISTS "IDX_messages_roomId"`);
    await queryRunner.query(`DROP INDEX IF EXISTS "IDX_messages_createdAt"`);
    await queryRunner.query(`DROP INDEX IF EXISTS "IDX_messages_expiresAt"`);
    await queryRunner.query(`DROP INDEX IF EXISTS "IDX_session_keys_users"`);
    await queryRunner.query(`DROP INDEX IF EXISTS "IDX_group_members_group"`);
    await queryRunner.query(`DROP INDEX IF EXISTS "IDX_group_members_user"`);
    await queryRunner.query(`DROP INDEX IF EXISTS "IDX_invites_code"`);
    await queryRunner.query(`DROP INDEX IF EXISTS "IDX_invites_email"`);

    // Note: Reverting schema changes would require recreating old tables
    // This is a destructive migration, so down migration is not fully implemented
    console.log('Warning: Down migration for schema update is not fully implemented');
  }
}
