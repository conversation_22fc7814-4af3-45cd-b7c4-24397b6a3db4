{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../services/group.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@angular/forms\";\nfunction GroupManagerComponent_form_12_option_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 30);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const option_r3 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", option_r3.value);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", option_r3.label, \" \");\n  }\n}\nfunction GroupManagerComponent_form_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"form\", 18)(1, \"div\", 19)(2, \"label\", 20);\n    i0.ɵɵtext(3, \"Onion Address\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"input\", 21);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function GroupManagerComponent_form_12_Template_input_ngModelChange_4_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.createForm.onionAddress, $event) || (ctx_r1.createForm.onionAddress = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"small\", 22);\n    i0.ɵɵtext(6, \"Unique .onion address for your group\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 19)(8, \"label\", 23);\n    i0.ɵɵtext(9, \"Security Threshold\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"select\", 24);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function GroupManagerComponent_form_12_Template_select_ngModelChange_10_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.createForm.securityThreshold, $event) || (ctx_r1.createForm.securityThreshold = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtemplate(11, GroupManagerComponent_form_12_option_11_Template, 2, 2, \"option\", 25);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"small\", 22);\n    i0.ɵɵtext(13, \"Number of confirmations required for sensitive operations\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"div\", 19)(15, \"label\", 26);\n    i0.ɵɵtext(16, \"Master Key\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"textarea\", 27);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function GroupManagerComponent_form_12_Template_textarea_ngModelChange_17_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.createForm.masterKey, $event) || (ctx_r1.createForm.masterKey = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"small\", 22);\n    i0.ɵɵtext(19, \"Cryptographic key for group encryption\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(20, \"div\", 19)(21, \"label\", 28);\n    i0.ɵɵtext(22, \"Encrypted Configuration\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"textarea\", 29);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function GroupManagerComponent_form_12_Template_textarea_ngModelChange_23_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.createForm.encryptedConfig, $event) || (ctx_r1.createForm.encryptedConfig = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"small\", 22);\n    i0.ɵɵtext(25, \"Encrypted group settings and metadata\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.createForm.onionAddress);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.isLoading);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.createForm.securityThreshold);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.isLoading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.securityThresholdOptions);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.createForm.masterKey);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.isLoading);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.createForm.encryptedConfig);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.isLoading);\n  }\n}\nfunction GroupManagerComponent_form_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"form\", 18)(1, \"div\", 19)(2, \"label\", 31);\n    i0.ɵɵtext(3, \"Invite Token\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"input\", 32);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function GroupManagerComponent_form_13_Template_input_ngModelChange_4_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.joinForm.inviteToken, $event) || (ctx_r1.joinForm.inviteToken = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"small\", 22);\n    i0.ɵɵtext(6, \"Token provided by the group administrator\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 19)(8, \"label\", 33);\n    i0.ɵɵtext(9, \"Secret Word\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"input\", 34);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function GroupManagerComponent_form_13_Template_input_ngModelChange_10_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.joinForm.secretWord, $event) || (ctx_r1.joinForm.secretWord = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"small\", 22);\n    i0.ɵɵtext(12, \"Secret word shared by the group administrator\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.joinForm.inviteToken);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.isLoading);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.joinForm.secretWord);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.isLoading);\n  }\n}\nfunction GroupManagerComponent_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.errorMessage, \" \");\n  }\n}\nfunction GroupManagerComponent_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 36);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.successMessage, \" \");\n  }\n}\nfunction GroupManagerComponent_span_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.mode === \"create\" ? \"Create Group\" : \"Join Group\", \" \");\n  }\n}\nfunction GroupManagerComponent_span_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 37);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.mode === \"create\" ? \"Creating...\" : \"Joining...\", \" \");\n  }\n}\nfunction GroupManagerComponent_div_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 38);\n    i0.ɵɵtext(1, \" \\u26A0\\uFE0F Save your group credentials securely. They cannot be recovered if lost. \");\n    i0.ɵɵelementEnd();\n  }\n}\nexport let GroupManagerComponent = /*#__PURE__*/(() => {\n  class GroupManagerComponent {\n    constructor(groupService, router, route) {\n      this.groupService = groupService;\n      this.router = router;\n      this.route = route;\n      this.mode = 'create';\n      this.isLoading = false;\n      this.errorMessage = '';\n      this.successMessage = '';\n      // Create group form\n      this.createForm = {\n        onionAddress: '',\n        encryptedConfig: '',\n        masterKey: '',\n        securityThreshold: 2\n      };\n      // Join group form\n      this.joinForm = {\n        inviteToken: '',\n        secretWord: ''\n      };\n      this.securityThresholdOptions = [{\n        value: 1,\n        label: 'Low (1 confirmation)'\n      }, {\n        value: 2,\n        label: 'Medium (2 confirmations)'\n      }, {\n        value: 3,\n        label: 'High (3 confirmations)'\n      }, {\n        value: 5,\n        label: 'Maximum (5 confirmations)'\n      }];\n    }\n    ngOnInit() {\n      // Determine mode from route\n      const path = this.route.snapshot.url[this.route.snapshot.url.length - 1]?.path;\n      this.mode = path === 'join' ? 'join' : 'create';\n      // Generate default values for create mode\n      if (this.mode === 'create') {\n        this.generateDefaults();\n      }\n    }\n    generateDefaults() {\n      // Generate a random onion address placeholder\n      this.createForm.onionAddress = this.generateOnionAddress();\n      // Generate a random master key\n      this.createForm.masterKey = this.generateMasterKey();\n      // Generate encrypted config placeholder\n      this.createForm.encryptedConfig = this.generateEncryptedConfig();\n    }\n    generateOnionAddress() {\n      // Generate a realistic-looking onion address\n      const chars = 'abcdefghijklmnopqrstuvwxyz234567';\n      let result = '';\n      for (let i = 0; i < 56; i++) {\n        result += chars.charAt(Math.floor(Math.random() * chars.length));\n      }\n      return result + '.onion';\n    }\n    generateMasterKey() {\n      // Generate a base64-like master key\n      const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/';\n      let result = '';\n      for (let i = 0; i < 64; i++) {\n        result += chars.charAt(Math.floor(Math.random() * chars.length));\n      }\n      return result;\n    }\n    generateEncryptedConfig() {\n      // Generate encrypted config placeholder\n      const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/';\n      let result = '';\n      for (let i = 0; i < 128; i++) {\n        result += chars.charAt(Math.floor(Math.random() * chars.length));\n      }\n      return result;\n    }\n    onCreateGroup() {\n      if (!this.validateCreateForm()) {\n        return;\n      }\n      this.isLoading = true;\n      this.errorMessage = '';\n      this.successMessage = '';\n      this.groupService.createGroup(this.createForm).subscribe({\n        next: group => {\n          this.isLoading = false;\n          this.successMessage = 'Group created successfully!';\n          setTimeout(() => {\n            this.router.navigate(['/main']);\n          }, 1500);\n        },\n        error: error => {\n          this.isLoading = false;\n          this.errorMessage = error.message || 'Failed to create group. Please try again.';\n        }\n      });\n    }\n    onJoinGroup() {\n      if (!this.validateJoinForm()) {\n        return;\n      }\n      this.isLoading = true;\n      this.errorMessage = '';\n      this.successMessage = '';\n      this.groupService.joinGroup(this.joinForm).subscribe({\n        next: membership => {\n          this.isLoading = false;\n          this.successMessage = 'Successfully joined group!';\n          setTimeout(() => {\n            this.router.navigate(['/main']);\n          }, 1500);\n        },\n        error: error => {\n          this.isLoading = false;\n          this.errorMessage = error.message || 'Failed to join group. Please check your invite token and secret word.';\n        }\n      });\n    }\n    validateCreateForm() {\n      if (!this.createForm.onionAddress.trim()) {\n        this.errorMessage = 'Onion address is required';\n        return false;\n      }\n      if (!this.createForm.masterKey.trim()) {\n        this.errorMessage = 'Master key is required';\n        return false;\n      }\n      if (!this.createForm.encryptedConfig.trim()) {\n        this.errorMessage = 'Encrypted config is required';\n        return false;\n      }\n      return true;\n    }\n    validateJoinForm() {\n      if (!this.joinForm.inviteToken.trim()) {\n        this.errorMessage = 'Invite token is required';\n        return false;\n      }\n      if (!this.joinForm.secretWord.trim()) {\n        this.errorMessage = 'Secret word is required';\n        return false;\n      }\n      return true;\n    }\n    onCancel() {\n      this.router.navigate(['/main']);\n    }\n    switchMode(newMode) {\n      this.mode = newMode;\n      this.errorMessage = '';\n      this.successMessage = '';\n      if (newMode === 'create') {\n        this.generateDefaults();\n      }\n    }\n    static {\n      this.ɵfac = function GroupManagerComponent_Factory(t) {\n        return new (t || GroupManagerComponent)(i0.ɵɵdirectiveInject(i1.GroupService), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i2.ActivatedRoute));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: GroupManagerComponent,\n        selectors: [[\"app-group-manager\"]],\n        standalone: true,\n        features: [i0.ɵɵStandaloneFeature],\n        decls: 27,\n        vars: 16,\n        consts: [[1, \"group-manager-container\"], [1, \"group-manager-card\"], [1, \"manager-header\"], [\"title\", \"Cancel\", 1, \"close-btn\", 3, \"click\"], [1, \"mode-switcher\"], [1, \"mode-btn\", 3, \"click\", \"disabled\"], [\"class\", \"group-form\", 4, \"ngIf\"], [1, \"message-feedback\"], [\"class\", \"error-message\", 4, \"ngIf\"], [\"class\", \"success-message\", 4, \"ngIf\"], [1, \"manager-actions\"], [\"type\", \"button\", 1, \"cancel-btn\", 3, \"click\", \"disabled\"], [\"type\", \"button\", 1, \"action-btn\", 3, \"click\", \"disabled\"], [4, \"ngIf\"], [\"class\", \"loading-spinner\", 4, \"ngIf\"], [1, \"security-notice\"], [1, \"security-badge\"], [\"class\", \"warning-text\", 4, \"ngIf\"], [1, \"group-form\"], [1, \"form-group\"], [\"for\", \"onion-address\"], [\"type\", \"text\", \"id\", \"onion-address\", \"name\", \"onionAddress\", \"placeholder\", \"Generated automatically\", \"required\", \"\", \"readonly\", \"\", 3, \"ngModelChange\", \"ngModel\", \"disabled\"], [1, \"help-text\"], [\"for\", \"security-threshold\"], [\"id\", \"security-threshold\", \"name\", \"securityThreshold\", 3, \"ngModelChange\", \"ngModel\", \"disabled\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"for\", \"master-key\"], [\"id\", \"master-key\", \"name\", \"masterKey\", \"placeholder\", \"Generated automatically\", \"required\", \"\", \"readonly\", \"\", \"rows\", \"3\", 3, \"ngModelChange\", \"ngModel\", \"disabled\"], [\"for\", \"encrypted-config\"], [\"id\", \"encrypted-config\", \"name\", \"encryptedConfig\", \"placeholder\", \"Generated automatically\", \"required\", \"\", \"readonly\", \"\", \"rows\", \"4\", 3, \"ngModelChange\", \"ngModel\", \"disabled\"], [3, \"value\"], [\"for\", \"invite-token\"], [\"type\", \"text\", \"id\", \"invite-token\", \"name\", \"inviteToken\", \"placeholder\", \"Paste your invite token here\", \"required\", \"\", 3, \"ngModelChange\", \"ngModel\", \"disabled\"], [\"for\", \"secret-word\"], [\"type\", \"password\", \"id\", \"secret-word\", \"name\", \"secretWord\", \"placeholder\", \"Enter the group secret word\", \"required\", \"\", 3, \"ngModelChange\", \"ngModel\", \"disabled\"], [1, \"error-message\"], [1, \"success-message\"], [1, \"loading-spinner\"], [1, \"warning-text\"]],\n        template: function GroupManagerComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"h2\");\n            i0.ɵɵtext(4);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(5, \"button\", 3);\n            i0.ɵɵlistener(\"click\", function GroupManagerComponent_Template_button_click_5_listener() {\n              return ctx.onCancel();\n            });\n            i0.ɵɵtext(6, \"\\u00D7\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(7, \"div\", 4)(8, \"button\", 5);\n            i0.ɵɵlistener(\"click\", function GroupManagerComponent_Template_button_click_8_listener() {\n              return ctx.switchMode(\"create\");\n            });\n            i0.ɵɵtext(9, \" Create Group \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(10, \"button\", 5);\n            i0.ɵɵlistener(\"click\", function GroupManagerComponent_Template_button_click_10_listener() {\n              return ctx.switchMode(\"join\");\n            });\n            i0.ɵɵtext(11, \" Join Group \");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵtemplate(12, GroupManagerComponent_form_12_Template, 26, 9, \"form\", 6)(13, GroupManagerComponent_form_13_Template, 13, 4, \"form\", 6);\n            i0.ɵɵelementStart(14, \"div\", 7);\n            i0.ɵɵtemplate(15, GroupManagerComponent_div_15_Template, 2, 1, \"div\", 8)(16, GroupManagerComponent_div_16_Template, 2, 1, \"div\", 9);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(17, \"div\", 10)(18, \"button\", 11);\n            i0.ɵɵlistener(\"click\", function GroupManagerComponent_Template_button_click_18_listener() {\n              return ctx.onCancel();\n            });\n            i0.ɵɵtext(19, \" Cancel \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(20, \"button\", 12);\n            i0.ɵɵlistener(\"click\", function GroupManagerComponent_Template_button_click_20_listener() {\n              return ctx.mode === \"create\" ? ctx.onCreateGroup() : ctx.onJoinGroup();\n            });\n            i0.ɵɵtemplate(21, GroupManagerComponent_span_21_Template, 2, 1, \"span\", 13)(22, GroupManagerComponent_span_22_Template, 2, 1, \"span\", 14);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(23, \"div\", 15)(24, \"div\", 16);\n            i0.ɵɵtext(25, \" \\uD83D\\uDD12 All group communications are protected by post-quantum cryptography \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(26, GroupManagerComponent_div_26_Template, 2, 0, \"div\", 17);\n            i0.ɵɵelementEnd()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(4);\n            i0.ɵɵtextInterpolate(ctx.mode === \"create\" ? \"Create Group\" : \"Join Group\");\n            i0.ɵɵadvance(4);\n            i0.ɵɵclassProp(\"active\", ctx.mode === \"create\");\n            i0.ɵɵproperty(\"disabled\", ctx.isLoading);\n            i0.ɵɵadvance(2);\n            i0.ɵɵclassProp(\"active\", ctx.mode === \"join\");\n            i0.ɵɵproperty(\"disabled\", ctx.isLoading);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"ngIf\", ctx.mode === \"create\");\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.mode === \"join\");\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"ngIf\", ctx.errorMessage);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.successMessage);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"disabled\", ctx.isLoading);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"disabled\", ctx.isLoading);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", !ctx.isLoading);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n            i0.ɵɵadvance(4);\n            i0.ɵɵproperty(\"ngIf\", ctx.mode === \"create\");\n          }\n        },\n        dependencies: [CommonModule, i3.NgForOf, i3.NgIf, FormsModule, i4.ɵNgNoValidate, i4.NgSelectOption, i4.ɵNgSelectMultipleOption, i4.DefaultValueAccessor, i4.SelectControlValueAccessor, i4.NgControlStatus, i4.NgControlStatusGroup, i4.RequiredValidator, i4.NgModel, i4.NgForm],\n        styles: [\".group-manager-container[_ngcontent-%COMP%]{min-height:100vh;display:flex;align-items:center;justify-content:center;background:linear-gradient(135deg,#667eea,#764ba2);padding:1rem}.group-manager-card[_ngcontent-%COMP%]{background:#fff;border-radius:20px;padding:2rem;box-shadow:0 20px 40px #0000001a;width:100%;max-width:600px;max-height:90vh;overflow-y:auto}.manager-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;margin-bottom:2rem;padding-bottom:1rem;border-bottom:1px solid #eee}.manager-header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{margin:0;color:#333;font-weight:500}.manager-header[_ngcontent-%COMP%]   .close-btn[_ngcontent-%COMP%]{background:none;border:none;font-size:1.5rem;cursor:pointer;color:#999;padding:0;width:30px;height:30px;display:flex;align-items:center;justify-content:center;border-radius:50%;transition:all .2s ease}.manager-header[_ngcontent-%COMP%]   .close-btn[_ngcontent-%COMP%]:hover{background:#f5f5f5;color:#333}.mode-switcher[_ngcontent-%COMP%]{display:flex;background:#f5f5f5;border-radius:10px;padding:.25rem;margin-bottom:2rem}.mode-switcher[_ngcontent-%COMP%]   .mode-btn[_ngcontent-%COMP%]{flex:1;padding:.75rem;border:none;border-radius:8px;background:transparent;color:#666;cursor:pointer;transition:all .2s ease;font-weight:500}.mode-switcher[_ngcontent-%COMP%]   .mode-btn.active[_ngcontent-%COMP%]{background:#fff;color:#333;box-shadow:0 2px 4px #0000001a}.mode-switcher[_ngcontent-%COMP%]   .mode-btn[_ngcontent-%COMP%]:disabled{opacity:.6;cursor:not-allowed}.group-form[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]{margin-bottom:1.5rem}.group-form[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   label[_ngcontent-%COMP%]{display:block;margin-bottom:.5rem;color:#333;font-weight:500;font-size:.9rem}.group-form[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   input[_ngcontent-%COMP%], .group-form[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   select[_ngcontent-%COMP%], .group-form[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   textarea[_ngcontent-%COMP%]{width:100%;padding:.75rem 1rem;border:2px solid #e1e5e9;border-radius:10px;font-size:1rem;transition:all .3s ease;background:#fff;font-family:inherit}.group-form[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]:focus, .group-form[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   select[_ngcontent-%COMP%]:focus, .group-form[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   textarea[_ngcontent-%COMP%]:focus{outline:none;border-color:#667eea;box-shadow:0 0 0 3px #667eea1a}.group-form[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]:disabled, .group-form[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   input[readonly][_ngcontent-%COMP%], .group-form[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   select[_ngcontent-%COMP%]:disabled, .group-form[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   select[readonly][_ngcontent-%COMP%], .group-form[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   textarea[_ngcontent-%COMP%]:disabled, .group-form[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   textarea[readonly][_ngcontent-%COMP%]{background:#f8f9fa;cursor:not-allowed}.group-form[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]::placeholder, .group-form[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   select[_ngcontent-%COMP%]::placeholder, .group-form[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   textarea[_ngcontent-%COMP%]::placeholder{color:#999}.group-form[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   textarea[_ngcontent-%COMP%]{resize:vertical;min-height:80px;font-family:Courier New,monospace;font-size:.9rem}.group-form[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .help-text[_ngcontent-%COMP%]{display:block;margin-top:.5rem;color:#666;font-size:.8rem;line-height:1.4}.message-feedback[_ngcontent-%COMP%]{margin-bottom:1.5rem}.message-feedback[_ngcontent-%COMP%]   .error-message[_ngcontent-%COMP%]{background:#fee;color:#c33;padding:.75rem;border-radius:8px;font-size:.9rem;border:1px solid #fcc}.message-feedback[_ngcontent-%COMP%]   .success-message[_ngcontent-%COMP%]{background:#efe;color:#3c3;padding:.75rem;border-radius:8px;font-size:.9rem;border:1px solid #cfc}.manager-actions[_ngcontent-%COMP%]{display:flex;gap:1rem;margin-bottom:1.5rem}.manager-actions[_ngcontent-%COMP%]   .cancel-btn[_ngcontent-%COMP%], .manager-actions[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]{flex:1;padding:.875rem;border:none;border-radius:10px;font-size:1rem;font-weight:600;cursor:pointer;transition:all .3s ease}.manager-actions[_ngcontent-%COMP%]   .cancel-btn[_ngcontent-%COMP%]:disabled, .manager-actions[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]:disabled{opacity:.6;cursor:not-allowed}.manager-actions[_ngcontent-%COMP%]   .cancel-btn[_ngcontent-%COMP%]{background:#f5f5f5;color:#666}.manager-actions[_ngcontent-%COMP%]   .cancel-btn[_ngcontent-%COMP%]:hover:not(:disabled){background:#e8e8e8}.manager-actions[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]{background:linear-gradient(135deg,#667eea,#764ba2);color:#fff}.manager-actions[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]:hover:not(:disabled){transform:translateY(-2px);box-shadow:0 10px 20px #667eea4d}.manager-actions[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]   .loading-spinner[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_pulse 1.5s ease-in-out infinite}@keyframes _ngcontent-%COMP%_pulse{0%,to{opacity:1}50%{opacity:.5}}.security-notice[_ngcontent-%COMP%]{text-align:center;padding-top:1rem;border-top:1px solid #eee}.security-notice[_ngcontent-%COMP%]   .security-badge[_ngcontent-%COMP%]{color:#667eea;font-size:.9rem;margin-bottom:.5rem;font-weight:500}.security-notice[_ngcontent-%COMP%]   .warning-text[_ngcontent-%COMP%]{color:#e67e22;font-size:.8rem;font-weight:500;margin-top:.5rem}@media (max-width: 768px){.group-manager-container[_ngcontent-%COMP%]{padding:.5rem}.group-manager-card[_ngcontent-%COMP%]{padding:1.5rem;border-radius:15px}.manager-actions[_ngcontent-%COMP%], .mode-switcher[_ngcontent-%COMP%]{flex-direction:column}.mode-switcher[_ngcontent-%COMP%]   .mode-btn[_ngcontent-%COMP%]{margin-bottom:.25rem}.mode-switcher[_ngcontent-%COMP%]   .mode-btn[_ngcontent-%COMP%]:last-child{margin-bottom:0}}\"]\n      });\n    }\n  }\n  return GroupManagerComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}