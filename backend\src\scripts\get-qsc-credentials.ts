import { DataSource } from 'typeorm';
import { User } from '../users/entities/user.entity';
import * as argon2 from 'argon2';
import * as crypto from 'crypto';
import { join } from 'path';

async function getQSCCredentials() {
  console.log('🔄 Getting QSC user credentials...');

  // Create database connection
  const dataSource = new DataSource({
    type: 'better-sqlite3',
    database: process.env.DB_PATH || join(process.cwd(), 'secure_chat.sqlite'),
    entities: [User],
    synchronize: false,
    logging: false,
  });

  try {
    await dataSource.initialize();
    console.log('✅ Database connection established');

    const userRepository = dataSource.getRepository(User);

    // Find the QSC user
    const user = await userRepository.findOne({ 
      where: { username: 'QSC' } 
    });

    if (!user) {
      console.log('❌ QSC user not found. Creating new user...');
      
      // Generate a secure 4-character secret word
      function generateSecretWord(): string {
        const uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
        const lowercase = 'abcdefghijklmnopqrstuvwxyz';
        const digits = '0123456789';
        const symbols = '!@#$%^&*()_+-=[]{}|;:,.<>?';

        const getRandomChar = (str: string) => str[Math.floor(Math.random() * str.length)];

        return [
          getRandomChar(uppercase),
          getRandomChar(lowercase),
          getRandomChar(digits),
          getRandomChar(symbols)
        ].sort(() => Math.random() - 0.5).join('');
      }

      const secretWord = generateSecretWord();

      // Create secret word hash using argon2
      const salt = crypto.randomBytes(32);
      const hash = await argon2.hash(secretWord, {
        type: argon2.argon2id,
        memoryCost: 2 ** 16, // 64 MB
        timeCost: 3,
        parallelism: 1,
        salt,
      });

      const secretWordHash = {
        hash,
        salt: salt.toString('hex'),
        attempts: 0,
        lastAttempt: null,
      };

      // Create the QSC user
      const newUser = userRepository.create({
        username: 'QSC',
        email: '<EMAIL>',
        phone: '+************',
        secretWordHash,
        isAdmin: true,
        isActive: true,
        isVerified: true,
        deviceIds: [],
        accountStatus: 'active',
        failedAttempts: 0,
        isCompromised: false,
      });

      const savedUser = await userRepository.save(newUser);

      console.log('');
      console.log('✅ QSC user created successfully:');
      console.log(`   Username: ${savedUser.username}`);
      console.log(`   Email: ${savedUser.email}`);
      console.log(`   Phone: ${savedUser.phone}`);
      console.log(`   ID: ${savedUser.id}`);
      console.log(`   Secret Word: ${secretWord}`);
      console.log('');
      console.log('🔐 IMPORTANT: Save these credentials securely!');
      console.log('🔐 The secret word will not be displayed again.');
      
      await dataSource.destroy();
      return;
    }

    // User exists, reset the secret word
    console.log('✅ QSC user found. Resetting secret word...');

    // Generate a new secure 4-character secret word
    function generateSecretWord(): string {
      const uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
      const lowercase = 'abcdefghijklmnopqrstuvwxyz';
      const digits = '0123456789';
      const symbols = '!@#$%^&*()_+-=[]{}|;:,.<>?';

      const getRandomChar = (str: string) => str[Math.floor(Math.random() * str.length)];

      return [
        getRandomChar(uppercase),
        getRandomChar(lowercase),
        getRandomChar(digits),
        getRandomChar(symbols)
      ].sort(() => Math.random() - 0.5).join('');
    }

    const newSecretWord = generateSecretWord();

    // Create new secret word hash using argon2
    const salt = crypto.randomBytes(32);
    const hash = await argon2.hash(newSecretWord, {
      type: argon2.argon2id,
      memoryCost: 2 ** 16, // 64 MB
      timeCost: 3,
      parallelism: 1,
      salt,
    });

    const newSecretWordHash = {
      hash,
      salt: salt.toString('hex'),
      attempts: 0,
      lastAttempt: null,
    };

    // Update user
    user.secretWordHash = newSecretWordHash;
    user.failedAttempts = 0;
    user.lastAttemptAt = null;
    user.isCompromised = false;
    user.accountStatus = 'active';

    await userRepository.save(user);

    console.log('');
    console.log('✅ QSC user credentials updated successfully:');
    console.log(`   Username: ${user.username}`);
    console.log(`   Email: ${user.email}`);
    console.log(`   Phone: ${user.phone}`);
    console.log(`   ID: ${user.id}`);
    console.log(`   New Secret Word: ${newSecretWord}`);
    console.log('');
    console.log('🔐 IMPORTANT: Save these credentials securely!');
    console.log('🔐 The secret word will not be displayed again.');

  } catch (error) {
    console.error('❌ Failed to get QSC credentials:', error);
    process.exit(1);
  } finally {
    try {
      if (dataSource.isInitialized) {
        await dataSource.destroy();
      }
    } catch (error) {
      // Ignore cleanup errors
    }
  }
}

// Run the script if this file is executed directly
if (require.main === module) {
  getQSCCredentials();
}

export { getQSCCredentials };
