<div class="composer-container">
  <div class="composer-card">
    <div class="composer-header">
      <h2>Compose Secure Message</h2>
      <button class="close-btn" (click)="onCancel()" title="Cancel (Esc)">×</button>
    </div>

    <form class="composer-form" (keydown)="onKeyDown($event)">
      <div class="form-group">
        <label for="recipient">Recipient ID</label>
        <input
          type="text"
          id="recipient"
          name="recipient"
          [(ngModel)]="message.recipientId"
          placeholder="Enter recipient's user ID"
          required
          [disabled]="isLoading"
          class="recipient-input"
        />
      </div>

      <div class="form-group">
        <label for="expiration">Message Expiration</label>
        <select
          id="expiration"
          name="expiration"
          [(ngModel)]="message.expiresIn"
          [disabled]="isLoading"
          class="expiration-select"
        >
          <option *ngFor="let option of expirationOptions" [ngValue]="option.value">
            {{ option.label }}
          </option>
        </select>
      </div>

      <div class="form-group message-group">
        <label for="message-content">Message</label>
        <textarea
          id="message-content"
          name="content"
          [(ngModel)]="message.content"
          (input)="onContentChange()"
          placeholder="Type your secure message here..."
          required
          [disabled]="isLoading"
          class="message-textarea"
          rows="8"
        ></textarea>
        
        <div class="character-counter">
          <span [class.warning]="characterCount > maxCharacters * 0.8">
            {{ characterCount }}/{{ maxCharacters }}
          </span>
        </div>
      </div>

      <div class="message-feedback">
        <div class="error-message" *ngIf="errorMessage">
          {{ errorMessage }}
        </div>
        
        <div class="success-message" *ngIf="successMessage">
          {{ successMessage }}
        </div>
      </div>

      <div class="composer-actions">
        <button 
          type="button" 
          class="cancel-btn"
          (click)="onCancel()"
          [disabled]="isLoading"
        >
          Cancel
        </button>
        
        <button 
          type="button" 
          class="send-btn"
          (click)="onSend()"
          [disabled]="isLoading || !message.content.trim() || !message.recipientId.trim()"
        >
          <span *ngIf="!isLoading">Send Message</span>
          <span *ngIf="isLoading" class="loading-spinner">Sending...</span>
        </button>
      </div>
    </form>

    <div class="security-info">
      <div class="security-badge">
        🔒 End-to-end encrypted with post-quantum cryptography
      </div>
      <div class="keyboard-shortcuts">
        <span>Ctrl+Enter to send • Esc to cancel</span>
      </div>
    </div>
  </div>
</div>
