{"ast": null, "code": "// Placeholder WASM module for development\n// This will be replaced with actual LibOQS WASM implementation\n\nexport default function () {\n  console.warn('Using placeholder WASM module - LibOQS not available');\n  return Promise.resolve();\n}\n\n// Placeholder functions for development - these will trigger fallback mode\nexport function generate_key_pair() {\n  console.warn('WASM generate_key_pair not available - triggering fallback');\n  throw new Error('WASM module not available - using fallback implementation');\n}\nexport function encrypt_message() {\n  console.warn('WASM encrypt_message not available - triggering fallback');\n  throw new Error('WASM module not available - using fallback implementation');\n}\nexport function decrypt_message() {\n  console.warn('WASM decrypt_message not available - triggering fallback');\n  throw new Error('WASM module not available - using fallback implementation');\n}\nexport function rotate_keys() {\n  console.warn('WASM rotate_keys not available - triggering fallback');\n  throw new Error('WASM module not available - using fallback implementation');\n}", "map": {"version": 3, "names": ["console", "warn", "Promise", "resolve", "generate_key_pair", "Error", "encrypt_message", "decrypt_message", "rotate_keys"], "sources": ["C:/Users/<USER>/Projects/QSC1/frontend/src/assets/wasm/pqc_wasm.js"], "sourcesContent": ["// Placeholder WASM module for development\n// This will be replaced with actual LibOQS WASM implementation\n\nexport default function() {\n  console.warn('Using placeholder WASM module - LibOQS not available');\n  return Promise.resolve();\n}\n\n// Placeholder functions for development - these will trigger fallback mode\nexport function generate_key_pair() {\n  console.warn('WASM generate_key_pair not available - triggering fallback');\n  throw new Error('WASM module not available - using fallback implementation');\n}\n\nexport function encrypt_message() {\n  console.warn('WASM encrypt_message not available - triggering fallback');\n  throw new Error('WASM module not available - using fallback implementation');\n}\n\nexport function decrypt_message() {\n  console.warn('WASM decrypt_message not available - triggering fallback');\n  throw new Error('WASM module not available - using fallback implementation');\n}\n\nexport function rotate_keys() {\n  console.warn('WASM rotate_keys not available - triggering fallback');\n  throw new Error('WASM module not available - using fallback implementation');\n}\n"], "mappings": "AAAA;AACA;;AAEA,eAAe,YAAW;EACxBA,OAAO,CAACC,IAAI,CAAC,sDAAsD,CAAC;EACpE,OAAOC,OAAO,CAACC,OAAO,CAAC,CAAC;AAC1B;;AAEA;AACA,OAAO,SAASC,iBAAiBA,CAAA,EAAG;EAClCJ,OAAO,CAACC,IAAI,CAAC,4DAA4D,CAAC;EAC1E,MAAM,IAAII,KAAK,CAAC,2DAA2D,CAAC;AAC9E;AAEA,OAAO,SAASC,eAAeA,CAAA,EAAG;EAChCN,OAAO,CAACC,IAAI,CAAC,0DAA0D,CAAC;EACxE,MAAM,IAAII,KAAK,CAAC,2DAA2D,CAAC;AAC9E;AAEA,OAAO,SAASE,eAAeA,CAAA,EAAG;EAChCP,OAAO,CAACC,IAAI,CAAC,0DAA0D,CAAC;EACxE,MAAM,IAAII,KAAK,CAAC,2DAA2D,CAAC;AAC9E;AAEA,OAAO,SAASG,WAAWA,CAAA,EAAG;EAC5BR,OAAO,CAACC,IAAI,CAAC,sDAAsD,CAAC;EACpE,MAAM,IAAII,KAAK,CAAC,2DAA2D,CAAC;AAC9E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}