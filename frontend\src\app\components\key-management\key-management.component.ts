import { Component, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { WasmService } from '../../services/wasm.service';
import { ErrorHandlingService } from '../../services/error-handling.service';

@Component({
  selector: 'qs-key-management',
  standalone: true,
  imports: [CommonModule, FormsModule],
  template: `
    <div class="key-management">
      <h2>Key Management</h2>

      <div class="key-status" *ngIf="currentKeyPair">
        <h3>Current Key Pair</h3>
        <p>Version: {{ currentKeyPair.version }}</p>
        <p>Last Rotation: {{ currentKeyPair.timestamp | date:'medium' }}</p>

        <div class="key-actions">
          <button (click)="rotateKeys()" [disabled]="isRotating">
            {{ isRotating ? 'Rotating...' : 'Rotate Keys' }}
          </button>
          <button (click)="exportPublicKey()">Export Public Key</button>
        </div>
      </div>

      <div class="key-import" *ngIf="!currentKeyPair">
        <h3>Import Key Pair</h3>
        <div class="import-form">
          <input
            type="file"
            accept=".key"
            (change)="onFileSelected($event)"
            [disabled]="isImporting"
          />
          <button (click)="importKeys()" [disabled="!selectedFile || isImporting">
            {{ isImporting ? 'Importing...' : 'Import Keys' }}
          </button>
        </div>
      </div>

      <div class="error-message" *ngIf="errorMessage">
        {{ errorMessage }}
      </div>
    </div>
  `,
  styles: [`
    .key-management {
      padding: 20px;
      max-width: 800px;
      margin: 0 auto;
    }

    .key-status {
      background: #f5f5f5;
      padding: 20px;
      border-radius: 8px;
      margin-bottom: 20px;
    }

    .key-actions {
      display: flex;
      gap: 10px;
      margin-top: 15px;
    }

    button {
      padding: 8px 16px;
      border: none;
      border-radius: 4px;
      background: #007bff;
      color: white;
      cursor: pointer;
      transition: background 0.2s;
    }

    button:disabled {
      background: #ccc;
      cursor: not-allowed;
    }

    button:hover:not(:disabled) {
      background: #0056b3;
    }

    .key-import {
      background: #f5f5f5;
      padding: 20px;
      border-radius: 8px;
    }

    .import-form {
      display: flex;
      gap: 10px;
      margin-top: 15px;
    }

    input[type="file"] {
      flex: 1;
      padding: 8px;
      border: 1px solid #ddd;
      border-radius: 4px;
    }

    .error-message {
      color: #dc3545;
      margin-top: 15px;
      padding: 10px;
      background: #f8d7da;
      border-radius: 4px;
    }
  `]
})
export class KeyManagementComponent implements OnInit, OnDestroy {
  currentKeyPair: any = null;
  isRotating = false;
  isImporting = false;
  selectedFile: File | null = null;
  errorMessage = '';

  constructor(
    private wasmService: WasmService,
    private errorHandling: ErrorHandlingService
  ) {}

  ngOnInit(): void {
    this.loadCurrentKeyPair();
  }

  ngOnDestroy(): void {
    // Clean up any subscriptions or resources
  }

  private async loadCurrentKeyPair(): Promise<void> {
    try {
      this.currentKeyPair = this.wasmService.getCurrentKeyPair();
    } catch (error) {
      this.errorMessage = 'Failed to load current key pair';
      await this.errorHandling.handleError(
        error instanceof Error ? error : new Error(this.errorMessage),
        'SECURITY'
      );
    }
  }

  async rotateKeys(): Promise<void> {
    if (this.isRotating) return;

    this.isRotating = true;
    this.errorMessage = '';

    try {
      await this.wasmService.rotateKeys();
      await this.loadCurrentKeyPair();
    } catch (error) {
      this.errorMessage = 'Failed to rotate keys';
      await this.errorHandling.handleError(
        error instanceof Error ? error : new Error(this.errorMessage),
        'SECURITY'
      );
    } finally {
      this.isRotating = false;
    }
  }

  onFileSelected(event: Event): void {
    const input = event.target as HTMLInputElement;
    if (input.files?.length) {
      this.selectedFile = input.files[0];
    }
  }

  async importKeys(): Promise<void> {
    if (!this.selectedFile || this.isImporting) return;

    this.isImporting = true;
    this.errorMessage = '';

    try {
      const fileContent = await this.selectedFile.text();
      const keyPair = JSON.parse(fileContent);
      await this.wasmService.importKeyPair(keyPair);
      await this.loadCurrentKeyPair();
    } catch (error) {
      this.errorMessage = 'Failed to import keys';
      await this.errorHandling.handleError(
        error instanceof Error ? error : new Error(this.errorMessage),
        'SECURITY'
      );
    } finally {
      this.isImporting = false;
      this.selectedFile = null;
    }
  }

  async exportPublicKey(): Promise<void> {
    if (!this.currentKeyPair) return;

    try {
      const publicKey = this.currentKeyPair.public_key;
      const blob = new Blob([publicKey], { type: 'application/octet-stream' });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `public-key-v${this.currentKeyPair.version}.key`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (error) {
      this.errorMessage = 'Failed to export public key';
      await this.errorHandling.handleError(
        error instanceof Error ? error : new Error(this.errorMessage),
        'SECURITY'
      );
    }
  }
}
