use wasm_bindgen::prelude::*;
use pqcrypto::dilithium::dilithium5::*;
use pqcrypto::prelude::*;
use aes_gcm::{
    aead::{Aead, KeyInit},
    Aes256Gcm, Key, Nonce,
};
use rand::{rngs::OsRng, RngCore};
use serde::{Serialize, Deserialize};

#[derive(Serialize, Deserialize)]
pub struct KeyPair {
    public_key: Vec<u8>,
    private_key: Vec<u8>,
}

#[derive(Serialize, Deserialize)]
pub struct EncryptedData {
    ciphertext: Vec<u8>,
    nonce: Vec<u8>,
    signature: Vec<u8>,
}

#[wasm_bindgen]
pub fn generate_key_pair() -> Result<Vec<u8>, JsValue> {
    let (public_key, private_key) = keypair();
    let key_pair = KeyPair {
        public_key: public_key.as_bytes().to_vec(),
        private_key: private_key.as_bytes().to_vec(),
    };
    Ok(serde_wasm_bindgen::to_value(&key_pair)?.into())
}

#[wasm_bindgen]
pub fn encrypt_message(message: &[u8], public_key: &[u8]) -> Result<Vec<u8>, JsValue> {
    // Generate a random AES key
    let mut aes_key = [0u8; 32];
    OsRng.fill_bytes(&mut aes_key);
    let key = Key::<Aes256Gcm>::from_slice(&aes_key);
    let cipher = Aes256Gcm::new(key);

    // Generate a random nonce
    let mut nonce_bytes = [0u8; 12];
    OsRng.fill_bytes(&mut nonce_bytes);
    let nonce = Nonce::from_slice(&nonce_bytes);

    // Encrypt the message with AES-GCM
    let ciphertext = cipher.encrypt(nonce, message)
        .map_err(|e| JsValue::from_str(&format!("Encryption failed: {}", e)))?;

    // Sign the ciphertext with Dilithium
    let private_key = SecretKey::from_bytes(public_key)
        .map_err(|e| JsValue::from_str(&format!("Invalid public key: {}", e)))?;
    let signature = sign(&ciphertext, &private_key);

    let encrypted_data = EncryptedData {
        ciphertext,
        nonce: nonce_bytes.to_vec(),
        signature: signature.as_bytes().to_vec(),
    };

    Ok(serde_wasm_bindgen::to_value(&encrypted_data)?.into())
}

#[wasm_bindgen]
pub fn decrypt_message(encrypted_data: &[u8], private_key: &[u8]) -> Result<Vec<u8>, JsValue> {
    let encrypted: EncryptedData = serde_wasm_bindgen::from_value(encrypted_data.into())?;

    // Verify the signature
    let public_key = PublicKey::from_bytes(private_key)
        .map_err(|e| JsValue::from_str(&format!("Invalid private key: {}", e)))?;
    verify(&encrypted.ciphertext, &encrypted.signature, &public_key)
        .map_err(|e| JsValue::from_str(&format!("Signature verification failed: {}", e)))?;

    // Decrypt the message
    let key = Key::<Aes256Gcm>::from_slice(private_key);
    let cipher = Aes256Gcm::new(key);
    let nonce = Nonce::from_slice(&encrypted.nonce);

    let plaintext = cipher.decrypt(nonce, encrypted.ciphertext.as_slice())
        .map_err(|e| JsValue::from_str(&format!("Decryption failed: {}", e)))?;

    Ok(plaintext)
}

#[wasm_bindgen]
pub fn rotate_keys(old_private_key: &[u8]) -> Result<Vec<u8>, JsValue> {
    // Generate new key pair
    let (new_public_key, new_private_key) = keypair();

    // Sign the new public key with the old private key
    let old_private = SecretKey::from_bytes(old_private_key)
        .map_err(|e| JsValue::from_str(&format!("Invalid old private key: {}", e)))?;
    let signature = sign(new_public_key.as_bytes(), &old_private);

    let key_pair = KeyPair {
        public_key: new_public_key.as_bytes().to_vec(),
        private_key: new_private_key.as_bytes().to_vec(),
    };

    Ok(serde_wasm_bindgen::to_value(&key_pair)?.into())
}

#[wasm_bindgen]
pub fn free(ptr: *mut u8) {
    unsafe {
        if !ptr.is_null() {
            let _ = Box::from_raw(ptr);
        }
    }
}
