import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { ApiService } from './api.service';
import { 
  Group, 
  CreateGroupRequest, 
  GroupMember, 
  Invite, 
  CreateInviteRequest, 
  JoinGroupRequest,
  GroupListItem 
} from '../types/group.types';

@Injectable({
  providedIn: 'root'
})
export class GroupService {

  constructor(private apiService: ApiService) {}

  createGroup(groupData: CreateGroupRequest): Observable<Group> {
    return this.apiService.post<Group>('/groups', groupData);
  }

  getUserGroups(): Observable<GroupListItem[]> {
    // This would need to be implemented in the backend
    return this.apiService.get<GroupListItem[]>('/groups/user');
  }

  getGroup(groupId: string): Observable<Group> {
    return this.apiService.get<Group>(`/groups/${groupId}`);
  }

  createInvite(groupId: string, inviteData: CreateInviteRequest): Observable<Invite> {
    return this.apiService.post<Invite>(`/groups/${groupId}/invites`, inviteData);
  }

  joinGroup(joinData: JoinGroupRequest): Observable<GroupMember> {
    return this.apiService.post<GroupMember>('/groups/join', joinData);
  }

  getGroupMembers(groupId: string): Observable<GroupMember[]> {
    return this.apiService.get<GroupMember[]>(`/groups/${groupId}/members`);
  }

  leaveGroup(groupId: string): Observable<void> {
    return this.apiService.delete<void>(`/groups/${groupId}/leave`);
  }

  rotateGroupKeys(groupId: string, newMasterKey: string): Observable<void> {
    return this.apiService.post<void>(`/groups/${groupId}/rotate-keys`, { newMasterKey });
  }
}
