{"ast": null, "code": "import { provideRouter } from '@angular/router';\nimport { provideHttpClient, withInterceptors } from '@angular/common/http';\nimport { routes } from './app.routes';\nimport { provideClientHydration } from '@angular/platform-browser';\nimport { WasmService } from './services/wasm.service';\nimport { ErrorHandlingService } from './services/error-handling.service';\nimport { authInterceptor } from './interceptors/auth.interceptor';\nexport const appConfig = {\n  providers: [provideRouter(routes), provideHttpClient(withInterceptors([authInterceptor])), provideClientHydration(), WasmService, ErrorHandlingService]\n};", "map": {"version": 3, "names": ["provideRouter", "provideHttpClient", "withInterceptors", "routes", "provideClientHydration", "WasmService", "ErrorHandlingService", "authInterceptor", "appConfig", "providers"], "sources": ["C:\\Users\\<USER>\\Projects\\QSC1\\frontend\\src\\app\\app.config.ts"], "sourcesContent": ["import { ApplicationConfig } from '@angular/core';\r\nimport { provideRouter } from '@angular/router';\r\nimport { provideHttpClient, withInterceptors } from '@angular/common/http';\r\nimport { routes } from './app.routes';\r\nimport { provideClientHydration } from '@angular/platform-browser';\r\nimport { WasmService } from './services/wasm.service';\r\nimport { ErrorHandlingService } from './services/error-handling.service';\r\nimport { authInterceptor } from './interceptors/auth.interceptor';\r\n\r\nexport const appConfig: ApplicationConfig = {\r\n  providers: [\r\n    provideRouter(routes),\r\n    provideHttpClient(withInterceptors([authInterceptor])),\r\n    provideClientHydration(),\r\n    WasmService,\r\n    ErrorHandlingService\r\n  ]\r\n};\r\n"], "mappings": "AACA,SAASA,aAAa,QAAQ,iBAAiB;AAC/C,SAASC,iBAAiB,EAAEC,gBAAgB,QAAQ,sBAAsB;AAC1E,SAASC,MAAM,QAAQ,cAAc;AACrC,SAASC,sBAAsB,QAAQ,2BAA2B;AAClE,SAASC,WAAW,QAAQ,yBAAyB;AACrD,SAASC,oBAAoB,QAAQ,mCAAmC;AACxE,SAASC,eAAe,QAAQ,iCAAiC;AAEjE,OAAO,MAAMC,SAAS,GAAsB;EAC1CC,SAAS,EAAE,CACTT,aAAa,CAACG,MAAM,CAAC,EACrBF,iBAAiB,CAACC,gBAAgB,CAAC,CAACK,eAAe,CAAC,CAAC,CAAC,EACtDH,sBAAsB,EAAE,EACxBC,WAAW,EACXC,oBAAoB;CAEvB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}