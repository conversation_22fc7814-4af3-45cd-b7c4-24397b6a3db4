/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = function(knex) {
  return knex.schema.createTable('users', function(table) {
    table.uuid('id').primary();
    table.string('username').unique().notNullable();
    table.string('secret_word_hash').notNullable();
    table.integer('failed_attempts').defaultTo(0);
    table.timestamp('last_attempt_at');
    table.boolean('is_compromised').defaultTo(false);
    table.enum('account_status', ['active', 'deactivated', 'compromised']).defaultTo('active');
    table.uuid('deactivated_by').references('id').inTable('users');
    table.timestamp('deactivated_at');
    table.text('deactivation_reason');
    table.uuid('reinvited_by').references('id').inTable('users');
    table.timestamp('reinvited_at');
    table.string('new_invite_code').unique();
    table.timestamp('created_at').defaultTo(knex.fn.now());

    // Indexes for faster lookups
    table.index(['account_status', 'is_compromised']);
    table.index('new_invite_code');
  });
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function(knex) {
  return knex.schema.dropTable('users');
}; 