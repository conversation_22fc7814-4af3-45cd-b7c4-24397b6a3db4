{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Projects/QSC1/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { __decorate } from \"tslib\";\nimport { Injectable } from '@angular/core';\nimport { StateService } from './state.service';\nimport { EncryptionService } from './encryption.service';\nimport { NotificationService } from './notification.service';\nimport { ChainDeletionService } from './chain-deletion.service';\nlet MessageDeletionService = class MessageDeletionService {\n  constructor(stateService, encryptionService, notificationService, chainDeletionService) {\n    this.stateService = stateService;\n    this.encryptionService = encryptionService;\n    this.notificationService = notificationService;\n    this.chainDeletionService = chainDeletionService;\n  }\n  deleteMessage(id) {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      // 1. Remove from state\n      _this.stateService.removeMessage(id);\n      // 2. Wipe encryption keys\n      _this.encryptionService.wipeMemory();\n      // 3. Delete from chain\n      yield _this.chainDeletionService.deleteFromChain(id);\n      // 4. Notify sender\n      yield _this.notificationService.createDeletionNotification(id);\n    })();\n  }\n  deleteCompromisedMessages(ids) {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      // 1. Remove all compromised messages\n      ids.forEach(id => _this2.stateService.removeMessage(id));\n      // 2. Wipe all encryption keys\n      _this2.encryptionService.wipeMemory();\n      // 3. Delete from chain\n      yield _this2.chainDeletionService.deleteCompromisedChain(ids);\n      // 4. Force re-authentication\n      _this2.stateService.setAuthenticated(false);\n    })();\n  }\n  static {\n    this.ctorParameters = () => [{\n      type: StateService\n    }, {\n      type: EncryptionService\n    }, {\n      type: NotificationService\n    }, {\n      type: ChainDeletionService\n    }];\n  }\n};\nMessageDeletionService = __decorate([Injectable({\n  providedIn: 'root'\n})], MessageDeletionService);\nexport { MessageDeletionService };", "map": {"version": 3, "names": ["Injectable", "StateService", "EncryptionService", "NotificationService", "ChainDeletionService", "MessageDeletionService", "constructor", "stateService", "encryptionService", "notificationService", "chainDeletionService", "deleteMessage", "id", "_this", "_asyncToGenerator", "removeMessage", "wipeMemory", "deleteFrom<PERSON><PERSON><PERSON>", "createDeletionNotification", "deleteCompromisedMessages", "ids", "_this2", "for<PERSON>ach", "deleteCompromised<PERSON><PERSON>n", "setAuthenticated", "__decorate", "providedIn"], "sources": ["C:\\Users\\<USER>\\Projects\\QSC1\\frontend\\src\\app\\services\\message-deletion.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { StateService } from './state.service';\r\nimport { EncryptionService } from './encryption.service';\r\nimport { NotificationService } from './notification.service';\r\nimport { ChainDeletionService } from './chain-deletion.service';\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class MessageDeletionService {\r\n  constructor(\r\n    private stateService: StateService,\r\n    private encryptionService: EncryptionService,\r\n    private notificationService: NotificationService,\r\n    private chainDeletionService: ChainDeletionService\r\n  ) {}\r\n\r\n  public async deleteMessage(id: string): Promise<void> {\r\n    // 1. Remove from state\r\n    this.stateService.removeMessage(id);\r\n\r\n    // 2. Wipe encryption keys\r\n    this.encryptionService.wipeMemory();\r\n\r\n    // 3. Delete from chain\r\n    await this.chainDeletionService.deleteFromChain(id);\r\n\r\n    // 4. Notify sender\r\n    await this.notificationService.createDeletionNotification(id);\r\n  }\r\n\r\n  public async deleteCompromisedMessages(ids: string[]): Promise<void> {\r\n    // 1. Remove all compromised messages\r\n    ids.forEach(id => this.stateService.removeMessage(id));\r\n\r\n    // 2. Wipe all encryption keys\r\n    this.encryptionService.wipeMemory();\r\n\r\n    // 3. Delete from chain\r\n    await this.chainDeletionService.deleteCompromisedChain(ids);\r\n\r\n    // 4. Force re-authentication\r\n    this.stateService.setAuthenticated(false);\r\n  }\r\n}\r\n"], "mappings": ";;AAAA,SAASA,UAAU,QAAQ,eAAe;AAC1C,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,iBAAiB,QAAQ,sBAAsB;AACxD,SAASC,mBAAmB,QAAQ,wBAAwB;AAC5D,SAASC,oBAAoB,QAAQ,0BAA0B;AAKxD,IAAMC,sBAAsB,GAA5B,MAAMA,sBAAsB;EACjCC,YACUC,YAA0B,EAC1BC,iBAAoC,EACpCC,mBAAwC,EACxCC,oBAA0C;IAH1C,KAAAH,YAAY,GAAZA,YAAY;IACZ,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,mBAAmB,GAAnBA,mBAAmB;IACnB,KAAAC,oBAAoB,GAApBA,oBAAoB;EAC3B;EAEUC,aAAaA,CAACC,EAAU;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MACnC;MACAD,KAAI,CAACN,YAAY,CAACQ,aAAa,CAACH,EAAE,CAAC;MAEnC;MACAC,KAAI,CAACL,iBAAiB,CAACQ,UAAU,EAAE;MAEnC;MACA,MAAMH,KAAI,CAACH,oBAAoB,CAACO,eAAe,CAACL,EAAE,CAAC;MAEnD;MACA,MAAMC,KAAI,CAACJ,mBAAmB,CAACS,0BAA0B,CAACN,EAAE,CAAC;IAAC;EAChE;EAEaO,yBAAyBA,CAACC,GAAa;IAAA,IAAAC,MAAA;IAAA,OAAAP,iBAAA;MAClD;MACAM,GAAG,CAACE,OAAO,CAACV,EAAE,IAAIS,MAAI,CAACd,YAAY,CAACQ,aAAa,CAACH,EAAE,CAAC,CAAC;MAEtD;MACAS,MAAI,CAACb,iBAAiB,CAACQ,UAAU,EAAE;MAEnC;MACA,MAAMK,MAAI,CAACX,oBAAoB,CAACa,sBAAsB,CAACH,GAAG,CAAC;MAE3D;MACAC,MAAI,CAACd,YAAY,CAACiB,gBAAgB,CAAC,KAAK,CAAC;IAAC;EAC5C;;;;;;;;;;;;;AAlCWnB,sBAAsB,GAAAoB,UAAA,EAHlCzB,UAAU,CAAC;EACV0B,UAAU,EAAE;CACb,CAAC,C,EACWrB,sBAAsB,CAmClC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}