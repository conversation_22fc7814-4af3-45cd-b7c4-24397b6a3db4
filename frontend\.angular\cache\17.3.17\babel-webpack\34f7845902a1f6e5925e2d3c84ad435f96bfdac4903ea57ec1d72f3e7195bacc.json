{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Projects/QSC1/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"C:/Users/<USER>/Projects/QSC1/frontend/src/app/components/chain-node-list/chain-node-list.component.ts.css?ngResource!=!C:\\\\Users\\\\<USER>\\\\Projects\\\\QSC1\\\\frontend\\\\node_modules\\\\@ngtools\\\\webpack\\\\src\\\\loaders\\\\inline-resource.js?data=CiAgICAuY2hhaW4tbm9kZS1saXN0IHsKICAgICAgcGFkZGluZzogMXJlbTsKICAgICAgbWF4LXdpZHRoOiA4MDBweDsKICAgICAgbWFyZ2luOiAwIGF1dG87CiAgICB9CgogICAgaDIgewogICAgICBtYXJnaW4tYm90dG9tOiAxcmVtOwogICAgICBjb2xvcjogIzMzMzsKICAgIH0KCiAgICAuZXJyb3ItbWVzc2FnZSB7CiAgICAgIGNvbG9yOiAjZGMzNTQ1OwogICAgICBwYWRkaW5nOiAwLjVyZW07CiAgICAgIG1hcmdpbi1ib3R0b206IDFyZW07CiAgICAgIGJvcmRlcjogMXB4IHNvbGlkICNkYzM1NDU7CiAgICAgIGJvcmRlci1yYWRpdXM6IDRweDsKICAgICAgYmFja2dyb3VuZC1jb2xvcjogI2Y4ZDdkYTsKICAgIH0KCiAgICAubm8tbm9kZXMgewogICAgICB0ZXh0LWFsaWduOiBjZW50ZXI7CiAgICAgIGNvbG9yOiAjNmM3NTdkOwogICAgICBwYWRkaW5nOiAycmVtOwogICAgfQoKICAgIC5jaGFpbi1ub2RlIHsKICAgICAgcGFkZGluZzogMXJlbTsKICAgICAgbWFyZ2luLWJvdHRvbTogMXJlbTsKICAgICAgYm9yZGVyOiAxcHggc29saWQgI2RlZTJlNjsKICAgICAgYm9yZGVyLXJhZGl1czogNHB4OwogICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjZmZmOwogICAgICB0cmFuc2l0aW9uOiBhbGwgMC4ycyBlYXNlOwoKICAgICAgJjpob3ZlciB7CiAgICAgICAgYm94LXNoYWRvdzogMCAycHggNHB4IHJnYmEoMCwwLDAsMC4xKTsKICAgICAgfQoKICAgICAgJi5kZWxldGVkIHsKICAgICAgICBib3JkZXItbGVmdDogNHB4IHNvbGlkICNkYzM1NDU7CiAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogI2Y4ZjlmYTsKICAgICAgICBvcGFjaXR5OiAwLjg7CiAgICAgIH0KICAgIH0KCiAgICAubm9kZS1oZWFkZXIgewogICAgICBkaXNwbGF5OiBmbGV4OwogICAgICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47CiAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAgICAgIG1hcmdpbi1ib3R0b206IDAuNXJlbTsKICAgIH0KCiAgICAubm9kZS1pZCB7CiAgICAgIGZvbnQtd2VpZ2h0OiBib2xkOwogICAgICBjb2xvcjogIzAwN2JmZjsKICAgIH0KCiAgICAubm9kZS10aW1lIHsKICAgICAgY29sb3I6ICM2Yzc1N2Q7CiAgICAgIGZvbnQtc2l6ZTogMC44NzVyZW07CiAgICB9CgogICAgLm5vZGUtZGV0YWlscyB7CiAgICAgIGNvbG9yOiAjMjEyNTI5OwogICAgICBsaW5lLWhlaWdodDogMS41OwogICAgfQoKICAgIC5tZXNzYWdlLWlkIHsKICAgICAgZm9udC1mYW1pbHk6IG1vbm9zcGFjZTsKICAgICAgYmFja2dyb3VuZC1jb2xvcjogI2Y4ZjlmYTsKICAgICAgcGFkZGluZzogMC4yNXJlbSAwLjVyZW07CiAgICAgIGJvcmRlci1yYWRpdXM6IDRweDsKICAgICAgbWFyZ2luLWJvdHRvbTogMC41cmVtOwogICAgfQoKICAgIC5kZWxldGlvbi1pbmZvIHsKICAgICAgY29sb3I6ICNkYzM1NDU7CiAgICAgIGZvbnQtc2l6ZTogMC44NzVyZW07CiAgICAgIG1hcmdpbi10b3A6IDAuNXJlbTsKICAgIH0KICA%3D!C:/Users/<USER>/Projects/QSC1/frontend/src/app/components/chain-node-list/chain-node-list.component.ts\";\nimport { Component } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { ChainDeletionService } from '../../services/chain-deletion.service';\nimport { ErrorHandlingService } from '../../services/error-handling.service';\nlet ChainNodeListComponent = class ChainNodeListComponent {\n  constructor(chainDeletionService, errorHandlingService) {\n    this.chainDeletionService = chainDeletionService;\n    this.errorHandlingService = errorHandlingService;\n    this.chainNodes = [];\n    this.errorMessage = null;\n  }\n  ngOnInit() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      yield _this.loadChainNodes();\n    })();\n  }\n  loadChainNodes() {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        _this2.chainNodes = yield _this2.chainDeletionService.getChainNodes();\n      } catch (error) {\n        _this2.errorHandlingService.handleError(error, 'STORAGE');\n        _this2.errorMessage = 'Failed to load chain nodes';\n      }\n    })();\n  }\n  static {\n    this.ctorParameters = () => [{\n      type: ChainDeletionService\n    }, {\n      type: ErrorHandlingService\n    }];\n  }\n};\nChainNodeListComponent = __decorate([Component({\n  selector: 'app-chain-node-list',\n  standalone: true,\n  imports: [CommonModule],\n  template: `\n    <div class=\"chain-node-list\">\n      <h2>Message Chain</h2>\n      <div *ngIf=\"errorMessage\" class=\"error-message\">\n        {{ errorMessage }}\n      </div>\n      <div *ngIf=\"chainNodes.length === 0\" class=\"no-nodes\">\n        No messages in chain\n      </div>\n      <div *ngFor=\"let node of chainNodes\"\n           class=\"chain-node\"\n           [class.deleted]=\"node.deleted\">\n        <div class=\"node-header\">\n          <span class=\"node-id\">ID: {{ node.id }}</span>\n          <span class=\"node-time\">\n            {{ node.timestamp | date:'medium' }}\n          </span>\n        </div>\n        <div class=\"node-details\">\n          <div class=\"message-id\">Message ID: {{ node.messageId }}</div>\n          <div *ngIf=\"node.deleted\" class=\"deletion-info\">\n            Deleted at: {{ node.deletionTimestamp | date:'medium' }}\n          </div>\n        </div>\n      </div>\n    </div>\n  `,\n  styles: [__NG_CLI_RESOURCE__0]\n})], ChainNodeListComponent);\nexport { ChainNodeListComponent };", "map": {"version": 3, "names": ["Component", "CommonModule", "ChainDeletionService", "ErrorHandlingService", "ChainNodeListComponent", "constructor", "chainDeletionService", "errorHandlingService", "chainNodes", "errorMessage", "ngOnInit", "_this", "_asyncToGenerator", "loadChainNodes", "_this2", "getChainNodes", "error", "handleError", "__decorate", "selector", "standalone", "imports", "template"], "sources": ["C:\\Users\\<USER>\\Projects\\QSC1\\frontend\\src\\app\\components\\chain-node-list\\chain-node-list.component.ts"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { ChainDeletionService } from '../../services/chain-deletion.service';\r\nimport { ErrorHandlingService } from '../../services/error-handling.service';\r\n\r\ninterface ChainNode {\r\n  id: string;\r\n  messageId: string;\r\n  timestamp: Date;\r\n  deleted: boolean;\r\n  deletionTimestamp?: Date;\r\n}\r\n\r\n@Component({\r\n  selector: 'app-chain-node-list',\r\n  standalone: true,\r\n  imports: [CommonModule],\r\n  template: `\r\n    <div class=\"chain-node-list\">\r\n      <h2>Message Chain</h2>\r\n      <div *ngIf=\"errorMessage\" class=\"error-message\">\r\n        {{ errorMessage }}\r\n      </div>\r\n      <div *ngIf=\"chainNodes.length === 0\" class=\"no-nodes\">\r\n        No messages in chain\r\n      </div>\r\n      <div *ngFor=\"let node of chainNodes\"\r\n           class=\"chain-node\"\r\n           [class.deleted]=\"node.deleted\">\r\n        <div class=\"node-header\">\r\n          <span class=\"node-id\">ID: {{ node.id }}</span>\r\n          <span class=\"node-time\">\r\n            {{ node.timestamp | date:'medium' }}\r\n          </span>\r\n        </div>\r\n        <div class=\"node-details\">\r\n          <div class=\"message-id\">Message ID: {{ node.messageId }}</div>\r\n          <div *ngIf=\"node.deleted\" class=\"deletion-info\">\r\n            Deleted at: {{ node.deletionTimestamp | date:'medium' }}\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  `,\r\n  styles: [`\r\n    .chain-node-list {\r\n      padding: 1rem;\r\n      max-width: 800px;\r\n      margin: 0 auto;\r\n    }\r\n\r\n    h2 {\r\n      margin-bottom: 1rem;\r\n      color: #333;\r\n    }\r\n\r\n    .error-message {\r\n      color: #dc3545;\r\n      padding: 0.5rem;\r\n      margin-bottom: 1rem;\r\n      border: 1px solid #dc3545;\r\n      border-radius: 4px;\r\n      background-color: #f8d7da;\r\n    }\r\n\r\n    .no-nodes {\r\n      text-align: center;\r\n      color: #6c757d;\r\n      padding: 2rem;\r\n    }\r\n\r\n    .chain-node {\r\n      padding: 1rem;\r\n      margin-bottom: 1rem;\r\n      border: 1px solid #dee2e6;\r\n      border-radius: 4px;\r\n      background-color: #fff;\r\n      transition: all 0.2s ease;\r\n\r\n      &:hover {\r\n        box-shadow: 0 2px 4px rgba(0,0,0,0.1);\r\n      }\r\n\r\n      &.deleted {\r\n        border-left: 4px solid #dc3545;\r\n        background-color: #f8f9fa;\r\n        opacity: 0.8;\r\n      }\r\n    }\r\n\r\n    .node-header {\r\n      display: flex;\r\n      justify-content: space-between;\r\n      align-items: center;\r\n      margin-bottom: 0.5rem;\r\n    }\r\n\r\n    .node-id {\r\n      font-weight: bold;\r\n      color: #007bff;\r\n    }\r\n\r\n    .node-time {\r\n      color: #6c757d;\r\n      font-size: 0.875rem;\r\n    }\r\n\r\n    .node-details {\r\n      color: #212529;\r\n      line-height: 1.5;\r\n    }\r\n\r\n    .message-id {\r\n      font-family: monospace;\r\n      background-color: #f8f9fa;\r\n      padding: 0.25rem 0.5rem;\r\n      border-radius: 4px;\r\n      margin-bottom: 0.5rem;\r\n    }\r\n\r\n    .deletion-info {\r\n      color: #dc3545;\r\n      font-size: 0.875rem;\r\n      margin-top: 0.5rem;\r\n    }\r\n  `]\r\n})\r\nexport class ChainNodeListComponent implements OnInit {\r\n  chainNodes: ChainNode[] = [];\r\n  errorMessage: string | null = null;\r\n\r\n  constructor(\r\n    private chainDeletionService: ChainDeletionService,\r\n    private errorHandlingService: ErrorHandlingService\r\n  ) {}\r\n\r\n  async ngOnInit() {\r\n    await this.loadChainNodes();\r\n  }\r\n\r\n  private async loadChainNodes() {\r\n    try {\r\n      this.chainNodes = await this.chainDeletionService.getChainNodes();\r\n    } catch (error) {\r\n      this.errorHandlingService.handleError(error as Error, 'STORAGE');\r\n      this.errorMessage = 'Failed to load chain nodes';\r\n    }\r\n  }\r\n}\r\n"], "mappings": ";;;AAAA,SAASA,SAAS,QAAgB,eAAe;AACjD,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,oBAAoB,QAAQ,uCAAuC;AAC5E,SAASC,oBAAoB,QAAQ,uCAAuC;AA4HrE,IAAMC,sBAAsB,GAA5B,MAAMA,sBAAsB;EAIjCC,YACUC,oBAA0C,EAC1CC,oBAA0C;IAD1C,KAAAD,oBAAoB,GAApBA,oBAAoB;IACpB,KAAAC,oBAAoB,GAApBA,oBAAoB;IAL9B,KAAAC,UAAU,GAAgB,EAAE;IAC5B,KAAAC,YAAY,GAAkB,IAAI;EAK/B;EAEGC,QAAQA,CAAA;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MACZ,MAAMD,KAAI,CAACE,cAAc,EAAE;IAAC;EAC9B;EAEcA,cAAcA,CAAA;IAAA,IAAAC,MAAA;IAAA,OAAAF,iBAAA;MAC1B,IAAI;QACFE,MAAI,CAACN,UAAU,SAASM,MAAI,CAACR,oBAAoB,CAACS,aAAa,EAAE;OAClE,CAAC,OAAOC,KAAK,EAAE;QACdF,MAAI,CAACP,oBAAoB,CAACU,WAAW,CAACD,KAAc,EAAE,SAAS,CAAC;QAChEF,MAAI,CAACL,YAAY,GAAG,4BAA4B;;IACjD;EACH;;;;;;;;;AApBWL,sBAAsB,GAAAc,UAAA,EAlHlClB,SAAS,CAAC;EACTmB,QAAQ,EAAE,qBAAqB;EAC/BC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CAACpB,YAAY,CAAC;EACvBqB,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;GA0BT;;CAmFF,CAAC,C,EACWlB,sBAAsB,CAqBlC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}