import { DataSource } from 'typeorm';
import { config } from '../config';
import { dataSource } from './data-source';

async function initializeDatabase() {
  try {
    // Initialize the actual database connection
    await dataSource.initialize();
    console.log('Database connection initialized successfully');

    // Run migrations
    await dataSource.runMigrations();
    console.log('Migrations completed successfully');

    // Close the connection
    await dataSource.destroy();
    console.log('Database initialization completed');
  } catch (error) {
    console.error('Error during database initialization:', error);
    process.exit(1);
  }
}

initializeDatabase(); 