import { <PERSON><PERSON><PERSON>, Column, PrimaryGenerated<PERSON><PERSON>umn, CreateDateColumn, UpdateDateColumn, ManyToOne, JoinColumn } from 'typeorm';
import { User } from '../../users/entities/user.entity';
import { ChatRoom } from './chat-room.entity';
import { IMessage } from '@qsc/shared';

@Entity('messages')
export class Message implements IMessage {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'sender_id' })
  sender: User;

  @Column()
  senderId: string;

  // For direct messages
  @Column({ nullable: true })
  recipientId?: string;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'recipient_id' })
  recipient?: User;

  // For room/group messages
  @Column({ nullable: true })
  roomId?: string;

  @ManyToOne(() => ChatRoom)
  @JoinColumn({ name: 'room_id' })
  room?: ChatRoom;

  @Column()
  encryptedContent: string;

  @Column({ nullable: true })
  symmetricKey?: string;

  @Column({ nullable: true })
  iv?: string;

  @Column({ nullable: true })
  authTag?: string;

  @Column({ nullable: true })
  pqcSignature?: string;

  @Column()
  messageHash: string;

  @Column({ default: false })
  isDeleted: boolean;

  @Column({ nullable: true })
  deletedAt?: Date;

  @Column({ nullable: true })
  expiresAt?: Date;

  @Column({ default: false })
  isRead: boolean;

  @Column({ nullable: true })
  readAt?: Date;

  @Column({ default: false })
  isCompromised: boolean;

  @Column({ default: 0 })
  compromiseAttempts: number;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // Legacy field mappings for compatibility
  get sender_id(): string {
    return this.senderId;
  }

  get recipient_id(): string | undefined {
    return this.recipientId;
  }

  get encrypted_content(): string {
    return this.encryptedContent;
  }

  get is_deleted(): boolean {
    return this.isDeleted;
  }

  get expires_at(): Date | undefined {
    return this.expiresAt;
  }

  get is_read(): boolean {
    return this.isRead;
  }

  get compromise_attempts(): number {
    return this.compromiseAttempts;
  }
}