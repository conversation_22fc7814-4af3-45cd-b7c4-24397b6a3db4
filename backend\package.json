{"name": "qsc-backend", "version": "1.0.0", "description": "Quantum-Secure <PERSON>", "main": "dist/main.js", "scripts": {"prebuild": "<PERSON><PERSON><PERSON> dist", "build": "nest build", "format": "prettier --write \"src/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\"", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:e2e": "jest --config ./test/jest-e2e.json", "db:init": "ts-node src/database/init.ts", "db:migrate": "ts-node ./node_modules/typeorm/cli.js migration:run -d src/database/data-source.ts", "db:migrate:revert": "ts-node ./node_modules/typeorm/cli.js migration:revert -d src/database/data-source.ts", "db:seed": "ts-node src/database/seed.ts", "create:admin": "ts-node src/scripts/create-admin.ts", "dev": "ts-node src/main.ts", "cleanup:migrations": "ts-node src/scripts/cleanup-legacy-migrations.ts", "validate:user-entity": "ts-node src/scripts/validate-user-entity.ts", "test:user-entity": "ts-node src/scripts/test-user-entity-consolidation.ts", "db:reset": "npm run cleanup:migrations && npm run db:migrate && npm run db:seed"}, "dependencies": {"@nestjs/common": "^10.0.0", "@nestjs/config": "^3.0.0", "@nestjs/core": "^10.0.0", "@nestjs/jwt": "^10.0.0", "@nestjs/passport": "^10.0.0", "@nestjs/platform-express": "^10.0.0", "@nestjs/throttler": "^6.4.0", "@nestjs/typeorm": "^10.0.0", "@nestjs/websockets": "^10.0.0", "@noble/post-quantum": "^0.4.1", "@qsc/shared": "file:../shared", "@types/libsodium-wrappers": "^0.7.14", "@types/socket.io": "^3.0.1", "argon2": "^0.31.0", "bcrypt": "^5.1.0", "better-sqlite3": "^8.7.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.0", "express-rate-limit": "^7.0.0", "helmet": "^7.0.0", "ioredis": "^5.6.1", "libsodium-wrappers": "^0.7.15", "node-forge": "^1.3.1", "nodemailer": "^7.0.3", "passport": "^0.6.0", "passport-jwt": "^4.0.0", "reflect-metadata": "^0.1.13", "rimraf": "^5.0.0", "rxjs": "^7.8.1", "socket.io": "^4.8.1", "typeorm": "^0.3.0", "winston": "^3.10.0"}, "devDependencies": {"@nestjs/cli": "^10.0.0", "@nestjs/schematics": "^10.0.0", "@nestjs/testing": "^10.0.0", "@types/bcrypt": "^5.0.0", "@types/cors": "^2.8.18", "@types/express": "^4.17.17", "@types/jest": "^29.5.2", "@types/node": "^20.3.1", "@types/node-forge": "^1.3.11", "@types/nodemailer": "^6.4.17", "@types/passport-jwt": "^3.0.8", "@types/supertest": "^2.0.12", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.42.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "jest": "^29.5.0", "prettier": "^3.0.0", "source-map-support": "^0.5.21", "supertest": "^6.3.3", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.1.3"}}