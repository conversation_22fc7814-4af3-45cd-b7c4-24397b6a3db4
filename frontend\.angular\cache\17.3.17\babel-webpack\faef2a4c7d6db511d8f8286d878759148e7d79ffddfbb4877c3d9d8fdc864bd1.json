{"ast": null, "code": "import { Subject, BehaviorSubject } from 'rxjs';\nimport { filter, map } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nexport class WebSocketService {\n  constructor() {\n    this.socket = null;\n    this.messageSubject = new Subject();\n    this.connectionStateSubject = new BehaviorSubject(false);\n    this.messages$ = this.messageSubject.asObservable();\n    this.connectionState$ = this.connectionStateSubject.asObservable();\n    this.reconnectAttempts = 0;\n    this.maxReconnectAttempts = 5;\n    this.reconnectInterval = 5000; // 5 seconds\n    // Auto-connect when service is instantiated\n    // this.connect();\n  }\n  /**\n   * Connect to WebSocket server\n   */\n  connect(url) {\n    if (this.socket && this.socket.readyState === WebSocket.OPEN) {\n      return; // Already connected\n    }\n    const wsUrl = url || this.getWebSocketUrl();\n    try {\n      this.socket = new WebSocket(wsUrl);\n      this.socket.onopen = () => {\n        console.log('WebSocket connected');\n        this.connectionStateSubject.next(true);\n        this.reconnectAttempts = 0;\n      };\n      this.socket.onmessage = event => {\n        try {\n          const message = JSON.parse(event.data);\n          this.messageSubject.next(message);\n        } catch (error) {\n          console.error('Failed to parse WebSocket message:', error);\n        }\n      };\n      this.socket.onclose = () => {\n        console.log('WebSocket disconnected');\n        this.connectionStateSubject.next(false);\n        this.attemptReconnect();\n      };\n      this.socket.onerror = error => {\n        console.error('WebSocket error:', error);\n        this.connectionStateSubject.next(false);\n      };\n    } catch (error) {\n      console.error('Failed to create WebSocket connection:', error);\n      this.connectionStateSubject.next(false);\n    }\n  }\n  /**\n   * Disconnect from WebSocket server\n   */\n  disconnect() {\n    if (this.socket) {\n      this.socket.close();\n      this.socket = null;\n    }\n    this.connectionStateSubject.next(false);\n  }\n  /**\n   * Send a message through WebSocket\n   */\n  send(message) {\n    if (this.socket && this.socket.readyState === WebSocket.OPEN) {\n      this.socket.send(JSON.stringify(message));\n    } else {\n      console.warn('WebSocket is not connected. Message not sent:', message);\n    }\n  }\n  /**\n   * Listen for specific message types\n   */\n  on(messageType) {\n    return this.messages$.pipe(filter(message => message.type === messageType), map(message => message.data));\n  }\n  /**\n   * Check if WebSocket is connected\n   */\n  isConnected() {\n    return this.socket?.readyState === WebSocket.OPEN;\n  }\n  /**\n   * Get WebSocket URL based on current environment\n   */\n  getWebSocketUrl() {\n    const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';\n    const host = window.location.host;\n    // In development, use localhost:3000 for backend\n    if (host.includes('localhost') || host.includes('127.0.0.1')) {\n      return `${protocol}//localhost:3000/ws`;\n    }\n    // In production, use same host\n    return `${protocol}//${host}/ws`;\n  }\n  /**\n   * Attempt to reconnect to WebSocket\n   */\n  attemptReconnect() {\n    if (this.reconnectAttempts < this.maxReconnectAttempts) {\n      this.reconnectAttempts++;\n      console.log(`Attempting to reconnect... (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);\n      setTimeout(() => {\n        this.connect();\n      }, this.reconnectInterval);\n    } else {\n      console.error('Max reconnection attempts reached. Please refresh the page.');\n    }\n  }\n  /**\n   * Send authentication message\n   */\n  authenticate(token) {\n    this.send({\n      type: 'auth',\n      data: {\n        token\n      }\n    });\n  }\n  /**\n   * Join a room/channel\n   */\n  joinRoom(roomId) {\n    this.send({\n      type: 'join',\n      data: {\n        roomId\n      }\n    });\n  }\n  /**\n   * Leave a room/channel\n   */\n  leaveRoom(roomId) {\n    this.send({\n      type: 'leave',\n      data: {\n        roomId\n      }\n    });\n  }\n  /**\n   * Send a chat message\n   */\n  sendMessage(content, recipient, groupId) {\n    this.send({\n      type: 'message',\n      data: {\n        content,\n        recipient,\n        groupId,\n        timestamp: Date.now()\n      }\n    });\n  }\n  /**\n   * Send typing indicator\n   */\n  sendTyping(isTyping, recipient, groupId) {\n    this.send({\n      type: 'typing',\n      data: {\n        isTyping,\n        recipient,\n        groupId\n      }\n    });\n  }\n  static {\n    this.ɵfac = function WebSocketService_Factory(t) {\n      return new (t || WebSocketService)();\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: WebSocketService,\n      factory: WebSocketService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["Subject", "BehaviorSubject", "filter", "map", "WebSocketService", "constructor", "socket", "messageSubject", "connectionStateSubject", "messages$", "asObservable", "connectionState$", "reconnectAttempts", "maxReconnectAttempts", "reconnectInterval", "connect", "url", "readyState", "WebSocket", "OPEN", "wsUrl", "getWebSocketUrl", "onopen", "console", "log", "next", "onmessage", "event", "message", "JSON", "parse", "data", "error", "onclose", "attemptReconnect", "onerror", "disconnect", "close", "send", "stringify", "warn", "on", "messageType", "pipe", "type", "isConnected", "protocol", "window", "location", "host", "includes", "setTimeout", "authenticate", "token", "joinRoom", "roomId", "leaveRoom", "sendMessage", "content", "recipient", "groupId", "timestamp", "Date", "now", "sendTyping", "isTyping", "factory", "ɵfac", "providedIn"], "sources": ["D:\\TCL1\\Projects\\Projects\\QSC1\\frontend\\src\\app\\services\\websocket.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { Observable, Subject, BehaviorSubject } from 'rxjs';\nimport { filter, map } from 'rxjs/operators';\n\nexport interface WebSocketMessage {\n  type: string;\n  data: any;\n  timestamp?: number;\n}\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class WebSocketService {\n  private socket: WebSocket | null = null;\n  private messageSubject = new Subject<WebSocketMessage>();\n  private connectionStateSubject = new BehaviorSubject<boolean>(false);\n  \n  public messages$ = this.messageSubject.asObservable();\n  public connectionState$ = this.connectionStateSubject.asObservable();\n\n  private reconnectAttempts = 0;\n  private maxReconnectAttempts = 5;\n  private reconnectInterval = 5000; // 5 seconds\n\n  constructor() {\n    // Auto-connect when service is instantiated\n    // this.connect();\n  }\n\n  /**\n   * Connect to WebSocket server\n   */\n  connect(url?: string): void {\n    if (this.socket && this.socket.readyState === WebSocket.OPEN) {\n      return; // Already connected\n    }\n\n    const wsUrl = url || this.getWebSocketUrl();\n    \n    try {\n      this.socket = new WebSocket(wsUrl);\n      \n      this.socket.onopen = () => {\n        console.log('WebSocket connected');\n        this.connectionStateSubject.next(true);\n        this.reconnectAttempts = 0;\n      };\n\n      this.socket.onmessage = (event) => {\n        try {\n          const message: WebSocketMessage = JSON.parse(event.data);\n          this.messageSubject.next(message);\n        } catch (error) {\n          console.error('Failed to parse WebSocket message:', error);\n        }\n      };\n\n      this.socket.onclose = () => {\n        console.log('WebSocket disconnected');\n        this.connectionStateSubject.next(false);\n        this.attemptReconnect();\n      };\n\n      this.socket.onerror = (error) => {\n        console.error('WebSocket error:', error);\n        this.connectionStateSubject.next(false);\n      };\n\n    } catch (error) {\n      console.error('Failed to create WebSocket connection:', error);\n      this.connectionStateSubject.next(false);\n    }\n  }\n\n  /**\n   * Disconnect from WebSocket server\n   */\n  disconnect(): void {\n    if (this.socket) {\n      this.socket.close();\n      this.socket = null;\n    }\n    this.connectionStateSubject.next(false);\n  }\n\n  /**\n   * Send a message through WebSocket\n   */\n  send(message: WebSocketMessage): void {\n    if (this.socket && this.socket.readyState === WebSocket.OPEN) {\n      this.socket.send(JSON.stringify(message));\n    } else {\n      console.warn('WebSocket is not connected. Message not sent:', message);\n    }\n  }\n\n  /**\n   * Listen for specific message types\n   */\n  on(messageType: string): Observable<any> {\n    return this.messages$.pipe(\n      filter(message => message.type === messageType),\n      map(message => message.data)\n    );\n  }\n\n  /**\n   * Check if WebSocket is connected\n   */\n  isConnected(): boolean {\n    return this.socket?.readyState === WebSocket.OPEN;\n  }\n\n  /**\n   * Get WebSocket URL based on current environment\n   */\n  private getWebSocketUrl(): string {\n    const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';\n    const host = window.location.host;\n    \n    // In development, use localhost:3000 for backend\n    if (host.includes('localhost') || host.includes('127.0.0.1')) {\n      return `${protocol}//localhost:3000/ws`;\n    }\n    \n    // In production, use same host\n    return `${protocol}//${host}/ws`;\n  }\n\n  /**\n   * Attempt to reconnect to WebSocket\n   */\n  private attemptReconnect(): void {\n    if (this.reconnectAttempts < this.maxReconnectAttempts) {\n      this.reconnectAttempts++;\n      console.log(`Attempting to reconnect... (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);\n      \n      setTimeout(() => {\n        this.connect();\n      }, this.reconnectInterval);\n    } else {\n      console.error('Max reconnection attempts reached. Please refresh the page.');\n    }\n  }\n\n  /**\n   * Send authentication message\n   */\n  authenticate(token: string): void {\n    this.send({\n      type: 'auth',\n      data: { token }\n    });\n  }\n\n  /**\n   * Join a room/channel\n   */\n  joinRoom(roomId: string): void {\n    this.send({\n      type: 'join',\n      data: { roomId }\n    });\n  }\n\n  /**\n   * Leave a room/channel\n   */\n  leaveRoom(roomId: string): void {\n    this.send({\n      type: 'leave',\n      data: { roomId }\n    });\n  }\n\n  /**\n   * Send a chat message\n   */\n  sendMessage(content: string, recipient?: string, groupId?: string): void {\n    this.send({\n      type: 'message',\n      data: {\n        content,\n        recipient,\n        groupId,\n        timestamp: Date.now()\n      }\n    });\n  }\n\n  /**\n   * Send typing indicator\n   */\n  sendTyping(isTyping: boolean, recipient?: string, groupId?: string): void {\n    this.send({\n      type: 'typing',\n      data: {\n        isTyping,\n        recipient,\n        groupId\n      }\n    });\n  }\n}\n"], "mappings": "AACA,SAAqBA,OAAO,EAAEC,eAAe,QAAQ,MAAM;AAC3D,SAASC,MAAM,EAAEC,GAAG,QAAQ,gBAAgB;;AAW5C,OAAM,MAAOC,gBAAgB;EAY3BC,YAAA;IAXQ,KAAAC,MAAM,GAAqB,IAAI;IAC/B,KAAAC,cAAc,GAAG,IAAIP,OAAO,EAAoB;IAChD,KAAAQ,sBAAsB,GAAG,IAAIP,eAAe,CAAU,KAAK,CAAC;IAE7D,KAAAQ,SAAS,GAAG,IAAI,CAACF,cAAc,CAACG,YAAY,EAAE;IAC9C,KAAAC,gBAAgB,GAAG,IAAI,CAACH,sBAAsB,CAACE,YAAY,EAAE;IAE5D,KAAAE,iBAAiB,GAAG,CAAC;IACrB,KAAAC,oBAAoB,GAAG,CAAC;IACxB,KAAAC,iBAAiB,GAAG,IAAI,CAAC,CAAC;IAGhC;IACA;EACF;EAEA;;;EAGAC,OAAOA,CAACC,GAAY;IAClB,IAAI,IAAI,CAACV,MAAM,IAAI,IAAI,CAACA,MAAM,CAACW,UAAU,KAAKC,SAAS,CAACC,IAAI,EAAE;MAC5D,OAAO,CAAC;;IAGV,MAAMC,KAAK,GAAGJ,GAAG,IAAI,IAAI,CAACK,eAAe,EAAE;IAE3C,IAAI;MACF,IAAI,CAACf,MAAM,GAAG,IAAIY,SAAS,CAACE,KAAK,CAAC;MAElC,IAAI,CAACd,MAAM,CAACgB,MAAM,GAAG,MAAK;QACxBC,OAAO,CAACC,GAAG,CAAC,qBAAqB,CAAC;QAClC,IAAI,CAAChB,sBAAsB,CAACiB,IAAI,CAAC,IAAI,CAAC;QACtC,IAAI,CAACb,iBAAiB,GAAG,CAAC;MAC5B,CAAC;MAED,IAAI,CAACN,MAAM,CAACoB,SAAS,GAAIC,KAAK,IAAI;QAChC,IAAI;UACF,MAAMC,OAAO,GAAqBC,IAAI,CAACC,KAAK,CAACH,KAAK,CAACI,IAAI,CAAC;UACxD,IAAI,CAACxB,cAAc,CAACkB,IAAI,CAACG,OAAO,CAAC;SAClC,CAAC,OAAOI,KAAK,EAAE;UACdT,OAAO,CAACS,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;;MAE9D,CAAC;MAED,IAAI,CAAC1B,MAAM,CAAC2B,OAAO,GAAG,MAAK;QACzBV,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAC;QACrC,IAAI,CAAChB,sBAAsB,CAACiB,IAAI,CAAC,KAAK,CAAC;QACvC,IAAI,CAACS,gBAAgB,EAAE;MACzB,CAAC;MAED,IAAI,CAAC5B,MAAM,CAAC6B,OAAO,GAAIH,KAAK,IAAI;QAC9BT,OAAO,CAACS,KAAK,CAAC,kBAAkB,EAAEA,KAAK,CAAC;QACxC,IAAI,CAACxB,sBAAsB,CAACiB,IAAI,CAAC,KAAK,CAAC;MACzC,CAAC;KAEF,CAAC,OAAOO,KAAK,EAAE;MACdT,OAAO,CAACS,KAAK,CAAC,wCAAwC,EAAEA,KAAK,CAAC;MAC9D,IAAI,CAACxB,sBAAsB,CAACiB,IAAI,CAAC,KAAK,CAAC;;EAE3C;EAEA;;;EAGAW,UAAUA,CAAA;IACR,IAAI,IAAI,CAAC9B,MAAM,EAAE;MACf,IAAI,CAACA,MAAM,CAAC+B,KAAK,EAAE;MACnB,IAAI,CAAC/B,MAAM,GAAG,IAAI;;IAEpB,IAAI,CAACE,sBAAsB,CAACiB,IAAI,CAAC,KAAK,CAAC;EACzC;EAEA;;;EAGAa,IAAIA,CAACV,OAAyB;IAC5B,IAAI,IAAI,CAACtB,MAAM,IAAI,IAAI,CAACA,MAAM,CAACW,UAAU,KAAKC,SAAS,CAACC,IAAI,EAAE;MAC5D,IAAI,CAACb,MAAM,CAACgC,IAAI,CAACT,IAAI,CAACU,SAAS,CAACX,OAAO,CAAC,CAAC;KAC1C,MAAM;MACLL,OAAO,CAACiB,IAAI,CAAC,+CAA+C,EAAEZ,OAAO,CAAC;;EAE1E;EAEA;;;EAGAa,EAAEA,CAACC,WAAmB;IACpB,OAAO,IAAI,CAACjC,SAAS,CAACkC,IAAI,CACxBzC,MAAM,CAAC0B,OAAO,IAAIA,OAAO,CAACgB,IAAI,KAAKF,WAAW,CAAC,EAC/CvC,GAAG,CAACyB,OAAO,IAAIA,OAAO,CAACG,IAAI,CAAC,CAC7B;EACH;EAEA;;;EAGAc,WAAWA,CAAA;IACT,OAAO,IAAI,CAACvC,MAAM,EAAEW,UAAU,KAAKC,SAAS,CAACC,IAAI;EACnD;EAEA;;;EAGQE,eAAeA,CAAA;IACrB,MAAMyB,QAAQ,GAAGC,MAAM,CAACC,QAAQ,CAACF,QAAQ,KAAK,QAAQ,GAAG,MAAM,GAAG,KAAK;IACvE,MAAMG,IAAI,GAAGF,MAAM,CAACC,QAAQ,CAACC,IAAI;IAEjC;IACA,IAAIA,IAAI,CAACC,QAAQ,CAAC,WAAW,CAAC,IAAID,IAAI,CAACC,QAAQ,CAAC,WAAW,CAAC,EAAE;MAC5D,OAAO,GAAGJ,QAAQ,qBAAqB;;IAGzC;IACA,OAAO,GAAGA,QAAQ,KAAKG,IAAI,KAAK;EAClC;EAEA;;;EAGQf,gBAAgBA,CAAA;IACtB,IAAI,IAAI,CAACtB,iBAAiB,GAAG,IAAI,CAACC,oBAAoB,EAAE;MACtD,IAAI,CAACD,iBAAiB,EAAE;MACxBW,OAAO,CAACC,GAAG,CAAC,+BAA+B,IAAI,CAACZ,iBAAiB,IAAI,IAAI,CAACC,oBAAoB,GAAG,CAAC;MAElGsC,UAAU,CAAC,MAAK;QACd,IAAI,CAACpC,OAAO,EAAE;MAChB,CAAC,EAAE,IAAI,CAACD,iBAAiB,CAAC;KAC3B,MAAM;MACLS,OAAO,CAACS,KAAK,CAAC,6DAA6D,CAAC;;EAEhF;EAEA;;;EAGAoB,YAAYA,CAACC,KAAa;IACxB,IAAI,CAACf,IAAI,CAAC;MACRM,IAAI,EAAE,MAAM;MACZb,IAAI,EAAE;QAAEsB;MAAK;KACd,CAAC;EACJ;EAEA;;;EAGAC,QAAQA,CAACC,MAAc;IACrB,IAAI,CAACjB,IAAI,CAAC;MACRM,IAAI,EAAE,MAAM;MACZb,IAAI,EAAE;QAAEwB;MAAM;KACf,CAAC;EACJ;EAEA;;;EAGAC,SAASA,CAACD,MAAc;IACtB,IAAI,CAACjB,IAAI,CAAC;MACRM,IAAI,EAAE,OAAO;MACbb,IAAI,EAAE;QAAEwB;MAAM;KACf,CAAC;EACJ;EAEA;;;EAGAE,WAAWA,CAACC,OAAe,EAAEC,SAAkB,EAAEC,OAAgB;IAC/D,IAAI,CAACtB,IAAI,CAAC;MACRM,IAAI,EAAE,SAAS;MACfb,IAAI,EAAE;QACJ2B,OAAO;QACPC,SAAS;QACTC,OAAO;QACPC,SAAS,EAAEC,IAAI,CAACC,GAAG;;KAEtB,CAAC;EACJ;EAEA;;;EAGAC,UAAUA,CAACC,QAAiB,EAAEN,SAAkB,EAAEC,OAAgB;IAChE,IAAI,CAACtB,IAAI,CAAC;MACRM,IAAI,EAAE,QAAQ;MACdb,IAAI,EAAE;QACJkC,QAAQ;QACRN,SAAS;QACTC;;KAEH,CAAC;EACJ;;;uBA9LWxD,gBAAgB;IAAA;EAAA;;;aAAhBA,gBAAgB;MAAA8D,OAAA,EAAhB9D,gBAAgB,CAAA+D,IAAA;MAAAC,UAAA,EAFf;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}