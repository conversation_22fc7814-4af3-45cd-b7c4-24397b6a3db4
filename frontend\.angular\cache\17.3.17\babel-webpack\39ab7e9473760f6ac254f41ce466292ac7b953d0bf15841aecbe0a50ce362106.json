{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Projects/QSC1/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../services/wasm.service\";\nimport * as i2 from \"../../services/error-handling.service\";\nimport * as i3 from \"@angular/common\";\nfunction KeyManagementComponent_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 4)(1, \"h3\");\n    i0.ɵɵtext(2, \"Current Key Pair\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6);\n    i0.ɵɵpipe(7, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 5)(9, \"button\", 6);\n    i0.ɵɵlistener(\"click\", function KeyManagementComponent_div_3_Template_button_click_9_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.rotateKeys());\n    });\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"button\", 7);\n    i0.ɵɵlistener(\"click\", function KeyManagementComponent_div_3_Template_button_click_11_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.exportPublicKey());\n    });\n    i0.ɵɵtext(12, \"Export Public Key\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"Version: \", ctx_r1.currentKeyPair.version, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"Last Rotation: \", i0.ɵɵpipeBind2(7, 4, ctx_r1.currentKeyPair.timestamp, \"medium\"), \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.isRotating);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.isRotating ? \"Rotating...\" : \"Rotate Keys\", \" \");\n  }\n}\nfunction KeyManagementComponent_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 8)(1, \"h3\");\n    i0.ɵɵtext(2, \"Import Key Pair\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 9)(4, \"input\", 10);\n    i0.ɵɵlistener(\"change\", function KeyManagementComponent_div_4_Template_input_change_4_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onFileSelected($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 11);\n    i0.ɵɵlistener(\"click\", function KeyManagementComponent_div_4_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.importKeys());\n    });\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.isImporting);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.isImporting ? \"Importing...\" : \"Import Keys\", \" \");\n  }\n}\nfunction KeyManagementComponent_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 12);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.errorMessage, \" \");\n  }\n}\nexport class KeyManagementComponent {\n  constructor(wasmService, errorHandling) {\n    this.wasmService = wasmService;\n    this.errorHandling = errorHandling;\n    this.currentKeyPair = null;\n    this.isRotating = false;\n    this.isImporting = false;\n    this.selectedFile = null;\n    this.errorMessage = '';\n  }\n  ngOnInit() {\n    this.loadCurrentKeyPair();\n  }\n  ngOnDestroy() {\n    // Clean up any subscriptions or resources\n  }\n  loadCurrentKeyPair() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      try {\n        _this.currentKeyPair = _this.wasmService.getCurrentKeyPair();\n      } catch (error) {\n        _this.errorMessage = 'Failed to load current key pair';\n        yield _this.errorHandling.handleError(error instanceof Error ? error : new Error(_this.errorMessage), 'SECURITY');\n      }\n    })();\n  }\n  rotateKeys() {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      if (_this2.isRotating) return;\n      _this2.isRotating = true;\n      _this2.errorMessage = '';\n      try {\n        yield _this2.wasmService.rotateKeys();\n        yield _this2.loadCurrentKeyPair();\n      } catch (error) {\n        _this2.errorMessage = 'Failed to rotate keys';\n        yield _this2.errorHandling.handleError(error instanceof Error ? error : new Error(_this2.errorMessage), 'SECURITY');\n      } finally {\n        _this2.isRotating = false;\n      }\n    })();\n  }\n  onFileSelected(event) {\n    const input = event.target;\n    if (input.files?.length) {\n      this.selectedFile = input.files[0];\n    }\n  }\n  importKeys() {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      if (!_this3.selectedFile || _this3.isImporting) return;\n      _this3.isImporting = true;\n      _this3.errorMessage = '';\n      try {\n        const fileContent = yield _this3.selectedFile.text();\n        const keyPair = JSON.parse(fileContent);\n        yield _this3.wasmService.importKeyPair(keyPair);\n        yield _this3.loadCurrentKeyPair();\n      } catch (error) {\n        _this3.errorMessage = 'Failed to import keys';\n        yield _this3.errorHandling.handleError(error instanceof Error ? error : new Error(_this3.errorMessage), 'SECURITY');\n      } finally {\n        _this3.isImporting = false;\n        _this3.selectedFile = null;\n      }\n    })();\n  }\n  exportPublicKey() {\n    var _this4 = this;\n    return _asyncToGenerator(function* () {\n      if (!_this4.currentKeyPair) return;\n      try {\n        const publicKey = _this4.currentKeyPair.public_key;\n        const blob = new Blob([publicKey], {\n          type: 'application/octet-stream'\n        });\n        const url = window.URL.createObjectURL(blob);\n        const a = document.createElement('a');\n        a.href = url;\n        a.download = `public-key-v${_this4.currentKeyPair.version}.key`;\n        document.body.appendChild(a);\n        a.click();\n        window.URL.revokeObjectURL(url);\n        document.body.removeChild(a);\n      } catch (error) {\n        _this4.errorMessage = 'Failed to export public key';\n        yield _this4.errorHandling.handleError(error instanceof Error ? error : new Error(_this4.errorMessage), 'SECURITY');\n      }\n    })();\n  }\n  static {\n    this.ɵfac = function KeyManagementComponent_Factory(t) {\n      return new (t || KeyManagementComponent)(i0.ɵɵdirectiveInject(i1.WasmService), i0.ɵɵdirectiveInject(i2.ErrorHandlingService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: KeyManagementComponent,\n      selectors: [[\"qs-key-management\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 6,\n      vars: 3,\n      consts: [[1, \"key-management\"], [\"class\", \"key-status\", 4, \"ngIf\"], [\"class\", \"key-import\", 4, \"ngIf\"], [\"class\", \"error-message\", 4, \"ngIf\"], [1, \"key-status\"], [1, \"key-actions\"], [3, \"click\", \"disabled\"], [3, \"click\"], [1, \"key-import\"], [1, \"import-form\"], [\"type\", \"file\", \"accept\", \".key\", 3, \"change\", \"disabled\"], [\"[disabled\", \"!selectedFile || isImporting\", 3, \"click\"], [1, \"error-message\"]],\n      template: function KeyManagementComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"h2\");\n          i0.ɵɵtext(2, \"Key Management\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(3, KeyManagementComponent_div_3_Template, 13, 7, \"div\", 1)(4, KeyManagementComponent_div_4_Template, 7, 2, \"div\", 2)(5, KeyManagementComponent_div_5_Template, 2, 1, \"div\", 3);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.currentKeyPair);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.currentKeyPair);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.errorMessage);\n        }\n      },\n      dependencies: [CommonModule, i3.NgIf, i3.DatePipe, FormsModule],\n      styles: [\".key-management[_ngcontent-%COMP%] {\\n      padding: 20px;\\n      max-width: 800px;\\n      margin: 0 auto;\\n    }\\n\\n    .key-status[_ngcontent-%COMP%] {\\n      background: #f5f5f5;\\n      padding: 20px;\\n      border-radius: 8px;\\n      margin-bottom: 20px;\\n    }\\n\\n    .key-actions[_ngcontent-%COMP%] {\\n      display: flex;\\n      gap: 10px;\\n      margin-top: 15px;\\n    }\\n\\n    button[_ngcontent-%COMP%] {\\n      padding: 8px 16px;\\n      border: none;\\n      border-radius: 4px;\\n      background: #007bff;\\n      color: white;\\n      cursor: pointer;\\n      transition: background 0.2s;\\n    }\\n\\n    button[_ngcontent-%COMP%]:disabled {\\n      background: #ccc;\\n      cursor: not-allowed;\\n    }\\n\\n    button[_ngcontent-%COMP%]:hover:not(:disabled) {\\n      background: #0056b3;\\n    }\\n\\n    .key-import[_ngcontent-%COMP%] {\\n      background: #f5f5f5;\\n      padding: 20px;\\n      border-radius: 8px;\\n    }\\n\\n    .import-form[_ngcontent-%COMP%] {\\n      display: flex;\\n      gap: 10px;\\n      margin-top: 15px;\\n    }\\n\\n    input[type=\\\"file\\\"][_ngcontent-%COMP%] {\\n      flex: 1;\\n      padding: 8px;\\n      border: 1px solid #ddd;\\n      border-radius: 4px;\\n    }\\n\\n    .error-message[_ngcontent-%COMP%] {\\n      color: #dc3545;\\n      margin-top: 15px;\\n      padding: 10px;\\n      background: #f8d7da;\\n      border-radius: 4px;\\n    }\\n  \\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvY29tcG9uZW50cy9rZXktbWFuYWdlbWVudC9rZXktbWFuYWdlbWVudC5jb21wb25lbnQudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IjtJQUNJO01BQ0UsYUFBYTtNQUNiLGdCQUFnQjtNQUNoQixjQUFjO0lBQ2hCOztJQUVBO01BQ0UsbUJBQW1CO01BQ25CLGFBQWE7TUFDYixrQkFBa0I7TUFDbEIsbUJBQW1CO0lBQ3JCOztJQUVBO01BQ0UsYUFBYTtNQUNiLFNBQVM7TUFDVCxnQkFBZ0I7SUFDbEI7O0lBRUE7TUFDRSxpQkFBaUI7TUFDakIsWUFBWTtNQUNaLGtCQUFrQjtNQUNsQixtQkFBbUI7TUFDbkIsWUFBWTtNQUNaLGVBQWU7TUFDZiwyQkFBMkI7SUFDN0I7O0lBRUE7TUFDRSxnQkFBZ0I7TUFDaEIsbUJBQW1CO0lBQ3JCOztJQUVBO01BQ0UsbUJBQW1CO0lBQ3JCOztJQUVBO01BQ0UsbUJBQW1CO01BQ25CLGFBQWE7TUFDYixrQkFBa0I7SUFDcEI7O0lBRUE7TUFDRSxhQUFhO01BQ2IsU0FBUztNQUNULGdCQUFnQjtJQUNsQjs7SUFFQTtNQUNFLE9BQU87TUFDUCxZQUFZO01BQ1osc0JBQXNCO01BQ3RCLGtCQUFrQjtJQUNwQjs7SUFFQTtNQUNFLGNBQWM7TUFDZCxnQkFBZ0I7TUFDaEIsYUFBYTtNQUNiLG1CQUFtQjtNQUNuQixrQkFBa0I7SUFDcEI7O0FBRUosNDhFQUE0OEUiLCJzb3VyY2VzQ29udGVudCI6WyJcbiAgICAua2V5LW1hbmFnZW1lbnQge1xuICAgICAgcGFkZGluZzogMjBweDtcbiAgICAgIG1heC13aWR0aDogODAwcHg7XG4gICAgICBtYXJnaW46IDAgYXV0bztcbiAgICB9XG5cbiAgICAua2V5LXN0YXR1cyB7XG4gICAgICBiYWNrZ3JvdW5kOiAjZjVmNWY1O1xuICAgICAgcGFkZGluZzogMjBweDtcbiAgICAgIGJvcmRlci1yYWRpdXM6IDhweDtcbiAgICAgIG1hcmdpbi1ib3R0b206IDIwcHg7XG4gICAgfVxuXG4gICAgLmtleS1hY3Rpb25zIHtcbiAgICAgIGRpc3BsYXk6IGZsZXg7XG4gICAgICBnYXA6IDEwcHg7XG4gICAgICBtYXJnaW4tdG9wOiAxNXB4O1xuICAgIH1cblxuICAgIGJ1dHRvbiB7XG4gICAgICBwYWRkaW5nOiA4cHggMTZweDtcbiAgICAgIGJvcmRlcjogbm9uZTtcbiAgICAgIGJvcmRlci1yYWRpdXM6IDRweDtcbiAgICAgIGJhY2tncm91bmQ6ICMwMDdiZmY7XG4gICAgICBjb2xvcjogd2hpdGU7XG4gICAgICBjdXJzb3I6IHBvaW50ZXI7XG4gICAgICB0cmFuc2l0aW9uOiBiYWNrZ3JvdW5kIDAuMnM7XG4gICAgfVxuXG4gICAgYnV0dG9uOmRpc2FibGVkIHtcbiAgICAgIGJhY2tncm91bmQ6ICNjY2M7XG4gICAgICBjdXJzb3I6IG5vdC1hbGxvd2VkO1xuICAgIH1cblxuICAgIGJ1dHRvbjpob3Zlcjpub3QoOmRpc2FibGVkKSB7XG4gICAgICBiYWNrZ3JvdW5kOiAjMDA1NmIzO1xuICAgIH1cblxuICAgIC5rZXktaW1wb3J0IHtcbiAgICAgIGJhY2tncm91bmQ6ICNmNWY1ZjU7XG4gICAgICBwYWRkaW5nOiAyMHB4O1xuICAgICAgYm9yZGVyLXJhZGl1czogOHB4O1xuICAgIH1cblxuICAgIC5pbXBvcnQtZm9ybSB7XG4gICAgICBkaXNwbGF5OiBmbGV4O1xuICAgICAgZ2FwOiAxMHB4O1xuICAgICAgbWFyZ2luLXRvcDogMTVweDtcbiAgICB9XG5cbiAgICBpbnB1dFt0eXBlPVwiZmlsZVwiXSB7XG4gICAgICBmbGV4OiAxO1xuICAgICAgcGFkZGluZzogOHB4O1xuICAgICAgYm9yZGVyOiAxcHggc29saWQgI2RkZDtcbiAgICAgIGJvcmRlci1yYWRpdXM6IDRweDtcbiAgICB9XG5cbiAgICAuZXJyb3ItbWVzc2FnZSB7XG4gICAgICBjb2xvcjogI2RjMzU0NTtcbiAgICAgIG1hcmdpbi10b3A6IDE1cHg7XG4gICAgICBwYWRkaW5nOiAxMHB4O1xuICAgICAgYmFja2dyb3VuZDogI2Y4ZDdkYTtcbiAgICAgIGJvcmRlci1yYWRpdXM6IDRweDtcbiAgICB9XG4gICJdLCJzb3VyY2VSb290IjoiIn0= */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "FormsModule", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "KeyManagementComponent_div_3_Template_button_click_9_listener", "ɵɵrestoreView", "_r1", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "rotateKeys", "KeyManagementComponent_div_3_Template_button_click_11_listener", "exportPublicKey", "ɵɵadvance", "ɵɵtextInterpolate1", "currentKeyPair", "version", "ɵɵpipeBind2", "timestamp", "ɵɵproperty", "isRotating", "KeyManagementComponent_div_4_Template_input_change_4_listener", "$event", "_r3", "onFileSelected", "KeyManagementComponent_div_4_Template_button_click_5_listener", "importKeys", "isImporting", "errorMessage", "KeyManagementComponent", "constructor", "wasmService", "errorHandling", "selectedFile", "ngOnInit", "loadCurrentKeyPair", "ngOnDestroy", "_this", "_asyncToGenerator", "getCurrentKeyPair", "error", "handleError", "Error", "_this2", "event", "input", "target", "files", "length", "_this3", "fileContent", "text", "keyPair", "JSON", "parse", "importKeyPair", "_this4", "public<PERSON>ey", "public_key", "blob", "Blob", "type", "url", "window", "URL", "createObjectURL", "a", "document", "createElement", "href", "download", "body", "append<PERSON><PERSON><PERSON>", "click", "revokeObjectURL", "<PERSON><PERSON><PERSON><PERSON>", "ɵɵdirectiveInject", "i1", "WasmService", "i2", "ErrorHandlingService", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "KeyManagementComponent_Template", "rf", "ctx", "ɵɵtemplate", "KeyManagementComponent_div_3_Template", "KeyManagementComponent_div_4_Template", "KeyManagementComponent_div_5_Template", "i3", "NgIf", "DatePipe", "styles"], "sources": ["C:\\Users\\<USER>\\Projects\\QSC1\\frontend\\src\\app\\components\\key-management\\key-management.component.ts"], "sourcesContent": ["import { Component, OnInit, OnDestroy } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { FormsModule } from '@angular/forms';\r\nimport { WasmService } from '../../services/wasm.service';\r\nimport { ErrorHandlingService } from '../../services/error-handling.service';\r\n\r\n@Component({\r\n  selector: 'qs-key-management',\r\n  standalone: true,\r\n  imports: [CommonModule, FormsModule],\r\n  template: `\r\n    <div class=\"key-management\">\r\n      <h2>Key Management</h2>\r\n\r\n      <div class=\"key-status\" *ngIf=\"currentKeyPair\">\r\n        <h3>Current Key Pair</h3>\r\n        <p>Version: {{ currentKeyPair.version }}</p>\r\n        <p>Last Rotation: {{ currentKeyPair.timestamp | date:'medium' }}</p>\r\n\r\n        <div class=\"key-actions\">\r\n          <button (click)=\"rotateKeys()\" [disabled]=\"isRotating\">\r\n            {{ isRotating ? 'Rotating...' : 'Rotate Keys' }}\r\n          </button>\r\n          <button (click)=\"exportPublicKey()\">Export Public Key</button>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"key-import\" *ngIf=\"!currentKeyPair\">\r\n        <h3>Import Key Pair</h3>\r\n        <div class=\"import-form\">\r\n          <input\r\n            type=\"file\"\r\n            accept=\".key\"\r\n            (change)=\"onFileSelected($event)\"\r\n            [disabled]=\"isImporting\"\r\n          />\r\n          <button (click)=\"importKeys()\" [disabled=\"!selectedFile || isImporting\">\r\n            {{ isImporting ? 'Importing...' : 'Import Keys' }}\r\n          </button>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"error-message\" *ngIf=\"errorMessage\">\r\n        {{ errorMessage }}\r\n      </div>\r\n    </div>\r\n  `,\r\n  styles: [`\r\n    .key-management {\r\n      padding: 20px;\r\n      max-width: 800px;\r\n      margin: 0 auto;\r\n    }\r\n\r\n    .key-status {\r\n      background: #f5f5f5;\r\n      padding: 20px;\r\n      border-radius: 8px;\r\n      margin-bottom: 20px;\r\n    }\r\n\r\n    .key-actions {\r\n      display: flex;\r\n      gap: 10px;\r\n      margin-top: 15px;\r\n    }\r\n\r\n    button {\r\n      padding: 8px 16px;\r\n      border: none;\r\n      border-radius: 4px;\r\n      background: #007bff;\r\n      color: white;\r\n      cursor: pointer;\r\n      transition: background 0.2s;\r\n    }\r\n\r\n    button:disabled {\r\n      background: #ccc;\r\n      cursor: not-allowed;\r\n    }\r\n\r\n    button:hover:not(:disabled) {\r\n      background: #0056b3;\r\n    }\r\n\r\n    .key-import {\r\n      background: #f5f5f5;\r\n      padding: 20px;\r\n      border-radius: 8px;\r\n    }\r\n\r\n    .import-form {\r\n      display: flex;\r\n      gap: 10px;\r\n      margin-top: 15px;\r\n    }\r\n\r\n    input[type=\"file\"] {\r\n      flex: 1;\r\n      padding: 8px;\r\n      border: 1px solid #ddd;\r\n      border-radius: 4px;\r\n    }\r\n\r\n    .error-message {\r\n      color: #dc3545;\r\n      margin-top: 15px;\r\n      padding: 10px;\r\n      background: #f8d7da;\r\n      border-radius: 4px;\r\n    }\r\n  `]\r\n})\r\nexport class KeyManagementComponent implements OnInit, OnDestroy {\r\n  currentKeyPair: any = null;\r\n  isRotating = false;\r\n  isImporting = false;\r\n  selectedFile: File | null = null;\r\n  errorMessage = '';\r\n\r\n  constructor(\r\n    private wasmService: WasmService,\r\n    private errorHandling: ErrorHandlingService\r\n  ) {}\r\n\r\n  ngOnInit(): void {\r\n    this.loadCurrentKeyPair();\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    // Clean up any subscriptions or resources\r\n  }\r\n\r\n  private async loadCurrentKeyPair(): Promise<void> {\r\n    try {\r\n      this.currentKeyPair = this.wasmService.getCurrentKeyPair();\r\n    } catch (error) {\r\n      this.errorMessage = 'Failed to load current key pair';\r\n      await this.errorHandling.handleError(\r\n        error instanceof Error ? error : new Error(this.errorMessage),\r\n        'SECURITY'\r\n      );\r\n    }\r\n  }\r\n\r\n  async rotateKeys(): Promise<void> {\r\n    if (this.isRotating) return;\r\n\r\n    this.isRotating = true;\r\n    this.errorMessage = '';\r\n\r\n    try {\r\n      await this.wasmService.rotateKeys();\r\n      await this.loadCurrentKeyPair();\r\n    } catch (error) {\r\n      this.errorMessage = 'Failed to rotate keys';\r\n      await this.errorHandling.handleError(\r\n        error instanceof Error ? error : new Error(this.errorMessage),\r\n        'SECURITY'\r\n      );\r\n    } finally {\r\n      this.isRotating = false;\r\n    }\r\n  }\r\n\r\n  onFileSelected(event: Event): void {\r\n    const input = event.target as HTMLInputElement;\r\n    if (input.files?.length) {\r\n      this.selectedFile = input.files[0];\r\n    }\r\n  }\r\n\r\n  async importKeys(): Promise<void> {\r\n    if (!this.selectedFile || this.isImporting) return;\r\n\r\n    this.isImporting = true;\r\n    this.errorMessage = '';\r\n\r\n    try {\r\n      const fileContent = await this.selectedFile.text();\r\n      const keyPair = JSON.parse(fileContent);\r\n      await this.wasmService.importKeyPair(keyPair);\r\n      await this.loadCurrentKeyPair();\r\n    } catch (error) {\r\n      this.errorMessage = 'Failed to import keys';\r\n      await this.errorHandling.handleError(\r\n        error instanceof Error ? error : new Error(this.errorMessage),\r\n        'SECURITY'\r\n      );\r\n    } finally {\r\n      this.isImporting = false;\r\n      this.selectedFile = null;\r\n    }\r\n  }\r\n\r\n  async exportPublicKey(): Promise<void> {\r\n    if (!this.currentKeyPair) return;\r\n\r\n    try {\r\n      const publicKey = this.currentKeyPair.public_key;\r\n      const blob = new Blob([publicKey], { type: 'application/octet-stream' });\r\n      const url = window.URL.createObjectURL(blob);\r\n      const a = document.createElement('a');\r\n      a.href = url;\r\n      a.download = `public-key-v${this.currentKeyPair.version}.key`;\r\n      document.body.appendChild(a);\r\n      a.click();\r\n      window.URL.revokeObjectURL(url);\r\n      document.body.removeChild(a);\r\n    } catch (error) {\r\n      this.errorMessage = 'Failed to export public key';\r\n      await this.errorHandling.handleError(\r\n        error instanceof Error ? error : new Error(this.errorMessage),\r\n        'SECURITY'\r\n      );\r\n    }\r\n  }\r\n}\r\n"], "mappings": ";AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;;;;;;;;IAapCC,EADF,CAAAC,cAAA,aAA+C,SACzC;IAAAD,EAAA,CAAAE,MAAA,uBAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACzBH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,GAAqC;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAC5CH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,GAA6D;;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAGlEH,EADF,CAAAC,cAAA,aAAyB,gBACgC;IAA/CD,EAAA,CAAAI,UAAA,mBAAAC,8DAAA;MAAAL,EAAA,CAAAM,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAAG,UAAA,EAAY;IAAA,EAAC;IAC5BX,EAAA,CAAAE,MAAA,IACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,iBAAoC;IAA5BD,EAAA,CAAAI,UAAA,mBAAAQ,+DAAA;MAAAZ,EAAA,CAAAM,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAAK,eAAA,EAAiB;IAAA,EAAC;IAACb,EAAA,CAAAE,MAAA,yBAAiB;IAEzDF,EAFyD,CAAAG,YAAA,EAAS,EAC1D,EACF;;;;IATDH,EAAA,CAAAc,SAAA,GAAqC;IAArCd,EAAA,CAAAe,kBAAA,cAAAP,MAAA,CAAAQ,cAAA,CAAAC,OAAA,KAAqC;IACrCjB,EAAA,CAAAc,SAAA,GAA6D;IAA7Dd,EAAA,CAAAe,kBAAA,oBAAAf,EAAA,CAAAkB,WAAA,OAAAV,MAAA,CAAAQ,cAAA,CAAAG,SAAA,gBAA6D;IAG/BnB,EAAA,CAAAc,SAAA,GAAuB;IAAvBd,EAAA,CAAAoB,UAAA,aAAAZ,MAAA,CAAAa,UAAA,CAAuB;IACpDrB,EAAA,CAAAc,SAAA,EACF;IADEd,EAAA,CAAAe,kBAAA,MAAAP,MAAA,CAAAa,UAAA,sCACF;;;;;;IAMFrB,EADF,CAAAC,cAAA,aAAgD,SAC1C;IAAAD,EAAA,CAAAE,MAAA,sBAAe;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAEtBH,EADF,CAAAC,cAAA,aAAyB,gBAMrB;IAFAD,EAAA,CAAAI,UAAA,oBAAAkB,8DAAAC,MAAA;MAAAvB,EAAA,CAAAM,aAAA,CAAAkB,GAAA;MAAA,MAAAhB,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAAUF,MAAA,CAAAiB,cAAA,CAAAF,MAAA,CAAsB;IAAA,EAAC;IAHnCvB,EAAA,CAAAG,YAAA,EAKE;IACFH,EAAA,CAAAC,cAAA,iBAAwE;IAAhED,EAAA,CAAAI,UAAA,mBAAAsB,8DAAA;MAAA1B,EAAA,CAAAM,aAAA,CAAAkB,GAAA;MAAA,MAAAhB,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAAmB,UAAA,EAAY;IAAA,EAAC;IAC5B3B,EAAA,CAAAE,MAAA,GACF;IAEJF,EAFI,CAAAG,YAAA,EAAS,EACL,EACF;;;;IANAH,EAAA,CAAAc,SAAA,GAAwB;IAAxBd,EAAA,CAAAoB,UAAA,aAAAZ,MAAA,CAAAoB,WAAA,CAAwB;IAGxB5B,EAAA,CAAAc,SAAA,GACF;IADEd,EAAA,CAAAe,kBAAA,MAAAP,MAAA,CAAAoB,WAAA,uCACF;;;;;IAIJ5B,EAAA,CAAAC,cAAA,cAAgD;IAC9CD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAc,SAAA,EACF;IADEd,EAAA,CAAAe,kBAAA,MAAAP,MAAA,CAAAqB,YAAA,MACF;;;AAsEN,OAAM,MAAOC,sBAAsB;EAOjCC,YACUC,WAAwB,EACxBC,aAAmC;IADnC,KAAAD,WAAW,GAAXA,WAAW;IACX,KAAAC,aAAa,GAAbA,aAAa;IARvB,KAAAjB,cAAc,GAAQ,IAAI;IAC1B,KAAAK,UAAU,GAAG,KAAK;IAClB,KAAAO,WAAW,GAAG,KAAK;IACnB,KAAAM,YAAY,GAAgB,IAAI;IAChC,KAAAL,YAAY,GAAG,EAAE;EAKd;EAEHM,QAAQA,CAAA;IACN,IAAI,CAACC,kBAAkB,EAAE;EAC3B;EAEAC,WAAWA,CAAA;IACT;EAAA;EAGYD,kBAAkBA,CAAA;IAAA,IAAAE,KAAA;IAAA,OAAAC,iBAAA;MAC9B,IAAI;QACFD,KAAI,CAACtB,cAAc,GAAGsB,KAAI,CAACN,WAAW,CAACQ,iBAAiB,EAAE;OAC3D,CAAC,OAAOC,KAAK,EAAE;QACdH,KAAI,CAACT,YAAY,GAAG,iCAAiC;QACrD,MAAMS,KAAI,CAACL,aAAa,CAACS,WAAW,CAClCD,KAAK,YAAYE,KAAK,GAAGF,KAAK,GAAG,IAAIE,KAAK,CAACL,KAAI,CAACT,YAAY,CAAC,EAC7D,UAAU,CACX;;IACF;EACH;EAEMlB,UAAUA,CAAA;IAAA,IAAAiC,MAAA;IAAA,OAAAL,iBAAA;MACd,IAAIK,MAAI,CAACvB,UAAU,EAAE;MAErBuB,MAAI,CAACvB,UAAU,GAAG,IAAI;MACtBuB,MAAI,CAACf,YAAY,GAAG,EAAE;MAEtB,IAAI;QACF,MAAMe,MAAI,CAACZ,WAAW,CAACrB,UAAU,EAAE;QACnC,MAAMiC,MAAI,CAACR,kBAAkB,EAAE;OAChC,CAAC,OAAOK,KAAK,EAAE;QACdG,MAAI,CAACf,YAAY,GAAG,uBAAuB;QAC3C,MAAMe,MAAI,CAACX,aAAa,CAACS,WAAW,CAClCD,KAAK,YAAYE,KAAK,GAAGF,KAAK,GAAG,IAAIE,KAAK,CAACC,MAAI,CAACf,YAAY,CAAC,EAC7D,UAAU,CACX;OACF,SAAS;QACRe,MAAI,CAACvB,UAAU,GAAG,KAAK;;IACxB;EACH;EAEAI,cAAcA,CAACoB,KAAY;IACzB,MAAMC,KAAK,GAAGD,KAAK,CAACE,MAA0B;IAC9C,IAAID,KAAK,CAACE,KAAK,EAAEC,MAAM,EAAE;MACvB,IAAI,CAACf,YAAY,GAAGY,KAAK,CAACE,KAAK,CAAC,CAAC,CAAC;;EAEtC;EAEMrB,UAAUA,CAAA;IAAA,IAAAuB,MAAA;IAAA,OAAAX,iBAAA;MACd,IAAI,CAACW,MAAI,CAAChB,YAAY,IAAIgB,MAAI,CAACtB,WAAW,EAAE;MAE5CsB,MAAI,CAACtB,WAAW,GAAG,IAAI;MACvBsB,MAAI,CAACrB,YAAY,GAAG,EAAE;MAEtB,IAAI;QACF,MAAMsB,WAAW,SAASD,MAAI,CAAChB,YAAY,CAACkB,IAAI,EAAE;QAClD,MAAMC,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACJ,WAAW,CAAC;QACvC,MAAMD,MAAI,CAAClB,WAAW,CAACwB,aAAa,CAACH,OAAO,CAAC;QAC7C,MAAMH,MAAI,CAACd,kBAAkB,EAAE;OAChC,CAAC,OAAOK,KAAK,EAAE;QACdS,MAAI,CAACrB,YAAY,GAAG,uBAAuB;QAC3C,MAAMqB,MAAI,CAACjB,aAAa,CAACS,WAAW,CAClCD,KAAK,YAAYE,KAAK,GAAGF,KAAK,GAAG,IAAIE,KAAK,CAACO,MAAI,CAACrB,YAAY,CAAC,EAC7D,UAAU,CACX;OACF,SAAS;QACRqB,MAAI,CAACtB,WAAW,GAAG,KAAK;QACxBsB,MAAI,CAAChB,YAAY,GAAG,IAAI;;IACzB;EACH;EAEMrB,eAAeA,CAAA;IAAA,IAAA4C,MAAA;IAAA,OAAAlB,iBAAA;MACnB,IAAI,CAACkB,MAAI,CAACzC,cAAc,EAAE;MAE1B,IAAI;QACF,MAAM0C,SAAS,GAAGD,MAAI,CAACzC,cAAc,CAAC2C,UAAU;QAChD,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACH,SAAS,CAAC,EAAE;UAAEI,IAAI,EAAE;QAA0B,CAAE,CAAC;QACxE,MAAMC,GAAG,GAAGC,MAAM,CAACC,GAAG,CAACC,eAAe,CAACN,IAAI,CAAC;QAC5C,MAAMO,CAAC,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;QACrCF,CAAC,CAACG,IAAI,GAAGP,GAAG;QACZI,CAAC,CAACI,QAAQ,GAAG,eAAed,MAAI,CAACzC,cAAc,CAACC,OAAO,MAAM;QAC7DmD,QAAQ,CAACI,IAAI,CAACC,WAAW,CAACN,CAAC,CAAC;QAC5BA,CAAC,CAACO,KAAK,EAAE;QACTV,MAAM,CAACC,GAAG,CAACU,eAAe,CAACZ,GAAG,CAAC;QAC/BK,QAAQ,CAACI,IAAI,CAACI,WAAW,CAACT,CAAC,CAAC;OAC7B,CAAC,OAAO1B,KAAK,EAAE;QACdgB,MAAI,CAAC5B,YAAY,GAAG,6BAA6B;QACjD,MAAM4B,MAAI,CAACxB,aAAa,CAACS,WAAW,CAClCD,KAAK,YAAYE,KAAK,GAAGF,KAAK,GAAG,IAAIE,KAAK,CAACc,MAAI,CAAC5B,YAAY,CAAC,EAC7D,UAAU,CACX;;IACF;EACH;;;uBAvGWC,sBAAsB,EAAA9B,EAAA,CAAA6E,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAA/E,EAAA,CAAA6E,iBAAA,CAAAG,EAAA,CAAAC,oBAAA;IAAA;EAAA;;;YAAtBnD,sBAAsB;MAAAoD,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAApF,EAAA,CAAAqF,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,gCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAtG7B3F,EADF,CAAAC,cAAA,aAA4B,SACtB;UAAAD,EAAA,CAAAE,MAAA,qBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAK;UA8BvBH,EA5BA,CAAA6F,UAAA,IAAAC,qCAAA,kBAA+C,IAAAC,qCAAA,iBAaC,IAAAC,qCAAA,iBAeA;UAGlDhG,EAAA,CAAAG,YAAA,EAAM;;;UA/BqBH,EAAA,CAAAc,SAAA,GAAoB;UAApBd,EAAA,CAAAoB,UAAA,SAAAwE,GAAA,CAAA5E,cAAA,CAAoB;UAapBhB,EAAA,CAAAc,SAAA,EAAqB;UAArBd,EAAA,CAAAoB,UAAA,UAAAwE,GAAA,CAAA5E,cAAA,CAAqB;UAelBhB,EAAA,CAAAc,SAAA,EAAkB;UAAlBd,EAAA,CAAAoB,UAAA,SAAAwE,GAAA,CAAA/D,YAAA,CAAkB;;;qBAjCxC/B,YAAY,EAAAmG,EAAA,CAAAC,IAAA,EAAAD,EAAA,CAAAE,QAAA,EAAEpG,WAAW;MAAAqG,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}