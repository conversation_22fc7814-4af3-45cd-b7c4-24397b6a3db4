import { Injectable } from '@angular/core';
import { Observable, Subject, BehaviorSubject } from 'rxjs';
import { filter, map } from 'rxjs/operators';

export interface WebSocketMessage {
  type: string;
  data: any;
  timestamp?: number;
}

@Injectable({
  providedIn: 'root'
})
export class WebSocketService {
  private socket: WebSocket | null = null;
  private messageSubject = new Subject<WebSocketMessage>();
  private connectionStateSubject = new BehaviorSubject<boolean>(false);
  
  public messages$ = this.messageSubject.asObservable();
  public connectionState$ = this.connectionStateSubject.asObservable();

  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectInterval = 5000; // 5 seconds

  constructor() {
    // Auto-connect when service is instantiated
    // this.connect();
  }

  /**
   * Connect to WebSocket server
   */
  connect(url?: string): void {
    if (this.socket && this.socket.readyState === WebSocket.OPEN) {
      return; // Already connected
    }

    const wsUrl = url || this.getWebSocketUrl();
    
    try {
      this.socket = new WebSocket(wsUrl);
      
      this.socket.onopen = () => {
        console.log('WebSocket connected');
        this.connectionStateSubject.next(true);
        this.reconnectAttempts = 0;
      };

      this.socket.onmessage = (event) => {
        try {
          const message: WebSocketMessage = JSON.parse(event.data);
          this.messageSubject.next(message);
        } catch (error) {
          console.error('Failed to parse WebSocket message:', error);
        }
      };

      this.socket.onclose = () => {
        console.log('WebSocket disconnected');
        this.connectionStateSubject.next(false);
        this.attemptReconnect();
      };

      this.socket.onerror = (error) => {
        console.error('WebSocket error:', error);
        this.connectionStateSubject.next(false);
      };

    } catch (error) {
      console.error('Failed to create WebSocket connection:', error);
      this.connectionStateSubject.next(false);
    }
  }

  /**
   * Disconnect from WebSocket server
   */
  disconnect(): void {
    if (this.socket) {
      this.socket.close();
      this.socket = null;
    }
    this.connectionStateSubject.next(false);
  }

  /**
   * Send a message through WebSocket
   */
  send(message: WebSocketMessage): void {
    if (this.socket && this.socket.readyState === WebSocket.OPEN) {
      this.socket.send(JSON.stringify(message));
    } else {
      console.warn('WebSocket is not connected. Message not sent:', message);
    }
  }

  /**
   * Listen for specific message types
   */
  on(messageType: string): Observable<any> {
    return this.messages$.pipe(
      filter(message => message.type === messageType),
      map(message => message.data)
    );
  }

  /**
   * Check if WebSocket is connected
   */
  isConnected(): boolean {
    return this.socket?.readyState === WebSocket.OPEN;
  }

  /**
   * Get WebSocket URL based on current environment
   */
  private getWebSocketUrl(): string {
    const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
    const host = window.location.host;
    
    // In development, use localhost:3000 for backend
    if (host.includes('localhost') || host.includes('127.0.0.1')) {
      return `${protocol}//localhost:3000/ws`;
    }
    
    // In production, use same host
    return `${protocol}//${host}/ws`;
  }

  /**
   * Attempt to reconnect to WebSocket
   */
  private attemptReconnect(): void {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      console.log(`Attempting to reconnect... (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
      
      setTimeout(() => {
        this.connect();
      }, this.reconnectInterval);
    } else {
      console.error('Max reconnection attempts reached. Please refresh the page.');
    }
  }

  /**
   * Send authentication message
   */
  authenticate(token: string): void {
    this.send({
      type: 'auth',
      data: { token }
    });
  }

  /**
   * Join a room/channel
   */
  joinRoom(roomId: string): void {
    this.send({
      type: 'join',
      data: { roomId }
    });
  }

  /**
   * Leave a room/channel
   */
  leaveRoom(roomId: string): void {
    this.send({
      type: 'leave',
      data: { roomId }
    });
  }

  /**
   * Send a chat message
   */
  sendMessage(content: string, recipient?: string, groupId?: string): void {
    this.send({
      type: 'message',
      data: {
        content,
        recipient,
        groupId,
        timestamp: Date.now()
      }
    });
  }

  /**
   * Send typing indicator
   */
  sendTyping(isTyping: boolean, recipient?: string, groupId?: string): void {
    this.send({
      type: 'typing',
      data: {
        isTyping,
        recipient,
        groupId
      }
    });
  }
}
