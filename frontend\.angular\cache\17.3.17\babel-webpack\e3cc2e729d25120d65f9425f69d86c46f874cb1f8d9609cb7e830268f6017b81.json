{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Projects/QSC1/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"./error-handling.service\";\nexport let WasmService = /*#__PURE__*/(() => {\n  class WasmService {\n    constructor(errorHandling) {\n      this.errorHandling = errorHandling;\n      this.wasmModule = null;\n      this.currentKeyPair = null;\n      this.KEY_ROTATION_INTERVAL = 30 * 24 * 60 * 60 * 1000; // 30 days\n      this.wasmInstance = null;\n      this.wasmFunctions = null;\n      this.initializeWasm();\n      this.startKeyRotation();\n    }\n    init() {\n      var _this = this;\n      return _asyncToGenerator(function* () {\n        try {\n          // Dynamic import of WASM module\n          _this.wasmInstance = yield import('/assets/wasm/pqc_wasm.js');\n          yield _this.wasmInstance.default();\n          // Extract WASM functions\n          _this.wasmFunctions = {\n            generate_key_pair: _this.wasmInstance.generate_key_pair,\n            encrypt_message: _this.wasmInstance.encrypt_message,\n            decrypt_message: _this.wasmInstance.decrypt_message,\n            rotate_keys: _this.wasmInstance.rotate_keys\n          };\n        } catch (error) {\n          console.warn('WASM module not available, using fallback implementation');\n          _this.initializeFallback();\n        }\n      })();\n    }\n    initializeWasm() {\n      var _this2 = this;\n      return _asyncToGenerator(function* () {\n        try {\n          yield _this2.init();\n          yield _this2.generateNewKeyPair();\n        } catch (error) {\n          yield _this2.errorHandling.handleError(error instanceof Error ? error : new Error('WASM initialization failed'), 'WASM');\n          // Don't throw error, allow fallback to work\n          console.warn('WASM initialization failed, continuing with fallback');\n        }\n      })();\n    }\n    /**\n     * Initialize fallback cryptographic implementation\n     */\n    initializeFallback() {\n      console.warn('Using fallback cryptographic implementation - NOT SECURE FOR PRODUCTION');\n      this.wasmFunctions = {\n        generate_key_pair: this.fallbackGenerateKeyPair.bind(this),\n        encrypt_message: this.fallbackEncryptMessage.bind(this),\n        decrypt_message: this.fallbackDecryptMessage.bind(this),\n        rotate_keys: this.fallbackRotateKeys.bind(this)\n      };\n    }\n    generateNewKeyPair() {\n      var _this3 = this;\n      return _asyncToGenerator(function* () {\n        try {\n          if (!_this3.wasmFunctions) {\n            throw new Error('WASM functions not initialized');\n          }\n          const keyPairBytes = yield _this3.wasmFunctions.generate_key_pair();\n          const keyPair = JSON.parse(new TextDecoder().decode(keyPairBytes));\n          _this3.currentKeyPair = {\n            public_key: new Uint8Array(keyPair.public_key),\n            private_key: new Uint8Array(keyPair.private_key),\n            timestamp: new Date(),\n            version: (_this3.currentKeyPair?.version ?? 0) + 1\n          };\n        } catch (error) {\n          yield _this3.errorHandling.handleError(error instanceof Error ? error : new Error('Key pair generation failed'), 'WASM');\n          throw error;\n        }\n      })();\n    }\n    startKeyRotation() {\n      var _this4 = this;\n      setInterval(/*#__PURE__*/_asyncToGenerator(function* () {\n        try {\n          yield _this4.rotateKeys();\n        } catch (error) {\n          yield _this4.errorHandling.handleError(error instanceof Error ? error : new Error('Key rotation failed'), 'SECURITY');\n        }\n      }), this.KEY_ROTATION_INTERVAL);\n    }\n    rotateKeys() {\n      var _this5 = this;\n      return _asyncToGenerator(function* () {\n        if (!_this5.currentKeyPair) {\n          throw new Error('No key pair available for rotation');\n        }\n        if (!_this5.wasmFunctions) {\n          throw new Error('WASM functions not initialized');\n        }\n        try {\n          const newKeyPairBytes = yield _this5.wasmFunctions.rotate_keys(_this5.currentKeyPair.private_key);\n          const newKeyPair = JSON.parse(new TextDecoder().decode(newKeyPairBytes));\n          _this5.currentKeyPair = {\n            public_key: new Uint8Array(newKeyPair.public_key),\n            private_key: new Uint8Array(newKeyPair.private_key),\n            timestamp: new Date(),\n            version: _this5.currentKeyPair.version + 1\n          };\n        } catch (error) {\n          yield _this5.errorHandling.handleError(error instanceof Error ? error : new Error('Key rotation failed'), 'SECURITY');\n          throw error;\n        }\n      })();\n    }\n    encryptMessage(message) {\n      var _this6 = this;\n      return _asyncToGenerator(function* () {\n        if (!_this6.currentKeyPair) {\n          throw new Error('No key pair available for encryption');\n        }\n        if (!_this6.wasmFunctions) {\n          throw new Error('WASM functions not initialized');\n        }\n        try {\n          const messageBytes = new TextEncoder().encode(message);\n          const encryptedBytes = yield _this6.wasmFunctions.encrypt_message(messageBytes, _this6.currentKeyPair.public_key);\n          return new Uint8Array(encryptedBytes);\n        } catch (error) {\n          yield _this6.errorHandling.handleError(error instanceof Error ? error : new Error('Message encryption failed'), 'SECURITY');\n          throw error;\n        }\n      })();\n    }\n    decryptMessage(encrypted) {\n      var _this7 = this;\n      return _asyncToGenerator(function* () {\n        if (!_this7.currentKeyPair) {\n          throw new Error('No key pair available for decryption');\n        }\n        if (!_this7.wasmFunctions) {\n          throw new Error('WASM functions not initialized');\n        }\n        try {\n          const decryptedBytes = yield _this7.wasmFunctions.decrypt_message(encrypted, _this7.currentKeyPair.private_key);\n          return new TextDecoder().decode(decryptedBytes);\n        } catch (error) {\n          yield _this7.errorHandling.handleError(error instanceof Error ? error : new Error('Message decryption failed'), 'SECURITY');\n          throw error;\n        }\n      })();\n    }\n    signMessage(message) {\n      var _this8 = this;\n      return _asyncToGenerator(function* () {\n        if (!_this8.currentKeyPair) {\n          throw new Error('No key pair available for signing');\n        }\n        try {\n          // TODO: Implement signing logic using WASM\n          throw new Error('Signing method not implemented');\n        } catch (error) {\n          yield _this8.errorHandling.handleError(error instanceof Error ? error : new Error('Signing failed'), 'SECURITY');\n          throw error;\n        }\n      })();\n    }\n    verifySignature(message, signature) {\n      var _this9 = this;\n      return _asyncToGenerator(function* () {\n        if (!_this9.currentKeyPair) {\n          throw new Error('No key pair available for signature verification');\n        }\n        try {\n          // TODO: Implement signature verification logic using WASM\n          throw new Error('Signature verification method not implemented');\n        } catch (error) {\n          yield _this9.errorHandling.handleError(error instanceof Error ? error : new Error('Signature verification failed'), 'SECURITY');\n          throw error;\n        }\n      })();\n    }\n    importKeyPair(keyPair) {\n      var _this0 = this;\n      return _asyncToGenerator(function* () {\n        try {\n          _this0.currentKeyPair = keyPair;\n        } catch (error) {\n          yield _this0.errorHandling.handleError(error instanceof Error ? error : new Error('Key pair import failed'), 'WASM');\n          throw error;\n        }\n      })();\n    }\n    wipeMemory() {\n      this.currentKeyPair = null;\n      this.wasmModule = null;\n      this.wasmInstance = null;\n      this.wasmFunctions = null;\n    }\n    // Fallback implementations (NOT SECURE FOR PRODUCTION)\n    fallbackGenerateKeyPair() {\n      return _asyncToGenerator(function* () {\n        const keyPair = {\n          public_key: Array.from(crypto.getRandomValues(new Uint8Array(32))),\n          private_key: Array.from(crypto.getRandomValues(new Uint8Array(64)))\n        };\n        return new TextEncoder().encode(JSON.stringify(keyPair));\n      })();\n    }\n    fallbackEncryptMessage(message, publicKey) {\n      return _asyncToGenerator(function* () {\n        // Simple XOR encryption (NOT SECURE)\n        const key = publicKey.slice(0, 32);\n        const encrypted = new Uint8Array(message.length);\n        for (let i = 0; i < message.length; i++) {\n          encrypted[i] = message[i] ^ key[i % key.length];\n        }\n        return encrypted;\n      })();\n    }\n    fallbackDecryptMessage(encrypted, privateKey) {\n      return _asyncToGenerator(function* () {\n        // Simple XOR decryption (NOT SECURE)\n        const key = privateKey.slice(0, 32);\n        const decrypted = new Uint8Array(encrypted.length);\n        for (let i = 0; i < encrypted.length; i++) {\n          decrypted[i] = encrypted[i] ^ key[i % key.length];\n        }\n        return decrypted;\n      })();\n    }\n    fallbackRotateKeys(oldPrivateKey) {\n      var _this1 = this;\n      return _asyncToGenerator(function* () {\n        // Generate new key pair\n        return _this1.fallbackGenerateKeyPair();\n      })();\n    }\n    static {\n      this.ɵfac = function WasmService_Factory(t) {\n        return new (t || WasmService)(i0.ɵɵinject(i1.ErrorHandlingService));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: WasmService,\n        factory: WasmService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return WasmService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}