import { Injectable, CanActivate, ExecutionContext, BadRequestException } from '@nestjs/common';
import { timingSafeEqual } from 'crypto';

@Injectable()
export class DeviceGuard implements CanActivate {
  canActivate(context: ExecutionContext): boolean {
    const request = context.switchToHttp().getRequest();
    const deviceHash = request.headers['x-device-hash'];

    if (!deviceHash) {
      throw new BadRequestException('Device hash is required');
    }

    // Store device hash in request for later use
    request.deviceHash = deviceHash;

    return true;
  }
} 