{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Projects/QSC1/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { BehaviorSubject, Subject } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"./secure-storage.service\";\nimport * as i2 from \"./error-handling.service\";\nexport let NotificationService = /*#__PURE__*/(() => {\n  class NotificationService {\n    constructor(secureStorage, errorHandling) {\n      this.secureStorage = secureStorage;\n      this.errorHandling = errorHandling;\n      this.toastNotificationsSubject = new BehaviorSubject([]);\n      this.newToastSubject = new Subject();\n      this.toastNotifications$ = this.toastNotificationsSubject.asObservable();\n      this.newToast$ = this.newToastSubject.asObservable();\n      this.defaultDuration = 5000; // 5 seconds\n      // Request notification permission on service initialization\n      this.requestNotificationPermission();\n    }\n    getNotifications() {\n      var _this = this;\n      return _asyncToGenerator(function* () {\n        try {\n          const notifications = yield _this.secureStorage.retrieveSecurely('notifications', 'notifications');\n          return notifications || [];\n        } catch (error) {\n          _this.errorHandling.handleError(error, 'STORAGE');\n          throw error;\n        }\n      })();\n    }\n    markAsRead(id) {\n      var _this2 = this;\n      return _asyncToGenerator(function* () {\n        try {\n          const notifications = yield _this2.getNotifications();\n          const notification = notifications.find(n => n.id === id);\n          if (notification) {\n            notification.read = true;\n            yield _this2.secureStorage.storeSecurely('notifications', notifications, 'notifications');\n          }\n        } catch (error) {\n          _this2.errorHandling.handleError(error, 'STORAGE');\n          throw error;\n        }\n      })();\n    }\n    createCompromiseNotification(messageIds) {\n      var _this3 = this;\n      return _asyncToGenerator(function* () {\n        try {\n          const notifications = yield _this3.getNotifications();\n          const newNotification = {\n            id: crypto.randomUUID(),\n            type: 'COMPROMISE',\n            message: `Messages ${messageIds.join(', ')} may have been compromised`,\n            timestamp: new Date(),\n            read: false\n          };\n          notifications.push(newNotification);\n          yield _this3.secureStorage.storeSecurely('notifications', notifications, 'notifications');\n        } catch (error) {\n          _this3.errorHandling.handleError(error, 'STORAGE');\n          throw error;\n        }\n      })();\n    }\n    createDeletionNotification(messageId) {\n      var _this4 = this;\n      return _asyncToGenerator(function* () {\n        try {\n          const notifications = yield _this4.getNotifications();\n          const newNotification = {\n            id: crypto.randomUUID(),\n            type: 'DELETION',\n            message: `Message ${messageId} has been deleted from the chain`,\n            timestamp: new Date(),\n            read: false\n          };\n          notifications.push(newNotification);\n          yield _this4.secureStorage.storeSecurely('notifications', notifications, 'notifications');\n        } catch (error) {\n          _this4.errorHandling.handleError(error, 'STORAGE');\n          throw error;\n        }\n      })();\n    }\n    /**\n     * Show a toast notification (for UI feedback)\n     */\n    showNotification(message, type = 'info', duration) {\n      const notification = {\n        id: this.generateToastId(),\n        message,\n        type,\n        duration: duration || this.defaultDuration,\n        timestamp: new Date()\n      };\n      // Add to toast notifications list\n      const currentToasts = this.toastNotificationsSubject.value;\n      this.toastNotificationsSubject.next([notification, ...currentToasts]);\n      // Emit new toast\n      this.newToastSubject.next(notification);\n      // Show browser notification if permission granted\n      this.showBrowserNotification(message, type);\n      // Auto-remove notification after duration\n      if (notification.duration) {\n        setTimeout(() => {\n          this.removeToastNotification(notification.id);\n        }, notification.duration);\n      }\n    }\n    /**\n     * Show success notification\n     */\n    showSuccess(message, duration) {\n      this.showNotification(message, 'success', duration);\n    }\n    /**\n     * Show error notification\n     */\n    showError(message, duration) {\n      this.showNotification(message, 'error', duration || 8000);\n    }\n    /**\n     * Show warning notification\n     */\n    showWarning(message, duration) {\n      this.showNotification(message, 'warning', duration || 6000);\n    }\n    /**\n     * Show info notification\n     */\n    showInfo(message, duration) {\n      this.showNotification(message, 'info', duration);\n    }\n    /**\n     * Remove a specific toast notification\n     */\n    removeToastNotification(id) {\n      const currentToasts = this.toastNotificationsSubject.value;\n      const updatedToasts = currentToasts.filter(n => n.id !== id);\n      this.toastNotificationsSubject.next(updatedToasts);\n    }\n    /**\n     * Clear all toast notifications\n     */\n    clearAllToasts() {\n      this.toastNotificationsSubject.next([]);\n    }\n    /**\n     * Request browser notification permission\n     */\n    requestNotificationPermission() {\n      if ('Notification' in window && Notification.permission === 'default') {\n        Notification.requestPermission().then(permission => {\n          console.log('Notification permission:', permission);\n        });\n      }\n    }\n    /**\n     * Show browser notification\n     */\n    showBrowserNotification(message, type) {\n      if ('Notification' in window && Notification.permission === 'granted') {\n        const options = {\n          body: message,\n          icon: '/assets/icons/qsc-icon.png',\n          badge: '/assets/icons/qsc-badge.png',\n          tag: 'qsc-notification',\n          requireInteraction: type === 'error',\n          silent: false\n        };\n        const notification = new Notification('QSC', options);\n        // Auto-close after 5 seconds (except for errors)\n        if (type !== 'error') {\n          setTimeout(() => {\n            notification.close();\n          }, 5000);\n        }\n        // Handle notification click\n        notification.onclick = () => {\n          window.focus();\n          notification.close();\n        };\n      }\n    }\n    /**\n     * Generate unique ID for toast notifications\n     */\n    generateToastId() {\n      return `toast_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\n    }\n    static {\n      this.ɵfac = function NotificationService_Factory(t) {\n        return new (t || NotificationService)(i0.ɵɵinject(i1.SecureStorageService), i0.ɵɵinject(i2.ErrorHandlingService));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: NotificationService,\n        factory: NotificationService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return NotificationService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}