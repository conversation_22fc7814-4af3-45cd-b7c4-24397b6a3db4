#!/usr/bin/env ts-node

/**
 * QSC User Entity Validation Script
 * 
 * This script validates that the user entity implementation is consistent
 * across the codebase and follows the QSC security-first architecture.
 */

import { NestFactory } from '@nestjs/core';
import { AppModule } from '../app.module';
import { UserService } from '../user/user.service';
import { DataSource } from 'typeorm';
import { User } from '../users/entities/user.entity';
import { IUser, ISecretWordHash } from '@qsc/shared';
import { Logger } from '@nestjs/common';

const logger = new Logger('UserEntityValidation');

interface ValidationResult {
  passed: boolean;
  message: string;
  details?: any;
}

class UserEntityValidator {
  private dataSource: DataSource;
  private userService: UserService;

  constructor(dataSource: DataSource, userService: UserService) {
    this.dataSource = dataSource;
    this.userService = userService;
  }

  async validateEntityImplementation(): Promise<ValidationResult> {
    try {
      // Check if User entity implements IUser interface correctly
      const userMetadata = this.dataSource.getMetadata(User);
      const requiredFields = [
        'id', 'username', 'email', 'publicKey', 'isAdmin', 'isActive',
        'createdAt', 'updatedAt', 'lastLoginAt', 'deviceIds', 'secretWordHash',
        'isVerified', 'accountStatus', 'failedAttempts', 'isCompromised'
      ];

      const entityColumns = userMetadata.columns.map(col => col.propertyName);
      const missingFields = requiredFields.filter(field => !entityColumns.includes(field));

      if (missingFields.length > 0) {
        return {
          passed: false,
          message: 'User entity missing required fields',
          details: { missingFields, entityColumns }
        };
      }

      return {
        passed: true,
        message: 'User entity implements IUser interface correctly'
      };
    } catch (error) {
      return {
        passed: false,
        message: 'Failed to validate entity implementation',
        details: error
      };
    }
  }

  async validateSecretWordAuthentication(): Promise<ValidationResult> {
    try {
      // Check if secretWordHash field is properly configured
      const userMetadata = this.dataSource.getMetadata(User);
      const secretWordColumn = userMetadata.columns.find(col => col.propertyName === 'secretWordHash');

      if (!secretWordColumn) {
        return {
          passed: false,
          message: 'secretWordHash column not found in User entity'
        };
      }

      if (secretWordColumn.type !== 'json') {
        return {
          passed: false,
          message: 'secretWordHash should be JSON type',
          details: { actualType: secretWordColumn.type }
        };
      }

      // Check if password-related fields are absent (they should be)
      const passwordFields = ['password', 'passwordHash', 'hashedPassword'];
      const foundPasswordFields = userMetadata.columns
        .filter(col => passwordFields.includes(col.propertyName))
        .map(col => col.propertyName);

      if (foundPasswordFields.length > 0) {
        return {
          passed: false,
          message: 'Found legacy password fields in User entity',
          details: { foundPasswordFields }
        };
      }

      return {
        passed: true,
        message: 'Secret word authentication properly configured'
      };
    } catch (error) {
      return {
        passed: false,
        message: 'Failed to validate secret word authentication',
        details: error
      };
    }
  }

  async validateUsernameAuthentication(): Promise<ValidationResult> {
    try {
      const userMetadata = this.dataSource.getMetadata(User);
      const usernameColumn = userMetadata.columns.find(col => col.propertyName === 'username');

      if (!usernameColumn) {
        return {
          passed: false,
          message: 'username column not found in User entity'
        };
      }

      // Check if username has unique constraint via unique constraints or indices
      const usernameUniqueConstraint = userMetadata.uniques.find(unique =>
        unique.columns.some(col => col.propertyName === 'username')
      );

      const usernameIndex = userMetadata.indices.find(index =>
        index.columns.some(col => col.propertyName === 'username') && index.isUnique
      );

      // Check if username column has unique constraint defined
      const hasUniqueConstraint = usernameUniqueConstraint || usernameIndex;

      if (!hasUniqueConstraint) {
        return {
          passed: false,
          message: 'username column should have unique constraint'
        };
      }

      if (usernameColumn.isNullable) {
        return {
          passed: false,
          message: 'username column should not be nullable'
        };
      }

      return {
        passed: true,
        message: 'Username authentication properly configured'
      };
    } catch (error) {
      return {
        passed: false,
        message: 'Failed to validate username authentication',
        details: error
      };
    }
  }

  async validateDatabaseSchema(): Promise<ValidationResult> {
    try {
      // Check if the actual database table matches the entity
      const tableExists = await this.dataSource.query(
        "SELECT name FROM sqlite_master WHERE type='table' AND name='users'"
      );

      if (!tableExists || tableExists.length === 0) {
        return {
          passed: false,
          message: 'Users table does not exist in database'
        };
      }

      // Get table schema
      const tableInfo = await this.dataSource.query("PRAGMA table_info(users)");
      const columnNames = tableInfo.map((col: any) => col.name);

      // Check for required columns
      const requiredColumns = ['id', 'username', 'secretWordHash'];
      const missingColumns = requiredColumns.filter(col => !columnNames.includes(col));

      if (missingColumns.length > 0) {
        return {
          passed: false,
          message: 'Database table missing required columns',
          details: { missingColumns, actualColumns: columnNames }
        };
      }

      // Check for legacy password columns (should not exist)
      const legacyColumns = ['password', 'passwordHash', 'hashedPassword'];
      const foundLegacyColumns = legacyColumns.filter(col => columnNames.includes(col));

      if (foundLegacyColumns.length > 0) {
        return {
          passed: false,
          message: 'Database table contains legacy password columns',
          details: { foundLegacyColumns }
        };
      }

      return {
        passed: true,
        message: 'Database schema matches TypeORM entity'
      };
    } catch (error) {
      return {
        passed: false,
        message: 'Failed to validate database schema',
        details: error
      };
    }
  }

  async validateUserServiceMethods(): Promise<ValidationResult> {
    try {
      // Check if UserService has the correct methods
      const requiredMethods = [
        'createWithSecretWord',
        'findByUsername',
        'updateLastLogin',
        'incrementFailedAttempts',
        'resetFailedAttempts'
      ];

      const serviceMethods = Object.getOwnPropertyNames(Object.getPrototypeOf(this.userService));
      const missingMethods = requiredMethods.filter(method => !serviceMethods.includes(method));

      if (missingMethods.length > 0) {
        return {
          passed: false,
          message: 'UserService missing required methods',
          details: { missingMethods, availableMethods: serviceMethods }
        };
      }

      return {
        passed: true,
        message: 'UserService has all required methods'
      };
    } catch (error) {
      return {
        passed: false,
        message: 'Failed to validate UserService methods',
        details: error
      };
    }
  }
}

async function runValidation(): Promise<void> {
  const app = await NestFactory.create(AppModule);
  const dataSource = app.get(DataSource);
  const userService = app.get(UserService);

  const validator = new UserEntityValidator(dataSource, userService);

  logger.log('🔍 Starting QSC User Entity Validation...');
  logger.log('');

  const validations = [
    { name: 'Entity Implementation', test: () => validator.validateEntityImplementation() },
    { name: 'Secret Word Authentication', test: () => validator.validateSecretWordAuthentication() },
    { name: 'Username Authentication', test: () => validator.validateUsernameAuthentication() },
    { name: 'Database Schema', test: () => validator.validateDatabaseSchema() },
    { name: 'UserService Methods', test: () => validator.validateUserServiceMethods() },
  ];

  let allPassed = true;

  for (const validation of validations) {
    try {
      const result = await validation.test();
      
      if (result.passed) {
        logger.log(`✅ ${validation.name}: ${result.message}`);
      } else {
        logger.error(`❌ ${validation.name}: ${result.message}`);
        if (result.details) {
          logger.error(`   Details:`, result.details);
        }
        allPassed = false;
      }
    } catch (error) {
      logger.error(`❌ ${validation.name}: Validation failed with error`);
      logger.error(`   Error:`, error);
      allPassed = false;
    }
  }

  logger.log('');
  
  if (allPassed) {
    logger.log('🎉 All validations passed! User entity is properly configured.');
    logger.log('');
    logger.log('✨ QSC User Entity Summary:');
    logger.log('   - TypeORM entity implements IUser interface');
    logger.log('   - Uses username/secretWord authentication');
    logger.log('   - No legacy password fields');
    logger.log('   - Database schema matches entity definition');
    logger.log('   - UserService has all required methods');
  } else {
    logger.error('💥 Some validations failed. Please review the errors above.');
    process.exit(1);
  }

  await app.close();
}

if (require.main === module) {
  runValidation().catch(error => {
    logger.error('Validation script failed:', error);
    process.exit(1);
  });
}

export { runValidation as validateUserEntity };
