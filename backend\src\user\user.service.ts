import { Injectable, NotFoundException, ConflictException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { User } from '../users/entities/user.entity';
import { ISecretWordHash } from '@qsc/shared';
import { SecretWordService } from '../auth/services/secret-word.service';

@Injectable()
export class UserService {
  constructor(
    @InjectRepository(User)
    private userRepository: Repository<User>,
    private secretWordService: SecretWordService,
  ) {}

  async createWithSecretWord(username: string, email: string, secretWord: string): Promise<User> {
    // Check if username already exists
    const existingUser = await this.userRepository.findOne({ where: { username } });
    if (existingUser) {
      throw new ConflictException('Username already exists');
    }

    // Check if email already exists (if provided)
    if (email) {
      const existingEmail = await this.userRepository.findOne({ where: { email } });
      if (existingEmail) {
        throw new ConflictException('Email already exists');
      }
    }

    // Hash the secret word
    const secretWordHashResult = await this.secretWordService.hashSecretWord(secretWord);

    // Convert Date to string for storage
    const secretWordHash: ISecretWordHash = {
      hash: secretWordHashResult.hash,
      salt: secretWordHashResult.salt,
      attempts: secretWordHashResult.attempts,
      lastAttempt: secretWordHashResult.lastAttempt,
    };

    const user = this.userRepository.create({
      username,
      email,
      secretWordHash,
      deviceIds: [],
      accountStatus: 'active',
    });

    return this.userRepository.save(user);
  }

  // Legacy method for backward compatibility
  async create(username: string, email: string, password: string): Promise<User> {
    // Convert to secret word method
    return this.createWithSecretWord(username, email, password);
  }

  async findByEmail(email: string): Promise<User> {
    const user = await this.userRepository.findOne({ where: { email } });
    if (!user) {
      throw new NotFoundException(`User with email ${email} not found`);
    }
    return user;
  }

  async findByUsername(username: string): Promise<User> {
    const user = await this.userRepository.findOne({ where: { username } });
    if (!user) {
      throw new NotFoundException(`User with username ${username} not found`);
    }
    return user;
  }

  async updateLastLogin(userId: string, deviceId?: string): Promise<void> {
    const user = await this.findById(userId);
    user.lastLoginAt = new Date();

    if (deviceId && !user.deviceIds.includes(deviceId)) {
      user.deviceIds.push(deviceId);
    }

    await this.userRepository.save(user);
  }

  async incrementFailedAttempts(userId: string): Promise<void> {
    const user = await this.findById(userId);
    user.failedAttempts += 1;
    user.lastAttemptAt = new Date();

    // Lock account after 5 failed attempts
    if (user.failedAttempts >= 5) {
      user.accountStatus = 'compromised';
      user.isActive = false;
    }

    await this.userRepository.save(user);
  }

  async resetFailedAttempts(userId: string): Promise<void> {
    const user = await this.findById(userId);
    user.failedAttempts = 0;
    user.lastAttemptAt = null;
    await this.userRepository.save(user);
  }

  async findById(id: string): Promise<User> {
    const user = await this.userRepository.findOne({ where: { id } });
    if (!user) {
      throw new NotFoundException(`User with id ${id} not found`);
    }
    return user;
  }

  async updatePublicKey(id: string, publicKey: string): Promise<User> {
    const user = await this.findById(id);
    user.publicKey = publicKey;
    return this.userRepository.save(user);
  }

  async addDevice(id: string, deviceId: string): Promise<User> {
    const user = await this.findById(id);
    if (!user.deviceIds.includes(deviceId)) {
      user.deviceIds.push(deviceId);
      return this.userRepository.save(user);
    }
    return user;
  }

  async removeDevice(id: string, deviceId: string): Promise<User> {
    const user = await this.findById(id);
    user.deviceIds = user.deviceIds.filter(d => d !== deviceId);
    return this.userRepository.save(user);
  }

  async updateOnlineStatus(id: string, isOnline: boolean): Promise<User> {
    const user = await this.findById(id);
    if (isOnline) {
      user.lastLoginAt = new Date();
    }
    return this.userRepository.save(user);
  }

  async findAdminUsers(): Promise<User[]> {
    return this.userRepository.find({ where: { isAdmin: true } });
  }

  async createAdmin(adminData: {
    username: string;
    email: string;
    phone?: string;
    secretWordHash: ISecretWordHash;
  }): Promise<User> {
    const user = this.userRepository.create({
      username: adminData.username,
      email: adminData.email,
      phone: adminData.phone,
      secretWordHash: adminData.secretWordHash,
      isAdmin: true,
      isActive: true,
      isVerified: true,
      deviceIds: [],
      accountStatus: 'active',
    });
    return this.userRepository.save(user);
  }

  async findByEmailSafe(email: string): Promise<User | null> {
    return this.userRepository.findOne({ where: { email } });
  }

  async updatePhone(id: string, phone: string): Promise<User> {
    const user = await this.findById(id);
    user.phone = phone;
    return this.userRepository.save(user);
  }
}