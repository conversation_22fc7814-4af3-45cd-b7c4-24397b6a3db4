<!DOCTYPE html>
<html>
<head>
    <title>QSC Login Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 500px;
            margin: 50px auto;
            padding: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            width: 100%;
        }
        button:hover {
            background-color: #0056b3;
        }
        .result {
            margin-top: 20px;
            padding: 10px;
            border-radius: 4px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <h1>QSC Login Test</h1>
    <p>Test the login functionality with the QSC admin user.</p>
    
    <form id="loginForm">
        <div class="form-group">
            <label for="username">Username:</label>
            <input type="text" id="username" value="QSC" required>
        </div>
        
        <div class="form-group">
            <label for="secretWord">Secret Word:</label>
            <input type="password" id="secretWord" value="8l*R" required>
        </div>
        
        <button type="submit">Login</button>
    </form>
    
    <div id="result"></div>

    <script>
        document.getElementById('loginForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const username = document.getElementById('username').value;
            const secretWord = document.getElementById('secretWord').value;
            const resultDiv = document.getElementById('result');
            
            try {
                const response = await fetch('http://localhost:3001/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        username: username,
                        secretWord: secretWord
                    })
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = `
                        <h3>✅ Login Successful!</h3>
                        <p><strong>User:</strong> ${data.data.user.username}</p>
                        <p><strong>Email:</strong> ${data.data.user.email}</p>
                        <p><strong>Phone:</strong> ${data.data.user.phone || 'Not set'}</p>
                        <p><strong>Admin:</strong> ${data.data.user.isAdmin ? 'Yes' : 'No'}</p>
                        <p><strong>Token:</strong> ${data.data.accessToken.substring(0, 50)}...</p>
                    `;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = `
                        <h3>❌ Login Failed</h3>
                        <p><strong>Error:</strong> ${data.error || data.message || 'Unknown error'}</p>
                    `;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `
                    <h3>❌ Network Error</h3>
                    <p><strong>Error:</strong> ${error.message}</p>
                `;
            }
        });
    </script>
</body>
</html>
