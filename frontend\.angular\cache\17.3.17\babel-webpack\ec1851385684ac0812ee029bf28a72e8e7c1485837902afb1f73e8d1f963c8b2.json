{"ast": null, "code": "import { BehaviorSubject, tap, switchMap, combineLatest, catchError, throwError } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"./api.service\";\nimport * as i2 from \"./secure-token.service\";\nexport let AuthService = /*#__PURE__*/(() => {\n  class AuthService {\n    constructor(apiService, secureTokenService) {\n      this.apiService = apiService;\n      this.secureTokenService = secureTokenService;\n      this.userSubject = new BehaviorSubject(null);\n      // Combine token and user observables to create auth state\n      this.authState$ = combineLatest([this.secureTokenService.token$, this.userSubject.asObservable()]).pipe(tap(([token, user]) => {\n        // Auto-refresh token if it's expiring soon\n        if (token && this.secureTokenService.isTokenExpiringSoon()) {\n          this.refreshTokenIfNeeded();\n        }\n      }), switchMap(([token, user]) => {\n        const isAuthenticated = !!token && this.secureTokenService.isTokenValid();\n        return [{\n          isAuthenticated,\n          user\n        }];\n      }));\n      this.initializeAuthState();\n    }\n    initializeAuthState() {\n      // Check for existing tokens and migrate from localStorage if needed\n      const legacyToken = localStorage.getItem('qsc_token');\n      const legacyUserStr = localStorage.getItem('qsc_user');\n      if (legacyToken && legacyUserStr) {\n        try {\n          const user = JSON.parse(legacyUserStr);\n          // Migrate to secure storage\n          this.secureTokenService.setTokens({\n            token: legacyToken,\n            expiresAt: this.getTokenExpiration(legacyToken) || Date.now() + 3600000 // 1 hour default\n          });\n          this.userSubject.next(user);\n          // Clean up legacy storage\n          localStorage.removeItem('qsc_token');\n          localStorage.removeItem('qsc_user');\n        } catch (error) {\n          console.error('Error migrating legacy auth data:', error);\n          this.logout();\n        }\n      } else {\n        // Check if we have a valid token in secure storage\n        const currentToken = this.secureTokenService.getToken();\n        if (currentToken && this.secureTokenService.isTokenValid()) {\n          // Fetch current user data\n          this.getCurrentUser().subscribe({\n            next: user => this.userSubject.next(user),\n            error: () => this.logout()\n          });\n        }\n      }\n    }\n    login(emailOrCredentials, secretWord) {\n      let credentials;\n      if (typeof emailOrCredentials === 'string') {\n        credentials = {\n          email: emailOrCredentials,\n          password: secretWord\n        };\n      } else {\n        credentials = emailOrCredentials;\n      }\n      return this.apiService.post('/auth/login', credentials).pipe(tap(response => {\n        // Store tokens securely\n        const tokenData = {\n          token: response.accessToken,\n          refreshToken: response.refreshToken,\n          expiresAt: Date.now() + response.expiresIn * 1000,\n          tokenType: response.tokenType\n        };\n        this.secureTokenService.setTokens(tokenData);\n        // Update user state\n        this.userSubject.next(response.user);\n      }), catchError(error => {\n        console.error('Login failed:', error);\n        return throwError(() => error);\n      }));\n    }\n    register(registerData) {\n      return this.apiService.post('/auth/register', registerData).pipe(tap(response => {\n        // Store tokens securely after successful registration\n        const tokenData = {\n          token: response.accessToken,\n          refreshToken: response.refreshToken,\n          expiresAt: Date.now() + response.expiresIn * 1000,\n          tokenType: response.tokenType\n        };\n        this.secureTokenService.setTokens(tokenData);\n        // Update user state\n        this.userSubject.next(response.user);\n      }), catchError(error => {\n        console.error('Registration failed:', error);\n        return throwError(() => error);\n      }));\n    }\n    logout() {\n      // Clear all tokens\n      this.secureTokenService.clearTokens();\n      this.userSubject.next(null);\n      // Optionally call logout endpoint\n      this.apiService.post('/auth/logout', {}).subscribe({\n        error: error => console.warn('Logout endpoint failed:', error)\n      });\n    }\n    getCurrentUser() {\n      return this.apiService.get('/users/profile');\n    }\n    isAuthenticated() {\n      const token = this.secureTokenService.getToken();\n      return !!token && this.secureTokenService.isTokenValid();\n    }\n    getToken() {\n      return this.secureTokenService.getToken();\n    }\n    getUser() {\n      return this.userSubject.value;\n    }\n    getCurrentUser() {\n      return this.userSubject.value;\n    }\n    /**\n     * Refresh token if needed\n     */\n    refreshTokenIfNeeded() {\n      const refreshToken = this.secureTokenService.getRefreshToken();\n      if (refreshToken) {\n        this.apiService.post('/auth/refresh', {\n          refresh_token: refreshToken\n        }).subscribe({\n          next: response => {\n            this.secureTokenService.setTokens({\n              token: response.access_token,\n              refreshToken: response.refresh_token,\n              expiresAt: this.getTokenExpiration(response.access_token) || Date.now() + 3600000\n            });\n          },\n          error: () => {\n            // Refresh failed, logout user\n            this.logout();\n          }\n        });\n      }\n    }\n    /**\n     * Extract expiration time from JWT token\n     */\n    getTokenExpiration(token) {\n      try {\n        const payload = JSON.parse(atob(token.split('.')[1]));\n        return payload.exp;\n      } catch {\n        return null;\n      }\n    }\n    static {\n      this.ɵfac = function AuthService_Factory(t) {\n        return new (t || AuthService)(i0.ɵɵinject(i1.ApiService), i0.ɵɵinject(i2.SecureTokenService));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: AuthService,\n        factory: AuthService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return AuthService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}