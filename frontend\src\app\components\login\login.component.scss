.login-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 1rem;
}

.login-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 3rem 2rem;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  width: 100%;
  max-width: 400px;
  text-align: center;
}

.logo-container {
  margin-bottom: 2rem;

  h1 {
    font-size: 2.5rem;
    font-weight: 300;
    color: #333;
    margin: 1rem 0 0.5rem 0;
    letter-spacing: 0.1em;
  }

  p {
    color: #666;
    font-size: 0.9rem;
    margin: 0;
  }
}

.qsc-logo {
  display: flex;
  justify-content: center;
  margin-bottom: 1rem;

  .circle {
    width: 80px;
    height: 80px;
    border: 4px solid #667eea;
    border-radius: 50%;
    position: relative;
    overflow: hidden;

    .wind-effect {
      position: absolute;
      top: 0;
      right: -20px;
      width: 40px;
      height: 100%;
      background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.8) 50%, transparent 100%);
      transform: skew(-20deg);
      animation: windBlow 3s ease-in-out infinite;
    }
  }
}

@keyframes windBlow {
  0%, 100% {
    opacity: 0;
    transform: translateX(-40px) skew(-20deg);
  }
  50% {
    opacity: 1;
    transform: translateX(20px) skew(-20deg);
  }
}

.login-form {
  .form-group {
    margin-bottom: 1.5rem;
    text-align: left;

    label {
      display: block;
      margin-bottom: 0.5rem;
      color: #333;
      font-weight: 500;
      font-size: 0.9rem;
    }

    input {
      width: 100%;
      padding: 0.75rem 1rem;
      border: 2px solid #e1e5e9;
      border-radius: 10px;
      font-size: 1rem;
      transition: all 0.3s ease;
      background: white;

      &:focus {
        outline: none;
        border-color: #667eea;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
      }

      &:disabled {
        background: #f5f5f5;
        cursor: not-allowed;
      }

      &::placeholder {
        color: #999;
      }
    }
  }
}

.error-message {
  background: #fee;
  color: #c33;
  padding: 0.75rem;
  border-radius: 8px;
  margin-bottom: 1rem;
  font-size: 0.9rem;
  border: 1px solid #fcc;
}

.login-button {
  width: 100%;
  padding: 0.875rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 10px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-bottom: 1.5rem;

  &:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
  }

  &:disabled {
    opacity: 0.7;
    cursor: not-allowed;
    transform: none;
  }

  .loading-spinner {
    animation: spin 1s linear infinite;
  }
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.security-notice {
  p {
    color: #666;
    font-size: 0.8rem;
    margin: 0;
  }
}

@media (max-width: 480px) {
  .login-card {
    padding: 2rem 1.5rem;
  }

  .qsc-logo .circle {
    width: 60px;
    height: 60px;
  }

  .logo-container h1 {
    font-size: 2rem;
  }
}
