{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Projects/QSC1/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { __decorate } from \"tslib\";\nimport { Injectable } from '@angular/core';\nimport { ErrorHandlingService } from './error-handling.service';\nlet WasmService = class WasmService {\n  constructor(errorHandling) {\n    this.errorHandling = errorHandling;\n    this.wasmModule = null;\n    this.currentKeyPair = null;\n    this.KEY_ROTATION_INTERVAL = 30 * 24 * 60 * 60 * 1000; // 30 days\n    this.wasmInstance = null;\n    this.wasmFunctions = null;\n    this.initializeWasm();\n    this.startKeyRotation();\n  }\n  init() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      try {\n        // Dynamic import of WASM module - will fail gracefully in development\n        _this.wasmInstance = yield import('/assets/wasm/pqc_wasm.js');\n        yield _this.wasmInstance.default();\n        // Extract WASM functions\n        _this.wasmFunctions = {\n          generate_key_pair: _this.wasmInstance.generate_key_pair,\n          encrypt_message: _this.wasmInstance.encrypt_message,\n          decrypt_message: _this.wasmInstance.decrypt_message,\n          rotate_keys: _this.wasmInstance.rotate_keys\n        };\n      } catch (error) {\n        console.warn('WASM module not available, using fallback implementation');\n        _this.initializeFallback();\n      }\n    })();\n  }\n  initializeWasm() {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        yield _this2.init();\n        yield _this2.generateNewKeyPair();\n      } catch (error) {\n        yield _this2.errorHandling.handleError(error instanceof Error ? error : new Error('WASM initialization failed'), 'WASM');\n        // Don't throw error, allow fallback to work\n        console.warn('WASM initialization failed, continuing with fallback');\n      }\n    })();\n  }\n  /**\n   * Initialize fallback cryptographic implementation\n   */\n  initializeFallback() {\n    console.warn('Using fallback cryptographic implementation - NOT SECURE FOR PRODUCTION');\n    this.wasmFunctions = {\n      generate_key_pair: this.fallbackGenerateKeyPair.bind(this),\n      encrypt_message: this.fallbackEncryptMessage.bind(this),\n      decrypt_message: this.fallbackDecryptMessage.bind(this),\n      rotate_keys: this.fallbackRotateKeys.bind(this)\n    };\n  }\n  generateNewKeyPair() {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        if (!_this3.wasmFunctions) {\n          throw new Error('WASM functions not initialized');\n        }\n        const keyPairBytes = yield _this3.wasmFunctions.generate_key_pair();\n        const keyPair = JSON.parse(new TextDecoder().decode(keyPairBytes));\n        _this3.currentKeyPair = {\n          public_key: new Uint8Array(keyPair.public_key),\n          private_key: new Uint8Array(keyPair.private_key),\n          timestamp: new Date(),\n          version: (_this3.currentKeyPair?.version ?? 0) + 1\n        };\n      } catch (error) {\n        yield _this3.errorHandling.handleError(error instanceof Error ? error : new Error('Key pair generation failed'), 'WASM');\n        throw error;\n      }\n    })();\n  }\n  startKeyRotation() {\n    var _this4 = this;\n    setInterval(/*#__PURE__*/_asyncToGenerator(function* () {\n      try {\n        yield _this4.rotateKeys();\n      } catch (error) {\n        yield _this4.errorHandling.handleError(error instanceof Error ? error : new Error('Key rotation failed'), 'SECURITY');\n      }\n    }), this.KEY_ROTATION_INTERVAL);\n  }\n  rotateKeys() {\n    var _this5 = this;\n    return _asyncToGenerator(function* () {\n      if (!_this5.currentKeyPair) {\n        throw new Error('No key pair available for rotation');\n      }\n      if (!_this5.wasmFunctions) {\n        throw new Error('WASM functions not initialized');\n      }\n      try {\n        const newKeyPairBytes = yield _this5.wasmFunctions.rotate_keys(_this5.currentKeyPair.private_key);\n        const newKeyPair = JSON.parse(new TextDecoder().decode(newKeyPairBytes));\n        _this5.currentKeyPair = {\n          public_key: new Uint8Array(newKeyPair.public_key),\n          private_key: new Uint8Array(newKeyPair.private_key),\n          timestamp: new Date(),\n          version: _this5.currentKeyPair.version + 1\n        };\n      } catch (error) {\n        yield _this5.errorHandling.handleError(error instanceof Error ? error : new Error('Key rotation failed'), 'SECURITY');\n        throw error;\n      }\n    })();\n  }\n  encryptMessage(message) {\n    var _this6 = this;\n    return _asyncToGenerator(function* () {\n      if (!_this6.currentKeyPair) {\n        throw new Error('No key pair available for encryption');\n      }\n      if (!_this6.wasmFunctions) {\n        throw new Error('WASM functions not initialized');\n      }\n      try {\n        const messageBytes = new TextEncoder().encode(message);\n        const encryptedBytes = yield _this6.wasmFunctions.encrypt_message(messageBytes, _this6.currentKeyPair.public_key);\n        return new Uint8Array(encryptedBytes);\n      } catch (error) {\n        yield _this6.errorHandling.handleError(error instanceof Error ? error : new Error('Message encryption failed'), 'SECURITY');\n        throw error;\n      }\n    })();\n  }\n  decryptMessage(encrypted) {\n    var _this7 = this;\n    return _asyncToGenerator(function* () {\n      if (!_this7.currentKeyPair) {\n        throw new Error('No key pair available for decryption');\n      }\n      if (!_this7.wasmFunctions) {\n        throw new Error('WASM functions not initialized');\n      }\n      try {\n        const decryptedBytes = yield _this7.wasmFunctions.decrypt_message(encrypted, _this7.currentKeyPair.private_key);\n        return new TextDecoder().decode(decryptedBytes);\n      } catch (error) {\n        yield _this7.errorHandling.handleError(error instanceof Error ? error : new Error('Message decryption failed'), 'SECURITY');\n        throw error;\n      }\n    })();\n  }\n  signMessage(message) {\n    var _this8 = this;\n    return _asyncToGenerator(function* () {\n      if (!_this8.currentKeyPair) {\n        throw new Error('No key pair available for signing');\n      }\n      try {\n        // TODO: Implement signing logic using WASM\n        throw new Error('Signing method not implemented');\n      } catch (error) {\n        yield _this8.errorHandling.handleError(error instanceof Error ? error : new Error('Signing failed'), 'SECURITY');\n        throw error;\n      }\n    })();\n  }\n  verifySignature(message, signature) {\n    var _this9 = this;\n    return _asyncToGenerator(function* () {\n      if (!_this9.currentKeyPair) {\n        throw new Error('No key pair available for signature verification');\n      }\n      try {\n        // TODO: Implement signature verification logic using WASM\n        throw new Error('Signature verification method not implemented');\n      } catch (error) {\n        yield _this9.errorHandling.handleError(error instanceof Error ? error : new Error('Signature verification failed'), 'SECURITY');\n        throw error;\n      }\n    })();\n  }\n  importKeyPair(keyPair) {\n    var _this0 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        _this0.currentKeyPair = keyPair;\n      } catch (error) {\n        yield _this0.errorHandling.handleError(error instanceof Error ? error : new Error('Key pair import failed'), 'WASM');\n        throw error;\n      }\n    })();\n  }\n  wipeMemory() {\n    this.currentKeyPair = null;\n    this.wasmModule = null;\n    this.wasmInstance = null;\n    this.wasmFunctions = null;\n  }\n  // Fallback implementations (NOT SECURE FOR PRODUCTION)\n  fallbackGenerateKeyPair() {\n    return _asyncToGenerator(function* () {\n      const keyPair = {\n        public_key: Array.from(crypto.getRandomValues(new Uint8Array(32))),\n        private_key: Array.from(crypto.getRandomValues(new Uint8Array(64)))\n      };\n      return new TextEncoder().encode(JSON.stringify(keyPair));\n    })();\n  }\n  fallbackEncryptMessage(message, publicKey) {\n    return _asyncToGenerator(function* () {\n      // Simple XOR encryption (NOT SECURE)\n      const key = publicKey.slice(0, 32);\n      const encrypted = new Uint8Array(message.length);\n      for (let i = 0; i < message.length; i++) {\n        encrypted[i] = message[i] ^ key[i % key.length];\n      }\n      return encrypted;\n    })();\n  }\n  fallbackDecryptMessage(encrypted, privateKey) {\n    return _asyncToGenerator(function* () {\n      // Simple XOR decryption (NOT SECURE)\n      const key = privateKey.slice(0, 32);\n      const decrypted = new Uint8Array(encrypted.length);\n      for (let i = 0; i < encrypted.length; i++) {\n        decrypted[i] = encrypted[i] ^ key[i % key.length];\n      }\n      return decrypted;\n    })();\n  }\n  fallbackRotateKeys(oldPrivateKey) {\n    var _this1 = this;\n    return _asyncToGenerator(function* () {\n      // Generate new key pair\n      return _this1.fallbackGenerateKeyPair();\n    })();\n  }\n  static {\n    this.ctorParameters = () => [{\n      type: ErrorHandlingService\n    }];\n  }\n};\nWasmService = __decorate([Injectable({\n  providedIn: 'root'\n})], WasmService);\nexport { WasmService };", "map": {"version": 3, "names": ["Injectable", "ErrorHandlingService", "WasmService", "constructor", "errorHandling", "wasmModule", "currentKeyPair", "KEY_ROTATION_INTERVAL", "wasmInstance", "wasmFunctions", "initializeWasm", "startKeyRotation", "init", "_this", "_asyncToGenerator", "default", "generate_key_pair", "encrypt_message", "decrypt_message", "rotate_keys", "error", "console", "warn", "initializeFallback", "_this2", "generateNewKeyPair", "handleError", "Error", "fallbackGenerateKeyPair", "bind", "fallbackEncryptMessage", "fallbackDecryptMessage", "fallback<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_this3", "keyPairBytes", "keyPair", "JSON", "parse", "TextDecoder", "decode", "public_key", "Uint8Array", "private_key", "timestamp", "Date", "version", "_this4", "setInterval", "rotateKeys", "_this5", "newKeyPairBytes", "newKeyPair", "encryptMessage", "message", "_this6", "messageBytes", "TextEncoder", "encode", "encryptedBytes", "decryptMessage", "encrypted", "_this7", "decryptedBytes", "signMessage", "_this8", "verifySignature", "signature", "_this9", "importKeyPair", "_this0", "wipeMemory", "Array", "from", "crypto", "getRandomValues", "stringify", "public<PERSON>ey", "key", "slice", "length", "i", "privateKey", "decrypted", "oldPrivateKey", "_this1", "__decorate", "providedIn"], "sources": ["C:\\Users\\<USER>\\Projects\\QSC1\\frontend\\src\\app\\services\\wasm.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { ErrorHandlingService } from './error-handling.service';\r\n\r\nexport interface KeyPair {\r\n  version: number;\r\n  timestamp: Date;\r\n  public_key: Uint8Array;\r\n  private_key: Uint8Array;\r\n}\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class WasmService {\r\n  private wasmModule: WebAssembly.Module | null = null;\r\n  private currentKeyPair: KeyPair | null = null;\r\n  private readonly KEY_ROTATION_INTERVAL = 30 * 24 * 60 * 60 * 1000; // 30 days\r\n  private wasmInstance: any = null;\r\n  private wasmFunctions: any = null;\r\n\r\n  constructor(private errorHandling: ErrorHandlingService) {\r\n    this.initializeWasm();\r\n    this.startKeyRotation();\r\n  }\r\n\r\n  async init(): Promise<void> {\r\n    try {\r\n      // Dynamic import of WASM module - will fail gracefully in development\r\n      this.wasmInstance = await import('/assets/wasm/pqc_wasm.js' as any);\r\n      await this.wasmInstance.default();\r\n\r\n      // Extract WASM functions\r\n      this.wasmFunctions = {\r\n        generate_key_pair: this.wasmInstance.generate_key_pair,\r\n        encrypt_message: this.wasmInstance.encrypt_message,\r\n        decrypt_message: this.wasmInstance.decrypt_message,\r\n        rotate_keys: this.wasmInstance.rotate_keys\r\n      };\r\n    } catch (error) {\r\n      console.warn('WASM module not available, using fallback implementation');\r\n      this.initializeFallback();\r\n    }\r\n  }\r\n\r\n  private async initializeWasm(): Promise<void> {\r\n    try {\r\n      await this.init();\r\n      await this.generateNewKeyPair();\r\n    } catch (error) {\r\n      await this.errorHandling.handleError(\r\n        error instanceof Error ? error : new Error('WASM initialization failed'),\r\n        'WASM'\r\n      );\r\n      // Don't throw error, allow fallback to work\r\n      console.warn('WASM initialization failed, continuing with fallback');\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Initialize fallback cryptographic implementation\r\n   */\r\n  private initializeFallback(): void {\r\n    console.warn('Using fallback cryptographic implementation - NOT SECURE FOR PRODUCTION');\r\n    this.wasmFunctions = {\r\n      generate_key_pair: this.fallbackGenerateKeyPair.bind(this),\r\n      encrypt_message: this.fallbackEncryptMessage.bind(this),\r\n      decrypt_message: this.fallbackDecryptMessage.bind(this),\r\n      rotate_keys: this.fallbackRotateKeys.bind(this)\r\n    };\r\n  }\r\n\r\n  private async generateNewKeyPair(): Promise<void> {\r\n    try {\r\n      if (!this.wasmFunctions) {\r\n        throw new Error('WASM functions not initialized');\r\n      }\r\n\r\n      const keyPairBytes = await this.wasmFunctions.generate_key_pair();\r\n      const keyPair = JSON.parse(new TextDecoder().decode(keyPairBytes));\r\n\r\n      this.currentKeyPair = {\r\n        public_key: new Uint8Array(keyPair.public_key),\r\n        private_key: new Uint8Array(keyPair.private_key),\r\n        timestamp: new Date(),\r\n        version: (this.currentKeyPair?.version ?? 0) + 1\r\n      };\r\n    } catch (error) {\r\n      await this.errorHandling.handleError(\r\n        error instanceof Error ? error : new Error('Key pair generation failed'),\r\n        'WASM'\r\n      );\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  private startKeyRotation(): void {\r\n    setInterval(async () => {\r\n      try {\r\n        await this.rotateKeys();\r\n      } catch (error) {\r\n        await this.errorHandling.handleError(\r\n          error instanceof Error ? error : new Error('Key rotation failed'),\r\n          'SECURITY'\r\n        );\r\n      }\r\n    }, this.KEY_ROTATION_INTERVAL);\r\n  }\r\n\r\n  public async rotateKeys(): Promise<void> {\r\n    if (!this.currentKeyPair) {\r\n      throw new Error('No key pair available for rotation');\r\n    }\r\n\r\n    if (!this.wasmFunctions) {\r\n      throw new Error('WASM functions not initialized');\r\n    }\r\n\r\n    try {\r\n      const newKeyPairBytes = await this.wasmFunctions.rotate_keys(this.currentKeyPair.private_key);\r\n      const newKeyPair = JSON.parse(new TextDecoder().decode(newKeyPairBytes));\r\n\r\n      this.currentKeyPair = {\r\n        public_key: new Uint8Array(newKeyPair.public_key),\r\n        private_key: new Uint8Array(newKeyPair.private_key),\r\n        timestamp: new Date(),\r\n        version: this.currentKeyPair.version + 1\r\n      };\r\n    } catch (error) {\r\n      await this.errorHandling.handleError(\r\n        error instanceof Error ? error : new Error('Key rotation failed'),\r\n        'SECURITY'\r\n      );\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  public async encryptMessage(message: string): Promise<Uint8Array> {\r\n    if (!this.currentKeyPair) {\r\n      throw new Error('No key pair available for encryption');\r\n    }\r\n\r\n    if (!this.wasmFunctions) {\r\n      throw new Error('WASM functions not initialized');\r\n    }\r\n\r\n    try {\r\n      const messageBytes = new TextEncoder().encode(message);\r\n      const encryptedBytes = await this.wasmFunctions.encrypt_message(messageBytes, this.currentKeyPair.public_key);\r\n      return new Uint8Array(encryptedBytes);\r\n    } catch (error) {\r\n      await this.errorHandling.handleError(\r\n        error instanceof Error ? error : new Error('Message encryption failed'),\r\n        'SECURITY'\r\n      );\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  public async decryptMessage(encrypted: Uint8Array): Promise<string> {\r\n    if (!this.currentKeyPair) {\r\n      throw new Error('No key pair available for decryption');\r\n    }\r\n\r\n    if (!this.wasmFunctions) {\r\n      throw new Error('WASM functions not initialized');\r\n    }\r\n\r\n    try {\r\n      const decryptedBytes = await this.wasmFunctions.decrypt_message(encrypted, this.currentKeyPair.private_key);\r\n      return new TextDecoder().decode(decryptedBytes);\r\n    } catch (error) {\r\n      await this.errorHandling.handleError(\r\n        error instanceof Error ? error : new Error('Message decryption failed'),\r\n        'SECURITY'\r\n      );\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  public async signMessage(message: string): Promise<Uint8Array> {\r\n    if (!this.currentKeyPair) {\r\n      throw new Error('No key pair available for signing');\r\n    }\r\n\r\n    try {\r\n      // TODO: Implement signing logic using WASM\r\n      throw new Error('Signing method not implemented');\r\n    } catch (error) {\r\n      await this.errorHandling.handleError(\r\n        error instanceof Error ? error : new Error('Signing failed'),\r\n        'SECURITY'\r\n      );\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  public async verifySignature(message: string, signature: Uint8Array): Promise<boolean> {\r\n    if (!this.currentKeyPair) {\r\n      throw new Error('No key pair available for signature verification');\r\n    }\r\n\r\n    try {\r\n      // TODO: Implement signature verification logic using WASM\r\n      throw new Error('Signature verification method not implemented');\r\n    } catch (error) {\r\n      await this.errorHandling.handleError(\r\n        error instanceof Error ? error : new Error('Signature verification failed'),\r\n        'SECURITY'\r\n      );\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  public async importKeyPair(keyPair: KeyPair): Promise<void> {\r\n    try {\r\n      this.currentKeyPair = keyPair;\r\n    } catch (error) {\r\n      await this.errorHandling.handleError(\r\n        error instanceof Error ? error : new Error('Key pair import failed'),\r\n        'WASM'\r\n      );\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  public wipeMemory(): void {\r\n    this.currentKeyPair = null;\r\n    this.wasmModule = null;\r\n    this.wasmInstance = null;\r\n    this.wasmFunctions = null;\r\n  }\r\n\r\n  // Fallback implementations (NOT SECURE FOR PRODUCTION)\r\n  private async fallbackGenerateKeyPair(): Promise<Uint8Array> {\r\n    const keyPair = {\r\n      public_key: Array.from(crypto.getRandomValues(new Uint8Array(32))),\r\n      private_key: Array.from(crypto.getRandomValues(new Uint8Array(64)))\r\n    };\r\n    return new TextEncoder().encode(JSON.stringify(keyPair));\r\n  }\r\n\r\n  private async fallbackEncryptMessage(message: Uint8Array, publicKey: Uint8Array): Promise<Uint8Array> {\r\n    // Simple XOR encryption (NOT SECURE)\r\n    const key = publicKey.slice(0, 32);\r\n    const encrypted = new Uint8Array(message.length);\r\n    for (let i = 0; i < message.length; i++) {\r\n      encrypted[i] = message[i] ^ key[i % key.length];\r\n    }\r\n    return encrypted;\r\n  }\r\n\r\n  private async fallbackDecryptMessage(encrypted: Uint8Array, privateKey: Uint8Array): Promise<Uint8Array> {\r\n    // Simple XOR decryption (NOT SECURE)\r\n    const key = privateKey.slice(0, 32);\r\n    const decrypted = new Uint8Array(encrypted.length);\r\n    for (let i = 0; i < encrypted.length; i++) {\r\n      decrypted[i] = encrypted[i] ^ key[i % key.length];\r\n    }\r\n    return decrypted;\r\n  }\r\n\r\n  private async fallbackRotateKeys(oldPrivateKey: Uint8Array): Promise<Uint8Array> {\r\n    // Generate new key pair\r\n    return this.fallbackGenerateKeyPair();\r\n  }\r\n}\r\n"], "mappings": ";;AAAA,SAASA,UAAU,QAAQ,eAAe;AAC1C,SAASC,oBAAoB,QAAQ,0BAA0B;AAYxD,IAAMC,WAAW,GAAjB,MAAMA,WAAW;EAOtBC,YAAoBC,aAAmC;IAAnC,KAAAA,aAAa,GAAbA,aAAa;IANzB,KAAAC,UAAU,GAA8B,IAAI;IAC5C,KAAAC,cAAc,GAAmB,IAAI;IAC5B,KAAAC,qBAAqB,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;IAC3D,KAAAC,YAAY,GAAQ,IAAI;IACxB,KAAAC,aAAa,GAAQ,IAAI;IAG/B,IAAI,CAACC,cAAc,EAAE;IACrB,IAAI,CAACC,gBAAgB,EAAE;EACzB;EAEMC,IAAIA,CAAA;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MACR,IAAI;QACF;QACAD,KAAI,CAACL,YAAY,SAAS,MAAM,CAAC,0BAAiC,CAAC;QACnE,MAAMK,KAAI,CAACL,YAAY,CAACO,OAAO,EAAE;QAEjC;QACAF,KAAI,CAACJ,aAAa,GAAG;UACnBO,iBAAiB,EAAEH,KAAI,CAACL,YAAY,CAACQ,iBAAiB;UACtDC,eAAe,EAAEJ,KAAI,CAACL,YAAY,CAACS,eAAe;UAClDC,eAAe,EAAEL,KAAI,CAACL,YAAY,CAACU,eAAe;UAClDC,WAAW,EAAEN,KAAI,CAACL,YAAY,CAACW;SAChC;OACF,CAAC,OAAOC,KAAK,EAAE;QACdC,OAAO,CAACC,IAAI,CAAC,0DAA0D,CAAC;QACxET,KAAI,CAACU,kBAAkB,EAAE;;IAC1B;EACH;EAEcb,cAAcA,CAAA;IAAA,IAAAc,MAAA;IAAA,OAAAV,iBAAA;MAC1B,IAAI;QACF,MAAMU,MAAI,CAACZ,IAAI,EAAE;QACjB,MAAMY,MAAI,CAACC,kBAAkB,EAAE;OAChC,CAAC,OAAOL,KAAK,EAAE;QACd,MAAMI,MAAI,CAACpB,aAAa,CAACsB,WAAW,CAClCN,KAAK,YAAYO,KAAK,GAAGP,KAAK,GAAG,IAAIO,KAAK,CAAC,4BAA4B,CAAC,EACxE,MAAM,CACP;QACD;QACAN,OAAO,CAACC,IAAI,CAAC,sDAAsD,CAAC;;IACrE;EACH;EAEA;;;EAGQC,kBAAkBA,CAAA;IACxBF,OAAO,CAACC,IAAI,CAAC,yEAAyE,CAAC;IACvF,IAAI,CAACb,aAAa,GAAG;MACnBO,iBAAiB,EAAE,IAAI,CAACY,uBAAuB,CAACC,IAAI,CAAC,IAAI,CAAC;MAC1DZ,eAAe,EAAE,IAAI,CAACa,sBAAsB,CAACD,IAAI,CAAC,IAAI,CAAC;MACvDX,eAAe,EAAE,IAAI,CAACa,sBAAsB,CAACF,IAAI,CAAC,IAAI,CAAC;MACvDV,WAAW,EAAE,IAAI,CAACa,kBAAkB,CAACH,IAAI,CAAC,IAAI;KAC/C;EACH;EAEcJ,kBAAkBA,CAAA;IAAA,IAAAQ,MAAA;IAAA,OAAAnB,iBAAA;MAC9B,IAAI;QACF,IAAI,CAACmB,MAAI,CAACxB,aAAa,EAAE;UACvB,MAAM,IAAIkB,KAAK,CAAC,gCAAgC,CAAC;;QAGnD,MAAMO,YAAY,SAASD,MAAI,CAACxB,aAAa,CAACO,iBAAiB,EAAE;QACjE,MAAMmB,OAAO,GAAGC,IAAI,CAACC,KAAK,CAAC,IAAIC,WAAW,EAAE,CAACC,MAAM,CAACL,YAAY,CAAC,CAAC;QAElED,MAAI,CAAC3B,cAAc,GAAG;UACpBkC,UAAU,EAAE,IAAIC,UAAU,CAACN,OAAO,CAACK,UAAU,CAAC;UAC9CE,WAAW,EAAE,IAAID,UAAU,CAACN,OAAO,CAACO,WAAW,CAAC;UAChDC,SAAS,EAAE,IAAIC,IAAI,EAAE;UACrBC,OAAO,EAAE,CAACZ,MAAI,CAAC3B,cAAc,EAAEuC,OAAO,IAAI,CAAC,IAAI;SAChD;OACF,CAAC,OAAOzB,KAAK,EAAE;QACd,MAAMa,MAAI,CAAC7B,aAAa,CAACsB,WAAW,CAClCN,KAAK,YAAYO,KAAK,GAAGP,KAAK,GAAG,IAAIO,KAAK,CAAC,4BAA4B,CAAC,EACxE,MAAM,CACP;QACD,MAAMP,KAAK;;IACZ;EACH;EAEQT,gBAAgBA,CAAA;IAAA,IAAAmC,MAAA;IACtBC,WAAW,cAAAjC,iBAAA,CAAC,aAAW;MACrB,IAAI;QACF,MAAMgC,MAAI,CAACE,UAAU,EAAE;OACxB,CAAC,OAAO5B,KAAK,EAAE;QACd,MAAM0B,MAAI,CAAC1C,aAAa,CAACsB,WAAW,CAClCN,KAAK,YAAYO,KAAK,GAAGP,KAAK,GAAG,IAAIO,KAAK,CAAC,qBAAqB,CAAC,EACjE,UAAU,CACX;;IAEL,CAAC,GAAE,IAAI,CAACpB,qBAAqB,CAAC;EAChC;EAEayC,UAAUA,CAAA;IAAA,IAAAC,MAAA;IAAA,OAAAnC,iBAAA;MACrB,IAAI,CAACmC,MAAI,CAAC3C,cAAc,EAAE;QACxB,MAAM,IAAIqB,KAAK,CAAC,oCAAoC,CAAC;;MAGvD,IAAI,CAACsB,MAAI,CAACxC,aAAa,EAAE;QACvB,MAAM,IAAIkB,KAAK,CAAC,gCAAgC,CAAC;;MAGnD,IAAI;QACF,MAAMuB,eAAe,SAASD,MAAI,CAACxC,aAAa,CAACU,WAAW,CAAC8B,MAAI,CAAC3C,cAAc,CAACoC,WAAW,CAAC;QAC7F,MAAMS,UAAU,GAAGf,IAAI,CAACC,KAAK,CAAC,IAAIC,WAAW,EAAE,CAACC,MAAM,CAACW,eAAe,CAAC,CAAC;QAExED,MAAI,CAAC3C,cAAc,GAAG;UACpBkC,UAAU,EAAE,IAAIC,UAAU,CAACU,UAAU,CAACX,UAAU,CAAC;UACjDE,WAAW,EAAE,IAAID,UAAU,CAACU,UAAU,CAACT,WAAW,CAAC;UACnDC,SAAS,EAAE,IAAIC,IAAI,EAAE;UACrBC,OAAO,EAAEI,MAAI,CAAC3C,cAAc,CAACuC,OAAO,GAAG;SACxC;OACF,CAAC,OAAOzB,KAAK,EAAE;QACd,MAAM6B,MAAI,CAAC7C,aAAa,CAACsB,WAAW,CAClCN,KAAK,YAAYO,KAAK,GAAGP,KAAK,GAAG,IAAIO,KAAK,CAAC,qBAAqB,CAAC,EACjE,UAAU,CACX;QACD,MAAMP,KAAK;;IACZ;EACH;EAEagC,cAAcA,CAACC,OAAe;IAAA,IAAAC,MAAA;IAAA,OAAAxC,iBAAA;MACzC,IAAI,CAACwC,MAAI,CAAChD,cAAc,EAAE;QACxB,MAAM,IAAIqB,KAAK,CAAC,sCAAsC,CAAC;;MAGzD,IAAI,CAAC2B,MAAI,CAAC7C,aAAa,EAAE;QACvB,MAAM,IAAIkB,KAAK,CAAC,gCAAgC,CAAC;;MAGnD,IAAI;QACF,MAAM4B,YAAY,GAAG,IAAIC,WAAW,EAAE,CAACC,MAAM,CAACJ,OAAO,CAAC;QACtD,MAAMK,cAAc,SAASJ,MAAI,CAAC7C,aAAa,CAACQ,eAAe,CAACsC,YAAY,EAAED,MAAI,CAAChD,cAAc,CAACkC,UAAU,CAAC;QAC7G,OAAO,IAAIC,UAAU,CAACiB,cAAc,CAAC;OACtC,CAAC,OAAOtC,KAAK,EAAE;QACd,MAAMkC,MAAI,CAAClD,aAAa,CAACsB,WAAW,CAClCN,KAAK,YAAYO,KAAK,GAAGP,KAAK,GAAG,IAAIO,KAAK,CAAC,2BAA2B,CAAC,EACvE,UAAU,CACX;QACD,MAAMP,KAAK;;IACZ;EACH;EAEauC,cAAcA,CAACC,SAAqB;IAAA,IAAAC,MAAA;IAAA,OAAA/C,iBAAA;MAC/C,IAAI,CAAC+C,MAAI,CAACvD,cAAc,EAAE;QACxB,MAAM,IAAIqB,KAAK,CAAC,sCAAsC,CAAC;;MAGzD,IAAI,CAACkC,MAAI,CAACpD,aAAa,EAAE;QACvB,MAAM,IAAIkB,KAAK,CAAC,gCAAgC,CAAC;;MAGnD,IAAI;QACF,MAAMmC,cAAc,SAASD,MAAI,CAACpD,aAAa,CAACS,eAAe,CAAC0C,SAAS,EAAEC,MAAI,CAACvD,cAAc,CAACoC,WAAW,CAAC;QAC3G,OAAO,IAAIJ,WAAW,EAAE,CAACC,MAAM,CAACuB,cAAc,CAAC;OAChD,CAAC,OAAO1C,KAAK,EAAE;QACd,MAAMyC,MAAI,CAACzD,aAAa,CAACsB,WAAW,CAClCN,KAAK,YAAYO,KAAK,GAAGP,KAAK,GAAG,IAAIO,KAAK,CAAC,2BAA2B,CAAC,EACvE,UAAU,CACX;QACD,MAAMP,KAAK;;IACZ;EACH;EAEa2C,WAAWA,CAACV,OAAe;IAAA,IAAAW,MAAA;IAAA,OAAAlD,iBAAA;MACtC,IAAI,CAACkD,MAAI,CAAC1D,cAAc,EAAE;QACxB,MAAM,IAAIqB,KAAK,CAAC,mCAAmC,CAAC;;MAGtD,IAAI;QACF;QACA,MAAM,IAAIA,KAAK,CAAC,gCAAgC,CAAC;OAClD,CAAC,OAAOP,KAAK,EAAE;QACd,MAAM4C,MAAI,CAAC5D,aAAa,CAACsB,WAAW,CAClCN,KAAK,YAAYO,KAAK,GAAGP,KAAK,GAAG,IAAIO,KAAK,CAAC,gBAAgB,CAAC,EAC5D,UAAU,CACX;QACD,MAAMP,KAAK;;IACZ;EACH;EAEa6C,eAAeA,CAACZ,OAAe,EAAEa,SAAqB;IAAA,IAAAC,MAAA;IAAA,OAAArD,iBAAA;MACjE,IAAI,CAACqD,MAAI,CAAC7D,cAAc,EAAE;QACxB,MAAM,IAAIqB,KAAK,CAAC,kDAAkD,CAAC;;MAGrE,IAAI;QACF;QACA,MAAM,IAAIA,KAAK,CAAC,+CAA+C,CAAC;OACjE,CAAC,OAAOP,KAAK,EAAE;QACd,MAAM+C,MAAI,CAAC/D,aAAa,CAACsB,WAAW,CAClCN,KAAK,YAAYO,KAAK,GAAGP,KAAK,GAAG,IAAIO,KAAK,CAAC,+BAA+B,CAAC,EAC3E,UAAU,CACX;QACD,MAAMP,KAAK;;IACZ;EACH;EAEagD,aAAaA,CAACjC,OAAgB;IAAA,IAAAkC,MAAA;IAAA,OAAAvD,iBAAA;MACzC,IAAI;QACFuD,MAAI,CAAC/D,cAAc,GAAG6B,OAAO;OAC9B,CAAC,OAAOf,KAAK,EAAE;QACd,MAAMiD,MAAI,CAACjE,aAAa,CAACsB,WAAW,CAClCN,KAAK,YAAYO,KAAK,GAAGP,KAAK,GAAG,IAAIO,KAAK,CAAC,wBAAwB,CAAC,EACpE,MAAM,CACP;QACD,MAAMP,KAAK;;IACZ;EACH;EAEOkD,UAAUA,CAAA;IACf,IAAI,CAAChE,cAAc,GAAG,IAAI;IAC1B,IAAI,CAACD,UAAU,GAAG,IAAI;IACtB,IAAI,CAACG,YAAY,GAAG,IAAI;IACxB,IAAI,CAACC,aAAa,GAAG,IAAI;EAC3B;EAEA;EACcmB,uBAAuBA,CAAA;IAAA,OAAAd,iBAAA;MACnC,MAAMqB,OAAO,GAAG;QACdK,UAAU,EAAE+B,KAAK,CAACC,IAAI,CAACC,MAAM,CAACC,eAAe,CAAC,IAAIjC,UAAU,CAAC,EAAE,CAAC,CAAC,CAAC;QAClEC,WAAW,EAAE6B,KAAK,CAACC,IAAI,CAACC,MAAM,CAACC,eAAe,CAAC,IAAIjC,UAAU,CAAC,EAAE,CAAC,CAAC;OACnE;MACD,OAAO,IAAIe,WAAW,EAAE,CAACC,MAAM,CAACrB,IAAI,CAACuC,SAAS,CAACxC,OAAO,CAAC,CAAC;IAAC;EAC3D;EAEcL,sBAAsBA,CAACuB,OAAmB,EAAEuB,SAAqB;IAAA,OAAA9D,iBAAA;MAC7E;MACA,MAAM+D,GAAG,GAAGD,SAAS,CAACE,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC;MAClC,MAAMlB,SAAS,GAAG,IAAInB,UAAU,CAACY,OAAO,CAAC0B,MAAM,CAAC;MAChD,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG3B,OAAO,CAAC0B,MAAM,EAAEC,CAAC,EAAE,EAAE;QACvCpB,SAAS,CAACoB,CAAC,CAAC,GAAG3B,OAAO,CAAC2B,CAAC,CAAC,GAAGH,GAAG,CAACG,CAAC,GAAGH,GAAG,CAACE,MAAM,CAAC;;MAEjD,OAAOnB,SAAS;IAAC;EACnB;EAEc7B,sBAAsBA,CAAC6B,SAAqB,EAAEqB,UAAsB;IAAA,OAAAnE,iBAAA;MAChF;MACA,MAAM+D,GAAG,GAAGI,UAAU,CAACH,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC;MACnC,MAAMI,SAAS,GAAG,IAAIzC,UAAU,CAACmB,SAAS,CAACmB,MAAM,CAAC;MAClD,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGpB,SAAS,CAACmB,MAAM,EAAEC,CAAC,EAAE,EAAE;QACzCE,SAAS,CAACF,CAAC,CAAC,GAAGpB,SAAS,CAACoB,CAAC,CAAC,GAAGH,GAAG,CAACG,CAAC,GAAGH,GAAG,CAACE,MAAM,CAAC;;MAEnD,OAAOG,SAAS;IAAC;EACnB;EAEclD,kBAAkBA,CAACmD,aAAyB;IAAA,IAAAC,MAAA;IAAA,OAAAtE,iBAAA;MACxD;MACA,OAAOsE,MAAI,CAACxD,uBAAuB,EAAE;IAAC;EACxC;;;;;;;AA3PW1B,WAAW,GAAAmF,UAAA,EAHvBrF,UAAU,CAAC;EACVsF,UAAU,EAAE;CACb,CAAC,C,EACWpF,WAAW,CA4PvB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}