{"ast": null, "code": "import { BehaviorSubject, Subject, map, tap, catchError, throwError } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"./api.service\";\nimport * as i2 from \"./websocket.service\";\nexport class MessageService {\n  constructor(apiService, webSocketService) {\n    this.apiService = apiService;\n    this.webSocketService = webSocketService;\n    this.messagesSubject = new BehaviorSubject([]);\n    this.newMessageSubject = new Subject();\n    this.messages$ = this.messagesSubject.asObservable();\n    this.newMessage$ = this.newMessageSubject.asObservable();\n    this.initializeWebSocketListeners();\n  }\n  initializeWebSocketListeners() {\n    // Listen for incoming messages via WebSocket\n    this.webSocketService.on('message').subscribe(message => {\n      this.addMessage(message);\n      this.newMessageSubject.next(message);\n    });\n    // Listen for message status updates\n    this.webSocketService.on('messageStatus').subscribe(status => {\n      this.updateMessageStatus(status.messageId, status);\n    });\n  }\n  /**\n   * Load messages from the server\n   */\n  loadMessages() {\n    return this.apiService.get('/messages').pipe(map(messages => messages.map(msg => ({\n      ...msg,\n      timestamp: new Date(msg.timestamp),\n      read: msg.read || false\n    }))), tap(messages => {\n      this.messagesSubject.next(messages);\n    }), catchError(error => {\n      console.error('Failed to load messages:', error);\n      return throwError(() => error);\n    }));\n  }\n  /**\n   * Send a new message\n   */\n  sendMessage(message) {\n    const messageToSend = {\n      ...message,\n      timestamp: new Date(),\n      id: this.generateMessageId()\n    };\n    return this.apiService.post('/messages', messageToSend).pipe(map(response => ({\n      ...response,\n      timestamp: new Date(response.timestamp)\n    })), tap(sentMessage => {\n      this.addMessage(sentMessage);\n    }), catchError(error => {\n      console.error('Failed to send message:', error);\n      return throwError(() => error);\n    }));\n  }\n  /**\n   * Mark all messages as read\n   */\n  markAllAsRead() {\n    return this.apiService.post('/messages/mark-read', {}).pipe(tap(() => {\n      const currentMessages = this.messagesSubject.value;\n      const updatedMessages = currentMessages.map(msg => ({\n        ...msg,\n        read: true\n      }));\n      this.messagesSubject.next(updatedMessages);\n    }), catchError(error => {\n      console.error('Failed to mark messages as read:', error);\n      return throwError(() => error);\n    }));\n  }\n  /**\n   * Mark a specific message as read\n   */\n  markMessageAsRead(messageId) {\n    return this.apiService.post(`/messages/${messageId}/read`, {}).pipe(tap(() => {\n      this.updateMessageStatus(messageId, {\n        read: true\n      });\n    }), catchError(error => {\n      console.error('Failed to mark message as read:', error);\n      return throwError(() => error);\n    }));\n  }\n  /**\n   * Delete a message\n   */\n  deleteMessage(messageId) {\n    return this.apiService.delete(`/messages/${messageId}`).pipe(tap(() => {\n      const currentMessages = this.messagesSubject.value;\n      const updatedMessages = currentMessages.filter(msg => msg.id !== messageId);\n      this.messagesSubject.next(updatedMessages);\n    }), catchError(error => {\n      console.error('Failed to delete message:', error);\n      return throwError(() => error);\n    }));\n  }\n  /**\n   * Get unread message count\n   */\n  getUnreadCount() {\n    return this.messagesSubject.value.filter(msg => !msg.read).length;\n  }\n  /**\n   * Clear all messages (for logout)\n   */\n  clearMessages() {\n    this.messagesSubject.next([]);\n  }\n  /**\n   * Add a message to the local store\n   */\n  addMessage(message) {\n    const currentMessages = this.messagesSubject.value;\n    const messageExists = currentMessages.some(msg => msg.id === message.id);\n    if (!messageExists) {\n      const updatedMessages = [message, ...currentMessages];\n      this.messagesSubject.next(updatedMessages);\n    }\n  }\n  /**\n   * Update message status\n   */\n  updateMessageStatus(messageId, status) {\n    const currentMessages = this.messagesSubject.value;\n    const updatedMessages = currentMessages.map(msg => msg.id === messageId ? {\n      ...msg,\n      ...status\n    } : msg);\n    this.messagesSubject.next(updatedMessages);\n  }\n  /**\n   * Generate a unique message ID\n   */\n  generateMessageId() {\n    return `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\n  }\n  /**\n   * Search messages\n   */\n  searchMessages(query) {\n    return this.messages$.pipe(map(messages => messages.filter(msg => msg.content.toLowerCase().includes(query.toLowerCase()) || msg.sender.toLowerCase().includes(query.toLowerCase()))));\n  }\n  /**\n   * Get messages for a specific group\n   */\n  getGroupMessages(groupId) {\n    return this.messages$.pipe(map(messages => messages.filter(msg => msg.groupId === groupId)));\n  }\n  /**\n   * Get direct messages with a specific user\n   */\n  getDirectMessages(userId) {\n    return this.messages$.pipe(map(messages => messages.filter(msg => msg.recipient === userId || msg.sender === userId)));\n  }\n  static {\n    this.ɵfac = function MessageService_Factory(t) {\n      return new (t || MessageService)(i0.ɵɵinject(i1.ApiService), i0.ɵɵinject(i2.WebSocketService));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: MessageService,\n      factory: MessageService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["BehaviorSubject", "Subject", "map", "tap", "catchError", "throwError", "MessageService", "constructor", "apiService", "webSocketService", "messagesSubject", "newMessageSubject", "messages$", "asObservable", "newMessage$", "initializeWebSocketListeners", "on", "subscribe", "message", "addMessage", "next", "status", "updateMessageStatus", "messageId", "loadMessages", "get", "pipe", "messages", "msg", "timestamp", "Date", "read", "error", "console", "sendMessage", "messageToSend", "id", "generateMessageId", "post", "response", "sentMessage", "markAllAsRead", "currentMessages", "value", "updatedMessages", "markMessageAsRead", "deleteMessage", "delete", "filter", "getUnreadCount", "length", "clearMessages", "messageExists", "some", "now", "Math", "random", "toString", "substr", "searchMessages", "query", "content", "toLowerCase", "includes", "sender", "getGroupMessages", "groupId", "getDirectMessages", "userId", "recipient", "i0", "ɵɵinject", "i1", "ApiService", "i2", "WebSocketService", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Projects\\QSC1\\frontend\\src\\app\\services\\message.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { BehaviorSubject, Observable, Subject, map, tap, catchError, throwError } from 'rxjs';\nimport { ApiService } from './api.service';\nimport { WebSocketService } from './websocket.service';\n\nexport interface Message {\n  id: string;\n  content: string;\n  timestamp: Date;\n  sender: string;\n  recipient?: string;\n  groupId?: string;\n  read?: boolean;\n  encrypted?: boolean;\n}\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class MessageService {\n  private messagesSubject = new BehaviorSubject<Message[]>([]);\n  private newMessageSubject = new Subject<Message>();\n\n  public messages$ = this.messagesSubject.asObservable();\n  public newMessage$ = this.newMessageSubject.asObservable();\n\n  constructor(\n    private apiService: ApiService,\n    private webSocketService: WebSocketService\n  ) {\n    this.initializeWebSocketListeners();\n  }\n\n  private initializeWebSocketListeners(): void {\n    // Listen for incoming messages via WebSocket\n    this.webSocketService.on('message').subscribe((message: Message) => {\n      this.addMessage(message);\n      this.newMessageSubject.next(message);\n    });\n\n    // Listen for message status updates\n    this.webSocketService.on('messageStatus').subscribe((status: any) => {\n      this.updateMessageStatus(status.messageId, status);\n    });\n  }\n\n  /**\n   * Load messages from the server\n   */\n  loadMessages(): Observable<Message[]> {\n    return this.apiService.get<Message[]>('/messages').pipe(\n      map(messages => messages.map(msg => ({\n        ...msg,\n        timestamp: new Date(msg.timestamp),\n        read: msg.read || false\n      }))),\n      tap(messages => {\n        this.messagesSubject.next(messages);\n      }),\n      catchError(error => {\n        console.error('Failed to load messages:', error);\n        return throwError(() => error);\n      })\n    );\n  }\n\n  /**\n   * Send a new message\n   */\n  sendMessage(message: Partial<Message>): Observable<Message> {\n    const messageToSend = {\n      ...message,\n      timestamp: new Date(),\n      id: this.generateMessageId()\n    };\n\n    return this.apiService.post<Message>('/messages', messageToSend).pipe(\n      map(response => ({\n        ...response,\n        timestamp: new Date(response.timestamp)\n      })),\n      tap(sentMessage => {\n        this.addMessage(sentMessage);\n      }),\n      catchError(error => {\n        console.error('Failed to send message:', error);\n        return throwError(() => error);\n      })\n    );\n  }\n\n  /**\n   * Mark all messages as read\n   */\n  markAllAsRead(): Observable<void> {\n    return this.apiService.post<void>('/messages/mark-read', {}).pipe(\n      tap(() => {\n        const currentMessages = this.messagesSubject.value;\n        const updatedMessages = currentMessages.map(msg => ({\n          ...msg,\n          read: true\n        }));\n        this.messagesSubject.next(updatedMessages);\n      }),\n      catchError(error => {\n        console.error('Failed to mark messages as read:', error);\n        return throwError(() => error);\n      })\n    );\n  }\n\n  /**\n   * Mark a specific message as read\n   */\n  markMessageAsRead(messageId: string): Observable<void> {\n    return this.apiService.post<void>(`/messages/${messageId}/read`, {}).pipe(\n      tap(() => {\n        this.updateMessageStatus(messageId, { read: true });\n      }),\n      catchError(error => {\n        console.error('Failed to mark message as read:', error);\n        return throwError(() => error);\n      })\n    );\n  }\n\n  /**\n   * Delete a message\n   */\n  deleteMessage(messageId: string): Observable<void> {\n    return this.apiService.delete<void>(`/messages/${messageId}`).pipe(\n      tap(() => {\n        const currentMessages = this.messagesSubject.value;\n        const updatedMessages = currentMessages.filter(msg => msg.id !== messageId);\n        this.messagesSubject.next(updatedMessages);\n      }),\n      catchError(error => {\n        console.error('Failed to delete message:', error);\n        return throwError(() => error);\n      })\n    );\n  }\n\n  /**\n   * Get unread message count\n   */\n  getUnreadCount(): number {\n    return this.messagesSubject.value.filter(msg => !msg.read).length;\n  }\n\n  /**\n   * Clear all messages (for logout)\n   */\n  clearMessages(): void {\n    this.messagesSubject.next([]);\n  }\n\n  /**\n   * Add a message to the local store\n   */\n  private addMessage(message: Message): void {\n    const currentMessages = this.messagesSubject.value;\n    const messageExists = currentMessages.some(msg => msg.id === message.id);\n    \n    if (!messageExists) {\n      const updatedMessages = [message, ...currentMessages];\n      this.messagesSubject.next(updatedMessages);\n    }\n  }\n\n  /**\n   * Update message status\n   */\n  private updateMessageStatus(messageId: string, status: Partial<Message>): void {\n    const currentMessages = this.messagesSubject.value;\n    const updatedMessages = currentMessages.map(msg => \n      msg.id === messageId ? { ...msg, ...status } : msg\n    );\n    this.messagesSubject.next(updatedMessages);\n  }\n\n  /**\n   * Generate a unique message ID\n   */\n  private generateMessageId(): string {\n    return `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\n  }\n\n  /**\n   * Search messages\n   */\n  searchMessages(query: string): Observable<Message[]> {\n    return this.messages$.pipe(\n      map(messages => messages.filter(msg => \n        msg.content.toLowerCase().includes(query.toLowerCase()) ||\n        msg.sender.toLowerCase().includes(query.toLowerCase())\n      ))\n    );\n  }\n\n  /**\n   * Get messages for a specific group\n   */\n  getGroupMessages(groupId: string): Observable<Message[]> {\n    return this.messages$.pipe(\n      map(messages => messages.filter(msg => msg.groupId === groupId))\n    );\n  }\n\n  /**\n   * Get direct messages with a specific user\n   */\n  getDirectMessages(userId: string): Observable<Message[]> {\n    return this.messages$.pipe(\n      map(messages => messages.filter(msg => \n        msg.recipient === userId || msg.sender === userId\n      ))\n    );\n  }\n}\n"], "mappings": "AACA,SAASA,eAAe,EAAcC,OAAO,EAAEC,GAAG,EAAEC,GAAG,EAAEC,UAAU,EAAEC,UAAU,QAAQ,MAAM;;;;AAkB7F,OAAM,MAAOC,cAAc;EAOzBC,YACUC,UAAsB,EACtBC,gBAAkC;IADlC,KAAAD,UAAU,GAAVA,UAAU;IACV,KAAAC,gBAAgB,GAAhBA,gBAAgB;IARlB,KAAAC,eAAe,GAAG,IAAIV,eAAe,CAAY,EAAE,CAAC;IACpD,KAAAW,iBAAiB,GAAG,IAAIV,OAAO,EAAW;IAE3C,KAAAW,SAAS,GAAG,IAAI,CAACF,eAAe,CAACG,YAAY,EAAE;IAC/C,KAAAC,WAAW,GAAG,IAAI,CAACH,iBAAiB,CAACE,YAAY,EAAE;IAMxD,IAAI,CAACE,4BAA4B,EAAE;EACrC;EAEQA,4BAA4BA,CAAA;IAClC;IACA,IAAI,CAACN,gBAAgB,CAACO,EAAE,CAAC,SAAS,CAAC,CAACC,SAAS,CAAEC,OAAgB,IAAI;MACjE,IAAI,CAACC,UAAU,CAACD,OAAO,CAAC;MACxB,IAAI,CAACP,iBAAiB,CAACS,IAAI,CAACF,OAAO,CAAC;IACtC,CAAC,CAAC;IAEF;IACA,IAAI,CAACT,gBAAgB,CAACO,EAAE,CAAC,eAAe,CAAC,CAACC,SAAS,CAAEI,MAAW,IAAI;MAClE,IAAI,CAACC,mBAAmB,CAACD,MAAM,CAACE,SAAS,EAAEF,MAAM,CAAC;IACpD,CAAC,CAAC;EACJ;EAEA;;;EAGAG,YAAYA,CAAA;IACV,OAAO,IAAI,CAAChB,UAAU,CAACiB,GAAG,CAAY,WAAW,CAAC,CAACC,IAAI,CACrDxB,GAAG,CAACyB,QAAQ,IAAIA,QAAQ,CAACzB,GAAG,CAAC0B,GAAG,KAAK;MACnC,GAAGA,GAAG;MACNC,SAAS,EAAE,IAAIC,IAAI,CAACF,GAAG,CAACC,SAAS,CAAC;MAClCE,IAAI,EAAEH,GAAG,CAACG,IAAI,IAAI;KACnB,CAAC,CAAC,CAAC,EACJ5B,GAAG,CAACwB,QAAQ,IAAG;MACb,IAAI,CAACjB,eAAe,CAACU,IAAI,CAACO,QAAQ,CAAC;IACrC,CAAC,CAAC,EACFvB,UAAU,CAAC4B,KAAK,IAAG;MACjBC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChD,OAAO3B,UAAU,CAAC,MAAM2B,KAAK,CAAC;IAChC,CAAC,CAAC,CACH;EACH;EAEA;;;EAGAE,WAAWA,CAAChB,OAAyB;IACnC,MAAMiB,aAAa,GAAG;MACpB,GAAGjB,OAAO;MACVW,SAAS,EAAE,IAAIC,IAAI,EAAE;MACrBM,EAAE,EAAE,IAAI,CAACC,iBAAiB;KAC3B;IAED,OAAO,IAAI,CAAC7B,UAAU,CAAC8B,IAAI,CAAU,WAAW,EAAEH,aAAa,CAAC,CAACT,IAAI,CACnExB,GAAG,CAACqC,QAAQ,KAAK;MACf,GAAGA,QAAQ;MACXV,SAAS,EAAE,IAAIC,IAAI,CAACS,QAAQ,CAACV,SAAS;KACvC,CAAC,CAAC,EACH1B,GAAG,CAACqC,WAAW,IAAG;MAChB,IAAI,CAACrB,UAAU,CAACqB,WAAW,CAAC;IAC9B,CAAC,CAAC,EACFpC,UAAU,CAAC4B,KAAK,IAAG;MACjBC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/C,OAAO3B,UAAU,CAAC,MAAM2B,KAAK,CAAC;IAChC,CAAC,CAAC,CACH;EACH;EAEA;;;EAGAS,aAAaA,CAAA;IACX,OAAO,IAAI,CAACjC,UAAU,CAAC8B,IAAI,CAAO,qBAAqB,EAAE,EAAE,CAAC,CAACZ,IAAI,CAC/DvB,GAAG,CAAC,MAAK;MACP,MAAMuC,eAAe,GAAG,IAAI,CAAChC,eAAe,CAACiC,KAAK;MAClD,MAAMC,eAAe,GAAGF,eAAe,CAACxC,GAAG,CAAC0B,GAAG,KAAK;QAClD,GAAGA,GAAG;QACNG,IAAI,EAAE;OACP,CAAC,CAAC;MACH,IAAI,CAACrB,eAAe,CAACU,IAAI,CAACwB,eAAe,CAAC;IAC5C,CAAC,CAAC,EACFxC,UAAU,CAAC4B,KAAK,IAAG;MACjBC,OAAO,CAACD,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;MACxD,OAAO3B,UAAU,CAAC,MAAM2B,KAAK,CAAC;IAChC,CAAC,CAAC,CACH;EACH;EAEA;;;EAGAa,iBAAiBA,CAACtB,SAAiB;IACjC,OAAO,IAAI,CAACf,UAAU,CAAC8B,IAAI,CAAO,aAAaf,SAAS,OAAO,EAAE,EAAE,CAAC,CAACG,IAAI,CACvEvB,GAAG,CAAC,MAAK;MACP,IAAI,CAACmB,mBAAmB,CAACC,SAAS,EAAE;QAAEQ,IAAI,EAAE;MAAI,CAAE,CAAC;IACrD,CAAC,CAAC,EACF3B,UAAU,CAAC4B,KAAK,IAAG;MACjBC,OAAO,CAACD,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;MACvD,OAAO3B,UAAU,CAAC,MAAM2B,KAAK,CAAC;IAChC,CAAC,CAAC,CACH;EACH;EAEA;;;EAGAc,aAAaA,CAACvB,SAAiB;IAC7B,OAAO,IAAI,CAACf,UAAU,CAACuC,MAAM,CAAO,aAAaxB,SAAS,EAAE,CAAC,CAACG,IAAI,CAChEvB,GAAG,CAAC,MAAK;MACP,MAAMuC,eAAe,GAAG,IAAI,CAAChC,eAAe,CAACiC,KAAK;MAClD,MAAMC,eAAe,GAAGF,eAAe,CAACM,MAAM,CAACpB,GAAG,IAAIA,GAAG,CAACQ,EAAE,KAAKb,SAAS,CAAC;MAC3E,IAAI,CAACb,eAAe,CAACU,IAAI,CAACwB,eAAe,CAAC;IAC5C,CAAC,CAAC,EACFxC,UAAU,CAAC4B,KAAK,IAAG;MACjBC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjD,OAAO3B,UAAU,CAAC,MAAM2B,KAAK,CAAC;IAChC,CAAC,CAAC,CACH;EACH;EAEA;;;EAGAiB,cAAcA,CAAA;IACZ,OAAO,IAAI,CAACvC,eAAe,CAACiC,KAAK,CAACK,MAAM,CAACpB,GAAG,IAAI,CAACA,GAAG,CAACG,IAAI,CAAC,CAACmB,MAAM;EACnE;EAEA;;;EAGAC,aAAaA,CAAA;IACX,IAAI,CAACzC,eAAe,CAACU,IAAI,CAAC,EAAE,CAAC;EAC/B;EAEA;;;EAGQD,UAAUA,CAACD,OAAgB;IACjC,MAAMwB,eAAe,GAAG,IAAI,CAAChC,eAAe,CAACiC,KAAK;IAClD,MAAMS,aAAa,GAAGV,eAAe,CAACW,IAAI,CAACzB,GAAG,IAAIA,GAAG,CAACQ,EAAE,KAAKlB,OAAO,CAACkB,EAAE,CAAC;IAExE,IAAI,CAACgB,aAAa,EAAE;MAClB,MAAMR,eAAe,GAAG,CAAC1B,OAAO,EAAE,GAAGwB,eAAe,CAAC;MACrD,IAAI,CAAChC,eAAe,CAACU,IAAI,CAACwB,eAAe,CAAC;;EAE9C;EAEA;;;EAGQtB,mBAAmBA,CAACC,SAAiB,EAAEF,MAAwB;IACrE,MAAMqB,eAAe,GAAG,IAAI,CAAChC,eAAe,CAACiC,KAAK;IAClD,MAAMC,eAAe,GAAGF,eAAe,CAACxC,GAAG,CAAC0B,GAAG,IAC7CA,GAAG,CAACQ,EAAE,KAAKb,SAAS,GAAG;MAAE,GAAGK,GAAG;MAAE,GAAGP;IAAM,CAAE,GAAGO,GAAG,CACnD;IACD,IAAI,CAAClB,eAAe,CAACU,IAAI,CAACwB,eAAe,CAAC;EAC5C;EAEA;;;EAGQP,iBAAiBA,CAAA;IACvB,OAAO,OAAOP,IAAI,CAACwB,GAAG,EAAE,IAAIC,IAAI,CAACC,MAAM,EAAE,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;EACvE;EAEA;;;EAGAC,cAAcA,CAACC,KAAa;IAC1B,OAAO,IAAI,CAAChD,SAAS,CAACc,IAAI,CACxBxB,GAAG,CAACyB,QAAQ,IAAIA,QAAQ,CAACqB,MAAM,CAACpB,GAAG,IACjCA,GAAG,CAACiC,OAAO,CAACC,WAAW,EAAE,CAACC,QAAQ,CAACH,KAAK,CAACE,WAAW,EAAE,CAAC,IACvDlC,GAAG,CAACoC,MAAM,CAACF,WAAW,EAAE,CAACC,QAAQ,CAACH,KAAK,CAACE,WAAW,EAAE,CAAC,CACvD,CAAC,CACH;EACH;EAEA;;;EAGAG,gBAAgBA,CAACC,OAAe;IAC9B,OAAO,IAAI,CAACtD,SAAS,CAACc,IAAI,CACxBxB,GAAG,CAACyB,QAAQ,IAAIA,QAAQ,CAACqB,MAAM,CAACpB,GAAG,IAAIA,GAAG,CAACsC,OAAO,KAAKA,OAAO,CAAC,CAAC,CACjE;EACH;EAEA;;;EAGAC,iBAAiBA,CAACC,MAAc;IAC9B,OAAO,IAAI,CAACxD,SAAS,CAACc,IAAI,CACxBxB,GAAG,CAACyB,QAAQ,IAAIA,QAAQ,CAACqB,MAAM,CAACpB,GAAG,IACjCA,GAAG,CAACyC,SAAS,KAAKD,MAAM,IAAIxC,GAAG,CAACoC,MAAM,KAAKI,MAAM,CAClD,CAAC,CACH;EACH;;;uBAvMW9D,cAAc,EAAAgE,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,gBAAA;IAAA;EAAA;;;aAAdrE,cAAc;MAAAsE,OAAA,EAAdtE,cAAc,CAAAuE,IAAA;MAAAC,UAAA,EAFb;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}