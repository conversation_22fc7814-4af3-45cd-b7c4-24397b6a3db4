import { Injectable, OnModuleInit } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { DataSource } from 'typeorm';
import * as crypto from 'crypto';

@Injectable()
export class DatabaseService implements OnModuleInit {
  private encryptionKey: Buffer;

  constructor(
    private readonly dataSource: DataSource,
    private readonly configService: ConfigService,
  ) {
    // In production, this should be loaded from a secure HSM
    this.encryptionKey = this.generateEncryptionKey();
  }

  async onModuleInit() {
    await this.initializeDatabase();
  }

  private generateEncryptionKey(): Buffer {
    const key = this.configService.get<string>('DB_ENCRYPTION_KEY');
    if (!key) {
      throw new Error('DB_ENCRYPTION_KEY environment variable is required');
    }
    return crypto.scryptSync(key, 'salt', 32);
  }

  private async initializeDatabase() {
    if (!this.dataSource.isInitialized) {
      await this.dataSource.initialize();
    }

    // Enable encryption for the database
    await this.dataSource.query('PRAGMA cipher = "aes-256-gcm"');
    await this.dataSource.query('PRAGMA kdf_iter = 64000');
    await this.dataSource.query('PRAGMA cipher_page_size = 4096');
    await this.dataSource.query('PRAGMA cipher_use_hmac = ON');
    
    // Set the encryption key
    await this.dataSource.query(`PRAGMA key = '${this.encryptionKey.toString('hex')}'`);
  }

  async rotateEncryptionKey(newKey: Buffer) {
    // Backup the current database
    await this.dataSource.query('PRAGMA backup');
    
    // Set the new key
    await this.dataSource.query(`PRAGMA rekey = '${newKey.toString('hex')}'`);
    
    // Update the encryption key
    this.encryptionKey = newKey;
  }

  async verifyDatabaseIntegrity(): Promise<boolean> {
    try {
      const result = await this.dataSource.query('PRAGMA integrity_check');
      return result[0].integrity_check === 'ok';
    } catch (error) {
      return false;
    }
  }
} 